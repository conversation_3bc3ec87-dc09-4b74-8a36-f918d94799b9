en:
  data_source:
    no_data: "No data"
    errors:
      file_not_found: "File not found: %{path}"
      is_directory: "Path is a directory, not a file: %{path}"
      file_too_large: "File too large: %{size}MB exceeds maximum allowed size of %{max}MB"
      file_path_empty: "File path is empty in connection settings"
      csv_not_found: "CSV file not found: %{path}"
      download_failed: "Failed to download file: %{error}"
      invalid_csv_format: "Invalid CSV format: %{error}"
      encoding_error: "Encoding error: %{error}. Try specifying a different encoding."
      connection_failed: "Connection failed: %{error}"
      sync_failed: "Sync failed: %{error}"
      fetch_schema_failed: "Failed to fetch schema: %{error}"
      fetch_sample_data_failed: "Failed to fetch sample data: %{error}"
      upload_failed: "Failed to upload CSV file: %{error}"
      cannot_sync_inactive: "Cannot sync inactive source"
      connector_not_implemented: "Connector not implemented for %{type}"
    success:
      connection_successful: "Connection successful!"
      sync_started: "Sync started"
      csv_upload_successful: "CSV file uploaded successfully"
      file_path_fixed: "File path has been fixed. Please test the connection."
      data_source_created: "Data source created successfully!"
      data_source_updated: "Data source updated successfully!"
      data_source_deleted: "Data source deleted successfully!"
      connection_test_passed: "Data source created and connection successful!"
    warnings:
      encoding_issues: "Some rows had encoding issues. Consider specifying the correct encoding."
      parse_issues: "Some rows could not be parsed. Check delimiter and format settings."
      error_limit_reached: "Error limit reached. There may be more errors not shown."
      file_not_found_reupload: "Could not find the uploaded file. Please re-upload."
      upload_dir_missing: "Upload directory does not exist. Please re-upload the file."
      not_csv_action: "This action is only for CSV data sources."
    status:
      pending: "Pending"
      running: "Running"
      completed: "Completed"
      failed: "Failed"
      cancelled: "Cancelled"
    messages:
      coming_soon_html: |
        <div class='bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-lg'>
          <p class='font-medium'>Coming Soon</p>
          <p class='text-sm mt-1'>Connection settings for %{type} are not yet implemented.</p>
        </div>
      invalid_source_type: "Invalid source type"
      source_not_found: "Data source not found"