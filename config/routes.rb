Rails.application.routes.draw do
  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Root route - Landing Page
  root "landing#index"
  
  # Marketing pages
  get 'features', to: 'landing#features'
  get 'pricing', to: 'landing#pricing'
  get 'about', to: 'landing#about'
  get 'contact', to: 'landing#contact'
  get 'faq', to: 'landing#faq'
  get 'terms', to: 'landing#terms'
  get 'privacy', to: 'landing#privacy'
  get 'blog', to: 'landing#blog'
  get 'careers', to: 'landing#careers'
  get 'support', to: 'landing#support'
  get 'demo', to: 'landing#demo'
  get 'trial', to: 'landing#trial'
  get 'success', to: 'landing#success'
  get 'help_center', to: 'landing#help_center'
  
  # Demo and trial endpoints
  post 'request-demo', to: 'landing#request_demo', as: :request_demo
  post 'start-trial', to: 'landing#start_trial', as: :start_trial
  
  # Authentication (Devise routes will be added here)
  devise_for :users, controllers: {
    sessions: 'users/sessions',
    registrations: 'users/registrations',
    passwords: 'users/passwords',
    confirmations: 'users/confirmations'
  }
  
  # Dashboard - requires authentication
  authenticate :user do
    namespace :dashboard do
      root to: 'overview#index'
      
      # Analytics
      resources :analytics, only: [:index] do
        collection do
          get :revenue
          get :customers
          get :performance
          get :sync_performance
          get :data_insights
          get :export
        end
      end
      
      # Data Sources
      resources :data_sources do
        member do
          post :connect
          post :disconnect
          post :sync
          post :test_connection
          get :schema
          get :sample_data
          get :debug
          post :fix_file_path
          get :sync_history
        end
        collection do
          get "connection_fields/:source_type", action: :connection_fields, as: :connection_fields
          get "enhanced_connection_fields/:source_type", action: :enhanced_connection_fields, as: :enhanced_connection_fields
          post :test_connection_with_params
          get :test
          get :stimulus_test
          get :simple_test
          get :new_modal
        end
      end
      
      # Pipelines
      resources :pipelines do
        member do
          post :run
          post :schedule
          post :pause
          get :logs
        end
        resources :pipeline_steps
      end
      
      # Reports
      resources :reports do
        member do
          post :generate
          get :download
          post :share
        end
      end
      
      # Predictions
      resources :predictions do
        collection do
          get :models
          post :train
        end
      end
      
      # Settings
      namespace :settings do
        root to: 'general#index'
        resources :integrations
        resources :team_members
        resources :api_keys
        get :billing
        get :security
        get :notifications
      end
      
      # Monitoring
      resources :monitoring, only: [:index] do
        collection do
          get :metrics
          get :alerts
        end
        member do
          post :acknowledge_alert
          post :resolve_alert
        end
      end
    end
  end
  
  # API endpoints
  namespace :api do
    namespace :v1 do
      resources :data_imports, only: [:create]
      resources :analytics, only: [:index, :show]
      resources :predictions, only: [:create, :show]
      resources :reports, only: [:index, :show, :create]
      
      # Webhook endpoints
      post 'webhooks/:provider', to: 'webhooks#handle'
    end
  end
  
  # Admin interface (optional)
  # authenticate :user, ->(user) { user.admin? } do
  #   mount Administrate::Engine => '/admin'
  # end
  
  # Background job monitoring can be added here when needed
  # For Solid Queue, you can mount Mission Control - Jobs
  # authenticate :user, ->(user) { user.admin? } do
  #   mount MissionControl::Jobs::Engine => '/jobs'
  # end
  
  # Error pages
  match '/404', to: 'errors#not_found', via: :all
  match '/422', to: 'errors#unprocessable', via: :all
  match '/500', to: 'errors#internal_server', via: :all
  
  # Catch all route - must be last
  match '*path', to: 'errors#not_found', via: :all
end