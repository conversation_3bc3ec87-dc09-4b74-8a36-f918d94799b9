require_relative "boot"

require "rails/all"

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module DataReflow
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 8.0

    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    # config.time_zone = "Central Time (US & Canada)"
    # config.eager_load_paths << Rails.root.join("extras")
    
    # Add services directory to autoload paths
    config.autoload_paths << Rails.root.join("app/services")

    # Set default timezone
    config.time_zone = "UTC"
    
    # Set default locale
    config.i18n.default_locale = :en
    config.i18n.available_locales = [:en]
    
    # Skip generation of helpers, javascripts, and stylesheets for scaffold
    config.generators do |g|
      g.helper false
      g.assets false
      g.test_framework :rspec,
        fixtures: true,
        view_specs: false,
        helper_specs: false,
        routing_specs: false,
        controller_specs: false,
        request_specs: true
      g.fixture_replacement :factory_bot, dir: "spec/factories"
    end
    
    # Configure Active Job with Solid Queue
    config.active_job.queue_adapter = :solid_queue
    
    # Allow Action Cable connections from specified origins
    config.action_cable.allowed_request_origins = [
      %r{https?://localhost:\d+},
      'https://datareflow.com',
      'https://www.datareflow.com'
    ]
    
    # Configure Active Storage
    config.active_storage.variant_processor = :mini_magick
    
    # Use SQL schema format
    config.active_record.schema_format = :sql
    
    # Enable/disable caching in development
    config.action_controller.perform_caching = true
    
    # Configure session store
    config.session_store :cookie_store, key: '_data_reflow_session'
    
    # Security headers
    config.force_ssl = true unless Rails.env.development? || Rails.env.test?
    
    # Content Security Policy
    config.content_security_policy do |policy|
      policy.default_src :self, :https
      policy.font_src    :self, :https, :data
      policy.img_src     :self, :https, :data
      policy.object_src  :none
      
      if Rails.env.development?
        # In development, allow nonce-based scripts and unsafe-eval for better debugging
        policy.script_src  :self, :https, :unsafe_eval
        policy.style_src   :self, :https, :unsafe_inline
        policy.connect_src :self, :https, "http://localhost:3035", "ws://localhost:3035"
      else
        # In production, use strict CSP with nonces
        policy.script_src  :self, :https
        policy.style_src   :self, :https, :unsafe_inline
        policy.connect_src :self, :https
      end
    end
    
    # Permitted locales available for the application
    I18n.available_locales = [:en]
    
    # Set default headers
    config.action_dispatch.default_headers = {
      'X-Frame-Options' => 'SAMEORIGIN',
      'X-XSS-Protection' => '1; mode=block',
      'X-Content-Type-Options' => 'nosniff',
      'X-Download-Options' => 'noopen',
      'X-Permitted-Cross-Domain-Policies' => 'none',
      'Referrer-Policy' => 'strict-origin-when-cross-origin'
    }
  end
end
