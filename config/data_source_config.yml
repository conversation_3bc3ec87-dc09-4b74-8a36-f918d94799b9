# DataSource Configuration
# This file contains all configurable values for data sources

defaults: &defaults
  # File processing limits
  csv:
    max_file_size: <%= ENV.fetch('CSV_MAX_FILE_SIZE', '2147483648') %> # 2GB in bytes
    chunk_size: <%= ENV.fetch('CSV_CHUNK_SIZE', '10000') %>
    batch_size: <%= ENV.fetch('CSV_BATCH_SIZE', '1000') %>
    sample_read_size: <%= ENV.fetch('CSV_SAMPLE_READ_SIZE', '10000') %>
    row_estimate_lines: <%= ENV.fetch('CSV_ROW_ESTIMATE_LINES', '1000') %>
    error_limit: <%= ENV.fetch('CSV_ERROR_LIMIT', '100') %>
    
  # Database defaults
  postgresql:
    default_port: <%= ENV.fetch('POSTGRESQL_DEFAULT_PORT', '5432') %>
    default_timeout: <%= ENV.fetch('POSTGRESQL_DEFAULT_TIMEOUT', '10') %>
    
  mysql:
    default_port: <%= ENV.fetch('MYSQL_DEFAULT_PORT', '3306') %>
    default_timeout: <%= ENV.fetch('MYSQL_DEFAULT_TIMEOUT', '10') %>
    
  # Pagination and display limits
  ui:
    recent_syncs_limit: <%= ENV.fetch('RECENT_SYNCS_LIMIT', '5') %>
    sample_data_limit: <%= ENV.fetch('SAMPLE_DATA_LIMIT', '20') %>
    pagination_per_page: <%= ENV.fetch('PAGINATION_PER_PAGE', '20') %>
    
  # Upload configuration
  uploads:
    base_path: <%= ENV.fetch('UPLOADS_BASE_PATH', 'storage/uploads') %>
    
  # Sync configuration
  sync:
    needs_sync_threshold_hours: <%= ENV.fetch('NEEDS_SYNC_THRESHOLD_HOURS', '1') %>
    
  # Sync frequencies (in seconds)
  sync_frequencies:
    realtime: 60          # 1 minute
    every_5_minutes: 300  # 5 minutes
    every_15_minutes: 900 # 15 minutes
    every_30_minutes: 1800 # 30 minutes
    hourly: 3600          # 1 hour
    every_6_hours: 21600  # 6 hours
    daily: 86400          # 1 day
    weekly: 604800        # 1 week
    monthly: 2592000      # 30 days

development:
  <<: *defaults

test:
  <<: *defaults
  csv:
    max_file_size: 10485760 # 10MB for tests
    chunk_size: 100
    batch_size: 50

production:
  <<: *defaults
  csv:
    max_file_size: <%= ENV.fetch('CSV_MAX_FILE_SIZE', '5368709120') %> # 5GB in production