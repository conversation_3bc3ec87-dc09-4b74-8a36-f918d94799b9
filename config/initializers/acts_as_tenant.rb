# ActsAsTenant configuration for multi-tenancy
require 'acts_as_tenant'

# Configure ActsAsTenant
ActsAsTenant.configure do |config|
  # Require tenant to be set in all controllers
  config.require_tenant = false # We'll enable this after setting up models
  
  # Use the current subdomain to identify the tenant
  # This can be customized based on your needs
  # config.tenant_identifier = :subdomain
end