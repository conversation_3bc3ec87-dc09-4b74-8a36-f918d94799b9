# Load data source configuration
require 'erb'
require 'yaml'

module DataSourceConfig
  class << self
    def config
      @config ||= load_config
    end
    
    def csv
      config['csv']
    end
    
    def postgresql
      config['postgresql']
    end
    
    def mysql
      config['mysql']
    end
    
    def ui
      config['ui']
    end
    
    def uploads
      config['uploads']
    end
    
    def sync
      config['sync']
    end
    
    def sync_frequencies
      config['sync_frequencies']
    end
    
    private
    
    def load_config
      yaml_content = File.read(Rails.root.join('config', 'data_source_config.yml'))
      erb_result = ERB.new(yaml_content).result
      all_config = YAML.safe_load(erb_result, permitted_classes: [Symbol], aliases: true)
      all_config[Rails.env] || all_config['defaults']
    end
  end
end

# Freeze the configuration to prevent modifications
DataSourceConfig.config.freeze