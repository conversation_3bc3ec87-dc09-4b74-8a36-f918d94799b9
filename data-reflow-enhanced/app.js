// Data Reflow Platform - Enhanced Landing Page with Color Scheme System

// Application data
const platformData = {
  platform_name: "Data Reflow Platform",
  tagline: "Transform Your Data Into Competitive Advantage",
  key_benefits: [
    "Real-time Data Processing",
    "Automated ETL/ELT Pipelines", 
    "Smart Data Mapping",
    "Quality Scoring & Monitoring",
    "600+ Pre-built Connectors",
    "Custom Connector Builder"
  ],
  pricing_tiers: [
    {
      name: "Starter",
      price: 29,
      features: ["Up to 5 data sources", "Basic ETL/ELT", "Email support", "Standard dashboards"],
      cta: "Start Free Trial"
    },
    {
      name: "Professional", 
      price: 99,
      features: ["Unlimited data sources", "Advanced transformations", "Priority support", "Custom dashboards", "API access"],
      cta: "Start Free Trial",
      popular: true
    },
    {
      name: "Enterprise",
      price: "Custom",
      features: ["Custom connectors", "Dedicated support", "SLA guarantees", "Advanced security", "White-glove onboarding"],
      cta: "Contact Sales"
    }
  ],
  testimonials: [
    {
      quote: "Data Reflow transformed our SME operations with seamless ETL processes. We've seen 40% faster insights and reduced manual work by 80%.",
      author: "<PERSON> Johnson",
      title: "CTO, TechFlow Solutions",
      company: "TechFlow",
      roi: "300% ROI in 6 months"
    },
    {
      quote: "The smart data mapping feature saved us weeks of setup time. Our team can now focus on analysis instead of data wrangling.",
      author: "Michael Chen",
      title: "Data Director, RetailPlus",
      company: "RetailPlus",
      roi: "50% reduction in setup time"
    },
    {
      quote: "Outstanding platform! The custom connector builder allowed us to integrate our niche industry tools seamlessly.",
      author: "Lisa Rodriguez",
      title: "VP Analytics, HealthCore",
      company: "HealthCore",
      roi: "60% improvement in data quality"
    }
  ],
  client_logos: ["Microsoft", "Shopify", "Salesforce", "Stripe", "Asana", "Adobe", "Zoom", "HubSpot"],
  industry_solutions: [
    {
      name: "Healthcare",
      pain_points: ["Patient data silos", "Compliance requirements", "Real-time monitoring needs"],
      solutions: ["HIPAA-compliant processing", "Unified patient views", "Real-time alerts"],
      roi: "40% faster diagnosis times"
    },
    {
      name: "Finance", 
      pain_points: ["Regulatory reporting", "Risk assessment", "Market data integration"],
      solutions: ["SOX compliance", "Real-time risk monitoring", "Multi-source integration"],
      roi: "60% reduction in reporting time"
    },
    {
      name: "Retail",
      pain_points: ["Inventory data silos", "Customer behavior tracking", "Supply chain visibility"],
      solutions: ["Unified inventory management", "360° customer analytics", "Supply chain optimization"],
      roi: "35% improvement in inventory turnover"
    },
    {
      name: "Manufacturing",
      pain_points: ["Production data isolation", "Quality control monitoring", "Equipment maintenance scheduling"],
      solutions: ["Integrated production analytics", "Real-time quality monitoring", "Predictive maintenance"],
      roi: "25% reduction in downtime"
    }
  ],
  statistics: {
    customers: "2,500+",
    data_processed: "10TB+", 
    uptime: "99.9%",
    time_savings: "80%"
  },
  trust_indicators: [
    "SOC 2 Type II Certified",
    "GDPR Compliant", 
    "256-bit SSL Encryption",
    "99.9% Uptime SLA"
  ]
};

// Color schemes configuration
const colorSchemes = {
  default: {
    name: "Default Teal",
    primary: "#21808D",
    secondary: "#5E5240",
    accent: "#A84B2F"
  },
  sunset: {
    name: "Sunset Energy",
    primary: "#6B46C1",
    secondary: "#FF6B35", 
    accent: "#FF8A80"
  },
  electric: {
    name: "Electric Modern",
    primary: "#0EA5E9",
    secondary: "#10B981",
    accent: "#FBBF24"
  },
  earthy: {
    name: "Earthy Professional", 
    primary: "#059669",
    secondary: "#DC6803",
    accent: "#D97706"
  },
  royal: {
    name: "Royal Elegance",
    primary: "#1E3A8A",
    secondary: "#EC4899",
    accent: "#6EE7B7"
  },
  vibrant: {
    name: "Vibrant Tech",
    primary: "#DB2777",
    secondary: "#06B6D4", 
    accent: "#84CC16"
  }
};

// Enhanced application data
const appData = {
  keyBenefits: [
    {
      icon: "bolt",
      title: "Real-time Processing",
      description: "Process millions of records in real-time with our distributed architecture"
    },
    {
      icon: "brain", 
      title: "AI-Powered Mapping",
      description: "Smart field mapping with 95% accuracy using machine learning algorithms"
    },
    {
      icon: "shield-check",
      title: "Enterprise Security", 
      description: "Bank-grade security with end-to-end encryption and compliance certifications"
    },
    {
      icon: "chart-line",
      title: "Advanced Analytics",
      description: "Get actionable insights with predictive analytics and custom dashboards"
    }
  ],
  features: [
    {
      icon: "database",
      title: "Smart Data Mapping",
      description: "AI-powered field mapping with 95% accuracy. Automatically detects relationships and suggests optimal transformations.",
      benefits: ["Reduces setup time by 80%", "Eliminates mapping errors", "Self-learning algorithms"]
    },
    {
      icon: "check-double", 
      title: "Quality Scoring & Monitoring",
      description: "Real-time data quality assessment with automated anomaly detection and cleansing recommendations.",
      benefits: ["Improves accuracy by 40%", "Automated issue detection", "Compliance reporting"]
    },
    {
      icon: "cogs",
      title: "ETL/ELT Processing", 
      description: "Flexible data transformation supporting both ETL and ELT patterns for any scale of operation.",
      benefits: ["Handles billions of records", "Real-time & batch processing", "Custom transformations"]
    },
    {
      icon: "plug",
      title: "600+ Pre-built Connectors",
      description: "Extensive connector library for all major databases, APIs, and cloud services with no-code setup.",
      benefits: ["Instant connectivity", "No coding required", "Regular updates"]
    },
    {
      icon: "users",
      title: "Collaboration Tools",
      description: "Built-in collaboration features for teams with role-based access and approval workflows.", 
      benefits: ["Team workspaces", "Approval workflows", "Audit trails"]
    },
    {
      icon: "mobile-alt",
      title: "Mobile Analytics",
      description: "Access your dashboards and receive alerts on any device with our responsive mobile interface.",
      benefits: ["iOS & Android apps", "Push notifications", "Offline access"]
    }
  ],
  solutionsData: {
    "Healthcare": {
      icon: "heartbeat",
      pain_points: ["Patient data scattered across systems", "HIPAA compliance requirements", "Real-time monitoring needs", "Integration complexity"],
      solutions: ["HIPAA-compliant data processing", "Unified patient data views", "Real-time health monitoring", "Seamless EHR integration"],
      roi: "40% faster diagnosis times",
      case_study: "MedCorp reduced patient data retrieval time by 65% while maintaining full HIPAA compliance."
    },
    "Finance": {
      icon: "university", 
      pain_points: ["Regulatory reporting complexity", "Risk assessment challenges", "Market data fragmentation", "Compliance costs"],
      solutions: ["Automated SOX compliance", "Real-time risk monitoring", "Multi-source data integration", "Regulatory report generation"],
      roi: "60% reduction in reporting time", 
      case_study: "FinanceFlow automated 80% of their regulatory reporting processes."
    },
    "Retail": {
      icon: "shopping-cart",
      pain_points: ["Inventory data silos", "Customer behavior tracking", "Supply chain visibility", "Seasonal demand forecasting"],
      solutions: ["Unified inventory management", "360° customer analytics", "Supply chain optimization", "Predictive demand planning"],
      roi: "35% improvement in inventory turnover",
      case_study: "RetailPlus increased customer satisfaction by 50% with real-time inventory insights."
    },
    "Manufacturing": {
      icon: "industry",
      pain_points: ["Production data isolation", "Quality control monitoring", "Equipment maintenance scheduling", "Supply chain disruptions"],
      solutions: ["Integrated production analytics", "Real-time quality monitoring", "Predictive maintenance", "Supply chain visibility"], 
      roi: "25% reduction in downtime",
      case_study: "ManufacturingPro reduced unplanned downtime by 45% with predictive analytics."
    }
  },
  enterpriseFeatures: [
    {
      icon: "shield-alt",
      title: "Advanced Security",
      description: "Enterprise-grade security with field-level encryption, VPC deployment, and SOC 2 compliance."
    },
    {
      icon: "headset", 
      title: "Dedicated Support",
      description: "24/7 dedicated support team with guaranteed response times and technical account management."
    },
    {
      icon: "code",
      title: "Custom Connectors", 
      description: "Build custom connectors for proprietary systems with our SDK and professional services team."
    },
    {
      icon: "server",
      title: "On-Premise Deployment",
      description: "Deploy Data Reflow in your own infrastructure with full control and customization options."
    },
    {
      icon: "chart-bar",
      title: "Advanced Analytics",
      description: "Custom ML models, advanced statistical analysis, and integration with your existing BI tools."
    },
    {
      icon: "users-cog",
      title: "White-glove Onboarding", 
      description: "Comprehensive onboarding program with data migration assistance and team training."
    }
  ],
  trustIndicators: [
    {
      icon: "shield-check",
      title: "SOC 2 Type II",
      description: "Independently audited security controls and processes"
    },
    {
      icon: "lock",
      title: "GDPR Compliant",
      description: "Full compliance with European data protection regulations"
    },
    {
      icon: "certificate", 
      title: "ISO 27001",
      description: "International standard for information security management"
    },
    {
      icon: "server",
      title: "99.9% Uptime",
      description: "Guaranteed service availability with redundant infrastructure"
    }
  ]
};

// Application state
let isDarkMode = false;
let currentColorScheme = 'default';
let isMenuOpen = false;
let isAnnualPricing = false;
let currentSolutionTab = 'Healthcare';
let animatedCounters = new Set();
let observers = [];
let isColorSchemeDropdownOpen = false;

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
  initializeTheme();
  initializeColorScheme();
  setupEventListeners();
  populateContent();
  setupScrollAnimations();
  setupIntersectionObserver();
});

function initializeTheme() {
  const savedTheme = localStorage.getItem('dataReflow-theme');
  const systemDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
  
  isDarkMode = savedTheme ? savedTheme === 'dark' : systemDarkMode;
  updateTheme();
  setupNavbarScroll();
}

function initializeColorScheme() {
  const savedScheme = localStorage.getItem('dataReflow-color-scheme') || 'default';
  currentColorScheme = savedScheme;
  applyColorScheme(savedScheme, false);
  updateColorSchemeSelector();
}

function updateTheme() {
  const themeToggle = document.getElementById('themeToggle');
  if (!themeToggle) return;
  
  const icon = themeToggle.querySelector('i');
  const rootEl = document.documentElement;
  
  if (isDarkMode) {
    rootEl.setAttribute('data-theme', 'dark');
    if (icon) icon.className = 'fas fa-sun';
  } else {
    rootEl.setAttribute('data-theme', 'light');
    if (icon) icon.className = 'fas fa-moon';
  }
}

function applyColorScheme(scheme, animate = true) {
  const rootEl = document.documentElement;
  
  if (animate) {
    rootEl.classList.add('scheme-switching');
  }
  
  rootEl.setAttribute('data-color-scheme', scheme);
  currentColorScheme = scheme;
  
  if (animate) {
    setTimeout(() => {
      rootEl.classList.remove('scheme-switching');
    }, 500);
  }
  
  localStorage.setItem('dataReflow-color-scheme', scheme);
}

function updateColorSchemeSelector() {
  const options = document.querySelectorAll('.color-scheme-option');
  options.forEach(option => {
    const scheme = option.dataset.scheme;
    option.classList.toggle('active', scheme === currentColorScheme);
  });
}

function setupEventListeners() {
  // Theme toggle
  const themeToggle = document.getElementById('themeToggle');
  if (themeToggle) {
    themeToggle.addEventListener('click', toggleTheme);
  }

  // Color scheme selector
  const colorSchemeToggle = document.getElementById('colorSchemeToggle');
  const colorSchemeDropdown = document.getElementById('colorSchemeDropdown');
  
  if (colorSchemeToggle) {
    colorSchemeToggle.addEventListener('click', function(e) {
      e.stopPropagation();
      toggleColorSchemeDropdown();
    });
  }
  
  // Color scheme options
  const colorSchemeOptions = document.querySelectorAll('.color-scheme-option');
  colorSchemeOptions.forEach(option => {
    option.addEventListener('click', function() {
      const scheme = this.dataset.scheme;
      applyColorScheme(scheme);
      updateColorSchemeSelector();
      closeColorSchemeDropdown();
      showNotification(`✨ Switched to ${colorSchemes[scheme].name} theme`, 'success');
    });
  });
  
  // Close dropdown when clicking outside
  document.addEventListener('click', function(e) {
    if (!e.target.closest('.color-scheme-selector')) {
      closeColorSchemeDropdown();
    }
  });

  // Mobile menu toggle
  const menuToggle = document.getElementById('menuToggle');
  const navMenu = document.getElementById('navMenu');
  
  if (menuToggle && navMenu) {
    menuToggle.addEventListener('click', function() {
      isMenuOpen = !isMenuOpen;
      navMenu.classList.toggle('active', isMenuOpen);
      menuToggle.classList.toggle('active', isMenuOpen);
      document.body.style.overflow = isMenuOpen ? 'hidden' : '';
    });
  }

  // Smooth scrolling for navigation links
  document.addEventListener('click', handleNavigation);

  // CTA buttons
  document.addEventListener('click', handleCTAButtons);

  // Modal functionality
  setupModalHandlers();

  // ROI Calculator
  setupROICalculator();

  // Pricing toggle
  setupPricingToggle();

  // Solutions tabs
  setupSolutionsTabs();
}

function toggleColorSchemeDropdown() {
  const dropdown = document.getElementById('colorSchemeDropdown');
  if (dropdown) {
    isColorSchemeDropdownOpen = !isColorSchemeDropdownOpen;
    dropdown.classList.toggle('active', isColorSchemeDropdownOpen);
  }
}

function closeColorSchemeDropdown() {
  const dropdown = document.getElementById('colorSchemeDropdown');
  if (dropdown) {
    isColorSchemeDropdownOpen = false;
    dropdown.classList.remove('active');
  }
}

function handleNavigation(e) {
  if (e.target.matches('.nav-link') || e.target.matches('a[href^="#"]')) {
    e.preventDefault();
    const targetId = e.target.getAttribute('href');
    
    if (targetId && targetId.startsWith('#')) {
      const targetElement = document.querySelector(targetId);
      
      if (targetElement) {
        const offsetTop = targetElement.offsetTop - 80;
        window.scrollTo({
          top: offsetTop,
          behavior: 'smooth'
        });
        
        // Close mobile menu if open
        if (isMenuOpen) {
          const navMenu = document.getElementById('navMenu');
          const menuToggle = document.getElementById('menuToggle');
          
          if (navMenu) navMenu.classList.remove('active');
          if (menuToggle) menuToggle.classList.remove('active');
          document.body.style.overflow = '';
          isMenuOpen = false;
        }
      }
    }
  }
}

function handleCTAButtons(e) {
  const target = e.target.closest('button') || e.target;
  const text = target.textContent || '';
  
  if (text.includes('Start Free Trial') || target.id === 'startTrialBtn') {
    e.preventDefault();
    showNotification('🚀 Redirecting to free trial signup...', 'success');
    // In real app: window.open('https://signup.datareflow.com', '_blank');
  }
  
  if (text.includes('Watch Demo') || target.id === 'watchDemoBtn') {
    e.preventDefault();
    showDemoModal();
  }
  
  if (text.includes('Contact Sales') || text.includes('Schedule Consultation') || text.includes('Schedule Demo')) {
    e.preventDefault();
    showNotification('📅 Opening calendar booking...', 'info');
    // In real app: window.open('https://calendar.datareflow.com', '_blank');
  }
  
  if (text.includes('Calculate Savings') || target.id === 'calculateROI') {
    e.preventDefault();
    calculateROI();
  }
}

function setupModalHandlers() {
  const modal = document.getElementById('demoModal');
  const modalClose = document.getElementById('modalClose');
  const modalOverlay = document.getElementById('modalOverlay');
  
  if (modalClose) {
    modalClose.addEventListener('click', closeDemoModal);
  }
  
  if (modalOverlay) {
    modalOverlay.addEventListener('click', closeDemoModal);
  }
  
  // Close modal on escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      if (modal && modal.classList.contains('active')) {
        closeDemoModal();
      }
      if (isColorSchemeDropdownOpen) {
        closeColorSchemeDropdown();
      }
    }
  });
}

function setupROICalculator() {
  const calculateBtn = document.getElementById('calculateROI');
  if (calculateBtn) {
    calculateBtn.addEventListener('click', calculateROI);
  }
}

function setupPricingToggle() {
  const pricingToggle = document.getElementById('pricingToggle');
  if (pricingToggle) {
    pricingToggle.addEventListener('click', function() {
      isAnnualPricing = !isAnnualPricing;
      pricingToggle.classList.toggle('active', isAnnualPricing);
      updatePricingDisplay();
    });
  }
}

function setupSolutionsTabs() {
  // Will be handled when populating solutions content
}

function toggleTheme() {
  isDarkMode = !isDarkMode;
  updateTheme();
  localStorage.setItem('dataReflow-theme', isDarkMode ? 'dark' : 'light');
  showNotification(`Switched to ${isDarkMode ? 'dark' : 'light'} mode`, 'success');
}

function setupNavbarScroll() {
  const navbar = document.getElementById('navbar');
  if (!navbar) return;
  
  let lastScrollY = window.scrollY;
  
  window.addEventListener('scroll', function() {
    const currentScrollY = window.scrollY;
    
    navbar.classList.toggle('scrolled', currentScrollY > 50);
    
    // Hide/show navbar on scroll
    if (currentScrollY > lastScrollY && currentScrollY > 100) {
      navbar.style.transform = 'translateY(-100%)';
    } else {
      navbar.style.transform = 'translateY(0)';
    }
    
    lastScrollY = currentScrollY;
  });
}

function populateContent() {
  populateClientLogos();
  populateKeyBenefits();
  populateFeatures();
  populateTestimonials();
  populateSolutions();
  populatePricing();
  populateEnterpriseFeatures();
  populateTrustIndicators();
}

function populateClientLogos() {
  const logosGrid = document.getElementById('clientLogos');
  if (!logosGrid) return;

  logosGrid.innerHTML = '';
  
  platformData.client_logos.forEach((logo, index) => {
    const logoElement = document.createElement('div');
    logoElement.className = 'client-logo';
    logoElement.style.animationDelay = `${index * 0.1}s`;
    logoElement.textContent = logo;
    logosGrid.appendChild(logoElement);
  });
}

function populateKeyBenefits() {
  const benefitsGrid = document.getElementById('keyBenefitsGrid');
  if (!benefitsGrid) return;

  benefitsGrid.innerHTML = '';
  
  appData.keyBenefits.forEach((benefit, index) => {
    const benefitCard = createBenefitCard(benefit, index);
    benefitsGrid.appendChild(benefitCard);
  });
}

function createBenefitCard(benefit, index) {
  const card = document.createElement('div');
  card.className = 'benefit-card fade-in';
  card.style.animationDelay = `${index * 0.1}s`;
  
  card.innerHTML = `
    <div class="benefit-icon">
      <i class="fas fa-${benefit.icon}"></i>
    </div>
    <h3 class="benefit-title">${benefit.title}</h3>
    <p class="benefit-description">${benefit.description}</p>
  `;
  
  return card;
}

function populateFeatures() {
  const featuresGrid = document.getElementById('featuresGrid');
  if (!featuresGrid) return;

  featuresGrid.innerHTML = '';
  
  appData.features.forEach((feature, index) => {
    const featureCard = createFeatureCard(feature, index);
    featuresGrid.appendChild(featureCard);
  });
}

function createFeatureCard(feature, index) {
  const card = document.createElement('div');
  card.className = 'feature-card fade-in';
  card.style.animationDelay = `${index * 0.1}s`;
  
  card.innerHTML = `
    <div class="feature-icon">
      <i class="fas fa-${feature.icon}"></i>
    </div>
    <h3 class="feature-title">${feature.title}</h3>
    <p class="feature-description">${feature.description}</p>
    <ul class="feature-benefits">
      ${feature.benefits.map(benefit => `<li>${benefit}</li>`).join('')}
    </ul>
  `;
  
  return card;
}

function populateTestimonials() {
  const testimonialsCarousel = document.getElementById('testimonialsCarousel');
  if (!testimonialsCarousel) return;

  testimonialsCarousel.innerHTML = '';
  
  platformData.testimonials.forEach((testimonial, index) => {
    const testimonialCard = createTestimonialCard(testimonial, index);
    testimonialsCarousel.appendChild(testimonialCard);
  });
}

function createTestimonialCard(testimonial, index) {
  const card = document.createElement('div');
  card.className = 'testimonial-card fade-in';
  card.style.animationDelay = `${index * 0.1}s`;
  
  // Generate initials for avatar fallback
  const initials = testimonial.author.split(' ').map(n => n[0]).join('');
  
  card.innerHTML = `
    <div class="testimonial-quote">${testimonial.quote}</div>
    <div class="testimonial-author">
      <div class="author-avatar" style="background: linear-gradient(135deg, var(--color-primary), var(--scheme-secondary)); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 18px;">
        ${initials}
      </div>
      <div class="author-info">
        <div class="author-name">${testimonial.author}</div>
        <div class="author-title">${testimonial.title}, ${testimonial.company}</div>
        <div class="testimonial-rating">
          ${Array(5).fill().map(() => '<i class="fas fa-star star"></i>').join('')}
        </div>
      </div>
    </div>
  `;
  
  return card;
}

function populateSolutions() {
  const tabsNav = document.getElementById('solutionsTabsNav');
  const tabsContent = document.getElementById('solutionsTabsContent');
  
  if (!tabsNav || !tabsContent) return;

  // Create tab navigation
  tabsNav.innerHTML = '';
  Object.keys(appData.solutionsData).forEach((industry, index) => {
    const tabBtn = document.createElement('button');
    tabBtn.className = `tab-btn ${index === 0 ? 'active' : ''}`;
    tabBtn.textContent = industry;
    tabBtn.addEventListener('click', () => switchSolutionTab(industry));
    tabsNav.appendChild(tabBtn);
  });

  // Create tab content
  tabsContent.innerHTML = '';
  Object.entries(appData.solutionsData).forEach(([industry, data], index) => {
    const tabContent = document.createElement('div');
    tabContent.className = `tab-content ${index === 0 ? 'active' : ''}`;
    tabContent.id = `tab-${industry.toLowerCase()}`;
    
    tabContent.innerHTML = `
      <div class="solution-content">
        <div class="solution-info">
          <h3><i class="fas fa-${data.icon}"></i> ${industry} Solutions</h3>
          <div class="pain-points">
            <h4>Common Challenges</h4>
            <ul>
              ${data.pain_points.map(point => `<li>${point}</li>`).join('')}
            </ul>
          </div>
          <div class="solution-benefits">
            <h4>Our Solutions</h4>
            <ul>
              ${data.solutions.map(solution => `<li>${solution}</li>`).join('')}
            </ul>
          </div>
          <div class="roi-highlight">
            <div class="roi-value">${data.roi}</div>
            <p>Average improvement with Data Reflow</p>
          </div>
        </div>
        <div class="solution-visual">
          <div class="case-study-card">
            <h4>Success Story</h4>
            <p>${data.case_study}</p>
            <button class="btn btn--primary btn--sm">View Full Case Study</button>
          </div>
        </div>
      </div>
    `;
    
    tabsContent.appendChild(tabContent);
  });
}

function switchSolutionTab(industry) {
  // Update tab buttons
  document.querySelectorAll('.tab-btn').forEach(btn => {
    btn.classList.toggle('active', btn.textContent === industry);
  });
  
  // Update tab content
  document.querySelectorAll('.tab-content').forEach(content => {
    content.classList.toggle('active', content.id === `tab-${industry.toLowerCase()}`);
  });
  
  currentSolutionTab = industry;
}

function populatePricing() {
  const pricingGrid = document.getElementById('pricingGrid');
  if (!pricingGrid) return;

  pricingGrid.innerHTML = '';
  
  platformData.pricing_tiers.forEach((tier, index) => {
    const pricingCard = createPricingCard(tier, index);
    pricingGrid.appendChild(pricingCard);
  });
}

function createPricingCard(tier, index) {
  const card = document.createElement('div');
  card.className = `pricing-card fade-in ${tier.popular ? 'popular' : ''}`;
  card.style.animationDelay = `${index * 0.1}s`;
  
  const price = typeof tier.price === 'number' ? 
    `$${isAnnualPricing ? Math.round(tier.price * 0.8) : tier.price}` : 
    tier.price;
  
  const period = typeof tier.price === 'number' ? 
    `per ${isAnnualPricing ? 'month (billed annually)' : 'month'}` : 
    'contact for pricing';
  
  card.innerHTML = `
    <div class="pricing-header">
      <h3 class="pricing-name">${tier.name}</h3>
      <div class="pricing-price">${price}</div>
      <div class="pricing-period">${period}</div>
    </div>
    <ul class="pricing-features">
      ${tier.features.map(feature => `<li>${feature}</li>`).join('')}
    </ul>
    <button class="btn ${tier.popular ? 'btn--primary' : 'btn--outline'} btn--full-width">
      ${tier.cta}
    </button>
  `;
  
  return card;
}

function updatePricingDisplay() {
  populatePricing();
}

function populateEnterpriseFeatures() {
  const enterpriseFeatures = document.getElementById('enterpriseFeatures');
  if (!enterpriseFeatures) return;

  enterpriseFeatures.innerHTML = '';
  
  appData.enterpriseFeatures.forEach((feature, index) => {
    const featureCard = document.createElement('div');
    featureCard.className = 'enterprise-feature fade-in';
    featureCard.style.animationDelay = `${index * 0.1}s`;
    
    featureCard.innerHTML = `
      <div class="feature-icon">
        <i class="fas fa-${feature.icon}"></i>
      </div>
      <h4>${feature.title}</h4>
      <p>${feature.description}</p>
    `;
    
    enterpriseFeatures.appendChild(featureCard);
  });
}

function populateTrustIndicators() {
  const trustGrid = document.getElementById('trustGrid');
  if (!trustGrid) return;

  trustGrid.innerHTML = '';
  
  appData.trustIndicators.forEach((indicator, index) => {
    const trustCard = document.createElement('div');
    trustCard.className = 'trust-card fade-in';
    trustCard.style.animationDelay = `${index * 0.1}s`;
    
    trustCard.innerHTML = `
      <div class="trust-icon">
        <i class="fas fa-${indicator.icon}"></i>
      </div>
      <h4 class="trust-title">${indicator.title}</h4>
      <p class="trust-description">${indicator.description}</p>
    `;
    
    trustGrid.appendChild(trustCard);
  });
}

function calculateROI() {
  const currentCosts = parseFloat(document.getElementById('currentCosts')?.value) || 10000;
  const manualHours = parseFloat(document.getElementById('manualHours')?.value) || 40;
  const hourlyRate = parseFloat(document.getElementById('hourlyRate')?.value) || 75;
  
  // Calculate savings
  const laborCosts = (manualHours * hourlyRate * 52) / 12; // Monthly labor costs
  const timeSavings = laborCosts * 0.8; // 80% time savings
  const processingSavings = currentCosts * 0.4; // 40% processing cost reduction
  const monthlySavings = timeSavings + processingSavings;
  const annualSavings = monthlySavings * 12;
  
  // Assume average annual cost of our platform is $15,000
  const platformCost = 15000;
  const roi = ((annualSavings - platformCost) / platformCost) * 100;
  
  // Update results
  document.getElementById('monthlySavings').textContent = `$${Math.round(monthlySavings).toLocaleString()}`;
  document.getElementById('annualSavings').textContent = `$${Math.round(annualSavings).toLocaleString()}`;
  document.getElementById('roiPercentage').textContent = `${Math.round(roi)}%`;
  
  // Animate results
  animateResults();
  showNotification('💰 ROI calculation complete!', 'success');
}

function animateResults() {
  const resultCards = document.querySelectorAll('.result-card');
  resultCards.forEach((card, index) => {
    card.style.animation = 'none';
    setTimeout(() => {
      card.style.animation = `slideInUp 0.5s ease-out ${index * 0.1}s both`;
    }, 10);
  });
}

function setupScrollAnimations() {
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
      }
    });
  }, observerOptions);

  const fadeElements = document.querySelectorAll('.fade-in');
  fadeElements.forEach(element => {
    observer.observe(element);
  });
  
  observers.push(observer);
}

function setupIntersectionObserver() {
  // Setup counter animations for statistics
  const statNumbers = document.querySelectorAll('.stat-number');
  
  const statsObserver = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const statEl = entry.target;
        if (!animatedCounters.has(statEl)) {
          animateCounter(statEl);
          animatedCounters.add(statEl);
        }
      }
    });
  }, { threshold: 0.5 });

  statNumbers.forEach(stat => {
    statsObserver.observe(stat);
  });
  
  observers.push(statsObserver);
}

function animateCounter(element) {
  if (!element || element.classList.contains('animated')) return;
  
  element.classList.add('animated');
  const targetValue = element.dataset.target;
  let target, suffix = '';
  
  if (targetValue === '2500') {
    target = 2500;
    suffix = '+';
  } else if (targetValue === '10') {
    target = 10;
    suffix = 'TB+';
  } else if (targetValue === '80') {
    target = 80; 
    suffix = '%';
  } else if (targetValue === '99.9') {
    target = 99.9;
    suffix = '%';
  } else {
    target = parseFloat(targetValue);
  }
  
  let start = 0;
  let increment = target / 60;
  let current = start;
  
  const timer = setInterval(function() {
    current += increment;
    
    if (current >= target) {
      current = target;
      clearInterval(timer);
    }
    
    let displayValue;
    if (suffix === '%' && target % 1 !== 0) {
      displayValue = current.toFixed(1);
    } else if (target >= 1000) {
      displayValue = Math.floor(current).toLocaleString();
    } else {
      displayValue = Math.floor(current);
    }
    
    element.textContent = displayValue + suffix;
  }, 25);
}

function showDemoModal() {
  const modal = document.getElementById('demoModal');
  if (modal) {
    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
    showNotification('🎬 Demo modal opened!', 'success');
  }
}

function closeDemoModal() {
  const modal = document.getElementById('demoModal');
  if (modal) {
    modal.classList.remove('active');
    document.body.style.overflow = '';
  }
}

function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  
  const colors = {
    success: 'var(--color-success)',
    error: 'var(--color-error)', 
    warning: 'var(--color-warning)',
    info: 'var(--color-primary)'
  };
  
  notification.style.cssText = `
    position: fixed;
    top: 100px;
    right: 20px;
    padding: var(--space-12) var(--space-20);
    background: ${colors[type]};
    color: var(--color-white);
    border-radius: var(--radius-base);
    box-shadow: var(--shadow-lg);
    z-index: 2001;
    font-weight: var(--font-weight-medium);
    max-width: 350px;
    font-size: var(--font-size-sm);
    animation: slideInRight 0.3s ease-out;
    backdrop-filter: blur(10px);
  `;
  
  notification.textContent = message;
  document.body.appendChild(notification);
  
  setTimeout(() => {
    notification.style.animation = 'slideOutRight 0.3s ease-in';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 300);
  }, 4000);
}

// Add notification animations CSS
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
  @keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }
  
  @keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
  }

  .author-avatar {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-full);
    border: 3px solid var(--color-primary);
    transition: border-color 0.5s var(--ease-standard);
  }

  /* Enhance existing styles */
  .features-section { padding: var(--space-32) 0; background: rgba(var(--color-surface), 0.3); backdrop-filter: blur(10px); transition: background-color 0.5s var(--ease-standard); }
  .testimonials { padding: var(--space-32) 0; background: var(--color-background); transition: background-color 0.5s var(--ease-standard); }
  .solutions-section { padding: var(--space-32) 0; background: rgba(var(--color-surface), 0.3); backdrop-filter: blur(10px); transition: background-color 0.5s var(--ease-standard); }
  .roi-calculator { padding: var(--space-32) 0; background: var(--color-background); transition: background-color 0.5s var(--ease-standard); }
  .pricing-section { padding: var(--space-32) 0; background: rgba(var(--color-surface), 0.3); backdrop-filter: blur(10px); transition: background-color 0.5s var(--ease-standard); }
  .enterprise-section { padding: var(--space-32) 0; background: var(--color-background); transition: background-color 0.5s var(--ease-standard); }
  .trust-section { padding: var(--space-32) 0; background: rgba(var(--color-surface), 0.3); backdrop-filter: blur(10px); transition: background-color 0.5s var(--ease-standard); }

  .footer { background: var(--color-surface); border-top: 1px solid var(--color-border); padding: var(--space-32) 0 var(--space-24); transition: background-color 0.5s var(--ease-standard); }

  /* Additional responsive styles */
  .case-study-card { background: rgba(var(--color-surface), 0.8); backdrop-filter: blur(20px); border: 1px solid var(--color-border); border-radius: var(--radius-lg); padding: var(--space-20); transition: all 0.3s var(--ease-standard); }
  .case-study-card h4 { color: var(--color-text); margin-bottom: var(--space-12); }
  .case-study-card p { color: var(--color-text-secondary); margin-bottom: var(--space-16); }

  /* Solution content styles */
  .solution-content { display: grid; grid-template-columns: 1fr 1fr; gap: var(--space-32); align-items: start; }
  .pain-points, .solution-benefits { margin-bottom: var(--space-20); }
  .pain-points h4, .solution-benefits h4 { color: var(--color-text); margin-bottom: var(--space-12); font-size: var(--font-size-lg); }
  .pain-points ul, .solution-benefits ul { list-style: none; padding: 0; margin: 0; }
  .pain-points li, .solution-benefits li { display: flex; align-items: center; gap: var(--space-8); margin-bottom: var(--space-8); color: var(--color-text-secondary); }
  .pain-points li::before { content: '⚠'; color: var(--color-warning); }
  .solution-benefits li::before { content: '✓'; color: var(--color-success); font-weight: var(--font-weight-bold); }
  .roi-highlight { background: rgba(var(--color-success), 0.1); border: 1px solid rgba(var(--color-success), 0.2); border-radius: var(--radius-base); padding: var(--space-16); text-align: center; }
  .roi-highlight .roi-value { font-size: var(--font-size-xl); font-weight: var(--font-weight-bold); color: var(--color-success); }

  @media (max-width: 768px) {
    .solution-content { grid-template-columns: 1fr; gap: var(--space-24); }
    .calculator-container { grid-template-columns: 1fr; gap: var(--space-24); }
    .enterprise-content { grid-template-columns: 1fr; gap: var(--space-24); }
  }
`;
document.head.appendChild(notificationStyles);

// Handle window resize
let resizeTimeout;
window.addEventListener('resize', function() {
  clearTimeout(resizeTimeout);
  resizeTimeout = setTimeout(function() {
    if (window.innerWidth > 768 && isMenuOpen) {
      const navMenu = document.getElementById('navMenu');
      const menuToggle = document.getElementById('menuToggle');
      
      if (navMenu) navMenu.classList.remove('active');
      if (menuToggle) menuToggle.classList.remove('active');
      document.body.style.overflow = '';
      isMenuOpen = false;
    }
  }, 250);
});

// Cleanup observers on page unload
window.addEventListener('beforeunload', function() {
  observers.forEach(observer => observer.disconnect());
});

// Performance monitoring
window.addEventListener('load', function() {
  if (window.performance && window.performance.timing) {
    const perfData = window.performance.timing;
    const loadTime = perfData.loadEventEnd - perfData.navigationStart;
    console.log(`Data Reflow Platform loaded in ${loadTime}ms`);
    
    // Show welcome message with current color scheme
    const schemeName = colorSchemes[currentColorScheme].name;
    showNotification(`🎨 Welcome! Currently using ${schemeName} color scheme`, 'info');
  }
});

// Enhanced interactivity
document.addEventListener('DOMContentLoaded', function() {
  // Add hover effects to cards
  document.addEventListener('mouseenter', function(e) {
    if (e.target.matches('.feature-card') || 
        e.target.matches('.benefit-card') ||
        e.target.matches('.pricing-card:not(.popular)') ||
        e.target.matches('.enterprise-feature') ||
        e.target.matches('.case-study-card')) {
      e.target.style.transform = 'translateY(-6px)';
    }
  }, true);
  
  document.addEventListener('mouseleave', function(e) {
    if (e.target.matches('.feature-card') || 
        e.target.matches('.benefit-card') ||
        e.target.matches('.pricing-card:not(.popular)') ||
        e.target.matches('.enterprise-feature') ||
        e.target.matches('.case-study-card')) {
      e.target.style.transform = '';
    }
  }, true);
  
  // Add click tracking for analytics (mock)
  document.addEventListener('click', function(e) {
    const target = e.target.closest('button, .card, .tab-btn, .color-scheme-option');
    if (target) {
      console.log('User interaction:', {
        element: target.className,
        text: target.textContent?.slice(0, 50),
        colorScheme: currentColorScheme,
        darkMode: isDarkMode,
        timestamp: new Date().toISOString()
      });
    }
  });
});