:root {
  /* Primitive Color Tokens */
  --color-white: rgba(255, 255, 255, 1);
  --color-black: rgba(0, 0, 0, 1);
  --color-cream-50: rgba(252, 252, 249, 1);
  --color-cream-100: rgba(255, 255, 253, 1);
  --color-gray-200: rgba(245, 245, 245, 1);
  --color-gray-300: rgba(167, 169, 169, 1);
  --color-gray-400: rgba(119, 124, 124, 1);
  --color-slate-500: rgba(98, 108, 113, 1);
  --color-brown-600: rgba(94, 82, 64, 1);
  --color-charcoal-700: rgba(31, 33, 33, 1);
  --color-charcoal-800: rgba(38, 40, 40, 1);
  --color-slate-900: rgba(19, 52, 59, 1);
  --color-teal-300: rgba(50, 184, 198, 1);
  --color-teal-400: rgba(45, 166, 178, 1);
  --color-teal-500: rgba(33, 128, 141, 1);
  --color-teal-600: rgba(29, 116, 128, 1);
  --color-teal-700: rgba(26, 104, 115, 1);
  --color-teal-800: rgba(41, 150, 161, 1);
  --color-red-400: rgba(255, 84, 89, 1);
  --color-red-500: rgba(192, 21, 47, 1);
  --color-orange-400: rgba(230, 129, 97, 1);
  --color-orange-500: rgba(168, 75, 47, 1);

  /* RGB versions for opacity control */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  --color-slate-500-rgb: 98, 108, 113;
  --color-red-500-rgb: 192, 21, 47;
  --color-red-400-rgb: 255, 84, 89;
  --color-orange-500-rgb: 168, 75, 47;
  --color-orange-400-rgb: 230, 129, 97;

  /* Background color tokens (Light Mode) */
  --color-bg-1: rgba(59, 130, 246, 0.08); /* Light blue */
  --color-bg-2: rgba(245, 158, 11, 0.08); /* Light yellow */
  --color-bg-3: rgba(34, 197, 94, 0.08); /* Light green */
  --color-bg-4: rgba(239, 68, 68, 0.08); /* Light red */
  --color-bg-5: rgba(147, 51, 234, 0.08); /* Light purple */
  --color-bg-6: rgba(249, 115, 22, 0.08); /* Light orange */
  --color-bg-7: rgba(236, 72, 153, 0.08); /* Light pink */
  --color-bg-8: rgba(6, 182, 212, 0.08); /* Light cyan */

  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);
  --color-select-caret: rgba(var(--color-slate-900-rgb), 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    /* RGB versions for opacity control (Dark Mode) */
    --color-gray-400-rgb: 119, 124, 124;
    --color-teal-300-rgb: 50, 184, 198;
    --color-gray-300-rgb: 167, 169, 169;
    --color-gray-200-rgb: 245, 245, 245;

    /* Background color tokens (Dark Mode) */
    --color-bg-1: rgba(29, 78, 216, 0.15); /* Dark blue */
    --color-bg-2: rgba(180, 83, 9, 0.15); /* Dark yellow */
    --color-bg-3: rgba(21, 128, 61, 0.15); /* Dark green */
    --color-bg-4: rgba(185, 28, 28, 0.15); /* Dark red */
    --color-bg-5: rgba(107, 33, 168, 0.15); /* Dark purple */
    --color-bg-6: rgba(194, 65, 12, 0.15); /* Dark orange */
    --color-bg-7: rgba(190, 24, 93, 0.15); /* Dark pink */
    --color-bg-8: rgba(8, 145, 178, 0.15); /* Dark cyan */
    
    /* Semantic Color Tokens (Dark Mode) */
    --color-background: var(--color-charcoal-700);
    --color-surface: var(--color-charcoal-800);
    --color-text: var(--color-gray-200);
    --color-text-secondary: rgba(var(--color-gray-300-rgb), 0.7);
    --color-primary: var(--color-teal-300);
    --color-primary-hover: var(--color-teal-400);
    --color-primary-active: var(--color-teal-800);
    --color-secondary: rgba(var(--color-gray-400-rgb), 0.15);
    --color-secondary-hover: rgba(var(--color-gray-400-rgb), 0.25);
    --color-secondary-active: rgba(var(--color-gray-400-rgb), 0.3);
    --color-border: rgba(var(--color-gray-400-rgb), 0.3);
    --color-error: var(--color-red-400);
    --color-success: var(--color-teal-300);
    --color-warning: var(--color-orange-400);
    --color-info: var(--color-gray-300);
    --color-focus-ring: rgba(var(--color-teal-300-rgb), 0.4);
    --color-btn-primary-text: var(--color-slate-900);
    --color-card-border: rgba(var(--color-gray-400-rgb), 0.2);
    --color-card-border-inner: rgba(var(--color-gray-400-rgb), 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

    /* Common style patterns - updated for dark mode */
    --focus-ring: 0 0 0 3px var(--color-focus-ring);
    --focus-outline: 2px solid var(--color-primary);
    --status-bg-opacity: 0.15;
    --status-border-opacity: 0.25;
    --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

    /* RGB versions for dark mode */
    --color-success-rgb: var(--color-teal-300-rgb);
    --color-error-rgb: var(--color-red-400-rgb);
    --color-warning-rgb: var(--color-orange-400-rgb);
    --color-info-rgb: var(--color-gray-300-rgb);
  }
}

/* Data attribute for manual theme switching */
[data-color-scheme="dark"] {
  /* RGB versions for opacity control (dark mode) */
  --color-gray-400-rgb: 119, 124, 124;
  --color-teal-300-rgb: 50, 184, 198;
  --color-gray-300-rgb: 167, 169, 169;
  --color-gray-200-rgb: 245, 245, 245;

  /* Colorful background palette - Dark Mode */
  --color-bg-1: rgba(29, 78, 216, 0.15); /* Dark blue */
  --color-bg-2: rgba(180, 83, 9, 0.15); /* Dark yellow */
  --color-bg-3: rgba(21, 128, 61, 0.15); /* Dark green */
  --color-bg-4: rgba(185, 28, 28, 0.15); /* Dark red */
  --color-bg-5: rgba(107, 33, 168, 0.15); /* Dark purple */
  --color-bg-6: rgba(194, 65, 12, 0.15); /* Dark orange */
  --color-bg-7: rgba(190, 24, 93, 0.15); /* Dark pink */
  --color-bg-8: rgba(8, 145, 178, 0.15); /* Dark cyan */
  
  /* Semantic Color Tokens (Dark Mode) */
  --color-background: var(--color-charcoal-700);
  --color-surface: var(--color-charcoal-800);
  --color-text: var(--color-gray-200);
  --color-text-secondary: rgba(var(--color-gray-300-rgb), 0.7);
  --color-primary: var(--color-teal-300);
  --color-primary-hover: var(--color-teal-400);
  --color-primary-active: var(--color-teal-800);
  --color-secondary: rgba(var(--color-gray-400-rgb), 0.15);
  --color-secondary-hover: rgba(var(--color-gray-400-rgb), 0.25);
  --color-secondary-active: rgba(var(--color-gray-400-rgb), 0.3);
  --color-border: rgba(var(--color-gray-400-rgb), 0.3);
  --color-error: var(--color-red-400);
  --color-success: var(--color-teal-300);
  --color-warning: var(--color-orange-400);
  --color-info: var(--color-gray-300);
  --color-focus-ring: rgba(var(--color-teal-300-rgb), 0.4);
  --color-btn-primary-text: var(--color-slate-900);
  --color-card-border: rgba(var(--color-gray-400-rgb), 0.15);
  --color-card-border-inner: rgba(var(--color-gray-400-rgb), 0.15);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
  --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

  /* Common style patterns - updated for dark mode */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for dark mode */
  --color-success-rgb: var(--color-teal-300-rgb);
  --color-error-rgb: var(--color-red-400-rgb);
  --color-warning-rgb: var(--color-orange-400-rgb);
  --color-info-rgb: var(--color-gray-300-rgb);
}

[data-color-scheme="light"] {
  /* RGB versions for opacity control (light mode) */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  
  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: var(--color-teal-500-rgb);
  --color-error-rgb: var(--color-red-500-rgb);
  --color-warning-rgb: var(--color-orange-500-rgb);
  --color-info-rgb: var(--color-slate-500-rgb);
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
}

.btn--primary:active {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2')
    format('woff2');
}

/* END PERPLEXITY DESIGN SYSTEM */
/* Enhanced Glassmorphism Design System with Multiple Color Schemes */

/* Color Scheme Variables */
:root {
  /* Default Teal Scheme */
  --scheme-primary: #21808D;
  --scheme-primary-hover: #1D747F;
  --scheme-primary-active: #1A686F;
  --scheme-secondary: #5E5240;
  --scheme-accent: #A84B2F;
  --scheme-background: #FCFCF9;
  --scheme-surface: #FFFFFE;
  --scheme-text: #134252;
  --scheme-text-secondary: #626C71;
  --scheme-border: rgba(94, 82, 64, 0.2);
  --scheme-success: #21808D;
  --scheme-warning: #A84B2F;
  --scheme-error: #C0152F;
  
  /* RGB versions for opacity control */
  --scheme-primary-rgb: 33, 128, 141;
  --scheme-secondary-rgb: 94, 82, 64;
  --scheme-accent-rgb: 168, 75, 47;
}

/* Sunset Energy Theme */
[data-color-scheme="sunset"] {
  --scheme-primary: #6B46C1;
  --scheme-primary-hover: #5B3BA8;
  --scheme-primary-active: #4C3190;
  --scheme-secondary: #FF6B35;
  --scheme-accent: #FF8A80;
  --scheme-background: #F8FAFC;
  --scheme-surface: #FFFFFF;
  --scheme-text: #1F2937;
  --scheme-text-secondary: #6B7280;
  --scheme-border: rgba(255, 107, 53, 0.2);
  --scheme-success: #10B981;
  --scheme-warning: #F59E0B;
  --scheme-error: #EF4444;
  
  --scheme-primary-rgb: 107, 70, 193;
  --scheme-secondary-rgb: 255, 107, 53;
  --scheme-accent-rgb: 255, 138, 128;
}

/* Electric Modern Theme */
[data-color-scheme="electric"] {
  --scheme-primary: #0EA5E9;
  --scheme-primary-hover: #0284C7;
  --scheme-primary-active: #0369A1;
  --scheme-secondary: #10B981;
  --scheme-accent: #FBBF24;
  --scheme-background: #F1F5F9;
  --scheme-surface: #FFFFFF;
  --scheme-text: #0F172A;
  --scheme-text-secondary: #64748B;
  --scheme-border: rgba(16, 185, 129, 0.2);
  --scheme-success: #10B981;
  --scheme-warning: #F59E0B;
  --scheme-error: #EF4444;
  
  --scheme-primary-rgb: 14, 165, 233;
  --scheme-secondary-rgb: 16, 185, 129;
  --scheme-accent-rgb: 251, 191, 36;
}

/* Earthy Professional Theme */
[data-color-scheme="earthy"] {
  --scheme-primary: #059669;
  --scheme-primary-hover: #047857;
  --scheme-primary-active: #065F46;
  --scheme-secondary: #DC6803;
  --scheme-accent: #D97706;
  --scheme-background: #FEF7ED;
  --scheme-surface: #FFFBEB;
  --scheme-text: #451A03;
  --scheme-text-secondary: #92400E;
  --scheme-border: rgba(220, 104, 3, 0.2);
  --scheme-success: #059669;
  --scheme-warning: #D97706;
  --scheme-error: #DC2626;
  
  --scheme-primary-rgb: 5, 150, 105;
  --scheme-secondary-rgb: 220, 104, 3;
  --scheme-accent-rgb: 217, 119, 6;
}

/* Royal Elegance Theme */
[data-color-scheme="royal"] {
  --scheme-primary: #1E3A8A;
  --scheme-primary-hover: #1E40AF;
  --scheme-primary-active: #1D4ED8;
  --scheme-secondary: #EC4899;
  --scheme-accent: #6EE7B7;
  --scheme-background: #FAFAF9;
  --scheme-surface: #FFFFFF;
  --scheme-text: #111827;
  --scheme-text-secondary: #4B5563;
  --scheme-border: rgba(236, 72, 153, 0.2);
  --scheme-success: #10B981;
  --scheme-warning: #F59E0B;
  --scheme-error: #EF4444;
  
  --scheme-primary-rgb: 30, 58, 138;
  --scheme-secondary-rgb: 236, 72, 153;
  --scheme-accent-rgb: 110, 231, 183;
}

/* Vibrant Tech Theme */
[data-color-scheme="vibrant"] {
  --scheme-primary: #DB2777;
  --scheme-primary-hover: #BE185D;
  --scheme-primary-active: #9D174D;
  --scheme-secondary: #06B6D4;
  --scheme-accent: #84CC16;
  --scheme-background: #F8FAFC;
  --scheme-surface: #FFFFFF;
  --scheme-text: #030712;
  --scheme-text-secondary: #374151;
  --scheme-border: rgba(6, 182, 212, 0.2);
  --scheme-success: #10B981;
  --scheme-warning: #F59E0B;
  --scheme-error: #EF4444;
  
  --scheme-primary-rgb: 219, 39, 119;
  --scheme-secondary-rgb: 6, 182, 212;
  --scheme-accent-rgb: 132, 204, 22;
}

/* Dark Mode Variants */
[data-color-scheme="default"][data-theme="dark"] {
  --scheme-background: #1F2121;
  --scheme-surface: #262828;
  --scheme-text: #F5F5F5;
  --scheme-text-secondary: rgba(167, 169, 169, 0.7);
  --scheme-border: rgba(119, 124, 124, 0.3);
  --scheme-primary: #32B8C6;
}

[data-color-scheme="sunset"][data-theme="dark"] {
  --scheme-background: #1A1625;
  --scheme-surface: #241B33;
  --scheme-text: #F8FAFC;
  --scheme-text-secondary: #94A3B8;
  --scheme-primary: #8B5FD6;
  --scheme-secondary: #FF7A47;
}

[data-color-scheme="electric"][data-theme="dark"] {
  --scheme-background: #0F1419;
  --scheme-surface: #1E293B;
  --scheme-text: #F1F5F9;
  --scheme-text-secondary: #94A3B8;
  --scheme-primary: #38BDF8;
  --scheme-secondary: #34D399;
}

[data-color-scheme="earthy"][data-theme="dark"] {
  --scheme-background: #1C1917;
  --scheme-surface: #292524;
  --scheme-text: #FEF7ED;
  --scheme-text-secondary: #D6D3D1;
  --scheme-primary: #10B981;
  --scheme-secondary: #FB923C;
}

[data-color-scheme="royal"][data-theme="dark"] {
  --scheme-background: #111827;
  --scheme-surface: #1F2937;
  --scheme-text: #F9FAFB;
  --scheme-text-secondary: #D1D5DB;
  --scheme-primary: #3B82F6;
  --scheme-secondary: #F472B6;
}

[data-color-scheme="vibrant"][data-theme="dark"] {
  --scheme-background: #0F0A14;
  --scheme-surface: #1F1B24;
  --scheme-text: #F8FAFC;
  --scheme-text-secondary: #CBD5E1;
  --scheme-primary: #F472B6;
  --scheme-secondary: #22D3EE;
}

/* Apply color scheme variables to design system */
:root {
  --color-primary: var(--scheme-primary);
  --color-primary-hover: var(--scheme-primary-hover);
  --color-primary-active: var(--scheme-primary-active);
  --color-secondary: var(--scheme-secondary);
  --color-background: var(--scheme-background);
  --color-surface: var(--scheme-surface);
  --color-text: var(--scheme-text);
  --color-text-secondary: var(--scheme-text-secondary);
  --color-border: var(--scheme-border);
  --color-success: var(--scheme-success);
  --color-warning: var(--scheme-warning);
  --color-error: var(--scheme-error);
  --color-primary-rgb: var(--scheme-primary-rgb);
}

/* Color Scheme Selector Styles */
.theme-controls {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.color-scheme-selector {
  position: relative;
}

.color-scheme-toggle {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--color-text-secondary);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--space-10);
  border-radius: var(--radius-base);
  transition: all var(--duration-fast) var(--ease-standard);
  backdrop-filter: blur(10px);
}

.color-scheme-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--color-primary);
  transform: scale(1.05);
}

.color-scheme-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background: rgba(var(--color-surface), 0.95);
  backdrop-filter: blur(30px);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  padding: var(--space-12);
  min-width: 200px;
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--duration-normal) var(--ease-standard);
}

.color-scheme-dropdown.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.color-scheme-option {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  padding: var(--space-10) var(--space-12);
  border-radius: var(--radius-base);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-standard);
  margin-bottom: var(--space-4);
}

.color-scheme-option:last-child {
  margin-bottom: 0;
}

.color-scheme-option:hover {
  background: rgba(var(--color-primary-rgb), 0.1);
  transform: translateX(4px);
}

.color-scheme-option.active {
  background: rgba(var(--color-primary-rgb), 0.15);
  border: 1px solid rgba(var(--color-primary-rgb), 0.3);
}

.scheme-preview {
  display: flex;
  gap: var(--space-4);
}

.color-dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.scheme-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

/* Base Styles - FIXED SCROLLING ISSUE */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
  scroll-behavior: smooth;
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  background: var(--color-background);
  transition: background-color 0.5s var(--ease-standard);
  min-height: 100vh;
  /* FIXED: Remove overflow-x: hidden that was preventing scrolling */
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Glassmorphism Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(-5px) rotate(-1deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(var(--color-primary-rgb), 0.4); }
  50% { transform: scale(1.05); box-shadow: 0 0 0 20px rgba(var(--color-primary-rgb), 0); }
}

@keyframes slideInUp {
  from { opacity: 0; transform: translateY(60px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-60px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(60px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes gradientFlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(200%); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

@keyframes colorTransition {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Enhanced Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all var(--duration-normal) var(--ease-standard);
  box-shadow: 0 4px 32px rgba(0, 0, 0, 0.05);
}

.navbar.scrolled {
  background: rgba(var(--color-surface), 0.95);
  backdrop-filter: blur(30px);
  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-16) 0;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: var(--space-12);
}

.logo-icon {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, var(--color-primary), var(--scheme-secondary));
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  font-size: var(--font-size-lg);
  box-shadow: 0 8px 24px rgba(var(--color-primary-rgb), 0.3);
  position: relative;
  transition: all 0.5s var(--ease-standard);
}

.logo-icon::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(135deg, var(--color-primary), var(--scheme-secondary));
  border-radius: var(--radius-md);
  z-index: -1;
  opacity: 0.3;
  filter: blur(8px);
  transition: all 0.5s var(--ease-standard);
}

.logo-text {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  transition: color 0.5s var(--ease-standard);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--space-32);
}

.nav-link {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: all var(--duration-fast) var(--ease-standard);
  position: relative;
  padding: var(--space-8) 0;
}

.nav-link:hover {
  color: var(--color-primary);
  transform: translateY(-2px);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--color-primary), var(--scheme-secondary));
  transition: width var(--duration-fast) var(--ease-standard);
  border-radius: 2px;
}

.nav-link:hover::after {
  width: 100%;
}

.nav-controls {
  display: flex;
  align-items: center;
  gap: var(--space-16);
}

.theme-toggle {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--color-text-secondary);
  font-size: var(--font-size-lg);
  cursor: pointer;
  padding: var(--space-10);
  border-radius: var(--radius-base);
  transition: all var(--duration-fast) var(--ease-standard);
  backdrop-filter: blur(10px);
}

.theme-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
  color: var(--color-primary);
  transform: scale(1.1);
}

.menu-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-8);
}

.menu-toggle span {
  width: 24px;
  height: 2px;
  background: var(--color-text);
  transition: all var(--duration-fast) var(--ease-standard);
  border-radius: 2px;
}

/* Enhanced Hero Section */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  padding-top: 80px;
}

.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(var(--color-primary-rgb), 0.08) 0%,
    rgba(var(--scheme-secondary-rgb), 0.04) 25%,
    rgba(var(--color-primary-rgb), 0.06) 50%,
    rgba(var(--scheme-accent-rgb), 0.04) 75%,
    rgba(var(--color-primary-rgb), 0.08) 100%);
  background-size: 400% 400%;
  animation: gradientFlow 12s ease infinite;
  transition: all 0.8s var(--ease-standard);
}

.hero-shapes {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: -1;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, 
    rgba(var(--color-primary-rgb), 0.1),
    rgba(var(--scheme-secondary-rgb), 0.05));
  animation: float 8s ease-in-out infinite;
  filter: blur(1px);
  transition: all 0.8s var(--ease-standard);
}

.shape-1 {
  width: 300px;
  height: 300px;
  top: 5%;
  left: 5%;
  animation-delay: 0s;
}

.shape-2 {
  width: 200px;
  height: 200px;
  top: 60%;
  right: 10%;
  animation-delay: 3s;
}

.shape-3 {
  width: 150px;
  height: 150px;
  bottom: 15%;
  left: 15%;
  animation-delay: 6s;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-32);
  align-items: center;
  padding: var(--space-32) 0;
}

.hero-text {
  animation: slideInLeft 0.8s ease-out;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-8);
  padding: var(--space-10) var(--space-20);
  background: rgba(var(--color-primary-rgb), 0.1);
  border: 1px solid rgba(var(--color-primary-rgb), 0.2);
  border-radius: var(--radius-full);
  color: var(--color-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-24);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 16px rgba(var(--color-primary-rgb), 0.1);
  animation: bounce 2s infinite;
  transition: all 0.5s var(--ease-standard);
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4.5rem);
  font-weight: var(--font-weight-bold);
  line-height: 1.1;
  margin-bottom: var(--space-24);
  color: var(--color-text);
  transition: color 0.5s var(--ease-standard);
}

.title-main {
  display: block;
  margin-bottom: var(--space-8);
}

.title-highlight {
  display: block;
  background: linear-gradient(135deg, var(--color-primary), var(--scheme-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  transition: all 0.8s var(--ease-standard);
}

.title-highlight::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 60%;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), var(--scheme-secondary));
  border-radius: 2px;
  opacity: 0.6;
  transition: all 0.8s var(--ease-standard);
}

.hero-description {
  font-size: var(--font-size-xl);
  line-height: 1.7;
  color: var(--color-text-secondary);
  margin-bottom: var(--space-32);
  transition: color 0.5s var(--ease-standard);
}

.hero-actions {
  display: flex;
  gap: var(--space-16);
  margin-bottom: var(--space-32);
}

.trust-indicators {
  display: flex;
  gap: var(--space-24);
  flex-wrap: wrap;
}

.trust-item {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: color 0.5s var(--ease-standard);
}

.trust-item i {
  color: var(--color-success);
  font-size: var(--font-size-base);
  transition: color 0.5s var(--ease-standard);
}

.hero-visual {
  position: relative;
  animation: slideInRight 0.8s ease-out;
}

.dashboard-mockup {
  background: rgba(var(--color-surface), 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  transform: perspective(1000px) rotateY(-8deg) rotateX(4deg);
  transition: all var(--duration-normal) var(--ease-standard);
  position: relative;
}

.dashboard-mockup::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, 
    rgba(var(--color-primary-rgb), 0.05) 0%,
    rgba(var(--scheme-secondary-rgb), 0.02) 50%,
    rgba(var(--color-primary-rgb), 0.05) 100%);
  z-index: -1;
  transition: all 0.8s var(--ease-standard);
}

.dashboard-mockup:hover {
  transform: perspective(1000px) rotateY(-4deg) rotateX(2deg) scale(1.02);
}

.mockup-header {
  background: rgba(var(--color-background), 0.8);
  padding: var(--space-16);
  display: flex;
  align-items: center;
  gap: var(--space-16);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: background-color 0.5s var(--ease-standard);
}

.mockup-controls {
  display: flex;
  gap: var(--space-8);
}

.control {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.control-red { background: #ff5f57; }
.control-yellow { background: #ffbd2e; }
.control-green { background: #28ca42; }

.mockup-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  transition: color 0.5s var(--ease-standard);
}

.mockup-content {
  display: flex;
  height: 350px;
}

.mockup-sidebar {
  width: 80px;
  background: rgba(var(--color-background), 0.5);
  padding: var(--space-20);
  display: flex;
  flex-direction: column;
  gap: var(--space-16);
  border-right: 1px solid rgba(255, 255, 255, 0.05);
  transition: background-color 0.5s var(--ease-standard);
}

.sidebar-item {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-base);
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(var(--color-text-secondary), 0.1);
  color: var(--color-text-secondary);
  transition: all var(--duration-fast) var(--ease-standard);
  cursor: pointer;
}

.sidebar-item.active,
.sidebar-item:hover {
  background: var(--color-primary);
  color: var(--color-white);
  transform: scale(1.1);
}

.mockup-main {
  flex: 1;
  padding: var(--space-24);
  display: flex;
  flex-direction: column;
  gap: var(--space-20);
}

.chart-container {
  flex: 1;
  position: relative;
}

.chart-legend {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-16);
}

.legend-item {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  transition: color 0.5s var(--ease-standard);
}

.legend-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  transition: color 0.5s var(--ease-standard);
}

.chart-bars {
  display: flex;
  align-items: end;
  gap: var(--space-8);
  height: 150px;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(135deg, var(--color-primary), var(--scheme-secondary));
  border-radius: var(--radius-sm) var(--radius-sm) 0 0;
  animation: slideInUp 0.8s ease-out;
  position: relative;
  overflow: hidden;
  transition: all 0.8s var(--ease-standard);
}

.chart-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.4), 
    transparent);
  animation: shimmer 2s infinite;
}

.metrics-row {
  display: flex;
  gap: var(--space-20);
}

.metric {
  text-align: center;
  padding: var(--space-12);
  background: rgba(var(--color-primary-rgb), 0.05);
  border-radius: var(--radius-base);
  border: 1px solid rgba(var(--color-primary-rgb), 0.1);
  transition: all 0.5s var(--ease-standard);
}

.metric-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  transition: color 0.5s var(--ease-standard);
}

.metric-label {
  font-size: var(--font-size-xs);
  color: var(--color-text-secondary);
  margin-top: var(--space-4);
  transition: color 0.5s var(--ease-standard);
}

.floating-card {
  position: absolute;
  background: rgba(var(--color-surface), 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-base);
  padding: var(--space-12) var(--space-16);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: var(--space-8);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  animation: float 4s ease-in-out infinite;
  z-index: 10;
  transition: all 0.5s var(--ease-standard);
}

.floating-card i {
  color: var(--color-primary);
  font-size: var(--font-size-base);
  transition: color 0.5s var(--ease-standard);
}

.card-1 {
  top: 15%;
  right: -5%;
  animation-delay: 0s;
}

.card-2 {
  bottom: 35%;
  left: -8%;
  animation-delay: 1.5s;
}

.card-3 {
  top: 65%;
  right: -10%;
  animation-delay: 3s;
}

/* Client Logos Section */
.client-logos {
  padding: var(--space-24) 0;
  background: rgba(var(--color-surface), 0.3);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  transition: background-color 0.5s var(--ease-standard);
}

.logos-title {
  text-align: center;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--space-20);
  font-weight: var(--font-weight-medium);
  transition: color 0.5s var(--ease-standard);
}

.logos-grid {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-32);
  flex-wrap: wrap;
}

.client-logo {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  opacity: 0.6;
  transition: all var(--duration-fast) var(--ease-standard);
}

.client-logo:hover {
  opacity: 1;
  color: var(--color-primary);
}

/* Section Styles */
.section-header {
  text-align: center;
  margin-bottom: var(--space-32);
  animation: slideInUp 0.6s ease-out;
}

.section-title {
  font-size: clamp(2rem, 4vw, 3.5rem);
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: var(--space-16);
  position: relative;
  transition: color 0.5s var(--ease-standard);
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), var(--scheme-secondary));
  border-radius: 2px;
  transition: all 0.8s var(--ease-standard);
}

.section-description {
  font-size: var(--font-size-xl);
  color: var(--color-text-secondary);
  max-width: 700px;
  margin: 0 auto;
  line-height: 1.6;
  transition: color 0.5s var(--ease-standard);
}

/* Key Benefits Section */
.key-benefits {
  padding: var(--space-32) 0;
  background: var(--color-background);
  transition: background-color 0.5s var(--ease-standard);
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-24);
}

.benefit-card {
  background: rgba(var(--color-surface), 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  text-align: center;
  transition: all var(--duration-normal) var(--ease-standard);
  position: relative;
  overflow: hidden;
}

.benefit-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary), var(--scheme-secondary));
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-standard);
}

.benefit-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.benefit-card:hover::before {
  opacity: 1;
}

.benefit-icon {
  width: 70px;
  height: 70px;
  margin: 0 auto var(--space-20);
  background: linear-gradient(135deg, var(--color-primary), var(--scheme-secondary));
  border-radius: var(--radius-base);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  font-size: var(--font-size-2xl);
  box-shadow: 0 8px 24px rgba(var(--color-primary-rgb), 0.3);
  transition: all 0.5s var(--ease-standard);
}

.benefit-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  margin-bottom: var(--space-12);
  transition: color 0.5s var(--ease-standard);
}

.benefit-description {
  color: var(--color-text-secondary);
  line-height: 1.6;
  transition: color 0.5s var(--ease-standard);
}

/* Features, Solutions, and other sections */
.features-section { padding: var(--space-32) 0; background: rgba(var(--color-surface), 0.3); backdrop-filter: blur(10px); transition: background-color 0.5s var(--ease-standard); }
.testimonials { padding: var(--space-32) 0; background: var(--color-background); transition: background-color 0.5s var(--ease-standard); }
.solutions-section { padding: var(--space-32) 0; background: rgba(var(--color-surface), 0.3); backdrop-filter: blur(10px); transition: background-color 0.5s var(--ease-standard); }
.roi-calculator { padding: var(--space-32) 0; background: var(--color-background); transition: background-color 0.5s var(--ease-standard); }
.pricing-section { padding: var(--space-32) 0; background: rgba(var(--color-surface), 0.3); backdrop-filter: blur(10px); transition: background-color 0.5s var(--ease-standard); }
.enterprise-section { padding: var(--space-32) 0; background: var(--color-background); transition: background-color 0.5s var(--ease-standard); }
.trust-section { padding: var(--space-32) 0; background: rgba(var(--color-surface), 0.3); backdrop-filter: blur(10px); transition: background-color 0.5s var(--ease-standard); }

/* Final CTA section with enhanced gradient */
.final-cta {
  padding: var(--space-32) 0;
  background: linear-gradient(135deg, var(--color-primary), var(--scheme-secondary));
  color: var(--color-white);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.final-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  z-index: 0;
}

.cta-content {
  position: relative;
  z-index: 1;
}

.urgency-banner {
  display: inline-flex;
  align-items: center;
  gap: var(--space-8);
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  padding: var(--space-8) var(--space-20);
  margin-bottom: var(--space-24);
  animation: pulse 2s infinite;
}

.urgency-banner i {
  color: var(--color-warning);
}

.cta-content h2 {
  font-size: clamp(2rem, 4vw, 3.5rem);
  margin-bottom: var(--space-16);
  font-weight: var(--font-weight-bold);
}

.cta-content > p {
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-32);
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--space-24);
  max-width: 800px;
  margin: 0 auto var(--space-32);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-8);
  display: block;
}

.stat-label {
  font-size: var(--font-size-sm);
  opacity: 0.9;
  font-weight: var(--font-weight-medium);
}

.cta-actions {
  display: flex;
  justify-content: center;
  gap: var(--space-16);
  margin-bottom: var(--space-24);
}

.pulse-animation {
  animation: pulse 2s infinite;
}

.risk-reduction {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-8);
  font-size: var(--font-size-sm);
  opacity: 0.8;
}

.footer { 
  background: var(--color-surface); 
  border-top: 1px solid var(--color-border); 
  padding: var(--space-32) 0 var(--space-24); 
  transition: background-color 0.5s var(--ease-standard); 
}

/* Buttons and interactive elements */
.btn {
  transition: all 0.3s var(--ease-standard);
}

.btn--primary {
  background: linear-gradient(135deg, var(--color-primary), var(--scheme-secondary));
  color: var(--color-white);
  border: none;
}

.btn--primary:hover {
  background: linear-gradient(135deg, var(--color-primary-hover), var(--scheme-accent));
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(var(--color-primary-rgb), 0.4);
}

.btn--outline {
  background: transparent;
  border: 2px solid var(--color-primary);
  color: var(--color-primary);
}

.btn--outline:hover {
  background: var(--color-primary);
  color: var(--color-white);
  transform: translateY(-2px);
}

/* Form controls */
.form-control {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  color: var(--color-text);
  transition: all 0.3s var(--ease-standard);
}

.form-control:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(var(--color-primary-rgb), 0.1);
}

/* Cards and other components styling */
.feature-card, .testimonial-card, .pricing-card, .enterprise-feature, .trust-card, .case-study-card,
.calculator-form, .result-card {
  background: rgba(var(--color-surface), 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  transition: all var(--duration-normal) var(--ease-standard);
}

.feature-card:hover, .testimonial-card:hover, .pricing-card:hover, 
.enterprise-feature:hover, .trust-card:hover, .case-study-card:hover,
.result-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .color-scheme-dropdown {
    right: auto;
    left: 0;
    min-width: 180px;
  }
  
  .theme-controls {
    gap: var(--space-4);
  }
  
  .nav-menu {
    position: fixed;
    top: 80px;
    left: 0;
    right: 0;
    background: rgba(var(--color-surface), 0.95);
    backdrop-filter: blur(30px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-direction: column;
    padding: var(--space-20);
    gap: var(--space-16);
    transform: translateY(-100%);
    transition: transform var(--duration-normal) var(--ease-standard);
    opacity: 0;
    pointer-events: none;
  }

  .nav-menu.active {
    transform: translateY(0);
    opacity: 1;
    pointer-events: all;
  }

  .menu-toggle {
    display: flex;
  }

  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--space-24);
  }

  .hero-visual {
    order: -1;
  }

  .floating-card {
    display: none;
  }

  .dashboard-mockup {
    transform: none;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .trust-indicators {
    justify-content: center;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }
}

/* Color scheme transition animations */
* {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-duration: 0.5s;
  transition-timing-function: var(--ease-standard);
}

/* Ensure smooth transitions for all themed elements */
.hero-gradient,
.shape,
.logo-icon,
.logo-icon::before,
.title-highlight,
.title-highlight::after,
.chart-bar,
.floating-card,
.benefit-icon,
.section-title::after {
  transition: all 0.8s var(--ease-standard) !important;
}

/* Utility classes */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}