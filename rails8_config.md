# Gemfile - Rails 8 Native Stack
source "https://rubygems.org"

gem "rails", "~> 8.0.0"
gem "propshaft"
gem "pg", "~> 1.1"
gem "puma", ">= 5.0"
gem "importmap-rails"
gem "turbo-rails"
gem "stimulus-rails"
gem "tailwindcss-rails"
gem "solid_queue"
gem "solid_cache"
gem "solid_cable"
gem "image_processing", "~> 1.2"

# SME Data Platform Specific
gem "acts_as_tenant"
gem "pundit"
gem "kiba"
gem "kiba-common"
gem "faraday"
gem "jwt"
gem "bootsnap", require: false

group :development, :test do
  gem "debug", platforms: %i[ mri windows ], require: "debug/prelude"
  gem "brakeman", require: false
  gem "rubocop-rails-omakase", require: false
  gem "rspec-rails"
  gem "factory_bot_rails"
end

# config/application.rb
require_relative "boot"
require "rails/all"

Bundler.require(*Rails.groups)

module DataRefinery
  class Application < Rails::Application
    config.load_defaults 8.0
    
    # Background jobs with Solid Queue
    config.active_job.queue_adapter = :solid_queue
    
    # Caching with Solid Cache
    config.cache_store = :solid_cache_store
    
    # Real-time with Solid Cable
    config.action_cable.adapter = :solid_cable
    
    # Multi-tenancy
    config.middleware.use ActsAsTenant::Middleware
  end
end

# config/solid_queue.yml
production: &production
  dispatchers:
    - polling_interval: 1
      batch_size: 500
      concurrency_maintenance_interval: 600
  workers:
    - queues: "critical,default,low"
      threads: 3
      polling_interval: 0.1
      processes: 2

development:
  <<: *production
  workers:
    - queues: "*"
      threads: 1
      processes: 1

# app/services/application_service.rb
class ApplicationService
  def self.call(*args, **kwargs, &block)
    new(*args, **kwargs).call(&block)
  end

  private

  def success(result = nil)
    ServiceResult.new(success: true, result: result)
  end

  def failure(error, details = {})
    ServiceResult.new(success: false, error: error, details: details)
  end
end

# app/services/service_result.rb
class ServiceResult
  attr_reader :result, :error, :details

  def initialize(success:, result: nil, error: nil, details: {})
    @success = success
    @result = result
    @error = error
    @details = details
  end

  def success?
    @success
  end

  def failure?
    !@success
  end
end

# app/services/etl_processor_service.rb
class EtlProcessorService < ApplicationService
  def initialize(pipeline)
    @pipeline = pipeline
  end

  def call
    return failure("Pipeline not found") unless @pipeline

    @pipeline.update!(status: :running, started_at: Time.current)
    
    # Extract data from sources
    extracted_data = extract_data
    return failure("Extraction failed", extracted_data) unless extracted_data.success?

    # Transform if ETL mode
    if @pipeline.mode == 'etl'
      transformed_data = transform_data(extracted_data.result)
      return failure("Transformation failed") unless transformed_data.success?
      final_data = transformed_data.result
    else
      final_data = extracted_data.result
    end

    # Load to destination
    load_result = load_data(final_data)
    return failure("Load failed") unless load_result.success?

    # Generate quality report
    DataQualityScorerService.call(@pipeline)

    @pipeline.update!(status: :completed, completed_at: Time.current)
    success(@pipeline)

  rescue => e
    @pipeline.update!(status: :failed, error_message: e.message)
    Rails.logger.error "Pipeline #{@pipeline.id} failed: #{e.message}"
    failure(e.message)
  end

  private

  def extract_data
    # Implementation for data extraction
    success([]) # Placeholder
  end

  def transform_data(data)
    # Implementation for data transformation
    success(data) # Placeholder
  end

  def load_data(data)
    # Implementation for data loading
    success(data) # Placeholder
  end
end

# app/services/smart_mapping_service.rb
class SmartMappingService < ApplicationService
  def initialize(integration)
    @integration = integration
  end

  def call
    source_schema = fetch_source_schema
    return failure("Could not fetch source schema") unless source_schema

    target_schema = fetch_target_schema
    return failure("Could not fetch target schema") unless target_schema

    mappings = generate_mappings(source_schema, target_schema)
    
    @integration.update!(
      suggested_mappings: mappings,
      last_mapped_at: Time.current
    )

    success(mappings)
  end

  private

  def fetch_source_schema
    # Implement schema fetching logic
    []
  end

  def fetch_target_schema
    # Implement target schema logic
    []
  end

  def generate_mappings(source, target)
    # Implement Levenshtein-based field matching
    mappings = {}
    
    source.each do |source_field|
      best_match = target.min_by do |target_field|
        levenshtein_distance(source_field['name'], target_field['name'])
      end
      
      if best_match
        confidence = calculate_confidence(source_field['name'], best_match['name'])
        mappings[source_field['name']] = {
          target: best_match['name'],
          confidence: confidence
        }
      end
    end
    
    mappings
  end

  def levenshtein_distance(a, b)
    # Simple Levenshtein implementation
    matrix = Array.new(a.length + 1) { Array.new(b.length + 1) }
    
    (0..a.length).each { |i| matrix[i][0] = i }
    (0..b.length).each { |j| matrix[0][j] = j }
    
    (1..a.length).each do |i|
      (1..b.length).each do |j|
        cost = a[i-1] == b[j-1] ? 0 : 1
        matrix[i][j] = [
          matrix[i-1][j] + 1,     # deletion
          matrix[i][j-1] + 1,     # insertion
          matrix[i-1][j-1] + cost # substitution
        ].min
      end
    end
    
    matrix[a.length][b.length]
  end

  def calculate_confidence(source, target)
    max_length = [source.length, target.length].max
    return 1.0 if max_length == 0
    
    distance = levenshtein_distance(source.downcase, target.downcase)
    (max_length - distance).to_f / max_length
  end
end

# app/services/data_quality_scorer_service.rb
class DataQualityScorerService < ApplicationService
  def initialize(pipeline)
    @pipeline = pipeline
  end

  def call
    scores = calculate_quality_metrics
    overall_score = calculate_weighted_score(scores)
    recommendations = generate_recommendations(scores)

    report = @pipeline.data_quality_reports.create!(
      quality_score: overall_score,
      completeness_score: scores[:completeness],
      uniqueness_score: scores[:uniqueness],
      timeliness_score: scores[:timeliness],
      accuracy_score: scores[:accuracy],
      recommendations: recommendations
    )

    # Trigger alert if quality is below threshold
    if overall_score < 80
      DataQualityAlertJob.perform_later(report.id)
    end

    success(report)
  end

  private

  def calculate_quality_metrics
    {
      completeness: calculate_completeness,
      uniqueness: calculate_uniqueness,
      timeliness: calculate_timeliness,
      accuracy: calculate_accuracy
    }
  end

  def calculate_weighted_score(scores)
    weights = { completeness: 0.3, uniqueness: 0.25, timeliness: 0.25, accuracy: 0.2 }
    
    total_score = scores.sum { |metric, score| weights[metric] * score }
    total_weight = weights.values.sum
    
    (total_score / total_weight).round(2)
  end

  def calculate_completeness
    # Implementation for completeness scoring
    85.0 # Placeholder
  end

  def calculate_uniqueness
    # Implementation for uniqueness scoring
    90.0 # Placeholder
  end

  def calculate_timeliness
    # Implementation for timeliness scoring
    88.0 # Placeholder
  end

  def calculate_accuracy
    # Implementation for accuracy scoring
    92.0 # Placeholder
  end

  def generate_recommendations(scores)
    recommendations = []
    
    scores.each do |metric, score|
      if score < 80
        recommendations << recommendation_for_metric(metric, score)
      end
    end
    
    recommendations
  end

  def recommendation_for_metric(metric, score)
    case metric
    when :completeness
      "Completeness is #{score}%. Consider implementing data validation rules."
    when :uniqueness
      "Uniqueness is #{score}%. Check for duplicate records in source systems."
    when :timeliness
      "Timeliness is #{score}%. Review sync frequency and data freshness."
    when :accuracy
      "Accuracy is #{score}%. Implement data validation and cleansing rules."
    end
  end
end