-- PostgreSQL database dump
-- Dumped from database version 14.x
-- Dumped by pg_dump version 14.x

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: alerts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.alerts (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    monitoring_rule_id bigint NOT NULL,
    data_source_id bigint,
    sync_log_id bigint,
    status character varying DEFAULT 'active'::character varying NOT NULL,
    severity character varying NOT NULL,
    alert_type character varying NOT NULL,
    title character varying NOT NULL,
    message text,
    details jsonb DEFAULT '{}'::jsonb,
    context jsonb DEFAULT '{}'::jsonb,
    triggered_at timestamp(6) without time zone NOT NULL,
    acknowledged_at timestamp(6) without time zone,
    resolved_at timestamp(6) without time zone,
    acknowledged_by_id bigint,
    resolved_by_id bigint,
    resolution_notes text,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    pipeline_id bigint
);


--
-- Name: alerts_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.alerts_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: alerts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.alerts_id_seq OWNED BY public.alerts.id;


--
-- Name: ar_internal_metadata; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.ar_internal_metadata (
    key character varying NOT NULL,
    value character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: data_sources; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.data_sources (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    name character varying NOT NULL,
    description text,
    source_type character varying NOT NULL,
    connection_config text,
    settings jsonb DEFAULT '{}'::jsonb,
    status character varying DEFAULT 'inactive'::character varying NOT NULL,
    active boolean DEFAULT false NOT NULL,
    error_message text,
    last_error_at timestamp(6) without time zone,
    sync_frequency character varying DEFAULT 'manual'::character varying,
    last_sync_at timestamp(6) without time zone,
    last_successful_sync_at timestamp(6) without time zone,
    next_sync_at timestamp(6) without time zone,
    sync_count integer DEFAULT 0,
    failed_sync_count integer DEFAULT 0,
    row_count bigint DEFAULT 0,
    total_rows_synced bigint DEFAULT 0,
    data_size_mb numeric(10,2),
    last_sync_duration_seconds integer,
    avg_sync_duration_seconds numeric(10,2),
    metadata jsonb DEFAULT '{}'::jsonb,
    schema_cache jsonb,
    schema_updated_at timestamp(6) without time zone,
    original_filename character varying,
    file_size bigint,
    file_uploaded_at timestamp(6) without time zone,
    version integer DEFAULT 1,
    created_by character varying,
    updated_by character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    CONSTRAINT check_data_source_status CHECK (((status)::text = ANY (ARRAY[('inactive'::character varying)::text, ('active'::character varying)::text, ('syncing'::character varying)::text, ('error'::character varying)::text, ('maintenance'::character varying)::text, ('pending'::character varying)::text]))),
    CONSTRAINT check_data_source_type CHECK (((source_type)::text = ANY (ARRAY[('postgresql'::character varying)::text, ('mysql'::character varying)::text, ('sqlite'::character varying)::text, ('sqlserver'::character varying)::text, ('mongodb'::character varying)::text, ('redis'::character varying)::text, ('elasticsearch'::character varying)::text, ('csv'::character varying)::text, ('excel'::character varying)::text, ('json'::character varying)::text, ('xml'::character varying)::text, ('api'::character varying)::text, ('webhook'::character varying)::text, ('stream'::character varying)::text, ('s3'::character varying)::text, ('gcs'::character varying)::text, ('azure_blob'::character varying)::text, ('sftp'::character varying)::text, ('quickbooks'::character varying)::text, ('stripe'::character varying)::text, ('shopify'::character varying)::text, ('salesforce'::character varying)::text]))),
    CONSTRAINT check_sync_frequency CHECK (((sync_frequency)::text = ANY (ARRAY[('manual'::character varying)::text, ('realtime'::character varying)::text, ('every_5_minutes'::character varying)::text, ('every_15_minutes'::character varying)::text, ('every_30_minutes'::character varying)::text, ('hourly'::character varying)::text, ('every_6_hours'::character varying)::text, ('daily'::character varying)::text, ('weekly'::character varying)::text, ('monthly'::character varying)::text])))
);


--
-- Name: data_sources_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.data_sources_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: data_sources_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.data_sources_id_seq OWNED BY public.data_sources.id;


--
-- Name: imported_records; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.imported_records (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    data_source_id bigint NOT NULL,
    record_data jsonb DEFAULT '{}'::jsonb NOT NULL,
    external_id character varying,
    imported_at timestamp(6) without time zone NOT NULL,
    sync_log_id bigint,
    checksum character varying,
    row_number integer,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: imported_records_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.imported_records_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: imported_records_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.imported_records_id_seq OWNED BY public.imported_records.id;


--
-- Name: integrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.integrations (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    data_source_id bigint NOT NULL,
    name character varying NOT NULL,
    sync_type character varying,
    mapping json,
    transformation_rules json,
    target_table character varying,
    status character varying DEFAULT 'inactive'::character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: integrations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.integrations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: integrations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.integrations_id_seq OWNED BY public.integrations.id;


--
-- Name: monitoring_rules; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.monitoring_rules (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    data_source_id bigint,
    name character varying NOT NULL,
    rule_type character varying NOT NULL,
    severity character varying DEFAULT 'warning'::character varying NOT NULL,
    enabled boolean DEFAULT true,
    conditions jsonb DEFAULT '{}'::jsonb NOT NULL,
    notification_channels jsonb DEFAULT '{}'::jsonb,
    cooldown_minutes integer DEFAULT 60,
    last_triggered_at timestamp(6) without time zone,
    description text,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: monitoring_rules_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.monitoring_rules_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: monitoring_rules_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.monitoring_rules_id_seq OWNED BY public.monitoring_rules.id;


--
-- Name: organizations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.organizations (
    id bigint NOT NULL,
    name character varying NOT NULL,
    plan character varying DEFAULT 'starter'::character varying,
    settings text,
    subdomain character varying,
    monthly_row_limit integer,
    current_month_rows integer DEFAULT 0,
    plan_expires_at timestamp(6) without time zone,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: organizations_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.organizations_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: organizations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.organizations_id_seq OWNED BY public.organizations.id;


--
-- Name: performance_metrics; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.performance_metrics (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    data_source_id bigint,
    sync_log_id bigint,
    metric_type character varying NOT NULL,
    resource_type character varying,
    resource_id bigint,
    value numeric NOT NULL,
    unit character varying,
    metadata jsonb DEFAULT '{}'::jsonb,
    recorded_at timestamp(6) without time zone NOT NULL,
    hour_of_day integer,
    day_of_week integer,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: performance_metrics_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.performance_metrics_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: performance_metrics_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.performance_metrics_id_seq OWNED BY public.performance_metrics.id;


--
-- Name: pipelines; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.pipelines (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    data_source_id bigint NOT NULL,
    name character varying NOT NULL,
    description text,
    schedule character varying,
    status character varying DEFAULT 'inactive'::character varying NOT NULL,
    configuration json,
    last_run_at timestamp without time zone,
    error_message text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    enabled boolean DEFAULT false
);


--
-- Name: pipelines_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.pipelines_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: pipelines_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.pipelines_id_seq OWNED BY public.pipelines.id;


--
-- Name: schema_migrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.schema_migrations (
    version character varying NOT NULL
);


--
-- Name: solid_queue_blocked_executions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.solid_queue_blocked_executions (
    id bigint NOT NULL,
    job_id bigint NOT NULL,
    queue_name character varying NOT NULL,
    priority integer DEFAULT 0 NOT NULL,
    concurrency_key character varying NOT NULL,
    expires_at timestamp(6) without time zone NOT NULL,
    created_at timestamp(6) without time zone NOT NULL
);


--
-- Name: solid_queue_blocked_executions_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.solid_queue_blocked_executions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: solid_queue_blocked_executions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.solid_queue_blocked_executions_id_seq OWNED BY public.solid_queue_blocked_executions.id;


--
-- Name: solid_queue_claimed_executions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.solid_queue_claimed_executions (
    id bigint NOT NULL,
    job_id bigint NOT NULL,
    process_id bigint,
    created_at timestamp(6) without time zone NOT NULL
);


--
-- Name: solid_queue_claimed_executions_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.solid_queue_claimed_executions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: solid_queue_claimed_executions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.solid_queue_claimed_executions_id_seq OWNED BY public.solid_queue_claimed_executions.id;


--
-- Name: solid_queue_failed_executions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.solid_queue_failed_executions (
    id bigint NOT NULL,
    job_id bigint NOT NULL,
    error text,
    created_at timestamp(6) without time zone NOT NULL
);


--
-- Name: solid_queue_failed_executions_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.solid_queue_failed_executions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: solid_queue_failed_executions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.solid_queue_failed_executions_id_seq OWNED BY public.solid_queue_failed_executions.id;


--
-- Name: solid_queue_jobs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.solid_queue_jobs (
    id bigint NOT NULL,
    queue_name character varying NOT NULL,
    class_name character varying NOT NULL,
    arguments text,
    priority integer DEFAULT 0 NOT NULL,
    active_job_id character varying,
    scheduled_at timestamp(6) without time zone,
    finished_at timestamp(6) without time zone,
    concurrency_key character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: solid_queue_jobs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.solid_queue_jobs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: solid_queue_jobs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.solid_queue_jobs_id_seq OWNED BY public.solid_queue_jobs.id;


--
-- Name: solid_queue_pauses; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.solid_queue_pauses (
    id bigint NOT NULL,
    queue_name character varying NOT NULL,
    created_at timestamp(6) without time zone NOT NULL
);


--
-- Name: solid_queue_pauses_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.solid_queue_pauses_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: solid_queue_pauses_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.solid_queue_pauses_id_seq OWNED BY public.solid_queue_pauses.id;


--
-- Name: solid_queue_processes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.solid_queue_processes (
    id bigint NOT NULL,
    kind character varying NOT NULL,
    last_heartbeat_at timestamp(6) without time zone NOT NULL,
    supervisor_id bigint,
    pid integer NOT NULL,
    hostname character varying,
    metadata text,
    created_at timestamp(6) without time zone NOT NULL,
    name character varying NOT NULL
);


--
-- Name: solid_queue_processes_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.solid_queue_processes_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: solid_queue_processes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.solid_queue_processes_id_seq OWNED BY public.solid_queue_processes.id;


--
-- Name: solid_queue_ready_executions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.solid_queue_ready_executions (
    id bigint NOT NULL,
    job_id bigint NOT NULL,
    queue_name character varying NOT NULL,
    priority integer DEFAULT 0 NOT NULL,
    created_at timestamp(6) without time zone NOT NULL
);


--
-- Name: solid_queue_ready_executions_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.solid_queue_ready_executions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: solid_queue_ready_executions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.solid_queue_ready_executions_id_seq OWNED BY public.solid_queue_ready_executions.id;


--
-- Name: solid_queue_recurring_executions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.solid_queue_recurring_executions (
    id bigint NOT NULL,
    job_id bigint NOT NULL,
    task_key character varying NOT NULL,
    run_at timestamp(6) without time zone NOT NULL,
    created_at timestamp(6) without time zone NOT NULL
);


--
-- Name: solid_queue_recurring_executions_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.solid_queue_recurring_executions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: solid_queue_recurring_executions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.solid_queue_recurring_executions_id_seq OWNED BY public.solid_queue_recurring_executions.id;


--
-- Name: solid_queue_recurring_tasks; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.solid_queue_recurring_tasks (
    id bigint NOT NULL,
    key character varying NOT NULL,
    schedule character varying NOT NULL,
    command character varying(2048),
    class_name character varying,
    arguments text,
    queue_name character varying,
    priority integer DEFAULT 0,
    static boolean DEFAULT true NOT NULL,
    description text,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: solid_queue_recurring_tasks_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.solid_queue_recurring_tasks_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: solid_queue_recurring_tasks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.solid_queue_recurring_tasks_id_seq OWNED BY public.solid_queue_recurring_tasks.id;


--
-- Name: solid_queue_scheduled_executions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.solid_queue_scheduled_executions (
    id bigint NOT NULL,
    job_id bigint NOT NULL,
    queue_name character varying NOT NULL,
    priority integer DEFAULT 0 NOT NULL,
    scheduled_at timestamp(6) without time zone NOT NULL,
    created_at timestamp(6) without time zone NOT NULL
);


--
-- Name: solid_queue_scheduled_executions_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.solid_queue_scheduled_executions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: solid_queue_scheduled_executions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.solid_queue_scheduled_executions_id_seq OWNED BY public.solid_queue_scheduled_executions.id;


--
-- Name: solid_queue_semaphores; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.solid_queue_semaphores (
    id bigint NOT NULL,
    key character varying NOT NULL,
    value integer DEFAULT 1 NOT NULL,
    expires_at timestamp(6) without time zone NOT NULL,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL
);


--
-- Name: solid_queue_semaphores_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.solid_queue_semaphores_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: solid_queue_semaphores_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.solid_queue_semaphores_id_seq OWNED BY public.solid_queue_semaphores.id;


--
-- Name: sync_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.sync_logs (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    data_source_id bigint NOT NULL,
    status character varying DEFAULT 'pending'::character varying NOT NULL,
    started_at timestamp(6) without time zone NOT NULL,
    completed_at timestamp(6) without time zone,
    records_processed integer DEFAULT 0,
    records_imported integer DEFAULT 0,
    records_failed integer DEFAULT 0,
    records_skipped integer DEFAULT 0,
    error_message text,
    metadata jsonb DEFAULT '{}'::jsonb,
    error_details jsonb DEFAULT '{}'::jsonb,
    duration_seconds numeric,
    sync_type character varying,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    CONSTRAINT sync_logs_status_check CHECK (((status)::text = ANY ((ARRAY['pending'::character varying, 'running'::character varying, 'completed'::character varying, 'failed'::character varying, 'cancelled'::character varying])::text[])))
);


--
-- Name: sync_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.sync_logs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: sync_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.sync_logs_id_seq OWNED BY public.sync_logs.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.users (
    id bigint NOT NULL,
    organization_id bigint NOT NULL,
    email character varying NOT NULL,
    encrypted_password character varying NOT NULL,
    role character varying DEFAULT 'member'::character varying,
    first_name character varying,
    last_name character varying,
    last_sign_in_at timestamp(6) without time zone,
    active boolean DEFAULT true,
    created_at timestamp(6) without time zone NOT NULL,
    updated_at timestamp(6) without time zone NOT NULL,
    reset_password_token character varying,
    reset_password_sent_at timestamp(6) without time zone,
    remember_created_at timestamp(6) without time zone,
    sign_in_count integer DEFAULT 0 NOT NULL,
    current_sign_in_at timestamp(6) without time zone,
    current_sign_in_ip character varying,
    last_sign_in_ip character varying
);


--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.users_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: alerts id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.alerts ALTER COLUMN id SET DEFAULT nextval('public.alerts_id_seq'::regclass);


--
-- Name: data_sources id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_sources ALTER COLUMN id SET DEFAULT nextval('public.data_sources_id_seq'::regclass);


--
-- Name: imported_records id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.imported_records ALTER COLUMN id SET DEFAULT nextval('public.imported_records_id_seq'::regclass);


--
-- Name: integrations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.integrations ALTER COLUMN id SET DEFAULT nextval('public.integrations_id_seq'::regclass);


--
-- Name: monitoring_rules id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.monitoring_rules ALTER COLUMN id SET DEFAULT nextval('public.monitoring_rules_id_seq'::regclass);


--
-- Name: organizations id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations ALTER COLUMN id SET DEFAULT nextval('public.organizations_id_seq'::regclass);


--
-- Name: performance_metrics id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.performance_metrics ALTER COLUMN id SET DEFAULT nextval('public.performance_metrics_id_seq'::regclass);


--
-- Name: pipelines id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pipelines ALTER COLUMN id SET DEFAULT nextval('public.pipelines_id_seq'::regclass);


--
-- Name: solid_queue_blocked_executions id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_blocked_executions ALTER COLUMN id SET DEFAULT nextval('public.solid_queue_blocked_executions_id_seq'::regclass);


--
-- Name: solid_queue_claimed_executions id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_claimed_executions ALTER COLUMN id SET DEFAULT nextval('public.solid_queue_claimed_executions_id_seq'::regclass);


--
-- Name: solid_queue_failed_executions id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_failed_executions ALTER COLUMN id SET DEFAULT nextval('public.solid_queue_failed_executions_id_seq'::regclass);


--
-- Name: solid_queue_jobs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_jobs ALTER COLUMN id SET DEFAULT nextval('public.solid_queue_jobs_id_seq'::regclass);


--
-- Name: solid_queue_pauses id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_pauses ALTER COLUMN id SET DEFAULT nextval('public.solid_queue_pauses_id_seq'::regclass);


--
-- Name: solid_queue_processes id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_processes ALTER COLUMN id SET DEFAULT nextval('public.solid_queue_processes_id_seq'::regclass);


--
-- Name: solid_queue_ready_executions id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_ready_executions ALTER COLUMN id SET DEFAULT nextval('public.solid_queue_ready_executions_id_seq'::regclass);


--
-- Name: solid_queue_recurring_executions id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_recurring_executions ALTER COLUMN id SET DEFAULT nextval('public.solid_queue_recurring_executions_id_seq'::regclass);


--
-- Name: solid_queue_recurring_tasks id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_recurring_tasks ALTER COLUMN id SET DEFAULT nextval('public.solid_queue_recurring_tasks_id_seq'::regclass);


--
-- Name: solid_queue_scheduled_executions id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_scheduled_executions ALTER COLUMN id SET DEFAULT nextval('public.solid_queue_scheduled_executions_id_seq'::regclass);


--
-- Name: solid_queue_semaphores id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_semaphores ALTER COLUMN id SET DEFAULT nextval('public.solid_queue_semaphores_id_seq'::regclass);


--
-- Name: sync_logs id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sync_logs ALTER COLUMN id SET DEFAULT nextval('public.sync_logs_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Name: alerts alerts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.alerts
    ADD CONSTRAINT alerts_pkey PRIMARY KEY (id);


--
-- Name: ar_internal_metadata ar_internal_metadata_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.ar_internal_metadata
    ADD CONSTRAINT ar_internal_metadata_pkey PRIMARY KEY (key);


--
-- Name: data_sources data_sources_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_sources
    ADD CONSTRAINT data_sources_pkey PRIMARY KEY (id);


--
-- Name: imported_records imported_records_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.imported_records
    ADD CONSTRAINT imported_records_pkey PRIMARY KEY (id);


--
-- Name: integrations integrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.integrations
    ADD CONSTRAINT integrations_pkey PRIMARY KEY (id);


--
-- Name: monitoring_rules monitoring_rules_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.monitoring_rules
    ADD CONSTRAINT monitoring_rules_pkey PRIMARY KEY (id);


--
-- Name: organizations organizations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.organizations
    ADD CONSTRAINT organizations_pkey PRIMARY KEY (id);


--
-- Name: performance_metrics performance_metrics_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.performance_metrics
    ADD CONSTRAINT performance_metrics_pkey PRIMARY KEY (id);


--
-- Name: pipelines pipelines_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pipelines
    ADD CONSTRAINT pipelines_pkey PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: solid_queue_blocked_executions solid_queue_blocked_executions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_blocked_executions
    ADD CONSTRAINT solid_queue_blocked_executions_pkey PRIMARY KEY (id);


--
-- Name: solid_queue_claimed_executions solid_queue_claimed_executions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_claimed_executions
    ADD CONSTRAINT solid_queue_claimed_executions_pkey PRIMARY KEY (id);


--
-- Name: solid_queue_failed_executions solid_queue_failed_executions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_failed_executions
    ADD CONSTRAINT solid_queue_failed_executions_pkey PRIMARY KEY (id);


--
-- Name: solid_queue_jobs solid_queue_jobs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_jobs
    ADD CONSTRAINT solid_queue_jobs_pkey PRIMARY KEY (id);


--
-- Name: solid_queue_pauses solid_queue_pauses_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_pauses
    ADD CONSTRAINT solid_queue_pauses_pkey PRIMARY KEY (id);


--
-- Name: solid_queue_processes solid_queue_processes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_processes
    ADD CONSTRAINT solid_queue_processes_pkey PRIMARY KEY (id);


--
-- Name: solid_queue_ready_executions solid_queue_ready_executions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_ready_executions
    ADD CONSTRAINT solid_queue_ready_executions_pkey PRIMARY KEY (id);


--
-- Name: solid_queue_recurring_executions solid_queue_recurring_executions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_recurring_executions
    ADD CONSTRAINT solid_queue_recurring_executions_pkey PRIMARY KEY (id);


--
-- Name: solid_queue_recurring_tasks solid_queue_recurring_tasks_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_recurring_tasks
    ADD CONSTRAINT solid_queue_recurring_tasks_pkey PRIMARY KEY (id);


--
-- Name: solid_queue_scheduled_executions solid_queue_scheduled_executions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_scheduled_executions
    ADD CONSTRAINT solid_queue_scheduled_executions_pkey PRIMARY KEY (id);


--
-- Name: solid_queue_semaphores solid_queue_semaphores_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_semaphores
    ADD CONSTRAINT solid_queue_semaphores_pkey PRIMARY KEY (id);


--
-- Name: sync_logs sync_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sync_logs
    ADD CONSTRAINT sync_logs_pkey PRIMARY KEY (id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: idx_imported_records_checksum; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_imported_records_checksum ON public.imported_records USING btree (data_source_id, checksum);


--
-- Name: idx_imported_records_unique_external_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX idx_imported_records_unique_external_id ON public.imported_records USING btree (data_source_id, external_id);


--
-- Name: idx_on_data_source_id_metric_type_recorded_at_92c83678d6; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_on_data_source_id_metric_type_recorded_at_92c83678d6 ON public.performance_metrics USING btree (data_source_id, metric_type, recorded_at);


--
-- Name: index_alerts_on_acknowledged_by_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_alerts_on_acknowledged_by_id ON public.alerts USING btree (acknowledged_by_id);


--
-- Name: index_alerts_on_data_source_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_alerts_on_data_source_id ON public.alerts USING btree (data_source_id);


--
-- Name: index_alerts_on_data_source_id_and_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_alerts_on_data_source_id_and_status ON public.alerts USING btree (data_source_id, status);


--
-- Name: index_alerts_on_monitoring_rule_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_alerts_on_monitoring_rule_id ON public.alerts USING btree (monitoring_rule_id);


--
-- Name: index_alerts_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_alerts_on_organization_id ON public.alerts USING btree (organization_id);


--
-- Name: index_alerts_on_organization_id_and_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_alerts_on_organization_id_and_status ON public.alerts USING btree (organization_id, status);


--
-- Name: index_alerts_on_pipeline_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_alerts_on_pipeline_id ON public.alerts USING btree (pipeline_id);


--
-- Name: index_alerts_on_resolved_by_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_alerts_on_resolved_by_id ON public.alerts USING btree (resolved_by_id);


--
-- Name: index_alerts_on_severity; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_alerts_on_severity ON public.alerts USING btree (severity);


--
-- Name: index_alerts_on_sync_log_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_alerts_on_sync_log_id ON public.alerts USING btree (sync_log_id);


--
-- Name: index_alerts_on_triggered_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_alerts_on_triggered_at ON public.alerts USING btree (triggered_at);


--
-- Name: index_data_sources_on_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_data_sources_on_active ON public.data_sources USING btree (active);


--
-- Name: index_data_sources_on_last_sync_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_data_sources_on_last_sync_at ON public.data_sources USING btree (last_sync_at);


--
-- Name: index_data_sources_on_next_sync_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_data_sources_on_next_sync_at ON public.data_sources USING btree (next_sync_at);


--
-- Name: index_data_sources_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_data_sources_on_organization_id ON public.data_sources USING btree (organization_id);


--
-- Name: index_data_sources_on_organization_id_and_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_data_sources_on_organization_id_and_active ON public.data_sources USING btree (organization_id, active);


--
-- Name: index_data_sources_on_organization_id_and_name; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_data_sources_on_organization_id_and_name ON public.data_sources USING btree (organization_id, name);


--
-- Name: index_data_sources_on_organization_id_and_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_data_sources_on_organization_id_and_status ON public.data_sources USING btree (organization_id, status);


--
-- Name: index_data_sources_on_source_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_data_sources_on_source_type ON public.data_sources USING btree (source_type);


--
-- Name: index_data_sources_on_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_data_sources_on_status ON public.data_sources USING btree (status);


--
-- Name: index_imported_records_on_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_imported_records_on_created_at ON public.imported_records USING btree (created_at);


--
-- Name: index_imported_records_on_data_source_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_imported_records_on_data_source_id ON public.imported_records USING btree (data_source_id);


--
-- Name: index_imported_records_on_external_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_imported_records_on_external_id ON public.imported_records USING btree (external_id);


--
-- Name: index_imported_records_on_imported_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_imported_records_on_imported_at ON public.imported_records USING btree (imported_at);


--
-- Name: index_imported_records_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_imported_records_on_organization_id ON public.imported_records USING btree (organization_id);


--
-- Name: index_imported_records_on_record_data; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_imported_records_on_record_data ON public.imported_records USING gin (record_data);


--
-- Name: index_imported_records_on_sync_log_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_imported_records_on_sync_log_id ON public.imported_records USING btree (sync_log_id);


--
-- Name: index_integrations_on_data_source_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_integrations_on_data_source_id ON public.integrations USING btree (data_source_id);


--
-- Name: index_integrations_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_integrations_on_organization_id ON public.integrations USING btree (organization_id);


--
-- Name: index_integrations_on_organization_id_and_name; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_integrations_on_organization_id_and_name ON public.integrations USING btree (organization_id, name);


--
-- Name: index_monitoring_rules_on_data_source_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_monitoring_rules_on_data_source_id ON public.monitoring_rules USING btree (data_source_id);


--
-- Name: index_monitoring_rules_on_last_triggered_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_monitoring_rules_on_last_triggered_at ON public.monitoring_rules USING btree (last_triggered_at);


--
-- Name: index_monitoring_rules_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_monitoring_rules_on_organization_id ON public.monitoring_rules USING btree (organization_id);


--
-- Name: index_monitoring_rules_on_organization_id_and_enabled; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_monitoring_rules_on_organization_id_and_enabled ON public.monitoring_rules USING btree (organization_id, enabled);


--
-- Name: index_monitoring_rules_on_rule_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_monitoring_rules_on_rule_type ON public.monitoring_rules USING btree (rule_type);


--
-- Name: index_organizations_on_plan; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_organizations_on_plan ON public.organizations USING btree (plan);


--
-- Name: index_organizations_on_subdomain; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_organizations_on_subdomain ON public.organizations USING btree (subdomain);


--
-- Name: index_performance_metrics_on_data_source_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_performance_metrics_on_data_source_id ON public.performance_metrics USING btree (data_source_id);


--
-- Name: index_performance_metrics_on_day_of_week; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_performance_metrics_on_day_of_week ON public.performance_metrics USING btree (day_of_week);


--
-- Name: index_performance_metrics_on_hour_of_day; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_performance_metrics_on_hour_of_day ON public.performance_metrics USING btree (hour_of_day);


--
-- Name: index_performance_metrics_on_metric_type_and_recorded_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_performance_metrics_on_metric_type_and_recorded_at ON public.performance_metrics USING btree (metric_type, recorded_at);


--
-- Name: index_performance_metrics_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_performance_metrics_on_organization_id ON public.performance_metrics USING btree (organization_id);


--
-- Name: index_performance_metrics_on_organization_id_and_recorded_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_performance_metrics_on_organization_id_and_recorded_at ON public.performance_metrics USING btree (organization_id, recorded_at);


--
-- Name: index_performance_metrics_on_resource_type_and_resource_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_performance_metrics_on_resource_type_and_resource_id ON public.performance_metrics USING btree (resource_type, resource_id);


--
-- Name: index_performance_metrics_on_sync_log_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_performance_metrics_on_sync_log_id ON public.performance_metrics USING btree (sync_log_id);


--
-- Name: index_pipelines_on_organization_id_and_name; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_pipelines_on_organization_id_and_name ON public.pipelines USING btree (organization_id, name);


--
-- Name: index_solid_queue_blocked_executions_for_maintenance; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_solid_queue_blocked_executions_for_maintenance ON public.solid_queue_blocked_executions USING btree (expires_at, concurrency_key);


--
-- Name: index_solid_queue_blocked_executions_for_release; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_solid_queue_blocked_executions_for_release ON public.solid_queue_blocked_executions USING btree (concurrency_key, priority, job_id);


--
-- Name: index_solid_queue_blocked_executions_on_job_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_solid_queue_blocked_executions_on_job_id ON public.solid_queue_blocked_executions USING btree (job_id);


--
-- Name: index_solid_queue_claimed_executions_on_job_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_solid_queue_claimed_executions_on_job_id ON public.solid_queue_claimed_executions USING btree (job_id);


--
-- Name: index_solid_queue_claimed_executions_on_process_id_and_job_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_solid_queue_claimed_executions_on_process_id_and_job_id ON public.solid_queue_claimed_executions USING btree (process_id, job_id);


--
-- Name: index_solid_queue_dispatch_all; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_solid_queue_dispatch_all ON public.solid_queue_scheduled_executions USING btree (scheduled_at, priority, job_id);


--
-- Name: index_solid_queue_failed_executions_on_job_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_solid_queue_failed_executions_on_job_id ON public.solid_queue_failed_executions USING btree (job_id);


--
-- Name: index_solid_queue_jobs_for_alerting; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_solid_queue_jobs_for_alerting ON public.solid_queue_jobs USING btree (scheduled_at, finished_at);


--
-- Name: index_solid_queue_jobs_for_filtering; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_solid_queue_jobs_for_filtering ON public.solid_queue_jobs USING btree (queue_name, finished_at);


--
-- Name: index_solid_queue_jobs_on_active_job_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_solid_queue_jobs_on_active_job_id ON public.solid_queue_jobs USING btree (active_job_id);


--
-- Name: index_solid_queue_jobs_on_class_name; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_solid_queue_jobs_on_class_name ON public.solid_queue_jobs USING btree (class_name);


--
-- Name: index_solid_queue_jobs_on_finished_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_solid_queue_jobs_on_finished_at ON public.solid_queue_jobs USING btree (finished_at);


--
-- Name: index_solid_queue_pauses_on_queue_name; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_solid_queue_pauses_on_queue_name ON public.solid_queue_pauses USING btree (queue_name);


--
-- Name: index_solid_queue_poll_all; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_solid_queue_poll_all ON public.solid_queue_ready_executions USING btree (priority, job_id);


--
-- Name: index_solid_queue_poll_by_queue; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_solid_queue_poll_by_queue ON public.solid_queue_ready_executions USING btree (queue_name, priority, job_id);


--
-- Name: index_solid_queue_processes_on_last_heartbeat_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_solid_queue_processes_on_last_heartbeat_at ON public.solid_queue_processes USING btree (last_heartbeat_at);


--
-- Name: index_solid_queue_processes_on_name_and_supervisor_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_solid_queue_processes_on_name_and_supervisor_id ON public.solid_queue_processes USING btree (name, supervisor_id);


--
-- Name: index_solid_queue_processes_on_supervisor_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_solid_queue_processes_on_supervisor_id ON public.solid_queue_processes USING btree (supervisor_id);


--
-- Name: index_solid_queue_ready_executions_on_job_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_solid_queue_ready_executions_on_job_id ON public.solid_queue_ready_executions USING btree (job_id);


--
-- Name: index_solid_queue_recurring_executions_on_job_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_solid_queue_recurring_executions_on_job_id ON public.solid_queue_recurring_executions USING btree (job_id);


--
-- Name: index_solid_queue_recurring_executions_on_task_key_and_run_at; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_solid_queue_recurring_executions_on_task_key_and_run_at ON public.solid_queue_recurring_executions USING btree (task_key, run_at);


--
-- Name: index_solid_queue_recurring_tasks_on_key; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_solid_queue_recurring_tasks_on_key ON public.solid_queue_recurring_tasks USING btree (key);


--
-- Name: index_solid_queue_recurring_tasks_on_static; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_solid_queue_recurring_tasks_on_static ON public.solid_queue_recurring_tasks USING btree (static);


--
-- Name: index_solid_queue_scheduled_executions_on_job_id; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_solid_queue_scheduled_executions_on_job_id ON public.solid_queue_scheduled_executions USING btree (job_id);


--
-- Name: index_solid_queue_semaphores_on_expires_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_solid_queue_semaphores_on_expires_at ON public.solid_queue_semaphores USING btree (expires_at);


--
-- Name: index_solid_queue_semaphores_on_key; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_solid_queue_semaphores_on_key ON public.solid_queue_semaphores USING btree (key);


--
-- Name: index_solid_queue_semaphores_on_key_and_value; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_solid_queue_semaphores_on_key_and_value ON public.solid_queue_semaphores USING btree (key, value);


--
-- Name: index_sync_logs_on_data_source_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sync_logs_on_data_source_id ON public.sync_logs USING btree (data_source_id);


--
-- Name: index_sync_logs_on_data_source_id_and_started_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sync_logs_on_data_source_id_and_started_at ON public.sync_logs USING btree (data_source_id, started_at DESC);


--
-- Name: index_sync_logs_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sync_logs_on_organization_id ON public.sync_logs USING btree (organization_id);


--
-- Name: index_sync_logs_on_started_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sync_logs_on_started_at ON public.sync_logs USING btree (started_at);


--
-- Name: index_sync_logs_on_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_sync_logs_on_status ON public.sync_logs USING btree (status);


--
-- Name: index_users_on_organization_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_users_on_organization_id ON public.users USING btree (organization_id);


--
-- Name: index_users_on_organization_id_and_email; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_users_on_organization_id_and_email ON public.users USING btree (organization_id, email);


--
-- Name: index_users_on_reset_password_token; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX index_users_on_reset_password_token ON public.users USING btree (reset_password_token);


--
-- Name: index_users_on_role; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX index_users_on_role ON public.users USING btree (role);


--
-- Name: monitoring_rules fk_rails_0c2735479d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.monitoring_rules
    ADD CONSTRAINT fk_rails_0c2735479d FOREIGN KEY (data_source_id) REFERENCES public.data_sources(id);


--
-- Name: performance_metrics fk_rails_18d1b25f1f; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.performance_metrics
    ADD CONSTRAINT fk_rails_18d1b25f1f FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: sync_logs fk_rails_2b92e4434a; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sync_logs
    ADD CONSTRAINT fk_rails_2b92e4434a FOREIGN KEY (data_source_id) REFERENCES public.data_sources(id);


--
-- Name: alerts fk_rails_2ca599be37; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.alerts
    ADD CONSTRAINT fk_rails_2ca599be37 FOREIGN KEY (acknowledged_by_id) REFERENCES public.users(id);


--
-- Name: solid_queue_recurring_executions fk_rails_318a5533ed; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_recurring_executions
    ADD CONSTRAINT fk_rails_318a5533ed FOREIGN KEY (job_id) REFERENCES public.solid_queue_jobs(id) ON DELETE CASCADE;


--
-- Name: performance_metrics fk_rails_3371e19a9c; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.performance_metrics
    ADD CONSTRAINT fk_rails_3371e19a9c FOREIGN KEY (sync_log_id) REFERENCES public.sync_logs(id);


--
-- Name: solid_queue_failed_executions fk_rails_39bbc7a631; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_failed_executions
    ADD CONSTRAINT fk_rails_39bbc7a631 FOREIGN KEY (job_id) REFERENCES public.solid_queue_jobs(id) ON DELETE CASCADE;


--
-- Name: imported_records fk_rails_3c2a4cd1de; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.imported_records
    ADD CONSTRAINT fk_rails_3c2a4cd1de FOREIGN KEY (data_source_id) REFERENCES public.data_sources(id);


--
-- Name: monitoring_rules fk_rails_3ddda4588a; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.monitoring_rules
    ADD CONSTRAINT fk_rails_3ddda4588a FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: alerts fk_rails_4bc32add75; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.alerts
    ADD CONSTRAINT fk_rails_4bc32add75 FOREIGN KEY (data_source_id) REFERENCES public.data_sources(id);


--
-- Name: solid_queue_blocked_executions fk_rails_4cd34e2228; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_blocked_executions
    ADD CONSTRAINT fk_rails_4cd34e2228 FOREIGN KEY (job_id) REFERENCES public.solid_queue_jobs(id) ON DELETE CASCADE;


--
-- Name: alerts fk_rails_6ad39ae993; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.alerts
    ADD CONSTRAINT fk_rails_6ad39ae993 FOREIGN KEY (monitoring_rule_id) REFERENCES public.monitoring_rules(id);


--
-- Name: integrations fk_rails_755d734f25; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.integrations
    ADD CONSTRAINT fk_rails_755d734f25 FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: integrations fk_rails_81772e8cd9; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.integrations
    ADD CONSTRAINT fk_rails_81772e8cd9 FOREIGN KEY (data_source_id) REFERENCES public.data_sources(id);


--
-- Name: solid_queue_ready_executions fk_rails_81fcbd66af; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_ready_executions
    ADD CONSTRAINT fk_rails_81fcbd66af FOREIGN KEY (job_id) REFERENCES public.solid_queue_jobs(id) ON DELETE CASCADE;


--
-- Name: alerts fk_rails_8c36adee1e; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.alerts
    ADD CONSTRAINT fk_rails_8c36adee1e FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: imported_records fk_rails_8fc5de6d20; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.imported_records
    ADD CONSTRAINT fk_rails_8fc5de6d20 FOREIGN KEY (sync_log_id) REFERENCES public.sync_logs(id);


--
-- Name: data_sources fk_rails_99f4fec2c8; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.data_sources
    ADD CONSTRAINT fk_rails_99f4fec2c8 FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: solid_queue_claimed_executions fk_rails_9cfe4d4944; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_claimed_executions
    ADD CONSTRAINT fk_rails_9cfe4d4944 FOREIGN KEY (job_id) REFERENCES public.solid_queue_jobs(id) ON DELETE CASCADE;


--
-- Name: imported_records fk_rails_b134106e81; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.imported_records
    ADD CONSTRAINT fk_rails_b134106e81 FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: alerts fk_rails_b54663c000; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.alerts
    ADD CONSTRAINT fk_rails_b54663c000 FOREIGN KEY (sync_log_id) REFERENCES public.sync_logs(id);


--
-- Name: alerts fk_rails_b9355fd4e1; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.alerts
    ADD CONSTRAINT fk_rails_b9355fd4e1 FOREIGN KEY (pipeline_id) REFERENCES public.pipelines(id);


--
-- Name: solid_queue_scheduled_executions fk_rails_c4316f352d; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.solid_queue_scheduled_executions
    ADD CONSTRAINT fk_rails_c4316f352d FOREIGN KEY (job_id) REFERENCES public.solid_queue_jobs(id) ON DELETE CASCADE;


--
-- Name: alerts fk_rails_d57155fb1b; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.alerts
    ADD CONSTRAINT fk_rails_d57155fb1b FOREIGN KEY (resolved_by_id) REFERENCES public.users(id);


--
-- Name: users fk_rails_d7b9ff90af; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT fk_rails_d7b9ff90af FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: performance_metrics fk_rails_ec210249fa; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.performance_metrics
    ADD CONSTRAINT fk_rails_ec210249fa FOREIGN KEY (data_source_id) REFERENCES public.data_sources(id);


--
-- Name: sync_logs fk_rails_f6111b0686; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.sync_logs
    ADD CONSTRAINT fk_rails_f6111b0686 FOREIGN KEY (organization_id) REFERENCES public.organizations(id);


--
-- Name: pipelines pipelines_data_source_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pipelines
    ADD CONSTRAINT pipelines_data_source_id_fkey FOREIGN KEY (data_source_id) REFERENCES public.data_sources(id) ON DELETE CASCADE;


--
-- Name: pipelines pipelines_organization_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.pipelines
    ADD CONSTRAINT pipelines_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES public.organizations(id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--

SET search_path TO "$user", public;

INSERT INTO "schema_migrations" (version) VALUES
('20250721121000'),
('20250721120000'),
('20250721115155'),
('20250721115128'),
('20250721115053'),
('20250721015238'),
('20250721015237'),
('20250721014707'),
('20250720223045'),
('20250720223036'),
('20250720214000'),
('20250720163902'),
('20250720162902'),
('20250720162820');
