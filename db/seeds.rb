# Create default organization and admin user for development
if Rails.env.development?
  organization = Organization.create!(
    name: "Demo Company",
    plan: "professional",
    subdomain: "demo",
    monthly_row_limit: 100_000_000
  )
  
  admin = organization.users.create!(
    email: "<EMAIL>",
    password: "password123",
    role: "admin",
    first_name: "Admin",
    last_name: "User"
  )
  
  puts "✓ Demo organization created with admin user (<EMAIL> / password123)"
end