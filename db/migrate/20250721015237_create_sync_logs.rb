class CreateSyncLogs < ActiveRecord::Migration[8.0]
  def change
    create_table :sync_logs do |t|
      t.references :organization, null: false, foreign_key: true
      t.references :data_source, null: false, foreign_key: true
      t.string :status, null: false, default: 'pending'
      t.datetime :started_at, null: false
      t.datetime :completed_at
      t.integer :records_processed, default: 0
      t.integer :records_imported, default: 0
      t.integer :records_failed, default: 0
      t.integer :records_skipped, default: 0
      t.text :error_message
      t.jsonb :metadata, default: {}
      t.jsonb :error_details, default: {} # Store detailed error information
      t.decimal :duration_seconds # Track sync duration
      t.string :sync_type # full, incremental, etc.

      t.timestamps
    end
    
    # Indexes for performance
    add_index :sync_logs, :status
    add_index :sync_logs, :started_at
    add_index :sync_logs, [:data_source_id, :started_at], order: { started_at: :desc }
    
    # Check constraint for status
    add_check_constraint :sync_logs, "status IN ('pending', 'running', 'completed', 'failed', 'cancelled')", name: 'sync_logs_status_check'
  end
end
