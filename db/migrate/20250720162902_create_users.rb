class CreateUsers < ActiveRecord::Migration[8.0]
  def change
    create_table :users do |t|
      t.references :organization, null: false, foreign_key: true
      t.string :email, null: false
      t.string :password_digest, null: false
      t.string :role, default: 'member'
      t.string :first_name
      t.string :last_name
      t.datetime :last_login_at
      t.boolean :active, default: true
      
      t.timestamps
    end
    
    add_index :users, [:organization_id, :email], unique: true
    add_index :users, :role
  end
end
