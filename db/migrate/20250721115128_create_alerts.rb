class CreateAlerts < ActiveRecord::Migration[8.0]
  def change
    create_table :alerts do |t|
      t.references :organization, null: false, foreign_key: true
      t.references :monitoring_rule, null: false, foreign_key: true
      t.references :data_source, foreign_key: true
      t.references :sync_log, foreign_key: true
      t.string :status, null: false, default: 'active' # active, acknowledged, resolved
      t.string :severity, null: false # info, warning, critical
      t.string :alert_type, null: false
      t.string :title, null: false
      t.text :message
      t.jsonb :details, default: {}
      t.jsonb :context, default: {}
      t.datetime :triggered_at, null: false
      t.datetime :acknowledged_at
      t.datetime :resolved_at
      t.references :acknowledged_by, foreign_key: { to_table: :users }
      t.references :resolved_by, foreign_key: { to_table: :users }
      t.text :resolution_notes
      t.timestamps
      
      t.index [:organization_id, :status]
      t.index [:triggered_at]
      t.index :severity
      t.index [:data_source_id, :status]
    end
  end
end
