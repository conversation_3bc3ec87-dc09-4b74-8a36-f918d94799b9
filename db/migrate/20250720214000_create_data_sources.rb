class CreateDataSources < ActiveRecord::Migration[8.0]
  def change
    create_table :data_sources do |t|
      # Core relationships
      t.references :organization, null: false, foreign_key: true
      
      # Basic information
      t.string :name, null: false
      t.text :description
      t.string :source_type, null: false
      
      # Connection and configuration
      t.text :connection_config # Encrypted field for sensitive connection details
      t.jsonb :settings, default: {} # Non-sensitive settings and options
      
      # Status and state
      t.string :status, default: 'inactive', null: false
      t.boolean :active, default: false, null: false
      t.text :error_message
      t.datetime :last_error_at
      
      # Sync information
      t.string :sync_frequency, default: 'manual'
      t.datetime :last_sync_at
      t.datetime :last_successful_sync_at
      t.datetime :next_sync_at
      t.integer :sync_count, default: 0
      t.integer :failed_sync_count, default: 0
      
      # Data statistics
      t.bigint :row_count, default: 0
      t.bigint :total_rows_synced, default: 0
      t.decimal :data_size_mb, precision: 10, scale: 2
      
      # Performance metrics
      t.integer :last_sync_duration_seconds
      t.decimal :avg_sync_duration_seconds, precision: 10, scale: 2
      
      # Metadata
      t.jsonb :metadata, default: {}
      t.jsonb :schema_cache # Cached schema information
      t.datetime :schema_updated_at
      
      # File upload specific (for CSV, Excel, etc.)
      t.string :original_filename
      t.bigint :file_size
      t.datetime :file_uploaded_at
      
      # Versioning and auditing
      t.integer :version, default: 1
      t.string :created_by
      t.string :updated_by
      
      t.timestamps
    end
    
    # Indexes for performance
    add_index :data_sources, [:organization_id, :name], unique: true
    add_index :data_sources, :source_type
    add_index :data_sources, :status
    add_index :data_sources, :active
    add_index :data_sources, :last_sync_at
    add_index :data_sources, :next_sync_at
    add_index :data_sources, [:organization_id, :active]
    add_index :data_sources, [:organization_id, :status]
    
    # Add check constraint for valid statuses
    execute <<-SQL
      ALTER TABLE data_sources
      ADD CONSTRAINT check_data_source_status
      CHECK (status IN ('inactive', 'active', 'syncing', 'error', 'maintenance', 'pending'));
    SQL
    
    # Add check constraint for valid source types
    execute <<-SQL
      ALTER TABLE data_sources
      ADD CONSTRAINT check_data_source_type
      CHECK (source_type IN (
        'postgresql', 'mysql', 'sqlite', 'sqlserver',
        'mongodb', 'redis', 'elasticsearch',
        'csv', 'excel', 'json', 'xml',
        'api', 'webhook', 'stream',
        's3', 'gcs', 'azure_blob', 'sftp',
        'quickbooks', 'stripe', 'shopify', 'salesforce'
      ));
    SQL
    
    # Add check constraint for valid sync frequencies
    execute <<-SQL
      ALTER TABLE data_sources
      ADD CONSTRAINT check_sync_frequency
      CHECK (sync_frequency IN (
        'manual', 'realtime', 
        'every_5_minutes', 'every_15_minutes', 'every_30_minutes',
        'hourly', 'every_6_hours', 'daily', 'weekly', 'monthly'
      ));
    SQL
  end
end