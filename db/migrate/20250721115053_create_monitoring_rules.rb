class CreateMonitoringRules < ActiveRecord::Migration[8.0]
  def change
    create_table :monitoring_rules do |t|
      t.references :organization, null: false, foreign_key: true
      t.references :data_source, foreign_key: true
      t.string :name, null: false
      t.string :rule_type, null: false # sync_failure, performance, data_quality, uptime
      t.string :severity, null: false, default: 'warning' # info, warning, critical
      t.boolean :enabled, default: true
      t.jsonb :conditions, null: false, default: {}
      t.jsonb :notification_channels, default: {}
      t.integer :cooldown_minutes, default: 60
      t.datetime :last_triggered_at
      t.text :description
      t.timestamps
      
      t.index [:organization_id, :enabled]
      t.index [:rule_type]
      t.index :last_triggered_at
    end
  end
end
