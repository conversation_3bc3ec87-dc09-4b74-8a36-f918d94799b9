class CreateIntegrations < ActiveRecord::Migration[8.0]
  def change
    create_table :integrations do |t|
      t.references :organization, null: false, foreign_key: true
      t.references :data_source, null: false, foreign_key: true
      t.string :name, null: false
      t.string :sync_type
      t.json :mapping
      t.json :transformation_rules
      t.string :target_table
      t.string :status, null: false, default: 'inactive'

      t.timestamps
    end
    
    add_index :integrations, [:organization_id, :name], unique: true
  end
end
