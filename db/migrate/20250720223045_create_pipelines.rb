class CreatePipelines < ActiveRecord::Migration[8.0]
  def change
    create_table :pipelines do |t|
      t.references :organization, null: false, foreign_key: true
      t.references :data_source, null: false, foreign_key: true
      t.string :name, null: false
      t.text :description
      t.string :schedule
      t.string :status, null: false, default: 'inactive'
      t.json :configuration
      t.datetime :last_run_at
      t.text :error_message

      t.timestamps
    end
    
    add_index :pipelines, [:organization_id, :name], unique: true
  end
end
