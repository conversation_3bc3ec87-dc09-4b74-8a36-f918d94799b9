class CreatePipelineRuns < ActiveRecord::Migration[8.0]
  def change
    create_table :pipeline_runs do |t|
      t.references :organization, null: false, foreign_key: true
      t.references :pipeline, null: false, foreign_key: true
      t.string :status, null: false, default: 'pending'
      t.datetime :started_at
      t.datetime :completed_at
      t.integer :duration_seconds
      t.integer :records_processed
      t.integer :records_imported
      t.integer :records_failed
      t.integer :steps_completed
      t.integer :total_steps
      t.text :error_message
      t.json :metadata

      t.timestamps
    end
    
    add_index :pipeline_runs, :status
    add_index :pipeline_runs, :started_at
  end
end