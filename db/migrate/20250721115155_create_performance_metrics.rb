class CreatePerformanceMetrics < ActiveRecord::Migration[8.0]
  def change
    create_table :performance_metrics do |t|
      t.references :organization, null: false, foreign_key: true
      t.references :data_source, foreign_key: true
      t.references :sync_log, foreign_key: true
      t.string :metric_type, null: false # sync_duration, memory_usage, cpu_usage, error_rate, throughput
      t.string :resource_type # data_source, sync, system
      t.bigint :resource_id
      t.decimal :value, null: false
      t.string :unit # seconds, bytes, percentage, rows_per_second
      t.jsonb :metadata, default: {}
      t.datetime :recorded_at, null: false
      t.integer :hour_of_day
      t.integer :day_of_week
      t.timestamps
      
      t.index [:organization_id, :recorded_at]
      t.index [:data_source_id, :metric_type, :recorded_at]
      t.index [:metric_type, :recorded_at]
      t.index [:resource_type, :resource_id]
      t.index :hour_of_day
      t.index :day_of_week
    end
  end
end
