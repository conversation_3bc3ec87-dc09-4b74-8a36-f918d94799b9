class CreateOrganizations < ActiveRecord::Migration[8.0]
  def change
    create_table :organizations do |t|
      t.string :name, null: false
      t.string :plan, default: 'starter'
      t.text :settings
      t.string :subdomain
      t.integer :monthly_row_limit
      t.integer :current_month_rows, default: 0
      t.datetime :plan_expires_at
      
      t.timestamps
    end
    
    add_index :organizations, :subdomain, unique: true
    add_index :organizations, :plan
  end
end
