class CreateImportedRecords < ActiveRecord::Migration[8.0]
  def change
    create_table :imported_records do |t|
      t.references :organization, null: false, foreign_key: true
      t.references :data_source, null: false, foreign_key: true
      t.jsonb :record_data, null: false, default: {}
      t.string :external_id
      t.datetime :imported_at, null: false
      t.references :sync_log, foreign_key: true
      t.string :checksum # For deduplication
      t.integer :row_number # Original row number from source

      t.timestamps
    end
    
    # Indexes for performance
    add_index :imported_records, :external_id
    add_index :imported_records, [:data_source_id, :external_id], unique: true, name: 'idx_imported_records_unique_external_id'
    add_index :imported_records, [:data_source_id, :checksum], name: 'idx_imported_records_checksum'
    add_index :imported_records, :imported_at
    add_index :imported_records, :created_at
    
    # GIN index for JSONB queries
    add_index :imported_records, :record_data, using: :gin
  end
end
