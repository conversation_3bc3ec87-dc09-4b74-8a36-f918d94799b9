# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_07_21_015238) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "data_sources", force: :cascade do |t|
    t.bigint "organization_id", null: false
    t.string "name", null: false
    t.text "description"
    t.string "source_type", null: false
    t.text "connection_config"
    t.jsonb "settings", default: {}
    t.string "status", default: "inactive", null: false
    t.boolean "active", default: false, null: false
    t.text "error_message"
    t.datetime "last_error_at"
    t.string "sync_frequency", default: "manual"
    t.datetime "last_sync_at"
    t.datetime "last_successful_sync_at"
    t.datetime "next_sync_at"
    t.integer "sync_count", default: 0
    t.integer "failed_sync_count", default: 0
    t.bigint "row_count", default: 0
    t.bigint "total_rows_synced", default: 0
    t.decimal "data_size_mb", precision: 10, scale: 2
    t.integer "last_sync_duration_seconds"
    t.decimal "avg_sync_duration_seconds", precision: 10, scale: 2
    t.jsonb "metadata", default: {}
    t.jsonb "schema_cache"
    t.datetime "schema_updated_at"
    t.string "original_filename"
    t.bigint "file_size"
    t.datetime "file_uploaded_at"
    t.integer "version", default: 1
    t.string "created_by"
    t.string "updated_by"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active"], name: "index_data_sources_on_active"
    t.index ["last_sync_at"], name: "index_data_sources_on_last_sync_at"
    t.index ["next_sync_at"], name: "index_data_sources_on_next_sync_at"
    t.index ["organization_id", "active"], name: "index_data_sources_on_organization_id_and_active"
    t.index ["organization_id", "name"], name: "index_data_sources_on_organization_id_and_name", unique: true
    t.index ["organization_id", "status"], name: "index_data_sources_on_organization_id_and_status"
    t.index ["organization_id"], name: "index_data_sources_on_organization_id"
    t.index ["source_type"], name: "index_data_sources_on_source_type"
    t.index ["status"], name: "index_data_sources_on_status"
    t.check_constraint "source_type::text = ANY (ARRAY['postgresql'::character varying, 'mysql'::character varying, 'sqlite'::character varying, 'sqlserver'::character varying, 'mongodb'::character varying, 'redis'::character varying, 'elasticsearch'::character varying, 'csv'::character varying, 'excel'::character varying, 'json'::character varying, 'xml'::character varying, 'api'::character varying, 'webhook'::character varying, 'stream'::character varying, 's3'::character varying, 'gcs'::character varying, 'azure_blob'::character varying, 'sftp'::character varying, 'quickbooks'::character varying, 'stripe'::character varying, 'shopify'::character varying, 'salesforce'::character varying]::text[])", name: "check_data_source_type"
    t.check_constraint "status::text = ANY (ARRAY['inactive'::character varying, 'active'::character varying, 'syncing'::character varying, 'error'::character varying, 'maintenance'::character varying, 'pending'::character varying]::text[])", name: "check_data_source_status"
    t.check_constraint "sync_frequency::text = ANY (ARRAY['manual'::character varying, 'realtime'::character varying, 'every_5_minutes'::character varying, 'every_15_minutes'::character varying, 'every_30_minutes'::character varying, 'hourly'::character varying, 'every_6_hours'::character varying, 'daily'::character varying, 'weekly'::character varying, 'monthly'::character varying]::text[])", name: "check_sync_frequency"
  end

  create_table "imported_records", force: :cascade do |t|
    t.bigint "organization_id", null: false
    t.bigint "data_source_id", null: false
    t.jsonb "record_data", default: {}, null: false
    t.string "external_id"
    t.datetime "imported_at", null: false
    t.bigint "sync_log_id"
    t.string "checksum"
    t.integer "row_number"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["created_at"], name: "index_imported_records_on_created_at"
    t.index ["data_source_id", "checksum"], name: "idx_imported_records_checksum"
    t.index ["data_source_id", "external_id"], name: "idx_imported_records_unique_external_id", unique: true
    t.index ["data_source_id"], name: "index_imported_records_on_data_source_id"
    t.index ["external_id"], name: "index_imported_records_on_external_id"
    t.index ["imported_at"], name: "index_imported_records_on_imported_at"
    t.index ["organization_id"], name: "index_imported_records_on_organization_id"
    t.index ["record_data"], name: "index_imported_records_on_record_data", using: :gin
    t.index ["sync_log_id"], name: "index_imported_records_on_sync_log_id"
  end

  create_table "integrations", force: :cascade do |t|
    t.bigint "organization_id", null: false
    t.bigint "data_source_id", null: false
    t.string "name", null: false
    t.string "sync_type"
    t.json "mapping"
    t.json "transformation_rules"
    t.string "target_table"
    t.string "status", default: "inactive", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["data_source_id"], name: "index_integrations_on_data_source_id"
    t.index ["organization_id", "name"], name: "index_integrations_on_organization_id_and_name", unique: true
    t.index ["organization_id"], name: "index_integrations_on_organization_id"
  end

  create_table "organizations", force: :cascade do |t|
    t.string "name", null: false
    t.string "plan", default: "starter"
    t.text "settings"
    t.string "subdomain"
    t.integer "monthly_row_limit"
    t.integer "current_month_rows", default: 0
    t.datetime "plan_expires_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["plan"], name: "index_organizations_on_plan"
    t.index ["subdomain"], name: "index_organizations_on_subdomain", unique: true
  end

  create_table "solid_queue_blocked_executions", force: :cascade do |t|
    t.bigint "job_id", null: false
    t.string "queue_name", null: false
    t.integer "priority", default: 0, null: false
    t.string "concurrency_key", null: false
    t.datetime "expires_at", null: false
    t.datetime "created_at", null: false
    t.index ["concurrency_key", "priority", "job_id"], name: "index_solid_queue_blocked_executions_for_release"
    t.index ["expires_at", "concurrency_key"], name: "index_solid_queue_blocked_executions_for_maintenance"
    t.index ["job_id"], name: "index_solid_queue_blocked_executions_on_job_id", unique: true
  end

  create_table "solid_queue_claimed_executions", force: :cascade do |t|
    t.bigint "job_id", null: false
    t.bigint "process_id"
    t.datetime "created_at", null: false
    t.index ["job_id"], name: "index_solid_queue_claimed_executions_on_job_id", unique: true
    t.index ["process_id", "job_id"], name: "index_solid_queue_claimed_executions_on_process_id_and_job_id"
  end

  create_table "solid_queue_failed_executions", force: :cascade do |t|
    t.bigint "job_id", null: false
    t.text "error"
    t.datetime "created_at", null: false
    t.index ["job_id"], name: "index_solid_queue_failed_executions_on_job_id", unique: true
  end

  create_table "solid_queue_jobs", force: :cascade do |t|
    t.string "queue_name", null: false
    t.string "class_name", null: false
    t.text "arguments"
    t.integer "priority", default: 0, null: false
    t.string "active_job_id"
    t.datetime "scheduled_at"
    t.datetime "finished_at"
    t.string "concurrency_key"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active_job_id"], name: "index_solid_queue_jobs_on_active_job_id"
    t.index ["class_name"], name: "index_solid_queue_jobs_on_class_name"
    t.index ["finished_at"], name: "index_solid_queue_jobs_on_finished_at"
    t.index ["queue_name", "finished_at"], name: "index_solid_queue_jobs_for_filtering"
    t.index ["scheduled_at", "finished_at"], name: "index_solid_queue_jobs_for_alerting"
  end

  create_table "solid_queue_pauses", force: :cascade do |t|
    t.string "queue_name", null: false
    t.datetime "created_at", null: false
    t.index ["queue_name"], name: "index_solid_queue_pauses_on_queue_name", unique: true
  end

  create_table "solid_queue_processes", force: :cascade do |t|
    t.string "kind", null: false
    t.datetime "last_heartbeat_at", null: false
    t.bigint "supervisor_id"
    t.integer "pid", null: false
    t.string "hostname"
    t.text "metadata"
    t.datetime "created_at", null: false
    t.string "name", null: false
    t.index ["last_heartbeat_at"], name: "index_solid_queue_processes_on_last_heartbeat_at"
    t.index ["name", "supervisor_id"], name: "index_solid_queue_processes_on_name_and_supervisor_id", unique: true
    t.index ["supervisor_id"], name: "index_solid_queue_processes_on_supervisor_id"
  end

  create_table "solid_queue_ready_executions", force: :cascade do |t|
    t.bigint "job_id", null: false
    t.string "queue_name", null: false
    t.integer "priority", default: 0, null: false
    t.datetime "created_at", null: false
    t.index ["job_id"], name: "index_solid_queue_ready_executions_on_job_id", unique: true
    t.index ["priority", "job_id"], name: "index_solid_queue_poll_all"
    t.index ["queue_name", "priority", "job_id"], name: "index_solid_queue_poll_by_queue"
  end

  create_table "solid_queue_recurring_executions", force: :cascade do |t|
    t.bigint "job_id", null: false
    t.string "task_key", null: false
    t.datetime "run_at", null: false
    t.datetime "created_at", null: false
    t.index ["job_id"], name: "index_solid_queue_recurring_executions_on_job_id", unique: true
    t.index ["task_key", "run_at"], name: "index_solid_queue_recurring_executions_on_task_key_and_run_at", unique: true
  end

  create_table "solid_queue_recurring_tasks", force: :cascade do |t|
    t.string "key", null: false
    t.string "schedule", null: false
    t.string "command", limit: 2048
    t.string "class_name"
    t.text "arguments"
    t.string "queue_name"
    t.integer "priority", default: 0
    t.boolean "static", default: true, null: false
    t.text "description"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key"], name: "index_solid_queue_recurring_tasks_on_key", unique: true
    t.index ["static"], name: "index_solid_queue_recurring_tasks_on_static"
  end

  create_table "solid_queue_scheduled_executions", force: :cascade do |t|
    t.bigint "job_id", null: false
    t.string "queue_name", null: false
    t.integer "priority", default: 0, null: false
    t.datetime "scheduled_at", null: false
    t.datetime "created_at", null: false
    t.index ["job_id"], name: "index_solid_queue_scheduled_executions_on_job_id", unique: true
    t.index ["scheduled_at", "priority", "job_id"], name: "index_solid_queue_dispatch_all"
  end

  create_table "solid_queue_semaphores", force: :cascade do |t|
    t.string "key", null: false
    t.integer "value", default: 1, null: false
    t.datetime "expires_at", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["expires_at"], name: "index_solid_queue_semaphores_on_expires_at"
    t.index ["key", "value"], name: "index_solid_queue_semaphores_on_key_and_value"
    t.index ["key"], name: "index_solid_queue_semaphores_on_key", unique: true
  end

  create_table "sync_logs", force: :cascade do |t|
    t.bigint "organization_id", null: false
    t.bigint "data_source_id", null: false
    t.string "status", default: "pending", null: false
    t.datetime "started_at", null: false
    t.datetime "completed_at"
    t.integer "records_processed", default: 0
    t.integer "records_imported", default: 0
    t.integer "records_failed", default: 0
    t.integer "records_skipped", default: 0
    t.text "error_message"
    t.jsonb "metadata", default: {}
    t.jsonb "error_details", default: {}
    t.decimal "duration_seconds"
    t.string "sync_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["data_source_id", "started_at"], name: "index_sync_logs_on_data_source_id_and_started_at", order: { started_at: :desc }
    t.index ["data_source_id"], name: "index_sync_logs_on_data_source_id"
    t.index ["organization_id"], name: "index_sync_logs_on_organization_id"
    t.index ["started_at"], name: "index_sync_logs_on_started_at"
    t.index ["status"], name: "index_sync_logs_on_status"
    t.check_constraint "status::text = ANY (ARRAY['pending'::character varying, 'running'::character varying, 'completed'::character varying, 'failed'::character varying, 'cancelled'::character varying]::text[])", name: "sync_logs_status_check"
  end

  create_table "users", force: :cascade do |t|
    t.bigint "organization_id", null: false
    t.string "email", null: false
    t.string "encrypted_password", null: false
    t.string "role", default: "member"
    t.string "first_name"
    t.string "last_name"
    t.datetime "last_sign_in_at"
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.index ["organization_id", "email"], name: "index_users_on_organization_id_and_email", unique: true
    t.index ["organization_id"], name: "index_users_on_organization_id"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
    t.index ["role"], name: "index_users_on_role"
  end

  add_foreign_key "data_sources", "organizations"
  add_foreign_key "imported_records", "data_sources"
  add_foreign_key "imported_records", "organizations"
  add_foreign_key "imported_records", "sync_logs"
  add_foreign_key "integrations", "data_sources"
  add_foreign_key "integrations", "organizations"
  add_foreign_key "solid_queue_blocked_executions", "solid_queue_jobs", column: "job_id", on_delete: :cascade
  add_foreign_key "solid_queue_claimed_executions", "solid_queue_jobs", column: "job_id", on_delete: :cascade
  add_foreign_key "solid_queue_failed_executions", "solid_queue_jobs", column: "job_id", on_delete: :cascade
  add_foreign_key "solid_queue_ready_executions", "solid_queue_jobs", column: "job_id", on_delete: :cascade
  add_foreign_key "solid_queue_recurring_executions", "solid_queue_jobs", column: "job_id", on_delete: :cascade
  add_foreign_key "solid_queue_scheduled_executions", "solid_queue_jobs", column: "job_id", on_delete: :cascade
  add_foreign_key "sync_logs", "data_sources"
  add_foreign_key "sync_logs", "organizations"
  add_foreign_key "users", "organizations"
end
