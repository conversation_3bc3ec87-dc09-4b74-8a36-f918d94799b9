# app/jobs/application_job.rb
class ApplicationJob < ActiveJob::Base
  queue_as :default
  
  # Set tenant context for all jobs
  around_perform do |job, block|
    tenant = job.arguments.find { |arg| arg.is_a?(Hash) && arg[:tenant_id] }
    if tenant
      ActsAsTenant.with_tenant(Organization.find(tenant[:tenant_id])) do
        block.call
      end
    else
      block.call
    end
  end

  private

  def broadcast_update(target, partial, locals = {})
    Turbo::StreamsChannel.broadcast_replace_to(
      target,
      target: target,
      partial: partial,
      locals: locals
    )
  end
end

# app/jobs/pipeline_execution_job.rb
class PipelineExecutionJob < ApplicationJob
  queue_as :default
  
  def perform(pipeline_id, tenant_id: nil)
    pipeline = Pipeline.find(pipeline_id)
    
    # Broadcast pipeline started
    broadcast_pipeline_status(pipeline, "Pipeline execution started")
    
    result = EtlProcessorService.call(pipeline)
    
    if result.success?
      broadcast_pipeline_status(pipeline, "Pipeline completed successfully")
      # Trigger downstream jobs
      DataQualityAnalysisJob.perform_later(pipeline.id, tenant_id: pipeline.organization_id)
    else
      broadcast_pipeline_status(pipeline, "Pipeline failed: #{result.error}")
      PipelineFailureNotificationJob.perform_later(pipeline.id, result.error)
    end
    
  rescue => e
    Rails.logger.error "Pipeline execution failed: #{e.message}"
    broadcast_pipeline_status(pipeline, "Pipeline execution error: #{e.message}")
    raise
  end

  private

  def broadcast_pipeline_status(pipeline, message)
    broadcast_update(
      "pipeline_#{pipeline.id}",
      "pipelines/status",
      { pipeline: pipeline, message: message }
    )
  end
end

# app/jobs/data_source_test_job.rb
class DataSourceTestJob < ApplicationJob
  queue_as :critical
  
  def perform(data_source_id)
    data_source = DataSource.find(data_source_id)
    
    result = DataSourceTestService.call(data_source)
    
    if result.success?
      data_source.update!(status: 'verified', last_tested_at: Time.current)
      message = "Connection test successful"
    else
      data_source.update!(status: 'failed', last_tested_at: Time.current)
      message = "Connection test failed: #{result.error}"
    end
    
    broadcast_update(
      "data_source_#{data_source.id}",
      "data_sources/test_result",
      { data_source: data_source, message: message }
    )
  end
end

# app/jobs/data_quality_analysis_job.rb
class DataQualityAnalysisJob < ApplicationJob
  queue_as :default
  
  def perform(pipeline_id, tenant_id: nil)
    pipeline = Pipeline.find(pipeline_id)
    
    result = DataQualityScorerService.call(pipeline)
    
    if result.success?
      report = result.result
      
      # Broadcast quality score update
      broadcast_update(
        "pipeline_#{pipeline.id}_quality",
        "pipelines/quality_score",
        { pipeline: pipeline, report: report }
      )
      
      # Update dashboard metrics
      broadcast_update(
        "dashboard_metrics",
        "dashboards/metrics",
        { organization: pipeline.organization }
      )
    end
  end
end

# app/jobs/schema_analysis_job.rb
class SchemaAnalysisJob < ApplicationJob
  queue_as :default
  
  def perform(integration_id)
    integration = Integration.find(integration_id)
    
    result = SmartMappingService.call(integration)
    
    if result.success?
      mappings = result.result
      
      broadcast_update(
        "integration_#{integration.id}_mappings",
        "integrations/suggested_mappings",
        { integration: integration, mappings: mappings }
      )
    end
  end
end

# config/routes.rb
Rails.application.routes.draw do
  root "dashboards#index"
  
  resources :organizations, only: [:show, :edit, :update] do
    resources :users
    resources :data_sources do
      member do
        post :test_connection
      end
      resources :integrations do
        member do
          post :analyze_schema
          patch :confirm_mappings
        end
      end
    end
    
    resources :pipelines do
      member do
        post :execute
        get :status
      end
      resources :data_quality_reports, only: [:index, :show]
    end
    
    resources :dashboards
    resources :custom_connectors
  end
  
  # Real-time updates
  mount ActionCable.server => '/cable'
end

# app/controllers/application_controller.rb
class ApplicationController < ActionController::Base
  include Pundit::Authorization
  
  before_action :authenticate_user!
  before_action :set_current_tenant
  
  private
  
  def authenticate_user!
    redirect_to login_path unless current_user
  end
  
  def current_user
    @current_user ||= User.find(session[:user_id]) if session[:user_id]
  end
  helper_method :current_user
  
  def set_current_tenant
    if current_user
      ActsAsTenant.current_tenant = current_user.organization
    end
  end
end

# app/controllers/pipelines_controller.rb
class PipelinesController < ApplicationController
  before_action :set_pipeline, only: [:show, :execute, :status]
  
  def index
    @pipelines = current_organization.pipelines.includes(:data_quality_reports)
  end
  
  def show
    @recent_reports = @pipeline.data_quality_reports.recent.limit(10)
  end
  
  def execute
    authorize @pipeline
    
    PipelineExecutionJob.perform_later(
      @pipeline.id, 
      tenant_id: current_organization.id
    )
    
    respond_to do |format|
      format.turbo_stream do
        render turbo_stream: turbo_stream.replace(
          "pipeline_#{@pipeline.id}",
          partial: "pipelines/executing",
          locals: { pipeline: @pipeline }
        )
      end
      format.html { redirect_to @pipeline, notice: "Pipeline execution started" }
    end
  end
  
  def status
    render json: {
      status: @pipeline.status,
      last_run: @pipeline.completed_at&.iso8601,
      quality_score: @pipeline.latest_quality_score
    }
  end
  
  private
  
  def set_pipeline
    @pipeline = current_organization.pipelines.find(params[:id])
  end
  
  def current_organization
    current_user.organization
  end
end

# app/channels/application_cable/connection.rb
module ApplicationCable
  class Connection < ActionCable::Connection::Base
    identified_by :current_user, :current_organization
    
    def connect
      self.current_user = find_verified_user
      self.current_organization = current_user.organization
    end
    
    private
    
    def find_verified_user
      if verified_user = User.find_by(id: cookies.signed[:user_id])
        verified_user
      else
        reject_unauthorized_connection
      end
    end
  end
end

# app/channels/pipeline_channel.rb
class PipelineChannel < ApplicationCable::Channel
  def subscribed
    stream_from "pipeline_#{params[:pipeline_id]}" if authorized?
  end
  
  def unsubscribed
    # Any cleanup needed when channel is unsubscribed
  end
  
  private
  
  def authorized?
    current_organization.pipelines.exists?(params[:pipeline_id])
  end
end

# app/views/pipelines/show.html.erb
<div data-controller="pipeline" 
     data-pipeline-id-value="<%= @pipeline.id %>"
     data-action="cable-ready:after-morph->pipeline#refresh">
  
  <div id="pipeline_<%= @pipeline.id %>">
    <%= render "pipeline_status", pipeline: @pipeline %>
  </div>
  
  <div class="mt-6">
    <%= button_to "Execute Pipeline", 
        execute_pipeline_path(@pipeline), 
        method: :post,
        class: "btn btn-primary",
        data: { 
          turbo_method: :post,
          turbo_confirm: "Are you sure you want to execute this pipeline?"
        } %>
  </div>
  
  <div id="pipeline_<%= @pipeline.id %>_quality" class="mt-8">
    <%= render "quality_metrics", pipeline: @pipeline %>
  </div>
</div>

<!-- app/views/pipelines/_pipeline_status.html.erb -->
<div class="bg-white shadow rounded-lg p-6">
  <div class="flex items-center justify-between">
    <h3 class="text-lg font-medium text-gray-900">
      <%= @pipeline.name %>
    </h3>
    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                 <%= status_color_class(@pipeline.status) %>">
      <%= @pipeline.status.humanize %>
    </span>
  </div>
  
  <% if @pipeline.running? %>
    <div class="mt-4">
      <div class="flex items-center">
        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        <span class="ml-2 text-sm text-gray-600">Pipeline is running...</span>
      </div>
    </div>
  <% end %>
</div>

<!-- app/javascript/controllers/pipeline_controller.js -->
import { Controller } from "@hotwired/stimulus"
import { createConsumer } from "@rails/actioncable"

export default class extends Controller {
  static values = { id: String }
  
  connect() {
    this.consumer = createConsumer()
    this.subscription = this.consumer.subscriptions.create(
      { channel: "PipelineChannel", pipeline_id: this.idValue },
      {
        connected: () => {
          console.log("Connected to pipeline channel")
        },
        
        disconnected: () => {
          console.log("Disconnected from pipeline channel")
        },
        
        received: (data) => {
          console.log("Received pipeline update:", data)
        }
      }
    )
  }
  
  disconnect() {
    if (this.subscription) {
      this.subscription.unsubscribe()
    }
  }
  
  refresh() {
    // Trigger any additional refresh logic
    console.log("Pipeline view refreshed")
  }
}