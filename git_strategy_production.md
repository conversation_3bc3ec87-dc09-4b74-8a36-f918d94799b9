# Production-Ready Git Strategy & Workflow
## SME Data Refinery Platform

**Document Version:** 1.0  
**Last Updated:** January 2025  
**Team:** Engineering Team  
**Review Cycle:** Quarterly  

---

## 1. Branching Strategy Overview

### 1.1 Modified GitHub Flow for SaaS
We use a **modified GitHub Flow** optimized for continuous deployment with safety gates:

```
main (production) ← Protected, auto-deploy to production
├── develop (staging) ← Protected, auto-deploy to staging  
├── feature/USER-123-data-source-connection
├── feature/USER-124-dashboard-builder
├── hotfix/critical-security-patch
└── release/v1.2.0 (for major releases only)
```

### 1.2 Branch Types & Purposes

**Main Branch (`main`)**
- **Purpose:** Production-ready code only
- **Protection:** Strict protection rules, requires PR + reviews
- **Auto-Deploy:** Direct deployment to production environment
- **Naming:** Always `main`

**Develop Branch (`develop`)**  
- **Purpose:** Integration branch for feature testing
- **Protection:** Protected, requires PR for merges
- **Auto-Deploy:** Continuous deployment to staging environment
- **Naming:** Always `develop`

**Feature Branches (`feature/*`)**
- **Purpose:** Individual user story development
- **Lifetime:** Created from `develop`, merged back to `develop`
- **Naming Convention:** `feature/USER-{story-number}-{brief-description}`
- **Examples:** 
  - `feature/USER-123-quickbooks-integration`
  - `feature/USER-124-dashboard-real-time-updates`

**Hotfix Branches (`hotfix/*`)**
- **Purpose:** Critical production fixes
- **Lifetime:** Created from `main`, merged to both `main` and `develop`
- **Naming Convention:** `hotfix/{brief-description}`
- **Examples:**
  - `hotfix/security-credential-encryption`
  - `hotfix/pipeline-execution-timeout`

**Release Branches (`release/*`)** (Optional)
- **Purpose:** Major release preparation and testing
- **Lifetime:** Created from `develop`, merged to `main` and `develop`
- **Naming Convention:** `release/v{major}.{minor}.{patch}`
- **Examples:** `release/v1.2.0`, `release/v2.0.0`

---

## 2. Branch Protection Rules

### 2.1 Main Branch Protection
```yaml
Branch: main
Protection Rules:
  - Require pull request reviews: true
  - Required reviewers: 2 (senior developers)
  - Dismiss stale reviews: true
  - Require review from code owners: true
  - Require status checks: true
  - Required status checks:
    - continuous-integration/github-actions
    - security-scan/brakeman
    - test-coverage/rspec (>90%)
    - performance-test/lighthouse
  - Require branches to be up to date: true
  - Require signed commits: true
  - Include administrators: true
  - Allow force pushes: false
  - Allow deletions: false
```

### 2.2 Develop Branch Protection
```yaml
Branch: develop
Protection Rules:
  - Require pull request reviews: true
  - Required reviewers: 1
  - Dismiss stale reviews: false
  - Require status checks: true
  - Required status checks:
    - continuous-integration/github-actions
    - security-scan/brakeman
    - test-coverage/rspec (>85%)
  - Require branches to be up to date: true
  - Allow force pushes: false
  - Allow deletions: false
```

### 2.3 CODEOWNERS Configuration
```
# Global owners
* @engineering-lead @senior-backend-dev

# Backend code
app/models/ @backend-team @senior-backend-dev
app/services/ @backend-team @senior-backend-dev
app/jobs/ @backend-team @senior-backend-dev
lib/ @backend-team @senior-backend-dev

# Frontend code  
app/views/ @frontend-team @senior-frontend-dev
app/javascript/ @frontend-team @senior-frontend-dev
app/assets/ @frontend-team @senior-frontend-dev

# Infrastructure
config/ @devops-engineer @engineering-lead
docker/ @devops-engineer @engineering-lead
.github/ @devops-engineer @engineering-lead

# Security sensitive
config/credentials/ @engineering-lead @security-team
config/database.yml @engineering-lead @devops-engineer
Gemfile @engineering-lead @senior-backend-dev

# Documentation
*.md @product-manager @engineering-lead
docs/ @product-manager @engineering-lead
```

---

## 3. Commit Message Standards

### 3.1 Conventional Commits Format
We follow [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 3.2 Commit Types
```
feat:     New feature for the user
fix:      Bug fix for the user
docs:     Documentation changes
style:    Code style changes (formatting, missing semi colons, etc)
refactor: Code change that neither fixes a bug nor adds a feature
perf:     Performance improvements
test:     Adding missing tests or correcting existing tests
build:    Changes to build system or external dependencies
ci:       Changes to CI configuration files and scripts
chore:    Other changes that don't modify src or test files
security: Security-related changes
```

### 3.3 Commit Message Examples
```bash
# Feature commits
feat(auth): add OAuth integration for QuickBooks
feat(dashboard): implement real-time data updates with Turbo Streams
feat(pipeline): add data quality scoring algorithm

# Bug fixes
fix(security): encrypt data source credentials at rest
fix(performance): optimize pipeline execution query performance
fix(ui): resolve dashboard loading state on mobile devices

# Documentation
docs(api): update REST API documentation for v1.2
docs(readme): add development setup instructions

# Infrastructure
ci(github): add automated security scanning workflow
build(docker): optimize production image size
```

### 3.4 Commit Message Rules
- **Mandatory:** Type and description
- **Length:** Description max 50 characters, body max 72 characters per line
- **Language:** Present tense ("add" not "added")
- **Case:** Lowercase type and description
- **Punctuation:** No period at end of description
- **Reference:** Include issue number in footer if applicable

```bash
# Good examples
feat(auth): add OAuth integration for QuickBooks

Implements OAuth 2.0 flow for QuickBooks authentication
with proper token refresh and error handling.

Closes #123
```

---

## 4. Pull Request Workflow

### 4.1 PR Creation Process

**Step 1: Branch Creation**
```bash
# Update develop branch
git checkout develop
git pull origin develop

# Create feature branch
git checkout -b feature/USER-123-quickbooks-integration

# Make changes and commit
git add .
git commit -m "feat(auth): add QuickBooks OAuth integration"
git push origin feature/USER-123-quickbooks-integration
```

**Step 2: PR Template**
All PRs must use this template:

```markdown
## Description
Brief description of changes and motivation.

## Type of Change
- [ ] Bug fix (non-breaking change that fixes an issue)
- [ ] New feature (non-breaking change that adds functionality)
- [ ] Breaking change (fix or feature that causes existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Security enhancement

## User Story
- **Story ID:** USER-123
- **Story Title:** QuickBooks Integration
- **Story Points:** 8

## Testing
- [ ] Unit tests added/updated (coverage >90%)
- [ ] Integration tests added/updated
- [ ] Manual testing completed
- [ ] Performance testing completed (if applicable)
- [ ] Security testing completed (if applicable)

## Security Review
- [ ] No sensitive data exposed in code
- [ ] Proper input validation implemented
- [ ] Authentication/authorization properly handled
- [ ] Dependencies scanned for vulnerabilities

## Database Changes
- [ ] No database changes
- [ ] Migration files included
- [ ] Migration tested on staging environment
- [ ] Rollback plan documented

## Deployment Notes
- [ ] No special deployment steps required
- [ ] Environment variables updated
- [ ] Third-party service configuration required
- [ ] Documentation updated

## Screenshots/Videos (if applicable)
[Add screenshots or videos demonstrating the changes]

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Code commented where necessary
- [ ] Documentation updated
- [ ] Tests pass locally
- [ ] Lint checks pass
- [ ] Security scan passes
```

### 4.2 PR Review Process

**Review Requirements:**
- **Feature PRs to develop:** 1 approved review (any team member)
- **PRs to main:** 2 approved reviews (must include senior developer)
- **Hotfix PRs:** 1 approved review (senior developer or engineering lead)

**Review Checklist:**
```markdown
## Code Review Checklist

### Functionality
- [ ] Code implements requirements correctly
- [ ] Edge cases are handled appropriately
- [ ] Error handling is comprehensive
- [ ] Performance impact is acceptable

### Code Quality
- [ ] Code is readable and well-structured
- [ ] Follows Rails conventions and project patterns
- [ ] No code duplication or unnecessary complexity
- [ ] Proper separation of concerns

### Security
- [ ] No sensitive data in code or logs
- [ ] Input validation and sanitization
- [ ] Authentication and authorization checks
- [ ] SQL injection prevention

### Testing
- [ ] Test coverage >90% for new code
- [ ] Tests are comprehensive and meaningful
- [ ] Integration tests cover happy and error paths
- [ ] Performance tests for data processing code

### Multi-tenancy
- [ ] Organization scoping implemented correctly
- [ ] No cross-tenant data leakage possible
- [ ] Tenant isolation maintained in all queries

### Documentation
- [ ] Code is self-documenting or well-commented
- [ ] API documentation updated (if applicable)
- [ ] README updated (if applicable)
```

### 4.3 Automated Checks

All PRs must pass these automated checks:

```yaml
# .github/workflows/pr-checks.yml
name: PR Checks
on: [pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
      - name: Run RSpec tests
        run: bundle exec rspec --format documentation
      - name: Check test coverage
        run: bundle exec rspec --format RspecJunitFormatter --out rspec.xml
      
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
      - name: Run RuboCop
        run: bundle exec rubocop --format github
      
  security:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
      - name: Run Brakeman security scan
        run: bundle exec brakeman --format github
      - name: Run bundle audit
        run: bundle exec bundle-audit check --update
      
  performance:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
      - name: Run performance tests
        run: bundle exec rspec spec/performance --tag performance
```

---

## 5. Release Management

### 5.1 Versioning Strategy

We use **Semantic Versioning (SemVer)**: `MAJOR.MINOR.PATCH`

```
MAJOR: Breaking changes or significant rewrites
MINOR: New features, backward compatible
PATCH: Bug fixes, backward compatible

Examples:
v1.0.0: Initial production release
v1.1.0: Added custom connector builder
v1.1.1: Fixed QuickBooks authentication bug
v2.0.0: New multi-tenant architecture (breaking)
```

### 5.2 Release Types

**Regular Releases (Weekly)**
- Automated from `develop` → `main`
- All feature PRs for the sprint
- Thorough testing on staging environment

**Hotfix Releases (As needed)**
- Critical security or data integrity issues
- Direct from `main` branch
- Expedited review and deployment process

**Major Releases (Quarterly)**
- Significant feature sets or architecture changes
- Extended testing period
- Customer communication and migration guides

### 5.3 Release Process

**Weekly Release Process:**
```bash
# 1. Code freeze (Monday of release week)
# No new features merged to develop

# 2. Final staging testing (Monday-Tuesday)
# QA team validates all features on staging

# 3. Release preparation (Wednesday)
git checkout develop
git pull origin develop

# Create release branch (if needed for major release)
git checkout -b release/v1.2.0

# Update version and changelog
# Edit config/application.rb version
# Update CHANGELOG.md

git add .
git commit -m "chore(release): prepare v1.2.0"
git push origin release/v1.2.0

# 4. Create release PR
# PR from release/v1.2.0 to main
# Requires 2 approvals including engineering lead

# 5. Deploy to production (Thursday)
# Automatic deployment after PR merge
# Monitor metrics and error rates

# 6. Post-release (Friday)
# Merge release branch back to develop
# Create GitHub release with changelog
# Communicate release to stakeholders
```

### 5.4 Changelog Management

Maintain `CHANGELOG.md` following [Keep a Changelog](https://keepachangelog.com/):

```markdown
# Changelog

## [Unreleased]
### Added
- Real-time dashboard updates with Turbo Streams
- Custom connector builder interface

### Changed
- Improved pipeline execution performance by 40%

### Fixed
- QuickBooks OAuth token refresh issue
- Dashboard loading on mobile devices

### Security
- Enhanced credential encryption at rest

## [1.1.0] - 2025-01-15
### Added
- QuickBooks integration with OAuth 2.0
- Shopify connector with real-time syncing
- Basic data quality scoring algorithm

### Changed
- Updated dashboard UI for better mobile experience

### Fixed
- Pipeline execution timeout handling
- Memory leak in data processing service

## [1.0.0] - 2025-01-01
### Added
- Initial production release
- Multi-tenant organization management
- Basic ETL pipeline functionality
- PostgreSQL and Google Sheets connectors
```

---

## 6. Environment Management

### 6.1 Environment Strategy

```
development → Feature development and testing
├── Local machines with Docker Compose
├── Shared test database
└── Mock external services

staging → Integration testing and QA
├── Production-like infrastructure
├── Anonymized production data
├── Real external service integrations (sandbox)
└── Continuous deployment from develop branch

production → Live customer environment
├── High availability infrastructure
├── Real customer data
├── Production external services
├── Deployment from main branch only
└── Blue-green deployment strategy
```

### 6.2 Branch-Environment Mapping

```yaml
# Automatic deployments
develop branch → staging environment
main branch → production environment

# Manual deployments (via GitHub Actions)
feature branches → review apps (optional)
hotfix branches → staging environment (for testing)

# Environment configuration
development:
  database: local PostgreSQL
  cache: local Redis
  background_jobs: inline processing
  external_apis: mock services

staging:
  database: managed PostgreSQL
  cache: managed Redis
  background_jobs: Solid Queue
  external_apis: sandbox/test environments

production:
  database: managed PostgreSQL with replicas
  cache: managed Redis cluster
  background_jobs: Solid Queue with multiple workers
  external_apis: production services
```

### 6.3 Configuration Management

**Environment Variables Strategy:**
```bash
# .env.example (committed)
DATABASE_URL=postgresql://localhost/data_refinery_development
REDIS_URL=redis://localhost:6379
RAILS_MASTER_KEY=
STRIPE_PUBLISHABLE_KEY=
STRIPE_SECRET_KEY=

# .env.development (local only)
# .env.staging (managed by deployment)
# .env.production (managed by deployment)

# GitHub Secrets (for CI/CD)
RAILS_MASTER_KEY_STAGING
RAILS_MASTER_KEY_PRODUCTION
DATABASE_URL_STAGING
DATABASE_URL_PRODUCTION
```

**Credentials Management:**
```bash
# Development
rails credentials:edit --environment development

# Staging  
rails credentials:edit --environment staging

# Production
rails credentials:edit --environment production

# Example structure
secret_key_base: ...
stripe:
  publishable_key: pk_test_...
  secret_key: sk_test_...
quickbooks:
  client_id: ...
  client_secret: ...
```

---

## 7. Deployment Strategy

### 7.1 Deployment Pipeline

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          bundler-cache: true
          
      - name: Run tests
        run: bundle exec rspec
        
      - name: Run security scan
        run: bundle exec brakeman --quiet --format plain
        
      - name: Build Docker image
        run: |
          docker build -t data-refinery:${{ github.sha }} .
          
      - name: Deploy to production
        env:
          DEPLOY_KEY: ${{ secrets.DEPLOY_KEY }}
        run: |
          # Blue-green deployment script
          ./scripts/deploy-production.sh ${{ github.sha }}
          
      - name: Run smoke tests
        run: |
          ./scripts/smoke-tests.sh
          
      - name: Notify team
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
```

### 7.2 Blue-Green Deployment

```bash
#!/bin/bash
# scripts/deploy-production.sh

set -e

NEW_VERSION=$1
CURRENT_VERSION=$(curl -s https://api.datarefinery.com/health | jq -r '.version')

echo "Deploying version $NEW_VERSION (current: $CURRENT_VERSION)"

# 1. Deploy to green environment
kubectl set image deployment/data-refinery-green \
  app=data-refinery:$NEW_VERSION

# 2. Wait for green deployment to be ready
kubectl rollout status deployment/data-refinery-green

# 3. Run health checks on green
./scripts/health-check.sh green

# 4. Switch traffic to green (50/50 split first)
kubectl patch service data-refinery-service \
  -p '{"spec":{"selector":{"version":"green"}}}'

# 5. Monitor metrics for 5 minutes
sleep 300

# 6. Check for errors
ERROR_RATE=$(./scripts/check-error-rate.sh)
if [ "$ERROR_RATE" -gt "1" ]; then
  echo "Error rate too high, rolling back"
  kubectl patch service data-refinery-service \
    -p '{"spec":{"selector":{"version":"blue"}}}'
  exit 1
fi

# 7. Complete switch to green
echo "Deployment successful, green is now live"

# 8. Update blue environment for next deployment
kubectl set image deployment/data-refinery-blue \
  app=data-refinery:$NEW_VERSION
```

### 7.3 Rollback Strategy

```bash
#!/bin/bash
# scripts/rollback.sh

REASON="$1"
ROLLBACK_VERSION="$2"

echo "EMERGENCY ROLLBACK: $REASON"

# 1. Immediate traffic switch
kubectl patch service data-refinery-service \
  -p '{"spec":{"selector":{"version":"blue"}}}'

# 2. Verify rollback successful
./scripts/health-check.sh blue

# 3. Notify team
curl -X POST -H 'Content-type: application/json' \
  --data "{\"text\":\"🚨 PRODUCTION ROLLBACK: $REASON\"}" \
  $SLACK_WEBHOOK_URL

# 4. Create incident ticket
./scripts/create-incident.sh "$REASON" "$ROLLBACK_VERSION"

echo "Rollback completed successfully"
```

---

## 8. Security & Compliance

### 8.1 Secure Development Practices

**Sensitive Data Handling:**
```bash
# Never commit these files
.env*
config/master.key
config/credentials/*.key
*.pem
*.p12

# Git hooks to prevent sensitive data
#!/bin/bash
# .git/hooks/pre-commit
git diff --cached --name-only | \
  xargs grep -l "password\|secret\|key\|token" && \
  echo "Potential sensitive data detected" && exit 1
```

**Dependency Security:**
```bash
# Automated security scanning
bundle audit check --update
brakeman --format json --output brakeman.json

# GitHub dependency scanning enabled
# Snyk integration for continuous monitoring
```

### 8.2 SOC2 Compliance Requirements

**Audit Trail:**
```ruby
# All git commits are signed
git config --global commit.gpgsign true
git config --global user.signingkey YOUR_GPG_KEY

# All deployments logged
class DeploymentLogger
  def self.log_deployment(version, user, environment)
    AuditLog.create!(
      event_type: 'deployment',
      user: user,
      environment: environment,
      metadata: { version: version, commit_sha: ENV['GITHUB_SHA'] }
    )
  end
end
```

**Access Control:**
```yaml
# Required 2FA for all team members
# Branch protection enforced
# Signed commits required
# Regular access reviews (quarterly)

# GitHub teams for access control
@data-refinery/admin-team:
  - admin access to repository
  - can merge to main branch
  - can modify security settings

@data-refinery/senior-developers:
  - write access to repository  
  - can approve PRs to main
  - can create releases

@data-refinery/developers:
  - write access to repository
  - can approve PRs to develop
  - cannot directly push to protected branches
```

---

## 9. Monitoring & Alerting

### 9.1 Git Workflow Metrics

**Deployment Metrics:**
```yaml
# Track in application monitoring
deployment_frequency: weekly
lead_time_for_changes: 2-3 days (feature to production)
mean_time_to_recovery: <4 hours
change_failure_rate: <5%

# GitHub Insights tracking
pr_review_time: target <24 hours
code_review_participation: >80% of team
hotfix_frequency: target <1 per month
```

### 9.2 Automated Alerts

```yaml
# Slack alerts for:
- Failed deployments
- Security scan failures
- Long-running PR reviews (>48 hours)
- Main branch build failures
- Dependency vulnerability alerts

# Email alerts for:
- Production hotfix deployments
- Security incidents
- Compliance audit requests
```

---

## 10. Team Guidelines

### 10.1 Developer Onboarding

**Day 1: Setup**
```bash
# 1. Clone repository
<NAME_EMAIL>:company/data-refinery.git
cd data-refinery

# 2. Setup development environment
cp .env.example .env.development
bundle install
rails db:setup

# 3. Configure Git
git config user.email "<EMAIL>"
git config user.name "Developer Name"
git config commit.gpgsign true

# 4. Test workflow
git checkout -b feature/setup-test
echo "# Test" > TEST.md
git add TEST.md
git commit -m "test: verify development setup"
git push origin feature/setup-test
# Create PR for review
```

**Day 2-3: First Contribution**
- Pick up beginner-friendly user story
- Follow complete PR workflow
- Get code review and feedback
- Deploy to staging environment

### 10.2 Code Review Culture

**Review Guidelines:**
- **Be constructive:** Suggest improvements, don't just point out problems
- **Be specific:** Reference line numbers and provide examples
- **Be timely:** Review within 24 hours of request
- **Be thorough:** Check functionality, security, performance, tests

**Review Checklist:**
```markdown
- [ ] Does this solve the user story requirements?
- [ ] Is the code readable and maintainable?
- [ ] Are there adequate tests with good coverage?
- [ ] Are there any security concerns?
- [ ] Is multi-tenant isolation maintained?
- [ ] Are database queries optimized?
- [ ] Is error handling comprehensive?
- [ ] Is the UI accessible and responsive?
```

### 10.3 Incident Response

**Hotfix Process:**
```bash
# 1. Create hotfix branch from main
git checkout main
git pull origin main
git checkout -b hotfix/critical-security-patch

# 2. Implement fix with tests
# 3. Push and create PR immediately
git push origin hotfix/critical-security-patch

# 4. Get expedited review (1 senior developer)
# 5. Deploy to staging for validation
# 6. Merge to main and deploy to production
# 7. Merge hotfix to develop branch
```

**Communication Protocol:**
```
Severity 1 (Critical): Immediate Slack alert + phone calls
Severity 2 (High): Slack alert within 15 minutes
Severity 3 (Medium): Slack alert within 1 hour
Severity 4 (Low): GitHub issue within 24 hours
```

---

## 11. Tools & Integrations

### 11.1 Required Tools

**Development Tools:**
- **Git Client:** Command line or GUI (SourceTree, GitHub Desktop)
- **Code Editor:** VS Code with Ruby/Rails extensions
- **Database Client:** TablePlus, DBeaver, or pgAdmin
- **API Testing:** Postman or Insomnia

**Team Tools:**
- **Repository:** GitHub Enterprise
- **CI/CD:** GitHub Actions
- **Project Management:** Linear (integrated with Git)
- **Communication:** Slack (with GitHub integration)
- **Monitoring:** DataDog, New Relic, or similar

### 11.2 Git Integrations

**Linear Integration:**
```bash
# Automatic linking of commits to user stories
git commit -m "feat(auth): add OAuth integration

Implements QuickBooks OAuth flow with proper error handling
and token refresh logic.

Closes USER-123"

# Linear automatically updates story status
```

**Slack Integration:**
```yaml
# .github/workflows/notifications.yml
- name: Notify deployment
  uses: 8398a7/action-slack@v3
  with:
    status: ${{ job.status }}
    text: |
      🚀 Deployment to production completed
      Version: ${{ github.sha }}
      Author: ${{ github.actor }}
      Changes: ${{ github.event.head_commit.message }}
```

---

## 12. Troubleshooting

### 12.1 Common Issues

**Merge Conflicts:**
```bash
# 1. Update your branch with latest develop
git checkout feature/your-branch
git fetch origin
git rebase origin/develop

# 2. Resolve conflicts in your editor
# 3. Mark as resolved and continue
git add .
git rebase --continue

# 4. Force push (only to feature branches!)
git push --force-with-lease origin feature/your-branch
```

**Failed CI Checks:**
```bash
# Run locally before pushing
bundle exec rspec
bundle exec rubocop --auto-correct
bundle exec brakeman
```

**Access Issues:**
```bash
# Setup SSH key authentication
ssh-keygen -t ed25519 -C "<EMAIL>"
cat ~/.ssh/id_ed25519.pub
# Add to GitHub settings

# Test connection
ssh -T **************
```

### 12.2 Emergency Procedures

**Repository Corruption:**
```bash
# 1. Verify issue
git fsck --full

# 2. Clone fresh copy
git clone --mirror **************:company/data-refinery.git

# 3. Contact GitHub support if needed
```

**Accidental Secret Commit:**
```bash
# 1. Remove from history immediately
git filter-branch --force --index-filter \
  'git rm --cached --ignore-unmatch path/to/secret/file' \
  --prune-empty --tag-name-filter cat -- --all

# 2. Force push to all branches
git push origin --force --all

# 3. Rotate the exposed secret immediately
# 4. Notify security team
```

---

## 13. Success Metrics

### 13.1 Development Velocity
- **Deployment Frequency:** Target weekly releases
- **Lead Time:** Feature development to production <1 week
- **Pull Request Size:** Average <400 lines changed
- **Review Time:** <24 hours for feature PRs

### 13.2 Quality Metrics
- **Change Failure Rate:** <5% of deployments
- **Mean Time to Recovery:** <4 hours
- **Code Coverage:** >90% for new code
- **Security Vulnerabilities:** 0 high/critical in production

### 13.3 Team Health
- **Code Review Participation:** >80% of team
- **Knowledge Sharing:** No single points of failure
- **Documentation Quality:** All processes documented
- **Incident Learning:** Post-mortems for all production issues

This comprehensive Git strategy ensures secure, scalable development while maintaining high code quality and team productivity. The workflow integrates seamlessly with your Rails 8 development process and agile methodology, providing clear guidelines for everything from daily development to emergency incident response.