# Create the initial migrations
# Run: rails generate migration CreateOrganizations
# db/migrate/001_create_organizations.rb
class CreateOrganizations < ActiveRecord::Migration[8.0]
  def change
    create_table :organizations do |t|
      t.string :name, null: false
      t.string :plan, default: 'starter'
      t.text :settings
      t.string :subdomain
      t.integer :monthly_row_limit
      t.integer :current_month_rows, default: 0
      t.datetime :plan_expires_at
      
      t.timestamps
    end
    
    add_index :organizations, :subdomain, unique: true
    add_index :organizations, :plan
  end
end

# db/migrate/002_create_users.rb
class CreateUsers < ActiveRecord::Migration[8.0]
  def change
    create_table :users do |t|
      t.references :organization, null: false, foreign_key: true
      t.string :email, null: false
      t.string :password_digest, null: false
      t.string :role, default: 'member'
      t.string :first_name
      t.string :last_name
      t.datetime :last_login_at
      t.boolean :active, default: true
      
      t.timestamps
    end
    
    add_index :users, [:organization_id, :email], unique: true
    add_index :users, :role
  end
end

# db/migrate/003_create_data_sources.rb
class CreateDataSources < ActiveRecord::Migration[8.0]
  def change
    create_table :data_sources do |t|
      t.references :organization, null: false, foreign_key: true
      t.string :name, null: false
      t.string :kind, null: false # api, database, file, stream, webhook
      t.string :connection_url
      t.text :credentials # encrypted
      t.string :status, default: 'pending'
      t.json :configuration
      t.datetime :last_tested_at
      t.text :test_error
      
      t.timestamps
    end
    
    add_index :data_sources, [:organization_id, :name], unique: true
    add_index :data_sources, :kind
    add_index :data_sources, :status
  end
end

# db/migrate/004_create_integrations.rb
class CreateIntegrations < ActiveRecord::Migration[8.0]
  def change
    create_table :integrations do |t|
      t.references :organization, null: false, foreign_key: true
      t.references :data_source, null: false, foreign_key: true
      t.string :status, default: 'pending'
      t.datetime :last_synced_at
      t.text :suggested_mappings # encrypted
      t.json :sync_configuration
      t.datetime :last_mapped_at
      t.integer :total_records_synced, default: 0
      t.text :last_error
      
      t.timestamps
    end
    
    add_index :integrations, [:organization_id, :status]
    add_index :integrations, :data_source_id
  end
end

# db/migrate/005_create_pipelines.rb
class CreatePipelines < ActiveRecord::Migration[8.0]
  def change
    create_table :pipelines do |t|
      t.references :organization, null: false, foreign_key: true
      t.string :name, null: false
      t.string :mode, default: 'etl' # etl or elt
      t.string :status, default: 'pending'
      t.text :configuration # encrypted
      t.datetime :started_at
      t.datetime :completed_at
      t.text :error_message
      t.integer :records_processed, default: 0
      t.string :cron_schedule
      t.boolean :active, default: true
      
      t.timestamps
    end
    
    add_index :pipelines, [:organization_id, :name], unique: true
    add_index :pipelines, :status
    add_index :pipelines, :mode
    add_index :pipelines, :active
  end
end

# db/migrate/006_create_source_pipelines.rb
class CreateSourcePipelines < ActiveRecord::Migration[8.0]
  def change
    create_table :source_pipelines do |t|
      t.references :organization, null: false, foreign_key: true
      t.references :integration, null: false, foreign_key: true
      t.references :pipeline, null: false, foreign_key: true
      t.integer :execution_order, default: 1
      
      t.timestamps
    end
    
    add_index :source_pipelines, [:pipeline_id, :execution_order]
    add_index :source_pipelines, :integration_id
  end
end

# db/migrate/007_create_data_mappings.rb
class CreateDataMappings < ActiveRecord::Migration[8.0]
  def change
    create_table :data_mappings do |t|
      t.references :organization, null: false, foreign_key: true
      t.references :pipeline, null: false, foreign_key: true
      t.text :mapping_json # encrypted
      t.decimal :confidence_score, precision: 3, scale: 2
      t.boolean :user_confirmed, default: false
      t.datetime :confirmed_at
      
      t.timestamps
    end
    
    add_index :data_mappings, :pipeline_id
    add_index :data_mappings, :confidence_score
  end
end

# db/migrate/008_create_data_quality_reports.rb
class CreateDataQualityReports < ActiveRecord::Migration[8.0]
  def change
    create_table :data_quality_reports do |t|
      t.references :organization, null: false, foreign_key: true
      t.references :pipeline, null: false, foreign_key: true
      t.decimal :quality_score, precision: 5, scale: 2
      t.decimal :completeness_score, precision: 5, scale: 2
      t.decimal :uniqueness_score, precision: 5, scale: 2
      t.decimal :timeliness_score, precision: 5, scale: 2
      t.decimal :accuracy_score, precision: 5, scale: 2
      t.text :recommendations # encrypted
      t.integer :total_records
      t.integer :valid_records
      t.json :detailed_metrics
      
      t.timestamps
    end
    
    add_index :data_quality_reports, [:organization_id, :created_at]
    add_index :data_quality_reports, :pipeline_id
    add_index :data_quality_reports, :quality_score
    
    # Partition by organization_id for better performance
    # Add this as a separate migration for production
  end
end

# db/migrate/009_create_dashboards.rb
class CreateDashboards < ActiveRecord::Migration[8.0]
  def change
    create_table :dashboards do |t|
      t.references :organization, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.string :name, null: false
      t.text :layout_json # encrypted
      t.boolean :shared, default: false
      t.integer :refresh_interval, default: 300 # seconds
      
      t.timestamps
    end
    
    add_index :dashboards, [:organization_id, :user_id]
    add_index :dashboards, :shared
  end
end

# db/migrate/010_create_insights.rb
class CreateInsights < ActiveRecord::Migration[8.0]
  def change
    create_table :insights do |t|
      t.references :organization, null: false, foreign_key: true
      t.references :dashboard, null: false, foreign_key: true
      t.string :title
      t.text :body
      t.string :insight_type # anomaly, trend, recommendation
      t.decimal :confidence, precision: 3, scale: 2
      t.boolean :dismissed, default: false
      
      t.timestamps
    end
    
    add_index :insights, [:organization_id, :insight_type]
    add_index :insights, :dashboard_id
  end
end

# db/migrate/011_create_custom_connectors.rb
class CreateCustomConnectors < ActiveRecord::Migration[8.0]
  def change
    create_table :custom_connectors do |t|
      t.references :organization, null: false, foreign_key: true
      t.references :user, null: false, foreign_key: true
      t.references :data_source, null: true, foreign_key: true
      t.string :name, null: false
      t.string :status, default: 'draft'
      t.text :config_json # encrypted
      t.text :test_results
      t.datetime :last_tested_at
      
      t.timestamps
    end
    
    add_index :custom_connectors, [:organization_id, :name], unique: true
    add_index :custom_connectors, :status
  end
end

# db/migrate/012_install_solid_queue.rb
class InstallSolidQueue < ActiveRecord::Migration[8.0]
  def change
    create_table :solid_queue_jobs do |t|
      t.string :queue_name, null: false
      t.string :class_name, null: false
      t.text :arguments
      t.integer :priority, default: 0
      t.string :active_job_id
      t.datetime :scheduled_at
      t.datetime :finished_at
      t.string :finished_with

      t.timestamps
    end

    add_index :solid_queue_jobs, [:queue_name, :finished_at]
    add_index :solid_queue_jobs, [:scheduled_at, :finished_at]
    
    create_table :solid_queue_scheduled_executions do |t|
      t.references :job, null: false, foreign_key: { to_table: :solid_queue_jobs }
      t.string :queue_name, null: false
      t.integer :priority, default: 0
      t.datetime :scheduled_at, null: false

      t.timestamps
    end

    add_index :solid_queue_scheduled_executions, [:scheduled_at, :priority, :queue_name], name: :index_solid_queue_scheduled_executions_for_polling
  end
end

# db/migrate/013_install_solid_cache.rb
class InstallSolidCache < ActiveRecord::Migration[8.0]
  def change
    create_table :solid_cache_entries do |t|
      t.binary :key, null: false, limit: 1024
      t.binary :value, null: false, limit: 512.megabytes
      t.datetime :created_at, null: false
      
      t.virtual :key_hash, type: :string, as: "SHA2(key, 256)", stored: true
    end
    
    add_index :solid_cache_entries, :key_hash, unique: true
  end
end

# db/migrate/014_install_solid_cable.rb
class InstallSolidCable < ActiveRecord::Migration[8.0]
  def change
    create_table :solid_cable_messages do |t|
      t.string :channel, null: false
      t.text :payload, null: false
      t.datetime :created_at, null: false
      t.string :channel_hash, null: false
    end
    
    add_index :solid_cable_messages, :channel
    add_index :solid_cable_messages, :created_at
  end
end

# db/seeds.rb
# Create default organization and admin user for development
if Rails.env.development?
  organization = Organization.create!(
    name: "Demo Company",
    plan: "professional",
    subdomain: "demo",
    monthly_row_limit: 100_000_000
  )
  
  admin = organization.users.create!(
    email: "<EMAIL>",
    password: "password123",
    role: "admin",
    first_name: "Admin",
    last_name: "User"
  )
  
  # Create sample data source
  data_source = organization.data_sources.create!(
    name: "Sample API",
    kind: "api",
    connection_url: "https://api.example.com",
    status: "verified"
  )
  
  # Create sample integration
  integration = data_source.integrations.create!(
    organization: organization,
    status: "active"
  )
  
  # Create sample pipeline
  pipeline = organization.pipelines.create!(
    name: "Daily Sync",
    mode: "etl",
    status: "pending",
    active: true
  )
  
  # Connect integration to pipeline
  pipeline.source_pipelines.create!(
    organization: organization,
    integration: integration
  )
  
  puts "✓ Demo organization created with admin user (<EMAIL> / password123)"
end

# config/database.yml
default: &default
  adapter: postgresql
  encoding: unicode
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>

development:
  <<: *default
  database: data_refinery_development
  username: <%= ENV['DATABASE_USER'] %>
  password: <%= ENV['DATABASE_PASSWORD'] %>

test:
  <<: *default
  database: data_refinery_test<%= ENV['TEST_ENV_NUMBER'] %>

production:
  <<: *default
  database: data_refinery_production
  username: <%= ENV['DATABASE_USER'] %>
  password: <%= ENV['DATABASE_PASSWORD'] %>
  host: <%= ENV['DATABASE_HOST'] %>
  port: <%= ENV['DATABASE_PORT'] %>

# Setup commands to run after rails new:
# 
# 1. Add gems to Gemfile (see rails8_config artifact)
# 2. Run: bundle install
# 3. Run: rails generate acts_as_tenant:install
# 4. Run: rails db:create
# 5. Run: rails db:migrate
# 6. Run: rails db:seed
# 7. Run: rails solid_queue:install
# 8. Run: rails solid_cache:install  
# 9. Run: rails solid_cable:install
# 10. Run: bin/dev (to start with Procfile.dev)