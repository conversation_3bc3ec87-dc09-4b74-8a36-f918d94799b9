<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DataReflow for Retail & E-commerce | Unified Customer Journey Analytics</title>
  <meta name="description" content="DataReflow helps retail and e-commerce businesses unify Shopify, Amazon, Facebook Ads, and analytics data. Optimize inventory, increase conversions, and boost customer lifetime value.">
  <meta name="keywords" content="retail analytics, ecommerce data, Shopify integration, Amazon seller analytics, inventory optimization, customer lifetime value">
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://datareflow.com/retail">
  <meta property="og:title" content="DataReflow for Retail & E-commerce">
  <meta property="og:description" content="Unified customer journey analytics for retail and e-commerce businesses">
  <meta property="og:image" content="https://datareflow.com/images/og-retail.jpg">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://datareflow.com/retail">
  <meta property="twitter:title" content="DataReflow for Retail & E-commerce">
  <meta property="twitter:description" content="Unified customer journey analytics for retail and e-commerce businesses">
  <meta property="twitter:image" content="https://datareflow.com/images/og-retail.jpg">

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "DataReflow",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web",
    "description": "Data analytics platform for retail and e-commerce businesses",
    "offers": {
      "@type": "Offer",
      "price": "149",
      "priceCurrency": "USD"
    },
    "audience": {
      "@type": "Audience",
      "audienceType": "Retail and E-commerce Businesses"
    }
  }
  </script>

  <style>
    :root {
      --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --retail-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
      --success-gradient: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
      --ecommerce-gradient: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
      --glass-bg: rgba(255, 255, 255, 0.1);
      --glass-border: rgba(255, 255, 255, 0.2);
      --text-primary: #2d3436;
      --text-secondary: #636e72;
      --text-light: #b2bec3;
      --retail-accent: #ff6b6b;
      --success-accent: #00b894;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6;
      color: var(--text-primary);
      background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
      min-height: 100vh;
    }

    .glass-morphism {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 20px;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    /* Header */
    .header {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-bottom: 1px solid rgba(0, 0, 0, 0.1);
      padding: 1rem 0;
      transition: all 0.3s ease;
    }

    .nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .logo {
      font-size: 1.8rem;
      font-weight: 700;
      color: var(--retail-accent);
      text-decoration: none;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .logo-icon {
      width: 32px;
      height: 32px;
      background: var(--retail-gradient);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
    }

    .nav-links {
      display: flex;
      list-style: none;
      gap: 2rem;
    }

    .nav-links a {
      color: var(--text-secondary);
      text-decoration: none;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .nav-links a:hover {
      color: var(--retail-accent);
    }

    .cta-button {
      background: var(--retail-gradient);
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 50px;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.3s ease;
      border: none;
      cursor: pointer;
      display: inline-block;
    }

    .cta-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(255, 107, 107, 0.3);
    }

    /* Hero Section */
    .hero {
      padding: 8rem 0 6rem;
      background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
      color: white;
      position: relative;
      overflow: hidden;
    }

    .hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    }

    .hero-content {
      position: relative;
      z-index: 1;
      text-align: center;
    }

    .hero h1 {
      font-size: 3.5rem;
      font-weight: 800;
      line-height: 1.1;
      margin-bottom: 1.5rem;
    }

    .hero .subtitle {
      font-size: 1.4rem;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 2rem;
      max-width: 700px;
      margin-left: auto;
      margin-right: auto;
    }

    .retail-badges {
      display: flex;
      justify-content: center;
      gap: 1rem;
      margin: 2rem 0;
      flex-wrap: wrap;
    }

    .retail-badge {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 50px;
      padding: 0.75rem 1.5rem;
      color: white;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .badge-icon {
      width: 24px;
      height: 24px;
      background: var(--retail-gradient);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.8rem;
    }

    /* Main Content */
    .main-content {
      background: white;
      border-radius: 30px 30px 0 0;
      margin-top: 4rem;
      padding: 4rem 0;
    }

    .section {
      padding: 4rem 0;
    }

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      text-align: center;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    .section-subtitle {
      font-size: 1.2rem;
      color: var(--text-secondary);
      text-align: center;
      max-width: 600px;
      margin: 0 auto 3rem;
    }

    /* Use Cases */
    .use-cases {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
      margin-top: 3rem;
    }

    .use-case-card {
      background: white;
      border-radius: 20px;
      padding: 2.5rem;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      border-left: 4px solid var(--retail-accent);
    }

    .use-case-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .use-case-icon {
      width: 60px;
      height: 60px;
      background: var(--retail-gradient);
      border-radius: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1.5rem;
      font-size: 1.5rem;
      color: white;
    }

    .use-case-title {
      font-size: 1.4rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    .use-case-description {
      color: var(--text-secondary);
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .use-case-benefits {
      list-style: none;
    }

    .use-case-benefits li {
      padding: 0.25rem 0;
      color: var(--success-accent);
      font-weight: 500;
      position: relative;
      padding-left: 1.5rem;
    }

    .use-case-benefits li:before {
      content: "✓";
      position: absolute;
      left: 0;
      color: var(--success-accent);
      font-weight: bold;
    }

    /* Customer Journey */
    .customer-journey {
      background: #f8fafc;
      padding: 6rem 0;
    }

    .journey-flow {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 2rem;
      margin: 3rem 0;
      position: relative;
    }

    .journey-step {
      background: white;
      padding: 2rem;
      border-radius: 15px;
      text-align: center;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      position: relative;
    }

    .journey-step:not(:last-child):after {
      content: '→';
      position: absolute;
      right: -1rem;
      top: 50%;
      transform: translateY(-50%);
      color: var(--retail-accent);
      font-size: 1.5rem;
      font-weight: bold;
    }

    .step-number {
      width: 40px;
      height: 40px;
      background: var(--retail-gradient);
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1rem;
      font-weight: bold;
    }

    .step-title {
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: var(--text-primary);
    }

    .step-description {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }

    /* ROI Calculator for Retail */
    .retail-roi {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 20px;
      padding: 3rem;
      margin: 4rem 0;
    }

    .roi-inputs {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin: 2rem 0;
    }

    .input-group {
      text-align: left;
    }

    .input-group label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 600;
      color: var(--text-primary);
    }

    .input-group input,
    .input-group select {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #e2e8f0;
      border-radius: 10px;
      font-size: 1rem;
    }

    .roi-results {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 2rem;
      margin-top: 2rem;
    }

    .roi-metric {
      background: white;
      padding: 1.5rem;
      border-radius: 15px;
      text-align: center;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .roi-metric-value {
      font-size: 2rem;
      font-weight: 800;
      color: var(--success-accent);
      margin-bottom: 0.5rem;
    }

    .roi-metric-label {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }

    /* Integrations Grid */
    .integrations-retail {
      background: white;
    }

    .integrations-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 1.5rem;
      margin: 3rem 0;
    }

    .integration-card {
      background: white;
      border: 2px solid #f1f3f4;
      border-radius: 15px;
      padding: 1.5rem;
      text-align: center;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .integration-card:hover {
      border-color: var(--retail-accent);
      transform: translateY(-3px);
      box-shadow: 0 10px 25px rgba(255, 107, 107, 0.1);
    }

    .integration-logo {
      width: 50px;
      height: 50px;
      background: var(--retail-gradient);
      border-radius: 12px;
      margin: 0 auto 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 1.2rem;
    }

    .integration-name {
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 0.5rem;
    }

    .integration-description {
      font-size: 0.8rem;
      color: var(--text-secondary);
    }

    /* Case Study */
    .case-study {
      background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
      color: white;
      padding: 6rem 0;
    }

    .case-study-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 4rem;
      align-items: center;
    }

    .case-study-text h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 2rem;
    }

    .case-study-text p {
      font-size: 1.1rem;
      margin-bottom: 1.5rem;
      opacity: 0.9;
    }

    .case-study-metrics {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 1.5rem;
    }

    .case-metric {
      text-align: center;
    }

    .case-metric-value {
      font-size: 2.5rem;
      font-weight: 800;
      margin-bottom: 0.5rem;
    }

    .case-metric-label {
      font-size: 0.9rem;
      opacity: 0.8;
    }

    .case-study-quote {
      background: rgba(255, 255, 255, 0.1);
      padding: 2rem;
      border-radius: 15px;
      margin-top: 2rem;
    }

    .quote-text {
      font-style: italic;
      font-size: 1.1rem;
      margin-bottom: 1rem;
    }

    .quote-author {
      font-weight: 600;
    }

    /* Pricing for Retail */
    .pricing-retail {
      background: #f8fafc;
    }

    .pricing-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin-top: 3rem;
    }

    .pricing-card {
      background: white;
      border-radius: 20px;
      padding: 2.5rem;
      text-align: center;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(0, 0, 0, 0.05);
      position: relative;
      transition: all 0.3s ease;
    }

    .pricing-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .pricing-card.featured {
      border: 2px solid var(--retail-accent);
      transform: scale(1.05);
    }

    .pricing-badge {
      position: absolute;
      top: -10px;
      left: 50%;
      transform: translateX(-50%);
      background: var(--retail-gradient);
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 600;
    }

    .pricing-plan {
      font-size: 1.2rem;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 1rem;
    }

    .pricing-price {
      font-size: 3rem;
      font-weight: 800;
      color: var(--retail-accent);
      margin-bottom: 0.5rem;
    }

    .pricing-period {
      color: var(--text-light);
      font-size: 1rem;
      margin-bottom: 2rem;
    }

    .pricing-features {
      list-style: none;
      margin: 2rem 0;
      text-align: left;
    }

    .pricing-features li {
      padding: 0.5rem 0;
      position: relative;
      padding-left: 1.5rem;
      color: var(--text-secondary);
    }

    .pricing-features li:before {
      content: "✓";
      position: absolute;
      left: 0;
      color: var(--success-accent);
      font-weight: bold;
    }

    /* CTA Section */
    .cta-section {
      background: var(--retail-gradient);
      color: white;
      text-align: center;
      padding: 6rem 0;
    }

    .cta-section h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
    }

    .cta-section p {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      opacity: 0.9;
    }

    .cta-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    .btn-secondary {
      background: rgba(255, 255, 255, 0.2);
      border: 2px solid white;
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 50px;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .btn-secondary:hover {
      background: white;
      color: var(--retail-accent);
    }

    /* Footer */
    .footer {
      background: #2d3436;
      color: white;
      padding: 3rem 0 1rem;
    }

    .footer-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-bottom: 2rem;
    }

    .footer-section h3 {
      margin-bottom: 1rem;
      color: white;
    }

    .footer-section ul {
      list-style: none;
    }

    .footer-section ul li {
      margin-bottom: 0.5rem;
    }

    .footer-section ul li a {
      color: rgba(255, 255, 255, 0.7);
      text-decoration: none;
      transition: color 0.3s ease;
    }

    .footer-section ul li a:hover {
      color: white;
    }

    .footer-bottom {
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      padding-top: 2rem;
      text-align: center;
      color: rgba(255, 255, 255, 0.7);
    }

    /* Animations */
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .animate-on-scroll {
      opacity: 0;
      animation: fadeInUp 0.8s ease forwards;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .hero h1 {
        font-size: 2.5rem;
      }
      
      .hero .subtitle {
        font-size: 1.1rem;
      }
      
      .nav-links {
        display: none;
      }
      
      .case-study-content {
        grid-template-columns: 1fr;
        text-align: center;
      }
      
      .pricing-card.featured {
        transform: none;
      }
      
      .roi-inputs {
        grid-template-columns: 1fr;
      }
      
      .journey-step:not(:last-child):after {
        content: '↓';
        right: 50%;
        top: 100%;
        transform: translateX(50%);
      }
    }
  </style>
</head>
<body>
  <!-- Header -->
  <header class="header" data-controller="navbar">
    <nav class="nav container">
      <a href="/" class="logo">
        <div class="logo-icon">🛍️</div>
        DataReflow
      </a>
      <ul class="nav-links">
        <li><a href="#use-cases">Use Cases</a></li>
        <li><a href="#integrations">Integrations</a></li>
        <li><a href="#case-study">Success Stories</a></li>
        <li><a href="#pricing">Pricing</a></li>
      </ul>
      <a href="#demo" class="cta-button">Start Free Trial</a>
    </nav>
  </header>

  <!-- Hero Section -->
  <section class="hero">
    <div class="container">
      <div class="hero-content">
        <h1>Unify Your Retail Data.<br>Optimize Every Sale.</h1>
        <p class="subtitle">
          DataReflow connects Shopify, Amazon, Facebook Ads, Google Analytics, and all your retail systems. Get real-time insights that drive inventory optimization, increase conversions, and maximize customer lifetime value.
        </p>
        
        <div class="retail-badges">
          <div class="retail-badge">
            <div class="badge-icon">🛒</div>
            E-commerce Platforms
          </div>
          <div class="retail-badge">
            <div class="badge-icon">📊</div>
            Marketing Analytics
          </div>
          <div class="retail-badge">
            <div class="badge-icon">💰</div>
            Financial Systems
          </div>
          <div class="retail-badge">
            <div class="badge-icon">📦</div>
            Inventory Management
          </div>
        </div>
        
        <div class="cta-buttons">
          <a href="#demo" class="cta-button">Start Free Trial</a>
          <a href="#case-study" class="btn-secondary">See Success Stories</a>
        </div>
      </div>
    </div>
  </section>

  <!-- Main Content -->
  <main class="main-content">
    <!-- Use Cases Section -->
    <section id="use-cases" class="section">
      <div class="container">
        <h2 class="section-title">Built for Modern Retail Operations</h2>
        <p class="section-subtitle">
          Solve the data challenges that matter most to growing retail and e-commerce businesses
        </p>
        
        <div class="use-cases">
          <div class="use-case-card animate-on-scroll">
            <div class="use-case-icon">📈</div>
            <h3 class="use-case-title">Customer Journey Analytics</h3>
            <p class="use-case-description">
              Track customers from first touchpoint to purchase across all channels. Understand attribution, optimize ad spend, and increase conversion rates.
            </p>
            <ul class="use-case-benefits">
              <li>Unified customer profiles across platforms</li>
              <li>Attribution modeling for marketing ROI</li>
              <li>Conversion funnel optimization</li>
              <li>Customer lifetime value tracking</li>
            </ul>
          </div>
          
          <div class="use-case-card animate-on-scroll">
            <div class="use-case-icon">📦</div>
            <h3 class="use-case-title">Inventory Optimization</h3>
            <p class="use-case-description">
              Prevent stockouts and reduce overstock with demand forecasting. Sync inventory levels across all sales channels in real-time.
            </p>
            <ul class="use-case-benefits">
              <li>Demand forecasting and planning</li>
              <li>Multi-channel inventory sync</li>
              <li>Automated reorder alerts</li>
              <li>Seasonal trend analysis</li>
            </ul>
          </div>
          
          <div class="use-case-card animate-on-scroll">
            <div class="use-case-icon">💎</div>
            <h3 class="use-case-title">Customer Segmentation</h3>
            <p class="use-case-description">
              Create targeted marketing campaigns based on purchase behavior, demographics, and engagement patterns across all touchpoints.
            </p>
            <ul class="use-case-benefits">
              <li>Behavioral segmentation models</li>
              <li>Personalized marketing campaigns</li>
              <li>Churn prediction and retention</li>
              <li>Cross-sell and upsell opportunities</li>
            </ul>
          </div>
          
          <div class="use-case-card animate-on-scroll">
            <div class="use-case-icon">🎯</div>
            <h3 class="use-case-title">Marketing Attribution</h3>
            <p class="use-case-description">
              Understand which marketing channels drive the highest ROI. Optimize ad spend across Facebook, Google, email, and other channels.
            </p>
            <ul class="use-case-benefits">
              <li>Multi-touch attribution modeling</li>
              <li>Channel performance comparison</li>
              <li>Budget allocation optimization</li>
              <li>Campaign effectiveness tracking</li>
            </ul>
          </div>
          
          <div class="use-case-card animate-on-scroll">
            <div class="use-case-icon">📊</div>
            <h3 class="use-case-title">Financial Reporting</h3>
            <p class="use-case-description">
              Automate P&L reporting with real-time revenue, costs, and margin analysis. Connect sales data with accounting systems seamlessly.
            </p>
            <ul class="use-case-benefits">
              <li>Automated financial reporting</li>
              <li>Real-time profit margin analysis</li>
              <li>Product profitability insights</li>
              <li>Tax preparation automation</li>
            </ul>
          </div>
          
          <div class="use-case-card animate-on-scroll">
            <div class="use-case-icon">🚀</div>
            <h3 class="use-case-title">Operational Excellence</h3>
            <p class="use-case-description">
              Monitor key performance indicators across all business functions. Identify bottlenecks and optimization opportunities.
            </p>
            <ul class="use-case-benefits">
              <li>Real-time KPI dashboards</li>
              <li>Operational efficiency metrics</li>
              <li>Performance benchmarking</li>
              <li>Process automation insights</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Customer Journey -->
    <section class="customer-journey">
      <div class="container">
        <h2 class="section-title">Complete Customer Journey Visibility</h2>
        <p class="section-subtitle">
          See every touchpoint from awareness to loyalty in one unified view
        </p>
        
        <div class="journey-flow">
          <div class="journey-step">
            <div class="step-number">1</div>
            <h3 class="step-title">Discovery</h3>
            <p class="step-description">Facebook Ads, Google Ads, Organic Search, Email Marketing</p>
          </div>
          
          <div class="journey-step">
            <div class="step-number">2</div>
            <h3 class="step-title">Consideration</h3>
            <p class="step-description">Website Visits, Product Views, Cart Additions, Wishlist Saves</p>
          </div>
          
          <div class="journey-step">
            <div class="step-number">3</div>
            <h3 class="step-title">Purchase</h3>
            <p class="step-description">Shopify, Amazon, In-Store POS, Mobile App Transactions</p>
          </div>
          
          <div class="journey-step">
            <div class="step-number">4</div>
            <h3 class="step-title">Fulfillment</h3>
            <p class="step-description">Inventory Systems, Shipping Providers, Order Tracking</p>
          </div>
          
          <div class="journey-step">
            <div class="step-number">5</div>
            <h3 class="step-title">Loyalty</h3>
            <p class="step-description">Reviews, Repeat Purchases, Referrals, Customer Support</p>
          </div>
        </div>
      </div>
    </section>

    <!-- ROI Calculator -->
    <section class="section">
      <div class="container">
        <div class="retail-roi" data-controller="retail-roi-calculator">
          <h2 class="section-title">Calculate Your Retail ROI</h2>
          <p class="section-subtitle">
            See how DataReflow can impact your retail business metrics
          </p>
          
          <div class="roi-inputs">
            <div class="input-group">
              <label for="monthly-revenue">Monthly Revenue ($)</label>
              <input type="number" id="monthly-revenue" data-retail-roi-calculator-target="revenue" value="500000" min="10000" step="10000">
            </div>
            
            <div class="input-group">
              <label for="business-type">Business Type</label>
              <select id="business-type" data-retail-roi-calculator-target="businessType">
                <option value="ecommerce">E-commerce Only</option>
                <option value="omnichannel">Omnichannel Retail</option>
                <option value="marketplace">Marketplace Seller</option>
                <option value="b2b">B2B Wholesale</option>
              </select>
            </div>
            
            <div class="input-group">
              <label for="marketing-spend">Monthly Marketing Spend ($)</label>
              <input type="number" id="marketing-spend" data-retail-roi-calculator-target="marketingSpend" value="50000" min="1000" step="5000">
            </div>
            
            <div class="input-group">
              <label for="inventory-value">Inventory Value ($)</label>
              <input type="number" id="inventory-value" data-retail-roi-calculator-target="inventoryValue" value="200000" min="10000" step="10000">
            </div>
          </div>
          
          <div class="roi-results">
            <div class="roi-metric">
              <div class="roi-metric-value">$<span data-retail-roi-calculator-target="conversionIncrease">45,000</span></div>
              <div class="roi-metric-label">Additional Monthly Revenue</div>
            </div>
            
            <div class="roi-metric">
              <div class="roi-metric-value">$<span data-retail-roi-calculator-target="inventorySavings">18,000</span></div>
              <div class="roi-metric-label">Inventory Cost Savings</div>
            </div>
            
            <div class="roi-metric">
              <div class="roi-metric-value"><span data-retail-roi-calculator-target="marketingEfficiency">23</span>%</div>
              <div class="roi-metric-label">Marketing Efficiency Gain</div>
            </div>
            
            <div class="roi-metric">
              <div class="roi-metric-value"><span data-retail-roi-calculator-target="totalRoi">1,247</span>%</div>
              <div class="roi-metric-label">Annual ROI</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Integrations -->
    <section id="integrations" class="section integrations-retail">
      <div class="container">
        <h2 class="section-title">Connect Your Entire Retail Stack</h2>
        <p class="section-subtitle">
          Pre-built integrations for all major retail and e-commerce platforms
        </p>
        
        <div class="integrations-grid">
          <div class="integration-card">
            <div class="integration-logo">SF</div>
            <div class="integration-name">Shopify</div>
            <div class="integration-description">Orders, customers, products, inventory</div>
          </div>
          
          <div class="integration-card">
            <div class="integration-logo">AZ</div>
            <div class="integration-name">Amazon</div>
            <div class="integration-description">Seller Central, FBA, advertising data</div>
          </div>
          
          <div class="integration-card">
            <div class="integration-logo">WC</div>
            <div class="integration-name">WooCommerce</div>
            <div class="integration-description">WordPress e-commerce data</div>
          </div>
          
          <div class="integration-card">
            <div class="integration-logo">FB</div>
            <div class="integration-name">Facebook Ads</div>
            <div class="integration-description">Campaign performance, conversions</div>
          </div>
          
          <div class="integration-card">
            <div class="integration-logo">GA</div>
            <div class="integration-name">Google Analytics</div>
            <div class="integration-description">Website traffic, user behavior</div>
          </div>
          
          <div class="integration-card">
            <div class="integration-logo">ST</div>
            <div class="integration-name">Stripe</div>
            <div class="integration-description">Payment processing, transactions</div>
          </div>
          
          <div class="integration-card">
            <div class="integration-logo">ML</div>
            <div class="integration-name">Mailchimp</div>
            <div class="integration-description">Email marketing campaigns</div>
          </div>
          
          <div class="integration-card">
            <div class="integration-logo">KB</div>
            <div class="integration-name">Klaviyo</div>
            <div class="integration-description">E-commerce email automation</div>
          </div>
          
          <div class="integration-card">
            <div class="integration-logo">ZD</div>
            <div class="integration-name">Zendesk</div>
            <div class="integration-description">Customer support tickets</div>
          </div>
          
          <div class="integration-card">
            <div class="integration-logo">QB</div>
            <div class="integration-name">QuickBooks</div>
            <div class="integration-description">Accounting, financial data</div>
          </div>
          
          <div class="integration-card">
            <div class="integration-logo">GS</div>
            <div class="integration-name">Google Sheets</div>
            <div class="integration-description">Spreadsheet data, manual reports</div>
          </div>
          
          <div class="integration-card">
            <div class="integration-logo">+50</div>
            <div class="integration-name">More...</div>
            <div class="integration-description">350+ total integrations</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Case Study -->
    <section id="case-study" class="case-study">
      <div class="container">
        <div class="case-study-content">
          <div class="case-study-text">
            <h2>GreenHome Essentials: 78% Improvement in Demand Forecasting</h2>
            <p>
              GreenHome Essentials, a sustainable home products retailer, struggled with inventory planning across Shopify, Amazon, and retail partnerships. Manual processes led to frequent stockouts and $300K in excess inventory.
            </p>
            <p>
              With DataReflow, they unified sales data from all channels, implemented predictive analytics, and automated inventory optimization. Results speak for themselves.
            </p>
            
            <div class="case-study-metrics">
              <div class="case-metric">
                <div class="case-metric-value">78%</div>
                <div class="case-metric-label">Better Demand Forecasting</div>
              </div>
              
              <div class="case-metric">
                <div class="case-metric-value">$240K</div>
                <div class="case-metric-label">Inventory Savings</div>
              </div>
              
              <div class="case-metric">
                <div class="case-metric-value">32%</div>
                <div class="case-metric-label">Marketing ROI Increase</div>
              </div>
              
              <div class="case-metric">
                <div class="case-metric-value">23%</div>
                <div class="case-metric-label">Higher Repeat Purchases</div>
              </div>
            </div>
          </div>
          
          <div class="case-study-quote">
            <div class="quote-text">
              "DataReflow transformed our inventory management and customer insights. We can now predict demand accurately and make data-driven decisions about our product mix and marketing spend."
            </div>
            <div class="quote-author">Alex Rodriguez, CEO, GreenHome Essentials</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing -->
    <section id="pricing" class="section pricing-retail">
      <div class="container">
        <h2 class="section-title">Pricing That Grows With Your Business</h2>
        <p class="section-subtitle">
          Start small and scale as your retail operations expand
        </p>
        
        <div class="pricing-grid">
          <div class="pricing-card">
            <div class="pricing-plan">Starter</div>
            <div class="pricing-price">$149</div>
            <div class="pricing-period">per month</div>
            <ul class="pricing-features">
              <li>5 retail platform connections</li>
              <li>Basic customer journey tracking</li>
              <li>Inventory sync across channels</li>
              <li>Email support</li>
              <li>10M records/month</li>
              <li>Standard reporting</li>
            </ul>
            <a href="#demo" class="cta-button">Start Free Trial</a>
          </div>
          
          <div class="pricing-card featured">
            <div class="pricing-badge">Most Popular</div>
            <div class="pricing-plan">Growth</div>
            <div class="pricing-price">$449</div>
            <div class="pricing-period">per month</div>
            <ul class="pricing-features">
              <li>15 platform connections</li>
              <li>Advanced customer analytics</li>
              <li>Demand forecasting</li>
              <li>Marketing attribution</li>
              <li>Priority support + onboarding</li>
              <li>100M records/month</li>
              <li>Custom dashboards</li>
            </ul>
            <a href="#demo" class="cta-button">Start Free Trial</a>
          </div>
          
          <div class="pricing-card">
            <div class="pricing-plan">Scale</div>
            <div class="pricing-price">$899</div>
            <div class="pricing-period">per month</div>
            <ul class="pricing-features">
              <li>Unlimited connections</li>
              <li>AI-powered insights</li>
              <li>White-label dashboards</li>
              <li>Advanced security & compliance</li>
              <li>Dedicated success manager</li>
              <li>500M records/month</li>
              <li>Custom integrations</li>
            </ul>
            <a href="#demo" class="cta-button">Start Free Trial</a>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- CTA Section -->
  <section class="cta-section">
    <div class="container">
      <h2>Ready to Optimize Your Retail Operations?</h2>
      <p>Join 200+ retail and e-commerce businesses using DataReflow to drive growth</p>
      <div class="cta-buttons">
        <a href="#demo" class="cta-button">Start Free 14-Day Trial</a>
        <a href="#contact" class="btn-secondary">Schedule Demo</a>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <h3>DataReflow</h3>
          <p>Unifying retail data for smarter business decisions</p>
        </div>
        
        <div class="footer-section">
          <h3>Solutions</h3>
          <ul>
            <li><a href="#ecommerce">E-commerce Analytics</a></li>
            <li><a href="#inventory">Inventory Optimization</a></li>
            <li><a href="#marketing">Marketing Attribution</a></li>
            <li><a href="#customer">Customer Analytics</a></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h3>Integrations</h3>
          <ul>
            <li><a href="#shopify">Shopify</a></li>
            <li><a href="#amazon">Amazon</a></li>
            <li><a href="#facebook">Facebook Ads</a></li>
            <li><a href="#google">Google Analytics</a></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h3>Resources</h3>
          <ul>
            <li><a href="#blog">Retail Blog</a></li>
            <li><a href="#case-studies">Case Studies</a></li>
            <li><a href="#webinars">Webinars</a></li>
            <li><a href="#support">Support</a></li>
          </ul>
        </div>
      </div>
      
      <div class="footer-bottom">
        <p>&copy; 2025 DataReflow. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- Stimulus Controllers -->
  <script>
    // Retail ROI Calculator Controller
    class RetailRoiCalculator {
      static targets = ["revenue", "businessType", "marketingSpend", "inventoryValue", 
                       "conversionIncrease", "inventorySavings", "marketingEfficiency", "totalRoi"]
      
      connect() {
        this.calculate();
        this.element.addEventListener("input", () => this.calculate());
        this.element.addEventListener("change", () => this.calculate());
      }
      
      calculate() {
        const revenue = parseInt(this.revenueTarget.value) || 0;
        const businessType = this.businessTypeTarget.value;
        const marketingSpend = parseInt(this.marketingSpendTarget.value) || 0;
        const inventoryValue = parseInt(this.inventoryValueTarget.value) || 0;
        
        // Business type multipliers
        const multipliers = {
          'ecommerce': { conversion: 0.09, inventory: 0.09, marketing: 0.23 },
          'omnichannel': { conversion: 0.12, inventory: 0.12, marketing: 0.28 },
          'marketplace': { conversion: 0.07, inventory: 0.06, marketing: 0.20 },
          'b2b': { conversion: 0.15, inventory: 0.15, marketing: 0.35 }
        };
        
        const mult = multipliers[businessType] || multipliers['ecommerce'];
        
        // Calculate improvements
        const conversionIncrease = revenue * mult.conversion;
        const inventorySavings = inventoryValue * mult.inventory / 12; // Monthly savings
        const marketingEfficiency = mult.marketing * 100;
        
        // Calculate annual ROI
        const monthlyBenefit = conversionIncrease + inventorySavings + (marketingSpend * mult.marketing);
        const annualBenefit = monthlyBenefit * 12;
        const annualCost = 5388; // Professional plan
        const roi = ((annualBenefit - annualCost) / annualCost * 100);
        
        // Update display
        this.conversionIncreaseTarget.textContent = Math.round(conversionIncrease).toLocaleString();
        this.inventorySavingsTarget.textContent = Math.round(inventorySavings).toLocaleString();
        this.marketingEfficiencyTarget.textContent = Math.round(marketingEfficiency);
        this.totalRoiTarget.textContent = Math.round(roi).toLocaleString();
      }
    }
    
    // Navbar Controller
    class Navbar {
      connect() {
        window.addEventListener("scroll", () => this.handleScroll());
      }
      
      handleScroll() {
        if (window.scrollY > 100) {
          this.element.style.background = "rgba(255, 255, 255, 0.98)";
          this.element.style.boxShadow = "0 4px 20px rgba(0, 0, 0, 0.1)";
        } else {
          this.element.style.background = "rgba(255, 255, 255, 0.95)";
          this.element.style.boxShadow = "none";
        }
      }
    }
    
    // Scroll Animation Controller
    class ScrollAnimation {
      connect() {
        this.observeElements();
      }
      
      observeElements() {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              entry.target.style.animationDelay = "0s";
              entry.target.classList.add("animate-on-scroll");
            }
          });
        });
        
        document.querySelectorAll(".animate-on-scroll").forEach(el => {
          observer.observe(el);
        });
      }
    }
    
    // Initialize controllers
    document.addEventListener("DOMContentLoaded", () => {
      // Initialize ROI calculator
      document.querySelectorAll('[data-controller*="retail-roi-calculator"]').forEach(element => {
        const calculator = new RetailRoiCalculator();
        calculator.element = element;
        calculator.revenueTarget = element.querySelector('[data-retail-roi-calculator-target="revenue"]');
        calculator.businessTypeTarget = element.querySelector('[data-retail-roi-calculator-target="businessType"]');
        calculator.marketingSpendTarget = element.querySelector('[data-retail-roi-calculator-target="marketingSpend"]');
        calculator.inventoryValueTarget = element.querySelector('[data-retail-roi-calculator-target="inventoryValue"]');
        calculator.conversionIncreaseTarget = element.querySelector('[data-retail-roi-calculator-target="conversionIncrease"]');
        calculator.inventorySavingsTarget = element.querySelector('[data-retail-roi-calculator-target="inventorySavings"]');
        calculator.marketingEfficiencyTarget = element.querySelector('[data-retail-roi-calculator-target="marketingEfficiency"]');
        calculator.totalRoiTarget = element.querySelector('[data-retail-roi-calculator-target="totalRoi"]');
        calculator.connect();
      });
      
      // Initialize navbar
      document.querySelectorAll('[data-controller*="navbar"]').forEach(element => {
        const navbar = new Navbar();
        navbar.element = element;
        navbar.connect();
      });
      
      // Initialize scroll animations
      const scrollAnimation = new ScrollAnimation();
      scrollAnimation.connect();
      
      // Smooth scrolling for anchor links
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href'));
          if (target) {
            target.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        });
      });
    });
  </script>
</body>
</html>