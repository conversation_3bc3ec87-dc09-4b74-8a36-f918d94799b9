<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DataReflow - Transform Your Business Data into Strategic Insights | Enterprise Data Platform</title>
  <meta name="description" content="Unlock 40% faster decision-making with DataReflow. Enterprise-grade data integration and analytics platform designed for SMEs. See ROI in 30 days.">
  <meta name="keywords" content="business intelligence, data analytics, SME data platform, ROI analytics, data integration">
  
  <!-- Open Graph -->
  <meta property="og:title" content="DataReflow - Strategic Data Intelligence for Growing Businesses">
  <meta property="og:description" content="Transform scattered data into actionable insights. 40% faster decisions, 60% cost reduction.">
  <meta property="og:type" content="website">
  
  <!-- Schema.org markup -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "DataReflow",
    "applicationCategory": "BusinessApplication",
    "offers": {
      "@type": "Offer",
      "price": "299",
      "priceCurrency": "USD"
    }
  }
  </script>
  
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Playfair+Display:wght@700&display=swap" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#1e40af',
            secondary: '#3730a3',
            accent: '#10b981',
            dark: '#111827',
            light: '#f3f4f6'
          },
          fontFamily: {
            'sans': ['Inter', 'sans-serif'],
            'display': ['Playfair Display', 'serif']
          },
          animation: {
            'fade-in': 'fadeIn 0.5s ease-in-out',
            'slide-up': 'slideUp 0.5s ease-out',
            'counter': 'counter 2s ease-out'
          }
        }
      }
    }
  </script>
  
  <style>
    :root {
      --color-primary: #1e40af;
      --color-secondary: #3730a3;
      --color-accent: #10b981;
      --color-dark: #111827;
      --color-light: #f3f4f6;
      --transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    @keyframes slideUp {
      from { transform: translateY(20px); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }
    
    @keyframes counter {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .animate-on-scroll {
      opacity: 0;
      transition: var(--transition-base);
    }
    
    .animate-on-scroll.visible {
      opacity: 1;
      animation: slideUp 0.6s ease-out forwards;
    }
    
    .gradient-text {
      background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .hover-lift {
      transition: var(--transition-base);
    }
    
    .hover-lift:hover {
      transform: translateY(-4px);
      box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    }
    
    .cta-glow {
      position: relative;
      overflow: hidden;
    }
    
    .cta-glow::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
      transition: left 0.5s;
    }
    
    .cta-glow:hover::before {
      left: 100%;
    }
    
    .metric-card {
      background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
      border: 1px solid #e5e7eb;
      backdrop-filter: blur(10px);
    }
  </style>
</head>

<body class="font-sans text-gray-900 bg-white" data-controller="landing">
  <!-- Navigation -->
  <nav class="fixed top-0 w-full bg-white/95 backdrop-blur-sm shadow-sm z-50" data-controller="navigation">
    <div class="container mx-auto px-6 py-4">
      <div class="flex justify-between items-center">
        <div class="flex items-center space-x-2">
          <div class="w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
          <span class="text-xl font-bold text-dark">DataReflow</span>
        </div>
        
        <div class="hidden md:flex items-center space-x-8">
          <a href="#features" class="text-gray-600 hover:text-primary transition-colors">Features</a>
          <a href="#roi" class="text-gray-600 hover:text-primary transition-colors">ROI Calculator</a>
          <a href="#testimonials" class="text-gray-600 hover:text-primary transition-colors">Success Stories</a>
          <a href="#pricing" class="text-gray-600 hover:text-primary transition-colors">Pricing</a>
          <button class="bg-primary text-white px-6 py-2 rounded-lg hover:bg-secondary transition-colors cta-glow">
            Book Strategy Call
          </button>
        </div>
        
        <button class="md:hidden" data-action="click->navigation#toggleMenu">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path>
          </svg>
        </button>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section class="pt-24 pb-16 px-6 bg-gradient-to-br from-gray-50 to-white">
    <div class="container mx-auto max-w-6xl">
      <div class="grid md:grid-cols-2 gap-12 items-center">
        <div class="space-y-6 animate-on-scroll">
          <div class="inline-flex items-center space-x-2 bg-accent/10 text-accent px-4 py-2 rounded-full">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-sm font-medium">Trusted by 500+ Growing Businesses</span>
          </div>
          
          <h1 class="text-4xl md:text-5xl lg:text-6xl font-display font-bold text-dark leading-tight">
            Transform Your Data Into 
            <span class="gradient-text">Strategic Advantage</span>
          </h1>
          
          <p class="text-xl text-gray-600 leading-relaxed">
            Make confident decisions 40% faster. DataReflow unifies your scattered business data into actionable insights that drive growth and profitability.
          </p>
          
          <div class="flex flex-col sm:flex-row gap-4">
            <button class="bg-primary text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-secondary transition-all transform hover:scale-105 cta-glow">
              See Your ROI Potential
            </button>
            <button class="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-lg text-lg font-semibold hover:border-primary hover:text-primary transition-all">
              Watch 2-Min Demo
            </button>
          </div>
          
          <div class="flex items-center space-x-6 pt-4">
            <div class="flex -space-x-2">
              <img src="https://i.pravatar.cc/40?img=1" class="w-10 h-10 rounded-full border-2 border-white" alt="Customer">
              <img src="https://i.pravatar.cc/40?img=2" class="w-10 h-10 rounded-full border-2 border-white" alt="Customer">
              <img src="https://i.pravatar.cc/40?img=3" class="w-10 h-10 rounded-full border-2 border-white" alt="Customer">
              <img src="https://i.pravatar.cc/40?img=4" class="w-10 h-10 rounded-full border-2 border-white" alt="Customer">
            </div>
            <div>
              <div class="flex text-yellow-400">★★★★★</div>
              <p class="text-sm text-gray-600">4.9/5 from 200+ executives</p>
            </div>
          </div>
        </div>
        
        <div class="relative animate-on-scroll" data-landing-target="heroImage">
          <div class="absolute inset-0 bg-gradient-to-br from-primary/20 to-accent/20 rounded-2xl transform rotate-6"></div>
          <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop" 
               alt="Executive Dashboard showing real-time business metrics" 
               class="relative rounded-2xl shadow-2xl">
          
          <!-- Floating metric cards -->
          <div class="absolute -top-4 -right-4 bg-white p-4 rounded-lg shadow-lg metric-card hover-lift">
            <div class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-accent/20 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
              </div>
              <div>
                <p class="text-xs text-gray-500">Revenue Growth</p>
                <p class="text-lg font-bold text-accent" data-counter="32">32%</p>
              </div>
            </div>
          </div>
          
          <div class="absolute -bottom-4 -left-4 bg-white p-4 rounded-lg shadow-lg metric-card hover-lift">
            <div class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div>
                <p class="text-xs text-gray-500">Time Saved</p>
                <p class="text-lg font-bold text-primary" data-counter="15">15h/week</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Trust Indicators -->
  <section class="py-12 bg-gray-50 border-y">
    <div class="container mx-auto px-6">
      <div class="flex flex-wrap justify-center items-center gap-8 opacity-60">
        <img src="https://via.placeholder.com/120x40/9CA3AF/FFFFFF?text=TechCorp" alt="TechCorp logo" class="h-8">
        <img src="https://via.placeholder.com/120x40/9CA3AF/FFFFFF?text=Innovate" alt="Innovate logo" class="h-8">
        <img src="https://via.placeholder.com/120x40/9CA3AF/FFFFFF?text=GrowthCo" alt="GrowthCo logo" class="h-8">
        <img src="https://via.placeholder.com/120x40/9CA3AF/FFFFFF?text=DataPro" alt="DataPro logo" class="h-8">
        <img src="https://via.placeholder.com/120x40/9CA3AF/FFFFFF?text=SmartBiz" alt="SmartBiz logo" class="h-8">
      </div>
    </div>
  </section>

  <!-- Key Metrics Section -->
  <section class="py-16 px-6">
    <div class="container mx-auto max-w-6xl">
      <div class="text-center mb-12 animate-on-scroll">
        <h2 class="text-3xl md:text-4xl font-display font-bold text-dark mb-4">
          Measurable Business Impact
        </h2>
        <p class="text-xl text-gray-600 max-w-2xl mx-auto">
          Join executives who've transformed their decision-making with data-driven insights
        </p>
      </div>
      
      <div class="grid md:grid-cols-4 gap-6">
        <div class="bg-white p-6 rounded-xl shadow-lg hover-lift animate-on-scroll text-center">
          <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <h3 class="text-3xl font-bold text-dark mb-2" data-counter="287">$287K</h3>
          <p class="text-gray-600">Average Annual Savings</p>
        </div>
        
        <div class="bg-white p-6 rounded-xl shadow-lg hover-lift animate-on-scroll text-center">
          <div class="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <h3 class="text-3xl font-bold text-dark mb-2" data-counter="40">40%</h3>
          <p class="text-gray-600">Faster Decision Making</p>
        </div>
        
        <div class="bg-white p-6 rounded-xl shadow-lg hover-lift animate-on-scroll text-center">
          <div class="w-16 h-16 bg-secondary/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
          </div>
          <h3 class="text-3xl font-bold text-dark mb-2" data-counter="99">99.9%</h3>
          <p class="text-gray-600">Data Accuracy</p>
        </div>
        
        <div class="bg-white p-6 rounded-xl shadow-lg hover-lift animate-on-scroll text-center">
          <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
            </svg>
          </div>
          <h3 class="text-3xl font-bold text-dark mb-2" data-counter="30">30</h3>
          <p class="text-gray-600">Days to First ROI</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section id="features" class="py-16 px-6 bg-gray-50">
    <div class="container mx-auto max-w-6xl">
      <div class="text-center mb-12 animate-on-scroll">
        <h2 class="text-3xl md:text-4xl font-display font-bold text-dark mb-4">
          Enterprise-Grade Intelligence for SMEs
        </h2>
        <p class="text-xl text-gray-600 max-w-2xl mx-auto">
          Get the insights that matter without the complexity or cost of traditional BI solutions
        </p>
      </div>
      
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div class="bg-white p-8 rounded-xl shadow-lg hover-lift animate-on-scroll" data-feature="unified-view">
          <div class="w-14 h-14 bg-primary/10 rounded-lg flex items-center justify-center mb-6">
            <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-dark mb-4">360° Business View</h3>
          <p class="text-gray-600 mb-4">
            Unify data from 50+ sources into a single executive dashboard. See your entire business at a glance.
          </p>
          <ul class="space-y-2 text-gray-600">
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Real-time KPI tracking
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Automated alerts & insights
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Custom executive reports
            </li>
          </ul>
        </div>
        
        <div class="bg-white p-8 rounded-xl shadow-lg hover-lift animate-on-scroll" data-feature="predictive">
          <div class="w-14 h-14 bg-accent/10 rounded-lg flex items-center justify-center mb-6">
            <svg class="w-8 h-8 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-dark mb-4">Predictive Intelligence</h3>
          <p class="text-gray-600 mb-4">
            AI-powered forecasting helps you anticipate trends and make proactive decisions.
          </p>
          <ul class="space-y-2 text-gray-600">
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Revenue forecasting
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Risk identification
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Opportunity scoring
            </li>
          </ul>
        </div>
        
        <div class="bg-white p-8 rounded-xl shadow-lg hover-lift animate-on-scroll" data-feature="automated">
          <div class="w-14 h-14 bg-secondary/10 rounded-lg flex items-center justify-center mb-6">
            <svg class="w-8 h-8 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-dark mb-4">Automated Workflows</h3>
          <p class="text-gray-600 mb-4">
            Eliminate manual data entry and reporting. Focus on strategy, not spreadsheets.
          </p>
          <ul class="space-y-2 text-gray-600">
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              15+ hours saved weekly
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Zero coding required
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Smart anomaly detection
            </li>
          </ul>
        </div>
        
        <div class="bg-white p-8 rounded-xl shadow-lg hover-lift animate-on-scroll" data-feature="integration">
          <div class="w-14 h-14 bg-primary/10 rounded-lg flex items-center justify-center mb-6">
            <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-dark mb-4">Seamless Integration</h3>
          <p class="text-gray-600 mb-4">
            Connect all your business tools in minutes. No IT team required.
          </p>
          <ul class="space-y-2 text-gray-600">
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              50+ pre-built connectors
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              5-minute setup
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Real-time sync
            </li>
          </ul>
        </div>
        
        <div class="bg-white p-8 rounded-xl shadow-lg hover-lift animate-on-scroll" data-feature="security">
          <div class="w-14 h-14 bg-accent/10 rounded-lg flex items-center justify-center mb-6">
            <svg class="w-8 h-8 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-dark mb-4">Bank-Grade Security</h3>
          <p class="text-gray-600 mb-4">
            Your data is protected with enterprise-level security and compliance standards.
          </p>
          <ul class="space-y-2 text-gray-600">
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              SOC 2 Type II certified
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              GDPR compliant
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              256-bit encryption
            </li>
          </ul>
        </div>
        
        <div class="bg-white p-8 rounded-xl shadow-lg hover-lift animate-on-scroll" data-feature="support">
          <div class="w-14 h-14 bg-secondary/10 rounded-lg flex items-center justify-center mb-6">
            <svg class="w-8 h-8 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-dark mb-4">White-Glove Support</h3>
          <p class="text-gray-600 mb-4">
            Dedicated success team ensures you achieve maximum value from day one.
          </p>
          <ul class="space-y-2 text-gray-600">
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              24/7 executive support
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Custom onboarding
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Quarterly business reviews
            </li>
          </ul>
        </div>
      </div>
    </div>
  </section>

  <!-- ROI Calculator Section -->
  <section id="roi" class="py-16 px-6 bg-white">
    <div class="container mx-auto max-w-4xl">
      <div class="text-center mb-12 animate-on-scroll">
        <h2 class="text-3xl md:text-4xl font-display font-bold text-dark mb-4">
          Calculate Your ROI Potential
        </h2>
        <p class="text-xl text-gray-600">
          See how much time and money DataReflow can save your business
        </p>
      </div>
      
      <div class="bg-gradient-to-br from-gray-50 to-white p-8 rounded-2xl shadow-xl" data-controller="roi-calculator">
        <div class="grid md:grid-cols-2 gap-8">
          <div class="space-y-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Number of Employees
              </label>
              <input type="range" min="10" max="500" value="50" 
                     class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                     data-roi-calculator-target="employees"
                     data-action="input->roi-calculator#calculate">
              <div class="flex justify-between text-sm text-gray-500 mt-1">
                <span>10</span>
                <span data-roi-calculator-target="employeesValue">50</span>
                <span>500</span>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Hours Spent on Manual Reporting (per week)
              </label>
              <input type="range" min="5" max="40" value="15" 
                     class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                     data-roi-calculator-target="hours"
                     data-action="input->roi-calculator#calculate">
              <div class="flex justify-between text-sm text-gray-500 mt-1">
                <span>5</span>
                <span data-roi-calculator-target="hoursValue">15</span>
                <span>40</span>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Average Hourly Cost per Employee
              </label>
              <input type="range" min="25" max="150" value="75" 
                     class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                     data-roi-calculator-target="rate"
                     data-action="input->roi-calculator#calculate">
              <div class="flex justify-between text-sm text-gray-500 mt-1">
                <span>$25</span>
                <span data-roi-calculator-target="rateValue">$75</span>
                <span>$150</span>
              </div>
            </div>
          </div>
          
          <div class="bg-primary/5 p-6 rounded-xl">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">Your Estimated Annual Savings</h3>
            
            <div class="space-y-4">
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Time Saved</span>
                <span class="text-2xl font-bold text-primary" data-roi-calculator-target="timeSaved">780 hours/year</span>
              </div>
              
              <div class="flex justify-between items-center">
                <span class="text-gray-600">Cost Savings</span>
                <span class="text-3xl font-bold text-accent" data-roi-calculator-target="costSaved">$58,500</span>
              </div>
              
              <div class="flex justify-between items-center">
                <span class="text-gray-600">ROI Timeline</span>
                <span class="text-xl font-bold text-secondary" data-roi-calculator-target="payback">2.1 months</span>
              </div>
              
              <div class="border-t pt-4 mt-4">
                <p class="text-sm text-gray-600 mb-4">
                  Based on DataReflow's average implementation results
                </p>
                <button class="w-full bg-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-secondary transition-all cta-glow">
                  Get Detailed ROI Report
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Testimonials Section -->
  <section id="testimonials" class="py-16 px-6 bg-gray-50">
    <div class="container mx-auto max-w-6xl">
      <div class="text-center mb-12 animate-on-scroll">
        <h2 class="text-3xl md:text-4xl font-display font-bold text-dark mb-4">
          Leaders Who've Transformed Their Business
        </h2>
        <p class="text-xl text-gray-600">
          Join executives achieving exceptional results with DataReflow
        </p>
      </div>
      
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div class="bg-white p-8 rounded-xl shadow-lg hover-lift animate-on-scroll">
          <div class="flex text-yellow-400 mb-4">★★★★★</div>
          <p class="text-gray-700 mb-6 italic">
            "DataReflow transformed how we make decisions. We've cut reporting time by 80% and increased revenue by 32% in just 6 months."
          </p>
          <div class="flex items-center">
            <img src="https://i.pravatar.cc/48?img=5" class="w-12 h-12 rounded-full mr-4" alt="Sarah Chen">
            <div>
              <p class="font-semibold text-dark">Sarah Chen</p>
              <p class="text-sm text-gray-600">CEO, TechVentures Inc.</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white p-8 rounded-xl shadow-lg hover-lift animate-on-scroll">
          <div class="flex text-yellow-400 mb-4">★★★★★</div>
          <p class="text-gray-700 mb-6 italic">
            "The predictive analytics alone paid for the entire platform. We avoided a $500K inventory mistake thanks to DataReflow's insights."
          </p>
          <div class="flex items-center">
            <img src="https://i.pravatar.cc/48?img=8" class="w-12 h-12 rounded-full mr-4" alt="Michael Torres">
            <div>
              <p class="font-semibold text-dark">Michael Torres</p>
              <p class="text-sm text-gray-600">CFO, Retail Solutions</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white p-8 rounded-xl shadow-lg hover-lift animate-on-scroll">
          <div class="flex text-yellow-400 mb-4">★★★★★</div>
          <p class="text-gray-700 mb-6 italic">
            "Finally, a BI tool that doesn't require a PhD to use. My entire leadership team adopted it within a week."
          </p>
          <div class="flex items-center">
            <img src="https://i.pravatar.cc/48?img=12" class="w-12 h-12 rounded-full mr-4" alt="Jennifer Park">
            <div>
              <p class="font-semibold text-dark">Jennifer Park</p>
              <p class="text-sm text-gray-600">COO, Growth Partners</p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="text-center mt-12 animate-on-scroll">
        <div class="inline-flex items-center space-x-8">
          <div>
            <p class="text-3xl font-bold text-primary">500+</p>
            <p class="text-gray-600">Happy Companies</p>
          </div>
          <div class="w-px h-12 bg-gray-300"></div>
          <div>
            <p class="text-3xl font-bold text-accent">4.9/5</p>
            <p class="text-gray-600">Average Rating</p>
          </div>
          <div class="w-px h-12 bg-gray-300"></div>
          <div>
            <p class="text-3xl font-bold text-secondary">98%</p>
            <p class="text-gray-600">Retention Rate</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Pricing Section -->
  <section id="pricing" class="py-16 px-6 bg-white">
    <div class="container mx-auto max-w-6xl">
      <div class="text-center mb-12 animate-on-scroll">
        <h2 class="text-3xl md:text-4xl font-display font-bold text-dark mb-4">
          Transparent Pricing, Exceptional Value
        </h2>
        <p class="text-xl text-gray-600">
          Start seeing ROI in 30 days or your money back
        </p>
      </div>
      
      <div class="grid md:grid-cols-3 gap-8">
        <div class="bg-gray-50 p-8 rounded-xl relative hover-lift animate-on-scroll">
          <h3 class="text-2xl font-bold text-dark mb-2">Starter</h3>
          <p class="text-gray-600 mb-6">Perfect for growing teams</p>
          <div class="mb-6">
            <span class="text-4xl font-bold text-dark">$299</span>
            <span class="text-gray-600">/month</span>
          </div>
          <ul class="space-y-3 mb-8">
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Up to 10 data sources
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              5 user seats
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Basic analytics & reporting
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Email support
            </li>
          </ul>
          <button class="w-full border-2 border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:border-primary hover:text-primary transition-all">
            Start Free Trial
          </button>
        </div>
        
        <div class="bg-gradient-to-br from-primary to-secondary p-8 rounded-xl relative transform scale-105 shadow-2xl hover-lift animate-on-scroll">
          <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <span class="bg-accent text-white px-4 py-1 rounded-full text-sm font-semibold">MOST POPULAR</span>
          </div>
          <h3 class="text-2xl font-bold text-white mb-2">Professional</h3>
          <p class="text-blue-100 mb-6">For data-driven organizations</p>
          <div class="mb-6">
            <span class="text-4xl font-bold text-white">$799</span>
            <span class="text-blue-100">/month</span>
          </div>
          <ul class="space-y-3 mb-8 text-white">
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Unlimited data sources
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              25 user seats
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              AI-powered insights
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Priority support & onboarding
            </li>
          </ul>
          <button class="w-full bg-white text-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all cta-glow">
            Start Free Trial
          </button>
        </div>
        
        <div class="bg-gray-50 p-8 rounded-xl relative hover-lift animate-on-scroll">
          <h3 class="text-2xl font-bold text-dark mb-2">Enterprise</h3>
          <p class="text-gray-600 mb-6">For large organizations</p>
          <div class="mb-6">
            <span class="text-4xl font-bold text-dark">Custom</span>
          </div>
          <ul class="space-y-3 mb-8">
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Everything in Professional
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Unlimited users
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Custom integrations
            </li>
            <li class="flex items-start">
              <svg class="w-5 h-5 text-accent mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              Dedicated success manager
            </li>
          </ul>
          <button class="w-full border-2 border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:border-primary hover:text-primary transition-all">
            Contact Sales
          </button>
        </div>
      </div>
      
      <div class="text-center mt-12 animate-on-scroll">
        <p class="text-gray-600 mb-4">All plans include:</p>
        <div class="flex flex-wrap justify-center gap-6 text-sm text-gray-700">
          <span class="flex items-center">
            <svg class="w-4 h-4 text-accent mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            30-day money-back guarantee
          </span>
          <span class="flex items-center">
            <svg class="w-4 h-4 text-accent mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            No setup fees
          </span>
          <span class="flex items-center">
            <svg class="w-4 h-4 text-accent mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            Cancel anytime
          </span>
        </div>
      </div>
    </div>
  </section>

  <!-- FAQ Section -->
  <section class="py-16 px-6 bg-gray-50">
    <div class="container mx-auto max-w-4xl">
      <div class="text-center mb-12 animate-on-scroll">
        <h2 class="text-3xl md:text-4xl font-display font-bold text-dark mb-4">
          Frequently Asked Questions
        </h2>
        <p class="text-xl text-gray-600">
          Everything you need to know about DataReflow
        </p>
      </div>
      
      <div class="space-y-4" data-controller="accordion">
        <div class="bg-white rounded-lg shadow-md animate-on-scroll">
          <button class="w-full px-6 py-4 text-left font-semibold text-dark flex justify-between items-center hover:bg-gray-50 transition-colors"
                  data-action="click->accordion#toggle"
                  data-accordion-target="trigger">
            <span>How quickly can we see ROI?</span>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform" data-accordion-target="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          <div class="hidden px-6 pb-4" data-accordion-target="content">
            <p class="text-gray-600">
              Most of our clients see measurable ROI within 30 days. The average time saved on reporting alone typically pays for the platform within the first month. Our onboarding team works with you to identify quick wins that deliver immediate value.
            </p>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md animate-on-scroll">
          <button class="w-full px-6 py-4 text-left font-semibold text-dark flex justify-between items-center hover:bg-gray-50 transition-colors"
                  data-action="click->accordion#toggle"
                  data-accordion-target="trigger">
            <span>Do we need technical expertise to use DataReflow?</span>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform" data-accordion-target="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          <div class="hidden px-6 pb-4" data-accordion-target="content">
            <p class="text-gray-600">
              No technical expertise required. DataReflow is designed for business users, not IT professionals. Our intuitive interface and pre-built templates mean you can start getting insights immediately. We also provide comprehensive training and support.
            </p>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md animate-on-scroll">
          <button class="w-full px-6 py-4 text-left font-semibold text-dark flex justify-between items-center hover:bg-gray-50 transition-colors"
                  data-action="click->accordion#toggle"
                  data-accordion-target="trigger">
            <span>How secure is our data?</span>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform" data-accordion-target="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          <div class="hidden px-6 pb-4" data-accordion-target="content">
            <p class="text-gray-600">
              Your data security is our top priority. DataReflow is SOC 2 Type II certified and GDPR compliant. We use bank-grade 256-bit encryption for data in transit and at rest. Your data is never shared with third parties, and you maintain complete ownership and control.
            </p>
          </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-md animate-on-scroll">
          <button class="w-full px-6 py-4 text-left font-semibold text-dark flex justify-between items-center hover:bg-gray-50 transition-colors"
                  data-action="click->accordion#toggle"
                  data-accordion-target="trigger">
            <span>Can DataReflow replace our existing BI tools?</span>
            <svg class="w-5 h-5 text-gray-500 transform transition-transform" data-accordion-target="icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          <div class="hidden px-6 pb-4" data-accordion-target="content">
            <p class="text-gray-600">
              Yes, DataReflow is designed as a complete BI solution for SMEs. It combines data integration, transformation, visualization, and predictive analytics in one platform. Many clients have successfully replaced multiple tools with DataReflow, reducing costs and complexity.
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="py-16 px-6 bg-gradient-to-br from-primary to-secondary">
    <div class="container mx-auto max-w-4xl text-center">
      <h2 class="text-3xl md:text-4xl font-display font-bold text-white mb-6 animate-on-scroll">
        Ready to Transform Your Business Intelligence?
      </h2>
      <p class="text-xl text-blue-100 mb-8 animate-on-scroll">
        Join 500+ companies making smarter decisions with DataReflow
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center animate-on-scroll">
        <button class="bg-white text-primary px-8 py-4 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-all transform hover:scale-105 cta-glow">
          Start Your Free Trial
        </button>
        <button class="border-2 border-white text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-white hover:text-primary transition-all">
          Schedule Executive Demo
        </button>
      </div>
      <p class="text-sm text-blue-100 mt-6 animate-on-scroll">
        No credit card required • 30-day money-back guarantee • Setup in 5 minutes
      </p>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-dark text-gray-300 py-12 px-6">
    <div class="container mx-auto max-w-6xl">
      <div class="grid md:grid-cols-4 gap-8 mb-8">
        <div>
          <div class="flex items-center space-x-2 mb-4">
            <div class="w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <span class="text-xl font-bold text-white">DataReflow</span>
          </div>
          <p class="text-sm">
            Enterprise-grade business intelligence made simple for growing companies.
          </p>
        </div>
        
        <div>
          <h4 class="font-semibold text-white mb-4">Product</h4>
          <ul class="space-y-2 text-sm">
            <li><a href="#" class="hover:text-white transition-colors">Features</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Integrations</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Pricing</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Security</a></li>
          </ul>
        </div>
        
        <div>
          <h4 class="font-semibold text-white mb-4">Company</h4>
          <ul class="space-y-2 text-sm">
            <li><a href="#" class="hover:text-white transition-colors">About Us</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Careers</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Blog</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Contact</a></li>
          </ul>
        </div>
        
        <div>
          <h4 class="font-semibold text-white mb-4">Resources</h4>
          <ul class="space-y-2 text-sm">
            <li><a href="#" class="hover:text-white transition-colors">Documentation</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Help Center</a></li>
            <li><a href="#" class="hover:text-white transition-colors">ROI Calculator</a></li>
            <li><a href="#" class="hover:text-white transition-colors">Case Studies</a></li>
          </ul>
        </div>
      </div>
      
      <div class="border-t border-gray-700 pt-8 flex flex-col md:flex-row justify-between items-center">
        <p class="text-sm mb-4 md:mb-0">
          © 2024 DataReflow. All rights reserved.
        </p>
        <div class="flex space-x-6 text-sm">
          <a href="#" class="hover:text-white transition-colors">Privacy Policy</a>
          <a href="#" class="hover:text-white transition-colors">Terms of Service</a>
          <a href="#" class="hover:text-white transition-colors">Cookie Policy</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- Stimulus Controllers -->
  <script type="module">
    import { Application, Controller } from "https://unpkg.com/@hotwired/stimulus/dist/stimulus.js"
    window.Stimulus = Application.start()

    // Landing Controller
    Stimulus.register("landing", class extends Controller {
      connect() {
        this.observeAnimations()
        this.initCounters()
      }

      observeAnimations() {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              entry.target.classList.add('visible')
            }
          })
        }, { threshold: 0.1 })

        document.querySelectorAll('.animate-on-scroll').forEach(el => {
          observer.observe(el)
        })
      }

      initCounters() {
        const counters = document.querySelectorAll('[data-counter]')
        counters.forEach(counter => {
          const target = parseInt(counter.getAttribute('data-counter'))
          const duration = 2000
          const increment = target / (duration / 16)
          let current = 0

          const updateCounter = () => {
            current += increment
            if (current < target) {
              counter.textContent = Math.floor(current) + (counter.textContent.includes('%') ? '%' : counter.textContent.includes('K') ? 'K' : '')
              requestAnimationFrame(updateCounter)
            } else {
              counter.textContent = counter.getAttribute('data-counter')
            }
          }

          const observer = new IntersectionObserver((entries) => {
            if (entries[0].isIntersecting) {
              updateCounter()
              observer.unobserve(counter)
            }
          })

          observer.observe(counter)
        })
      }
    })

    // Navigation Controller
    Stimulus.register("navigation", class extends Controller {
      toggleMenu() {
        // Mobile menu toggle logic
      }
    })

    // ROI Calculator Controller
    Stimulus.register("roi-calculator", class extends Controller {
      static targets = ["employees", "hours", "rate", "employeesValue", "hoursValue", "rateValue", "timeSaved", "costSaved", "payback"]

      connect() {
        this.calculate()
      }

      calculate() {
        const employees = parseInt(this.employeesTarget.value)
        const hours = parseInt(this.hoursTarget.value)
        const rate = parseInt(this.rateTarget.value)

        this.employeesValueTarget.textContent = employees
        this.hoursValueTarget.textContent = hours
        this.rateValueTarget.textContent = '$' + rate

        const annualHoursSaved = hours * 52 * 0.6 // 60% efficiency gain
        const annualCostSaved = annualHoursSaved * rate

        this.timeSavedTarget.textContent = Math.round(annualHoursSaved).toLocaleString() + ' hours/year'
        this.costSavedTarget.textContent = '$' + Math.round(annualCostSaved).toLocaleString()
        
        const monthlySubscription = 799
        const paybackMonths = (monthlySubscription * 12) / annualCostSaved * 12
        this.paybackTarget.textContent = paybackMonths.toFixed(1) + ' months'
      }
    })

    // Accordion Controller
    Stimulus.register("accordion", class extends Controller {
      static targets = ["trigger", "content", "icon"]

      toggle(event) {
        const button = event.currentTarget
        const content = button.nextElementSibling
        const icon = button.querySelector('svg')

        content.classList.toggle('hidden')
        icon.classList.toggle('rotate-180')
      }
    })
  </script>
</body>
</html>