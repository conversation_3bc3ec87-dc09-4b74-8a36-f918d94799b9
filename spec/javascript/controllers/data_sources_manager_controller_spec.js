import { Application } from "@hotwired/stimulus"
import DataSourcesManagerController from "../../../app/javascript/controllers/data_sources_manager_controller"

describe("DataSourcesManagerController", () => {
  let application
  let controller
  let element
  
  beforeEach(() => {
    application = Application.start()
    application.register("data-sources-manager", DataSourcesManagerController)
    
    document.body.innerHTML = `
      <div data-controller="data-sources-manager"
           data-data-sources-manager-current-filter-value="all"
           data-data-sources-manager-current-type-value="all">
        <input data-data-sources-manager-target="searchInput" type="text" />
        <button data-data-sources-manager-target="clearButton" style="display: none;"></button>
        <span data-data-sources-manager-target="visibleCount">3</span>
        <div data-data-sources-manager-target="filterLabel">All Filters</div>
        <span data-data-sources-manager-target="activeFilterCount" class="hidden">0</span>
        <div data-data-sources-manager-target="bulkActionsBar" class="hidden"></div>
        
        <div data-data-sources-manager-target="listContainer">
          <div data-data-sources-manager-target="sourceItem"
               data-source-id="1"
               data-name="production db"
               data-status="active"
               data-type="postgresql">
            <input type="checkbox" data-source-id="1" />
          </div>
          <div data-data-sources-manager-target="sourceItem"
               data-source-id="2"
               data-name="customer csv"
               data-status="error"
               data-type="csv">
            <input type="checkbox" data-source-id="2" />
          </div>
          <div data-data-sources-manager-target="sourceItem"
               data-source-id="3"
               data-name="api endpoint"
               data-status="active"
               data-type="api">
            <input type="checkbox" data-source-id="3" />
          </div>
        </div>
      </div>
    `
    
    element = document.querySelector('[data-controller="data-sources-manager"]')
    controller = application.getControllerForElementAndIdentifier(element, "data-sources-manager")
  })
  
  afterEach(() => {
    application.stop()
    document.body.innerHTML = ""
  })
  
  describe("search functionality", () => {
    it("filters items based on search query", () => {
      const searchInput = controller.searchInputTarget
      searchInput.value = "production"
      controller.search()
      
      const items = controller.sourceItemTargets
      expect(items[0].style.display).toBe("")
      expect(items[1].style.display).toBe("none")
      expect(items[2].style.display).toBe("none")
    })
    
    it("shows clear button when search has value", () => {
      const searchInput = controller.searchInputTarget
      searchInput.value = "test"
      controller.search()
      
      expect(controller.clearButtonTarget.style.display).toBe("flex")
    })
    
    it("clears search and shows all items", () => {
      controller.searchInputTarget.value = "production"
      controller.search()
      
      controller.clearSearch()
      
      expect(controller.searchInputTarget.value).toBe("")
      const items = controller.sourceItemTargets
      items.forEach(item => {
        expect(item.style.display).toBe("")
      })
    })
  })
  
  describe("filtering", () => {
    it("filters by status", () => {
      controller.currentFilterValue = "active"
      controller.applyFilters()
      
      const items = controller.sourceItemTargets
      expect(items[0].style.display).toBe("") // active
      expect(items[1].style.display).toBe("none") // error
      expect(items[2].style.display).toBe("") // active
    })
    
    it("filters by type", () => {
      controller.currentTypeValue = "database"
      controller.applyFilters()
      
      const items = controller.sourceItemTargets
      expect(items[0].style.display).toBe("") // postgresql
      expect(items[1].style.display).toBe("none") // csv
      expect(items[2].style.display).toBe("none") // api
    })
    
    it("combines search and filters", () => {
      controller.searchInputTarget.value = "db"
      controller.currentFilterValue = "active"
      controller.applyFilters()
      
      const items = controller.sourceItemTargets
      expect(items[0].style.display).toBe("") // matches both
      expect(items[1].style.display).toBe("none")
      expect(items[2].style.display).toBe("none")
    })
  })
  
  describe("filter indicators", () => {
    it("updates filter label and count", () => {
      controller.currentFilterValue = "active"
      controller.currentTypeValue = "database"
      controller.updateFilterIndicators()
      
      expect(controller.filterLabelTarget.textContent).toContain("Active")
      expect(controller.filterLabelTarget.textContent).toContain("Databases")
      expect(controller.activeFilterCountTarget.textContent).toBe("2")
      expect(controller.activeFilterCountTarget.classList.contains("hidden")).toBe(false)
    })
    
    it("hides count when no filters active", () => {
      controller.resetFilters()
      
      expect(controller.filterLabelTarget.textContent).toBe("All Filters")
      expect(controller.activeFilterCountTarget.classList.contains("hidden")).toBe(true)
    })
  })
  
  describe("bulk operations", () => {
    it("shows bulk actions bar when items selected", () => {
      const checkbox = element.querySelector('input[type="checkbox"]')
      checkbox.checked = true
      controller.updateBulkActions()
      
      expect(controller.bulkActionsBarTarget.classList.contains("hidden")).toBe(false)
    })
    
    it("toggles all checkboxes", () => {
      const event = { target: { checked: true } }
      controller.toggleSelectAll(event)
      
      const checkboxes = element.querySelectorAll('input[type="checkbox"]')
      checkboxes.forEach(checkbox => {
        expect(checkbox.checked).toBe(true)
      })
    })
    
    it("gets selected items data", () => {
      element.querySelectorAll('input[type="checkbox"]')[0].checked = true
      element.querySelectorAll('input[type="checkbox"]')[2].checked = true
      
      const selected = controller.getSelectedItems()
      expect(selected.length).toBe(2)
      expect(selected[0].id).toBe("1")
      expect(selected[0].name).toBe("production db")
      expect(selected[1].id).toBe("3")
      expect(selected[1].name).toBe("api endpoint")
    })
  })
  
  describe("keyboard shortcuts", () => {
    it("focuses search on Cmd+K", () => {
      const spy = jest.spyOn(controller.searchInputTarget, 'focus')
      
      const event = new KeyboardEvent('keydown', { key: 'k', metaKey: true })
      document.dispatchEvent(event)
      
      expect(spy).toHaveBeenCalled()
    })
  })
})