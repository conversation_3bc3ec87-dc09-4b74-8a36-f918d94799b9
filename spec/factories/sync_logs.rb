FactoryBot.define do
  factory :sync_log do
    organization
    data_source
    status { "completed" }
    started_at { 1.hour.ago }
    completed_at { Time.current }
    records_processed { rand(1000..10000) }
    records_imported { rand(900..9900) }
    records_failed { rand(0..100) }
    records_skipped { 0 }
    error_message { nil }
    metadata { {} }
    duration_seconds { rand(10..300) }
    sync_type { "full" }
    
    trait :completed do
      status { "completed" }
      error_message { nil }
      completed_at { started_at + duration_seconds.seconds }
    end
    
    trait :failed do
      status { "failed" }
      error_message { "Connection timeout" }
      records_imported { 0 }
      completed_at { started_at + 5.seconds }
    end
    
    trait :running do
      status { "running" }
      completed_at { nil }
    end
    
    trait :cancelled do
      status { "cancelled" }
      error_message { "Sync cancelled by user" }
    end
    
    trait :recent do
      started_at { rand(24.hours.ago..Time.current) }
    end
    
    trait :with_errors do
      records_failed { rand(10..100) }
      error_details { { errors: ["Invalid data format", "Missing required field"] } }
    end
  end
end
