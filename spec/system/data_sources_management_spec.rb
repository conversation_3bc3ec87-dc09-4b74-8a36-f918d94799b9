require 'rails_helper'

RSpec.describe "Data Sources Management", type: :system do
  let(:organization) { create(:organization) }
  let(:user) { create(:user, organization: organization) }
  
  before do
    sign_in user
  end
  
  describe "Search and filtering" do
    let!(:postgresql_source) { create(:data_source, name: "Production DB", source_type: 'postgresql', status: 'active', organization: organization) }
    let!(:csv_source) { create(:data_source, name: "Customer Data", source_type: 'csv', status: 'error', organization: organization) }
    let!(:api_source) { create(:data_source, name: "External API", source_type: 'api', status: 'inactive', organization: organization) }
    
    before do
      visit dashboard_data_sources_path
    end
    
    it "searches data sources by name" do
      within '[data-controller="data-sources-manager"]' do
        fill_in "Search by name, type, or status...", with: "Production"
        
        expect(page).to have_content("Production DB")
        expect(page).not_to have_content("Customer Data")
        expect(page).not_to have_content("External API")
      end
    end
    
    it "filters by status" do
      click_button "All Filters"
      
      within '[data-dropdown-target="menu"]' do
        click_button "Active"
      end
      
      expect(page).to have_content("Production DB")
      expect(page).not_to have_content("Customer Data")
      expect(page).not_to have_content("External API")
    end
    
    it "filters by type" do
      click_button "All Filters"
      
      within '[data-dropdown-target="menu"]' do
        click_button "Files"
      end
      
      expect(page).not_to have_content("Production DB")
      expect(page).to have_content("Customer Data")
      expect(page).not_to have_content("External API")
    end
    
    it "clears search with escape key" do
      fill_in "Search by name, type, or status...", with: "Production"
      
      find('[data-data-sources-manager-target="searchInput"]').send_keys(:escape)
      
      expect(page).to have_content("Production DB")
      expect(page).to have_content("Customer Data")
      expect(page).to have_content("External API")
    end
    
    it "shows active filter count" do
      click_button "All Filters"
      click_button "Active"
      
      within '[data-data-sources-manager-target="filterButton"]' do
        expect(page).to have_css('[data-data-sources-manager-target="activeFilterCount"]', text: "1")
      end
    end
  end
  
  describe "Bulk operations" do
    let!(:data_sources) { create_list(:data_source, 3, organization: organization) }
    
    before do
      visit dashboard_data_sources_path
    end
    
    it "shows bulk actions when items are selected" do
      expect(page).not_to have_css('[data-bulk-actions]', visible: true)
      
      first('input[type="checkbox"][data-action="change->data-sources-manager#updateBulkActions"]').click
      
      expect(page).to have_css('[data-bulk-actions]', visible: true)
      expect(page).to have_content("1 selected")
    end
    
    it "selects all items" do
      check "Select All"
      
      expect(page).to have_content("3 selected")
      
      checkboxes = all('input[type="checkbox"][data-source-id]')
      expect(checkboxes.all?(&:checked?)).to be true
    end
    
    it "exports selected items" do
      first('input[type="checkbox"][data-action="change->data-sources-manager#updateBulkActions"]').click
      
      click_button "Export"
      
      # Check that download was triggered (you might need to stub this in a real test)
      expect(page).to have_content("selected") # Bulk actions bar still visible
    end
  end
  
  describe "Data preview modal" do
    let!(:data_source) { create(:data_source, name: "Test Source", organization: organization) }
    
    before do
      allow_any_instance_of(DataSource).to receive(:fetch_sample_data).and_return({
        success: true,
        data: [
          { "id" => 1, "name" => "Test Record", "status" => "active" }
        ],
        metadata: { columns: ["id", "name", "status"] }
      })
      
      visit dashboard_data_sources_path
    end
    
    it "opens preview modal when clicking eye icon" do
      within "[data-source-id='#{data_source.id}']" do
        find('button[title="Preview data"]').click
      end
      
      within '[data-controller="data-preview-modal"]' do
        expect(page).to have_content("Data Preview: Test Source")
        expect(page).to have_content("Test Record")
        expect(page).to have_content("active")
      end
    end
    
    it "closes modal with close button" do
      within "[data-source-id='#{data_source.id}']" do
        find('button[title="Preview data"]').click
      end
      
      within '[data-controller="data-preview-modal"]' do
        click_button "Close"
      end
      
      expect(page).not_to have_css('[data-controller="data-preview-modal"]', visible: true)
    end
    
    it "closes modal with escape key" do
      within "[data-source-id='#{data_source.id}']" do
        find('button[title="Preview data"]').click
      end
      
      send_keys(:escape)
      
      expect(page).not_to have_css('[data-controller="data-preview-modal"]', visible: true)
    end
  end
  
  describe "Real-time status updates" do
    let!(:data_source) { create(:data_source, status: 'active', organization: organization) }
    
    it "displays real-time status indicators" do
      visit dashboard_data_sources_path
      
      within "[data-source-id='#{data_source.id}']" do
        expect(page).to have_css('[data-data-source-status-target="statusBadge"]')
        expect(page).to have_content("Active")
        expect(page).to have_css('.animate-pulse')
      end
    end
  end
  
  describe "View toggle" do
    let!(:data_sources) { create_list(:data_source, 2, organization: organization) }
    
    before do
      visit dashboard_data_sources_path
    end
    
    it "toggles between list and grid view" do
      expect(page).to have_css('#list-view', visible: true)
      expect(page).not_to have_css('#grid-view', visible: true)
      
      click_button "Grid"
      
      expect(page).not_to have_css('#list-view', visible: true)
      expect(page).to have_css('#grid-view', visible: true)
      
      click_button "List"
      
      expect(page).to have_css('#list-view', visible: true)
      expect(page).not_to have_css('#grid-view', visible: true)
    end
  end
  
  describe "Performance metrics" do
    before do
      visit dashboard_data_sources_path
    end
    
    it "displays performance metrics card" do
      within '.grid' do
        expect(page).to have_content("Avg Sync Time")
        expect(page).to have_content("seconds")
        expect(page).to have_content("Performance")
      end
    end
  end
end