require 'rails_helper'

RSpec.describe "Help Center Page", type: :system do
  before do
    driven_by(:rack_test)
  end

  describe "Page Structure" do
    before do
      visit help_center_path
    end

    it "displays the help center hero section" do
      expect(page).to have_content("Data Reflow Help Center")
      expect(page).to have_content("Find answers, tutorials, and resources")
      expect(page).to have_css('[data-controller="search"]')
    end

    it "includes search functionality" do
      expect(page).to have_field(placeholder: /Search for help articles/)
      expect(page).to have_css('input[data-action*="input->search#filter"]')
    end

    it "displays popular help topics" do
      expect(page).to have_content("Popular Help Topics")
      
      # Check all 6 main categories
      expect(page).to have_content("Getting Started")
      expect(page).to have_content("Data Sources")
      expect(page).to have_content("Analytics & Reports")
      expect(page).to have_content("Account & Billing")
      expect(page).to have_content("API & Integrations")
      expect(page).to have_content("Troubleshooting")
    end

    it "has proper category links and icons" do
      # Check for specific icons in the help topics section
      expect(page).to have_css('.fas.fa-rocket') # Getting Started icon
      expect(page).to have_css('.fas.fa-database') # Data Sources icon
      expect(page).to have_css('.fas.fa-chart-line') # Analytics icon
      expect(page).to have_css('.fas.fa-user-cog') # Account icon
      expect(page).to have_css('.fas.fa-code') # API icon
      expect(page).to have_css('.fas.fa-tools') # Troubleshooting icon
    end
  end

  describe "Knowledge Base Section" do
    before do
      visit help_center_path
    end

    it "displays knowledge base articles" do
      expect(page).to have_content("Knowledge Base")
      expect(page).to have_content("Featured Articles")
    end

    it "includes featured articles with metadata" do
      expect(page).to have_content("Getting Started with Data Reflow: Complete Setup Guide")
      expect(page).to have_content("Connecting Multiple Data Sources: Best Practices")
      expect(page).to have_content("Creating Custom Dashboards and Reports")
      expect(page).to have_content("AI-Powered Analytics: Understanding Predictions")
      
      # Check for article metadata
      expect(page).to have_content("15 min read")
      expect(page).to have_content("2.3k views")
      expect(page).to have_content("Beginner")
      expect(page).to have_content("Intermediate")
      expect(page).to have_content("Advanced")
    end

    it "includes sidebar with quick links" do
      expect(page).to have_content("Quick Links")
      expect(page).to have_content("Video Tutorials")
      expect(page).to have_content("User Manual (PDF)")
      expect(page).to have_content("API Documentation")
      expect(page).to have_content("Community Forum")
      expect(page).to have_content("Webinar Schedule")
    end

    it "includes popular downloads section" do
      expect(page).to have_content("Popular Downloads")
      expect(page).to have_content("Quick Start Guide")
      expect(page).to have_content("Sample Data Templates")
      expect(page).to have_css('.fas.fa-file-pdf')
      expect(page).to have_css('.fas.fa-file-excel')
    end
  end

  describe "User Guides Section" do
    before do
      visit help_center_path
    end

    it "displays user guides and tutorials" do
      expect(page).to have_content("User Guides & Tutorials")
      expect(page).to have_content("Step-by-step tutorials and comprehensive guides")
    end

    it "includes three main tutorial types" do
      expect(page).to have_content("Interactive Video Guides")
      expect(page).to have_content("Hands-On Practice")
      expect(page).to have_content("Expert-Led Sessions")
      
      # Check for corresponding icons
      expect(page).to have_css('.fas.fa-play-circle')
      expect(page).to have_css('.fas.fa-mouse-pointer')
      expect(page).to have_css('.fas.fa-users')
    end

    it "has proper call-to-action links" do
      expect(page).to have_link("Watch Now")
      expect(page).to have_link("Try Demo")
      expect(page).to have_link("View Schedule")
    end
  end

  describe "Contact Support Section" do
    before do
      visit help_center_path
    end

    it "displays contact support options" do
      expect(page).to have_content("Still Need Help?")
      expect(page).to have_content("Our support team is here to help")
    end

    it "includes all four support channels" do
      expect(page).to have_content("Live Chat")
      expect(page).to have_content("Email Support")
      expect(page).to have_content("Phone Support")
      expect(page).to have_content("Schedule Call")
    end

    it "has proper contact information" do
      expect(page).to have_content("Instant help available")
      expect(page).to have_content("Response within 4 hours")
      expect(page).to have_content("Mon-Fri, 9AM-6PM EST")
      expect(page).to have_content("Book a consultation")
    end

    it "includes contact links and buttons" do
      expect(page).to have_button("Start Chat")
      expect(page).to have_link("Send Email")
      expect(page).to have_link("Call Now")
      expect(page).to have_link("Book Now")
    end
  end

  describe "FAQ Section" do
    before do
      visit help_center_path
    end

    it "displays FAQ section with accordion" do
      expect(page).to have_content("Frequently Asked Questions")
      expect(page).to have_css('[data-controller="faq"]')
      expect(page).to have_css('[data-faq-accordion-value="true"]')
    end

    it "includes FAQ questions and proper structure" do
      expect(page).to have_content("How do I connect my first data source?")
      expect(page).to have_content("Can I customize my dashboard layout?")
      expect(page).to have_content("How do I share reports with my team?")
      expect(page).to have_content("What if I need help with data modeling?")
      
      # Check for proper accordion structure
      expect(page).to have_css('button[data-faq-target="trigger"]', count: 4)
      expect(page).to have_css('div[data-faq-target="content"]', count: 4)
      expect(page).to have_css('i[data-faq-target="icon"]', count: 4)
    end

    it "has proper ARIA attributes for accessibility" do
      within('[data-controller="faq"]') do
        expect(page).to have_css('button[aria-expanded="false"]', count: 4)
        expect(page).to have_css('button[type="button"]', count: 4)
      end
    end
  end

  describe "Final CTA Section" do
    before do
      visit help_center_path
    end

    it "displays final call-to-action" do
      expect(page).to have_content("Ready to Get Started?")
      expect(page).to have_content("Have questions? Our team is here to help")
    end

    it "includes action buttons" do
      expect(page).to have_link("Contact Support")
      expect(page).to have_link("Watch Demo")
    end
  end

  describe "Design and Accessibility" do
    before do
      visit help_center_path
    end

    it "uses consistent design patterns" do
      # Check for consistent use of primary colors and gradients
      expect(page).to have_css('.bg-gradient-to-br.from-slate-900')
      expect(page).to have_css('.text-primary-600')
      expect(page).to have_css('.bg-primary-600')
    end

    it "includes proper Font Awesome icons" do
      expect(page).to have_css('.fas.fa-graduation-cap')
      expect(page).to have_css('.fas.fa-search')
      expect(page).to have_css('.fas.fa-headset')
    end

    it "has responsive design classes" do
      expect(page).to have_css('.container.mx-auto')
      expect(page).to have_css('.grid.md\\:grid-cols-2.lg\\:grid-cols-3')
      expect(page).to have_css('.px-4.sm\\:px-6.lg\\:px-8')
    end

    it "includes animation controllers" do
      expect(page).to have_css('[data-controller="animate"]')
      expect(page).to have_css('[data-animate-animation-value="fadeInUp"]')
    end
  end
end
