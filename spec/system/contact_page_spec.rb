require 'rails_helper'

RSpec.describe "Contact Page", type: :system do
  before do
    driven_by(:rack_test)
  end

  describe "FAQ Accordion" do
    before do
      visit contact_path
    end

    it "displays FAQ section with questions" do
      expect(page).to have_content("Frequently Asked Questions")
      expect(page).to have_content("How quickly can I get started with Data Reflow?")
      expect(page).to have_content("What data sources does Data Reflow support?")
      expect(page).to have_content("Is my data secure with Data Reflow?")
      expect(page).to have_content("Do you offer training and support?")
    end

    it "has proper FAQ structure with data attributes" do
      expect(page).to have_css('[data-controller="faq"]')
      expect(page).to have_css('[data-faq-target="trigger"]', count: 4)
      expect(page).to have_css('[data-faq-target="content"]', count: 4)
      expect(page).to have_css('[data-faq-target="icon"]', count: 4)
    end

    it "has proper ARIA attributes for accessibility" do
      within('[data-controller="faq"]') do
        expect(page).to have_css('button[aria-expanded="false"]', count: 4)
        expect(page).to have_css('button[type="button"]', count: 4)
      end
    end

    it "includes keyboard navigation support" do
      within('[data-controller="faq"]') do
        expect(page).to have_css('button[data-action*="keydown->faq#keydown"]', count: 4)
      end
    end

    it "implements accordion behavior by default" do
      # The FAQ controller should have accordion behavior enabled by default
      # This means only one item can be open at a time
      within('[data-controller="faq"]') do
        # All items should start collapsed
        expect(page).to have_css('button[aria-expanded="false"]', count: 4)
      end
    end

    it "has proper focus management" do
      within('[data-controller="faq"]') do
        buttons = page.all('button[data-faq-target="trigger"]')
        buttons.each do |button|
          expect(button[:class]).to include('focus:outline-none')
          expect(button[:class]).to include('focus:ring-2')
          expect(button[:class]).to include('focus:ring-primary-500')
        end
      end
    end
  end

  describe "Contact Form" do
    before do
      visit contact_path
    end

    it "displays contact form with all required fields" do
      expect(page).to have_field("first_name")
      expect(page).to have_field("last_name") 
      expect(page).to have_field("email")
      expect(page).to have_field("company")
      expect(page).to have_field("subject")
      expect(page).to have_field("message")
    end

    it "has proper form validation attributes" do
      expect(page).to have_css('input[required]', count: 3) # first_name, last_name, email
      expect(page).to have_css('select[required]', count: 1) # subject
      expect(page).to have_css('textarea[required]', count: 1) # message
    end
  end

  describe "Contact Options" do
    before do
      visit contact_path
    end

    it "displays all contact methods" do
      expect(page).to have_content("Schedule a Demo")
      expect(page).to have_content("Sales Inquiry") 
      expect(page).to have_content("Technical Support")
      expect(page).to have_content("Email")
      expect(page).to have_content("Phone")
      expect(page).to have_content("Live Chat")
      expect(page).to have_content("Help Center")
    end

    it "includes contact information" do
      expect(page).to have_link("<EMAIL>")
      expect(page).to have_link("+1 (555) DATA-FLOW")
      expect(page).to have_link("Contact Sales")
      expect(page).to have_link("Get Support")
    end
  end
end
