require 'rails_helper'

RSpec.describe "Analytics Dashboard", type: :system do
  let(:organization) { create(:organization) }
  let(:user) { create(:user, organization: organization) }
  
  before do
    sign_in user
    
    # Create sample data
    @data_sources = create_list(:data_source, 3, :active, organization: organization)
    @data_sources.each do |source|
      # Create successful syncs
      create_list(:sync_log, 3, :completed, data_source: source, organization: organization, started_at: rand(7.days.ago..Time.current))
      # Create failed syncs
      create_list(:sync_log, 1, :failed, data_source: source, organization: organization, started_at: rand(7.days.ago..Time.current))
    end
  end
  
  describe "Dashboard Overview" do
    before do
      visit dashboard_analytics_path
    end
    
    it "displays the analytics dashboard" do
      expect(page).to have_content("Analytics Dashboard")
      expect(page).to have_content("Monitor performance, track trends, and gain insights")
    end
    
    it "shows key metrics cards" do
      expect(page).to have_content("Total Syncs")
      expect(page).to have_content("Success Rate")
      expect(page).to have_content("Data Processed")
      expect(page).to have_content("Avg Duration")
      expect(page).to have_content("Active Sources")
      expect(page).to have_content("Error Rate")
    end
    
    it "displays charts" do
      expect(page).to have_css("#syncTimelineChart")
      expect(page).to have_css("#dataVolumeTrendChart")
      expect(page).to have_css("#sourcePerformanceChart")
      expect(page).to have_css("#errorDistributionChart")
    end
    
    it "shows insights sections" do
      expect(page).to have_content("Top Performing Sources")
      expect(page).to have_content("Sources Needing Attention")
      expect(page).to have_content("Recent Errors")
    end
  end
  
  describe "Date Range Filtering" do
    before do
      visit dashboard_analytics_path
    end
    
    it "allows changing date range" do
      # Click date range dropdown
      find('[data-analytics-dashboard-target="dateRangeLabel"]').ancestor('button').click
      
      within '[data-dropdown-target="menu"]' do
        click_button "Last 7 Days"
      end
      
      expect(current_url).to include("date_range=7_days")
      expect(page).to have_content("Last 7 Days")
    end
    
    it "opens custom date range picker" do
      find('[data-analytics-dashboard-target="dateRangeLabel"]').ancestor('button').click
      
      within '[data-dropdown-target="menu"]' do
        click_button "Custom Range"
      end
      
      expect(page).to have_css('[data-analytics-dashboard-target="customDateModal"]', visible: true)
      expect(page).to have_field("From Date")
      expect(page).to have_field("To Date")
    end
    
    it "applies custom date range" do
      find('[data-analytics-dashboard-target="dateRangeLabel"]').ancestor('button').click
      click_button "Custom Range"
      
      within '[data-analytics-dashboard-target="customDateModal"]' do
        fill_in "From Date", with: 7.days.ago.to_date
        fill_in "To Date", with: Date.current
        click_button "Apply"
      end
      
      expect(current_url).to include("date_range=custom")
      expect(current_url).to include("date_from=")
      expect(current_url).to include("date_to=")
    end
  end
  
  describe "Export Functionality" do
    before do
      visit dashboard_analytics_path
    end
    
    it "shows export options" do
      find('button', text: 'Export').click
      
      within '[data-dropdown-target="menu"]' do
        expect(page).to have_link("Export as CSV")
        expect(page).to have_link("Export as PDF")
        expect(page).to have_link("Export as JSON")
      end
    end
  end
  
  describe "Real-time Updates" do
    before do
      visit dashboard_analytics_path
    end
    
    it "has refresh button" do
      expect(page).to have_button("Refresh")
    end
    
    it "animates refresh icon when clicked", js: true do
      refresh_button = find('button', text: 'Refresh')
      refresh_button.click
      
      expect(page).to have_css('.fa-sync-alt.animate-spin')
    end
  end
  
  describe "Chart Interactions", js: true do
    before do
      visit dashboard_analytics_path
    end
    
    it "displays interactive charts" do
      # Wait for charts to load
      expect(page).to have_css('canvas#syncTimelineChart')
      
      # Charts should be rendered (canvas should have content)
      canvas = find('canvas#syncTimelineChart')
      expect(canvas).to be_present
    end
  end
  
  describe "Performance Indicators" do
    before do
      visit dashboard_analytics_path
    end
    
    it "shows performance badges for metrics" do
      # Success rate badge
      within('.bg-white', text: 'Success Rate') do
        expect(page).to have_css('.rounded-full', text: /Excellent|Good|Needs Attention/)
      end
      
      # Average duration badge
      within('.bg-white', text: 'Avg Duration') do
        expect(page).to have_css('.rounded-full', text: /Fast|Normal|Slow/)
      end
      
      # Error rate badge
      within('.bg-white', text: 'Error Rate') do
        expect(page).to have_css('.rounded-full', text: /Low|Medium|High/)
      end
    end
  end
  
  describe "Responsive Design" do
    it "adapts to mobile view" do
      visit dashboard_analytics_path
      
      # Simulate mobile viewport
      page.driver.browser.manage.window.resize_to(375, 667)
      
      expect(page).to have_css('.grid-cols-1')
      expect(page).to have_content("Analytics Dashboard")
    end
  end
end