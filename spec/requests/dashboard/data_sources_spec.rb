require 'rails_helper'

RSpec.describe "Dashboard::DataSources", type: :request do
  let(:organization) { create(:organization) }
  let(:user) { create(:user, organization: organization) }
  
  before do
    sign_in user
  end
  
  describe "GET /dashboard/data_sources" do
    let!(:data_sources) { create_list(:data_source, 3, organization: organization) }
    let!(:active_source) { create(:data_source, :active, organization: organization) }
    let!(:error_source) { create(:data_source, :with_error, organization: organization) }
    let!(:syncing_source) { create(:data_source, :syncing, organization: organization) }
    
    it "returns http success" do
      get dashboard_data_sources_path
      expect(response).to have_http_status(:success)
    end
    
    it "displays all data sources" do
      get dashboard_data_sources_path
      expect(response.body).to include(*data_sources.map(&:name))
      expect(response.body).to include(active_source.name)
      expect(response.body).to include(error_source.name)
    end
    
    it "shows correct stats" do
      get dashboard_data_sources_path
      expect(response.body).to include("Total Sources")
      expect(response.body).to include("6") # Total count
      expect(response.body).to include("Active")
      expect(response.body).to include("1") # Active count
      expect(response.body).to include("Errors")
      expect(response.body).to include("1") # Error count
    end
    
    it "includes filter controls" do
      get dashboard_data_sources_path
      expect(response.body).to include("All Filters")
      expect(response.body).to include("data-action=\"input->data-sources-manager#search\"")
      expect(response.body).to include("data-action=\"click->data-sources-manager#filterByStatus\"")
    end
    
    it "includes bulk action controls" do
      get dashboard_data_sources_path
      expect(response.body).to include("data-bulk-actions")
      expect(response.body).to include("Select All")
      expect(response.body).to include("Sync Selected")
    end
    
    it "includes data preview modal" do
      get dashboard_data_sources_path
      expect(response.body).to include("data-controller=\"data-preview-modal\"")
      expect(response.body).to include("Data Preview:")
    end
  end
  
  describe "GET /dashboard/data_sources/:id/status.json" do
    let(:data_source) { create(:data_source, organization: organization) }
    
    it "returns status information" do
      get status_dashboard_data_source_path(data_source, format: :json)
      
      expect(response).to have_http_status(:success)
      json = JSON.parse(response.body)
      
      expect(json).to include(
        "id" => data_source.id,
        "status" => data_source.status,
        "formatted_row_count" => data_source.formatted_row_count
      )
    end
  end
  
  describe "GET /dashboard/data_sources/:id/sample_data.json" do
    let(:data_source) { create(:data_source, organization: organization) }
    
    before do
      allow_any_instance_of(DataSource).to receive(:fetch_sample_data).and_return({
        success: true,
        data: [
          { "id" => 1, "name" => "Test Record 1" },
          { "id" => 2, "name" => "Test Record 2" }
        ],
        metadata: { columns: ["id", "name"] }
      })
    end
    
    it "returns sample data in JSON format" do
      get sample_data_dashboard_data_source_path(data_source, format: :json)
      
      expect(response).to have_http_status(:success)
      json = JSON.parse(response.body)
      
      expect(json["success"]).to be true
      expect(json["data"]).to be_an(Array)
      expect(json["data"].length).to eq(2)
      expect(json["columns"]).to eq(["id", "name"])
    end
    
    context "when fetch fails" do
      before do
        allow_any_instance_of(DataSource).to receive(:fetch_sample_data).and_return({
          success: false,
          error: "Connection failed"
        })
      end
      
      it "returns error response" do
        get sample_data_dashboard_data_source_path(data_source, format: :json)
        
        expect(response).to have_http_status(:unprocessable_entity)
        json = JSON.parse(response.body)
        
        expect(json["success"]).to be false
        expect(json["error"]).to eq("Connection failed")
      end
    end
  end
  
  describe "GET /dashboard/data_sources/:id/sample_data.csv" do
    let(:data_source) { create(:data_source, organization: organization) }
    
    before do
      allow_any_instance_of(DataSource).to receive(:fetch_sample_data).and_return({
        success: true,
        data: [
          { "id" => 1, "name" => "Test Record 1" },
          { "id" => 2, "name" => "Test Record 2" }
        ],
        metadata: { columns: ["id", "name"] }
      })
    end
    
    it "returns CSV download" do
      get sample_data_dashboard_data_source_path(data_source, format: :csv)
      
      expect(response).to have_http_status(:success)
      expect(response.content_type).to include("text/csv")
      expect(response.headers["Content-Disposition"]).to include("#{data_source.name.parameterize}_sample")
      
      csv_data = CSV.parse(response.body, headers: true)
      expect(csv_data.headers).to eq(["id", "name"])
      expect(csv_data.length).to eq(2)
    end
  end
  
  describe "Enhanced filtering" do
    let!(:postgresql_source) { create(:data_source, source_type: 'postgresql', organization: organization) }
    let!(:csv_source) { create(:data_source, source_type: 'csv', organization: organization) }
    let!(:api_source) { create(:data_source, source_type: 'api', organization: organization) }
    
    it "shows count for each source type" do
      get dashboard_data_sources_path
      
      expect(response.body).to include("Databases")
      expect(response.body).to include("(1)") # PostgreSQL count
      expect(response.body).to include("Files") 
      expect(response.body).to include("(1)") # CSV count
    end
  end
  
  describe "Performance metrics" do
    it "displays performance card" do
      get dashboard_data_sources_path
      
      expect(response.body).to include("Avg Sync Time")
      expect(response.body).to include("Performance")
      expect(response.body).to include("fa-tachometer-alt")
    end
  end
end