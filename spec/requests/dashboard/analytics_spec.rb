require 'rails_helper'

RSpec.describe "Dashboard::Analytics", type: :request do
  let(:organization) { create(:organization) }
  let(:user) { create(:user, organization: organization) }
  
  before do
    sign_in user
  end
  
  describe "GET /dashboard/analytics" do
    let!(:data_sources) { create_list(:data_source, 3, :active, organization: organization) }
    let!(:sync_logs) do
      data_sources.map do |source|
        create_list(:sync_log, 5, data_source: source, organization: organization, started_at: 1.day.ago)
      end.flatten
    end
    
    it "returns http success" do
      get dashboard_analytics_path
      expect(response).to have_http_status(:success)
    end
    
    it "displays key metrics" do
      get dashboard_analytics_path
      
      expect(response.body).to include("Total Syncs")
      expect(response.body).to include("Success Rate")
      expect(response.body).to include("Data Processed")
      expect(response.body).to include("Avg Duration")
      expect(response.body).to include("Active Sources")
      expect(response.body).to include("Error Rate")
    end
    
    it "includes chart containers" do
      get dashboard_analytics_path
      
      expect(response.body).to include("syncTimelineChart")
      expect(response.body).to include("dataVolumeTrendChart")
      expect(response.body).to include("sourcePerformanceChart")
      expect(response.body).to include("errorDistributionChart")
    end
    
    it "includes date range controls" do
      get dashboard_analytics_path
      
      expect(response.body).to include("Last 24 Hours")
      expect(response.body).to include("Last 7 Days")
      expect(response.body).to include("Last 30 Days")
      expect(response.body).to include("Custom Range")
    end
    
    context "with date range parameter" do
      it "respects the date range filter" do
        get dashboard_analytics_path(date_range: '7_days')
        
        expect(response).to have_http_status(:success)
        expect(response.body).to include("Last 7 Days")
      end
    end
    
    context "with JSON format" do
      it "returns analytics data as JSON" do
        get dashboard_analytics_path(format: :json)
        
        expect(response).to have_http_status(:success)
        json = JSON.parse(response.body)
        
        expect(json).to have_key("metrics")
        expect(json).to have_key("charts")
        expect(json).to have_key("insights")
        expect(json).to have_key("real_time")
      end
    end
  end
  
  describe "GET /dashboard/analytics/sync_performance" do
    let(:data_source) { create(:data_source, organization: organization) }
    let!(:sync_logs) { create_list(:sync_log, 10, data_source: data_source, organization: organization) }
    
    it "returns performance data for all sources" do
      get sync_performance_dashboard_analytics_path
      
      expect(response).to have_http_status(:success)
      json = JSON.parse(response.body)
      
      expect(json).to have_key("by_source_type")
      expect(json).to have_key("by_hour_of_day")
      expect(json).to have_key("by_day_of_week")
    end
    
    it "returns performance data for specific source" do
      get sync_performance_dashboard_analytics_path(source_id: data_source.id)
      
      expect(response).to have_http_status(:success)
      json = JSON.parse(response.body)
      
      expect(json).to have_key("hourly_syncs")
      expect(json).to have_key("success_trend")
      expect(json).to have_key("duration_trend")
      expect(json).to have_key("row_count_trend")
    end
  end
  
  describe "GET /dashboard/analytics/data_insights" do
    it "returns data insights" do
      get data_insights_dashboard_analytics_path
      
      expect(response).to have_http_status(:success)
      json = JSON.parse(response.body)
      
      expect(json).to have_key("growth_rate")
      expect(json).to have_key("peak_sync_times")
      expect(json).to have_key("source_reliability")
      expect(json).to have_key("recommendations")
    end
  end
  
  describe "GET /dashboard/analytics/export" do
    context "CSV format" do
      it "exports analytics report as CSV" do
        get export_dashboard_analytics_path(format_type: 'csv')
        
        expect(response).to have_http_status(:success)
        expect(response.content_type).to include("text/csv")
        expect(response.headers["Content-Disposition"]).to include("analytics_report")
      end
    end
    
    context "JSON format" do
      it "exports analytics report as JSON" do
        get export_dashboard_analytics_path(format_type: 'json')
        
        expect(response).to have_http_status(:success)
        expect(response.content_type).to include("application/json")
        expect(response.headers["Content-Disposition"]).to include("analytics_report")
      end
    end
  end
  
  describe "Caching" do
    it "caches expensive calculations" do
      expect(Rails.cache).to receive(:fetch).at_least(:once).and_call_original
      
      get dashboard_analytics_path
      expect(response).to have_http_status(:success)
    end
    
    it "uses different cache keys for different date ranges" do
      get dashboard_analytics_path(date_range: '7_days')
      cache_key_7_days = controller.send(:analytics_cache_key, 'total_syncs')
      
      get dashboard_analytics_path(date_range: '30_days')
      cache_key_30_days = controller.send(:analytics_cache_key, 'total_syncs')
      
      expect(cache_key_7_days).not_to eq(cache_key_30_days)
    end
  end
  
  describe "Real-time updates" do
    it "includes ActionCable subscription setup" do
      get dashboard_analytics_path
      
      expect(response.body).to include("analytics-dashboard")
      expect(response.body).to include("setupWebSocket")
    end
  end
end