require 'rails_helper'

RSpec.describe "Landings", type: :request do
  describe "GET /" do
    it "returns http success" do
      get root_path
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET /about" do
    it "returns http success" do
      get about_path
      expect(response).to have_http_status(:success)
    end

    it "renders the about page with correct title" do
      get about_path
      expect(response.body).to include("About Data Reflow - Enterprise Analytics Platform")
    end

    it "includes company story content" do
      get about_path
      expect(response.body).to include("Our Story")
      expect(response.body).to include("Founded in 2023")
    end

    it "includes core values section" do
      get about_path
      expect(response.body).to include("Our Core Values")
      expect(response.body).to include("Accessibility")
      expect(response.body).to include("Innovation")
    end
  end

  describe "GET /contact" do
    it "returns http success" do
      get contact_path
      expect(response).to have_http_status(:success)
    end

    it "renders the contact page with correct title" do
      get contact_path
      expect(response.body).to include("Contact Data Reflow - Get in Touch")
    end

    it "includes contact options" do
      get contact_path
      expect(response.body).to include("How Can We Help You?")
      expect(response.body).to include("Schedule a Demo")
      expect(response.body).to include("Sales Inquiry")
      expect(response.body).to include("Technical Support")
    end

    it "includes contact form" do
      get contact_path
      expect(response.body).to include("Send Us a Message")
      expect(response.body).to include('name="first_name"')
      expect(response.body).to include('name="email"')
      expect(response.body).to include('name="message"')
    end

    it "includes FAQ section" do
      get contact_path
      expect(response.body).to include("Frequently Asked Questions")
      expect(response.body).to include("How quickly can I get started")
    end

    it "includes FAQ accordion functionality" do
      get contact_path
      expect(response.body).to include('data-controller="faq"')
      expect(response.body).to include('data-action="click->faq#toggle')
      expect(response.body).to include('data-faq-target="trigger"')
      expect(response.body).to include('data-faq-target="content"')
      expect(response.body).to include('data-faq-target="icon"')
    end

    it "includes proper ARIA attributes for accessibility" do
      get contact_path
      expect(response.body).to include('aria-expanded="false"')
      expect(response.body).to include('type="button"')
    end
  end

  describe "GET /help_center" do
    it "returns http success" do
      get help_center_path
      expect(response).to have_http_status(:success)
    end

    it "renders the help center page with correct title" do
      get help_center_path
      expect(response.body).to include("Help Center - Data Reflow Support &amp; Resources")
    end

    it "includes search functionality" do
      get help_center_path
      expect(response.body).to include("Search for help articles")
      expect(response.body).to include('data-controller="search"')
    end

    it "includes popular help topics section" do
      get help_center_path
      expect(response.body).to include("Popular Help Topics")
      expect(response.body).to include("Getting Started")
      expect(response.body).to include("Data Sources")
      expect(response.body).to include("Analytics & Reports")
    end

    it "includes knowledge base articles" do
      get help_center_path
      expect(response.body).to include("Knowledge Base")
      expect(response.body).to include("Featured Articles")
      expect(response.body).to include("Getting Started with Data Reflow")
    end

    it "includes user guides and tutorials section" do
      get help_center_path
      expect(response.body).to include("User Guides & Tutorials")
      expect(response.body).to include("Video Tutorials")
      expect(response.body).to include("Interactive Demos")
      expect(response.body).to include("Live Webinars")
    end

    it "includes contact support options" do
      get help_center_path
      expect(response.body).to include("Still Need Help?")
      expect(response.body).to include("Live Chat")
      expect(response.body).to include("Email Support")
      expect(response.body).to include("Phone Support")
    end

    it "includes FAQ section with accordion functionality" do
      get help_center_path
      expect(response.body).to include("Frequently Asked Questions")
      expect(response.body).to include('data-controller="faq"')
      expect(response.body).to include("How do I connect my first data source?")
    end
  end

end
