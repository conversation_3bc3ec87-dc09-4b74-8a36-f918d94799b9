require 'rails_helper'

RSpec.describe MonitoringRule, type: :model do
  describe 'associations' do
    it { should belong_to(:organization) }
    it { should belong_to(:pipeline).optional }
    it { should have_many(:alerts).dependent(:destroy) }
  end

  describe 'validations' do
    it { should validate_presence_of(:name) }
    it { should validate_presence_of(:rule_type) }
    it { should validate_presence_of(:threshold_value) }
    it { should validate_inclusion_of(:rule_type).in_array(%w[threshold rate_limit anomaly availability]) }
    it { should validate_inclusion_of(:severity).in_array(%w[critical warning info]) }
    it { should validate_numericality_of(:threshold_value) }
  end

  describe 'scopes' do
    let(:organization) { create(:organization) }
    let!(:enabled_rule) { create(:monitoring_rule, organization: organization, enabled: true) }
    let!(:disabled_rule) { create(:monitoring_rule, organization: organization, enabled: false) }
    let!(:critical_rule) { create(:monitoring_rule, organization: organization, severity: 'critical') }

    describe '.enabled' do
      it 'returns only enabled rules' do
        expect(MonitoringRule.enabled).to include(enabled_rule)
        expect(MonitoringRule.enabled).not_to include(disabled_rule)
      end
    end

    describe '.by_severity' do
      it 'returns rules filtered by severity' do
        expect(MonitoringRule.by_severity('critical')).to include(critical_rule)
        expect(MonitoringRule.by_severity('critical')).not_to include(enabled_rule)
      end
    end
  end

  describe '#check_condition' do
    let(:rule) { create(:monitoring_rule, rule_type: 'threshold', metric_name: 'execution_time', threshold_value: 60, comparison_operator: 'greater_than') }

    context 'with threshold rule' do
      it 'returns true when threshold is exceeded' do
        expect(rule.check_condition(70)).to be true
      end

      it 'returns false when threshold is not exceeded' do
        expect(rule.check_condition(50)).to be false
      end
    end

    context 'with different operators' do
      it 'handles less_than operator' do
        rule.update(comparison_operator: 'less_than')
        expect(rule.check_condition(50)).to be true
        expect(rule.check_condition(70)).to be false
      end

      it 'handles equals operator' do
        rule.update(comparison_operator: 'equals')
        expect(rule.check_condition(60)).to be true
        expect(rule.check_condition(61)).to be false
      end
    end
  end

  describe '#trigger_alert!' do
    let(:rule) { create(:monitoring_rule) }
    let(:pipeline) { create(:pipeline) }

    it 'creates a new alert' do
      expect {
        rule.trigger_alert!('High CPU usage detected', pipeline: pipeline)
      }.to change(Alert, :count).by(1)
    end

    it 'sets correct alert attributes' do
      alert = rule.trigger_alert!('Test message', pipeline: pipeline)
      
      expect(alert.monitoring_rule).to eq(rule)
      expect(alert.pipeline).to eq(pipeline)
      expect(alert.message).to eq('Test message')
      expect(alert.severity).to eq(rule.severity)
      expect(alert.status).to eq('triggered')
    end

    it 'enqueues notification job' do
      expect {
        rule.trigger_alert!('Test message')
      }.to have_enqueued_job(AlertNotificationJob)
    end
  end

  describe '#active_alerts' do
    let(:rule) { create(:monitoring_rule) }
    let!(:triggered_alert) { create(:alert, monitoring_rule: rule, status: 'triggered') }
    let!(:acknowledged_alert) { create(:alert, monitoring_rule: rule, status: 'acknowledged') }
    let!(:resolved_alert) { create(:alert, monitoring_rule: rule, status: 'resolved') }

    it 'returns only triggered and acknowledged alerts' do
      active = rule.active_alerts
      expect(active).to include(triggered_alert, acknowledged_alert)
      expect(active).not_to include(resolved_alert)
    end
  end
end