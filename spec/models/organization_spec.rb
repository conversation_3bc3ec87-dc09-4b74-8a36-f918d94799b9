require 'rails_helper'

RSpec.describe Organization, type: :model do
  describe 'validations' do
    it { should validate_presence_of(:name) }
    it { should validate_inclusion_of(:plan).in_array(%w[starter professional enterprise]) }
  end

  describe 'associations' do
    it { should have_many(:users).dependent(:destroy) }
  end

  describe 'defaults' do
    it 'sets default plan to starter' do
      org = Organization.new(name: 'Test Org')
      expect(org.plan).to eq('starter')
    end
  end

  describe '#monthly_data_limit' do
    context 'starter plan' do
      let(:org) { Organization.new(plan: 'starter') }
      it 'returns 10 million' do
        expect(org.monthly_data_limit).to eq(10_000_000)
      end
    end

    context 'professional plan' do
      let(:org) { Organization.new(plan: 'professional') }
      it 'returns 100 million' do
        expect(org.monthly_data_limit).to eq(100_000_000)
      end
    end

    context 'enterprise plan' do
      let(:org) { Organization.new(plan: 'enterprise') }
      it 'returns infinity' do
        expect(org.monthly_data_limit).to eq(Float::INFINITY)
      end
    end
  end

  describe '#usage_percentage' do
    let(:org) { Organization.create!(name: 'Test Org', plan: 'starter', current_month_rows: 5_000_000) }
    
    it 'calculates usage percentage correctly' do
      expect(org.usage_percentage).to eq(50)
    end
  end
end