require 'rails_helper'

RSpec.describe User, type: :model do
  describe 'validations' do
    subject { User.new(email: '<EMAIL>', password: 'password123', organization: Organization.new(name: 'Test')) }
    
    it { should validate_presence_of(:email) }
    it { should validate_inclusion_of(:role).in_array(%w[admin member viewer]) }
    
    context 'email uniqueness' do
      let(:organization) { Organization.create!(name: 'Test Org') }
      let!(:existing_user) { User.create!(email: '<EMAIL>', password: 'password123', organization: organization) }
      
      it 'validates uniqueness of email scoped to organization' do
        new_user = User.new(email: '<EMAIL>', password: 'password123', organization: organization)
        expect(new_user).not_to be_valid
        expect(new_user.errors[:email]).to include('has already been taken')
      end
      
      it 'allows same email in different organization' do
        other_org = Organization.create!(name: 'Other Org')
        ActsAsTenant.with_tenant(other_org) do
          new_user = User.new(email: '<EMAIL>', password: 'password123', organization: other_org)
          expect(new_user).to be_valid
        end
      end
    end
  end

  describe 'associations' do
    it { should belong_to(:organization) }
  end

  describe 'defaults' do
    it 'sets default role to member' do
      user = User.new
      expect(user.role).to eq('member')
    end
  end

  describe '#full_name' do
    context 'with both names' do
      let(:user) { User.new(first_name: 'John', last_name: 'Doe') }
      it 'returns full name' do
        expect(user.full_name).to eq('John Doe')
      end
    end

    context 'with only first name' do
      let(:user) { User.new(first_name: 'John') }
      it 'returns first name' do
        expect(user.full_name).to eq('John')
      end
    end

    context 'with no names' do
      let(:user) { User.new(email: '<EMAIL>') }
      it 'returns email' do
        expect(user.full_name).to eq('<EMAIL>')
      end
    end
  end

  describe 'devise modules' do
    it 'has devise password fields' do
      expect(User.column_names).to include('encrypted_password')
    end
    
    it 'has devise recoverable fields' do
      expect(User.column_names).to include('reset_password_token')
      expect(User.column_names).to include('reset_password_sent_at')
    end
    
    it 'has devise rememberable fields' do
      expect(User.column_names).to include('remember_created_at')
    end
    
    it 'has devise trackable fields' do
      expect(User.column_names).to include('sign_in_count')
      expect(User.column_names).to include('current_sign_in_at')
      expect(User.column_names).to include('last_sign_in_at')
      expect(User.column_names).to include('current_sign_in_ip')
      expect(User.column_names).to include('last_sign_in_ip')
    end
  end
end