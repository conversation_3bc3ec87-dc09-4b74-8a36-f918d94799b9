require 'rails_helper'

RSpec.describe Alert, type: :model do
  describe 'associations' do
    it { should belong_to(:monitoring_rule) }
    it { should belong_to(:organization) }
    it { should belong_to(:pipeline).optional }
    it { should belong_to(:acknowledged_by).class_name('User').optional }
    it { should belong_to(:resolved_by).class_name('User').optional }
  end

  describe 'validations' do
    it { should validate_presence_of(:message) }
    it { should validate_presence_of(:severity) }
    it { should validate_presence_of(:status) }
    it { should validate_inclusion_of(:severity).in_array(%w[critical warning info]) }
    it { should validate_inclusion_of(:status).in_array(%w[triggered acknowledged resolved]) }
  end

  describe 'scopes' do
    let!(:triggered_alert) { create(:alert, status: 'triggered') }
    let!(:acknowledged_alert) { create(:alert, status: 'acknowledged') }
    let!(:resolved_alert) { create(:alert, status: 'resolved') }
    let!(:critical_alert) { create(:alert, severity: 'critical') }

    describe '.active' do
      it 'returns only triggered and acknowledged alerts' do
        active = Alert.active
        expect(active).to include(triggered_alert, acknowledged_alert)
        expect(active).not_to include(resolved_alert)
      end
    end

    describe '.resolved' do
      it 'returns only resolved alerts' do
        expect(Alert.resolved).to include(resolved_alert)
        expect(Alert.resolved).not_to include(triggered_alert, acknowledged_alert)
      end
    end

    describe '.by_severity' do
      it 'returns alerts filtered by severity' do
        expect(Alert.by_severity('critical')).to include(critical_alert)
        expect(Alert.by_severity('critical')).not_to include(triggered_alert)
      end
    end

    describe '.recent' do
      it 'orders alerts by triggered_at descending' do
        older_alert = create(:alert, triggered_at: 2.days.ago)
        newer_alert = create(:alert, triggered_at: 1.hour.ago)
        
        expect(Alert.recent.first).to eq(newer_alert)
        expect(Alert.recent.last).to eq(older_alert)
      end
    end
  end

  describe '#acknowledge!' do
    let(:user) { create(:user) }
    let(:alert) { create(:alert, status: 'triggered') }

    it 'updates status to acknowledged' do
      alert.acknowledge!(user)
      expect(alert.reload.status).to eq('acknowledged')
    end

    it 'sets acknowledged_by and acknowledged_at' do
      alert.acknowledge!(user)
      alert.reload
      expect(alert.acknowledged_by).to eq(user)
      expect(alert.acknowledged_at).to be_present
    end

    it 'broadcasts update via ActionCable' do
      expect(ActionCable.server).to receive(:broadcast)
      alert.acknowledge!(user)
    end
  end

  describe '#resolve!' do
    let(:user) { create(:user) }
    let(:alert) { create(:alert, status: 'acknowledged') }

    it 'updates status to resolved' do
      alert.resolve!(user)
      expect(alert.reload.status).to eq('resolved')
    end

    it 'sets resolved_by and resolved_at' do
      alert.resolve!(user)
      alert.reload
      expect(alert.resolved_by).to eq(user)
      expect(alert.resolved_at).to be_present
    end

    it 'broadcasts update via ActionCable' do
      expect(ActionCable.server).to receive(:broadcast)
      alert.resolve!(user)
    end
  end

  describe '#duration' do
    let(:alert) { create(:alert, triggered_at: 2.hours.ago) }

    context 'when alert is not resolved' do
      it 'returns duration from triggered_at to now' do
        expect(alert.duration).to be_within(1.second).of(2.hours)
      end
    end

    context 'when alert is resolved' do
      before do
        alert.update(resolved_at: 1.hour.ago)
      end

      it 'returns duration from triggered_at to resolved_at' do
        expect(alert.duration).to be_within(1.second).of(1.hour)
      end
    end
  end

  describe 'callbacks' do
    let(:monitoring_rule) { create(:monitoring_rule) }
    let(:alert) { build(:alert, monitoring_rule: monitoring_rule) }

    describe 'before_validation' do
      it 'sets organization from monitoring_rule' do
        alert.save
        expect(alert.organization).to eq(monitoring_rule.organization)
      end

      it 'sets severity from monitoring_rule if not provided' do
        alert.severity = nil
        alert.save
        expect(alert.severity).to eq(monitoring_rule.severity)
      end
    end

    describe 'after_create' do
      it 'enqueues notification job' do
        expect {
          alert.save
        }.to have_enqueued_job(AlertNotificationJob)
      end

      it 'broadcasts new alert via ActionCable' do
        expect(ActionCable.server).to receive(:broadcast)
        alert.save
      end
    end
  end
end