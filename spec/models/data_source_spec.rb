require 'rails_helper'

RSpec.describe DataSource, type: :model do
  let(:organization) { create(:organization) }
  let(:data_source) { build(:data_source, organization: organization) }
  
  describe 'associations' do
    it { should belong_to(:organization) }
    it { should have_many(:integrations).dependent(:destroy) }
    it { should have_many(:pipelines).dependent(:destroy) }
  end
  
  describe 'validations' do
    it { should validate_presence_of(:name) }
    it { should validate_presence_of(:source_type) }
    it { should validate_presence_of(:status) }
    
    it 'validates uniqueness of name within organization' do
      create(:data_source, name: 'Test Source', organization: organization)
      duplicate = build(:data_source, name: 'Test Source', organization: organization)
      expect(duplicate).not_to be_valid
    end
    
    it 'allows same name in different organizations' do
      create(:data_source, name: 'Test Source', organization: organization)
      other_org = create(:organization)
      duplicate = build(:data_source, name: 'Test Source', organization: other_org)
      expect(duplicate).to be_valid
    end
    
    it { should validate_inclusion_of(:source_type).in_array(DataSource::SOURCE_TYPES) }
    it { should validate_inclusion_of(:status).in_array(DataSource::STATUSES) }
    it { should validate_inclusion_of(:sync_frequency).in_array(DataSource::SYNC_FREQUENCIES).allow_nil }
  end
  
  describe 'scopes' do
    let!(:active_source) { create(:data_source, status: 'active', last_sync_at: 30.minutes.ago, organization: organization) }
    let!(:inactive_source) { create(:data_source, status: 'inactive', organization: organization) }
    let!(:error_source) { create(:data_source, status: 'error', organization: organization) }
    let!(:old_sync_source) { create(:data_source, status: 'active', last_sync_at: 2.hours.ago, organization: organization) }
    let!(:never_synced_source) { create(:data_source, status: 'active', last_sync_at: nil, organization: organization) }
    
    describe '.active' do
      it 'returns only active sources' do
        expect(DataSource.active).to contain_exactly(active_source, old_sync_source, never_synced_source)
      end
    end
    
    describe '.inactive' do
      it 'returns only inactive sources' do
        expect(DataSource.inactive).to contain_exactly(inactive_source)
      end
    end
    
    describe '.with_errors' do
      it 'returns only sources with errors' do
        expect(DataSource.with_errors).to contain_exactly(error_source)
      end
    end
    
    describe '.needs_sync' do
      it 'returns active sources that need syncing' do
        expect(DataSource.needs_sync).to contain_exactly(old_sync_source, never_synced_source)
      end
    end
  end
  
  describe 'instance methods' do
    describe '#active?' do
      it 'returns true when status is active' do
        data_source.status = 'active'
        expect(data_source.active?).to be true
      end
      
      it 'returns false when status is not active' do
        data_source.status = 'inactive'
        expect(data_source.active?).to be false
      end
    end
    
    describe '#can_sync?' do
      it 'returns true when active and not syncing' do
        data_source.status = 'active'
        expect(data_source.can_sync?).to be true
      end
      
      it 'returns false when inactive' do
        data_source.status = 'inactive'
        expect(data_source.can_sync?).to be false
      end
      
      it 'returns false when syncing' do
        data_source.status = 'syncing'
        expect(data_source.can_sync?).to be false
      end
    end
    
    describe '#database_source?' do
      it 'returns true for database types' do
        %w[postgresql mysql sqlite].each do |type|
          data_source.source_type = type
          expect(data_source.database_source?).to be true
        end
      end
      
      it 'returns false for non-database types' do
        data_source.source_type = 'csv'
        expect(data_source.database_source?).to be false
      end
    end
    
    describe '#connection_settings' do
      it 'returns parsed JSON settings' do
        settings = { host: 'localhost', port: 5432 }
        data_source.connection_config = settings.to_json
        expect(data_source.connection_settings).to eq(settings.stringify_keys)
      end
      
      it 'returns empty hash for blank config' do
        data_source.connection_config = nil
        expect(data_source.connection_settings).to eq({})
      end
      
      it 'returns empty hash for invalid JSON' do
        data_source.connection_config = 'invalid json'
        expect(data_source.connection_settings).to eq({})
      end
    end
    
    describe '#update_connection_settings' do
      it 'updates connection config with JSON' do
        settings = { host: 'localhost', port: 5432 }
        data_source.save!
        data_source.update_connection_settings(settings)
        expect(data_source.reload.connection_settings).to eq(settings.stringify_keys)
      end
    end
    
    describe '#formatted_row_count' do
      it 'returns formatted number with delimiter' do
        data_source.row_count = 1234567
        expect(data_source.formatted_row_count).to eq('1,234,567')
      end
      
      it 'returns "No data" for zero rows' do
        data_source.row_count = 0
        expect(data_source.formatted_row_count).to eq('No data')
      end
      
      it 'returns "No data" for nil rows' do
        data_source.row_count = nil
        expect(data_source.formatted_row_count).to eq('No data')
      end
    end
    
    describe '#source_type_text' do
      it 'returns human-readable source type' do
        data_source.source_type = 'postgresql'
        expect(data_source.source_type_text).to eq('PostgreSQL')
        
        data_source.source_type = 'csv'
        expect(data_source.source_type_text).to eq('CSV File')
      end
    end
  end
  
  describe 'callbacks' do
    describe 'before_validation' do
      it 'normalizes name by stripping whitespace' do
        data_source.name = '  Test Source  '
        data_source.valid?
        expect(data_source.name).to eq('Test Source')
      end
    end
    
    describe 'after_update' do
      it 'clears error message when status changes from error' do
        data_source.status = 'error'
        data_source.error_message = 'Connection failed'
        data_source.save!
        
        data_source.update!(status: 'active')
        expect(data_source.error_message).to be_nil
      end
      
      it 'keeps error message when status remains error' do
        data_source.status = 'error'
        data_source.error_message = 'Connection failed'
        data_source.save!
        
        data_source.update!(name: 'New Name')
        expect(data_source.error_message).to eq('Connection failed')
      end
    end
  end
  
  describe 'encryption' do
    it 'encrypts connection_config' do
      settings = { password: 'secret123' }.to_json
      data_source.connection_config = settings
      data_source.save!
      
      # The encrypted value should not equal the plain text
      expect(data_source.connection_config_before_type_cast).not_to eq(settings)
      
      # But we should be able to read it back
      expect(data_source.connection_config).to eq(settings)
    end
  end
  
  describe '#sync_now!' do
    let(:data_source) { create(:data_source, :postgresql, organization: organization, status: 'active') }
    let(:connector) { instance_double(Connectors::PostgresqlConnector) }
    
    before do
      allow(Connectors::PostgresqlConnector).to receive(:new).and_return(connector)
    end
    
    context 'when sync is successful' do
      before do
        allow(connector).to receive(:sync).and_return({
          success: true,
          row_count: 100,
          metadata: { tables: ['users', 'posts'] }
        })
      end
      
      it 'creates a sync log' do
        expect {
          data_source.sync_now!
        }.to change { data_source.sync_logs.count }.by(1)
      end
      
      it 'sets sync log to completed status' do
        data_source.sync_now!
        sync_log = data_source.sync_logs.last
        
        expect(sync_log.status).to eq('completed')
        expect(sync_log.records_processed).to eq(100)
        expect(sync_log.records_imported).to eq(100)
        expect(sync_log.completed_at).to be_present
        expect(sync_log.duration_seconds).to be > 0
      end
      
      it 'updates data source status and metadata' do
        data_source.sync_now!
        
        expect(data_source.status).to eq('active')
        expect(data_source.row_count).to eq(100)
        expect(data_source.last_sync_at).to be_present
        expect(data_source.error_message).to be_nil
      end
    end
    
    context 'when sync fails' do
      before do
        allow(connector).to receive(:sync).and_return({
          success: false,
          error: 'Connection timeout',
          error_details: { host: 'localhost', port: 5432 }
        })
      end
      
      it 'creates a sync log with failed status' do
        data_source.sync_now!
        sync_log = data_source.sync_logs.last
        
        expect(sync_log.status).to eq('failed')
        expect(sync_log.error_message).to eq('Connection timeout')
        expect(sync_log.error_details).to eq({ 'host' => 'localhost', 'port' => 5432 })
        expect(sync_log.completed_at).to be_present
      end
      
      it 'updates data source to error status' do
        data_source.sync_now!
        
        expect(data_source.status).to eq('error')
        expect(data_source.error_message).to eq('Connection timeout')
      end
    end
    
    context 'when sync raises an exception' do
      before do
        allow(connector).to receive(:sync).and_raise(StandardError, 'Unexpected error')
      end
      
      it 'creates a sync log with failed status' do
        data_source.sync_now!
        sync_log = data_source.sync_logs.last
        
        expect(sync_log.status).to eq('failed')
        expect(sync_log.error_message).to eq('Unexpected error')
        expect(sync_log.error_details['backtrace']).to be_present
      end
      
      it 'updates data source to error status' do
        data_source.sync_now!
        
        expect(data_source.status).to eq('error')
        expect(data_source.error_message).to eq('Unexpected error')
      end
    end
    
    context 'when source cannot sync' do
      before do
        data_source.update!(status: 'inactive')
      end
      
      it 'does not create a sync log' do
        expect {
          data_source.sync_now!
        }.not_to change { data_source.sync_logs.count }
      end
      
      it 'returns error result' do
        result = data_source.sync_now!
        
        expect(result[:success]).to be false
        expect(result[:error]).to eq('Cannot sync inactive source')
      end
    end
    
    context 'with CSV connector' do
      let(:data_source) { create(:data_source, :csv, organization: organization, status: 'active') }
      let(:csv_connector) { instance_double(Connectors::CsvConnector) }
      
      before do
        allow(Connectors::CsvConnector).to receive(:new).and_return(csv_connector)
        allow(csv_connector).to receive(:is_a?).with(Connectors::CsvConnector).and_return(true)
        allow(csv_connector).to receive(:sync).and_return({
          success: true,
          row_count: 50,
          metadata: { file_path: '/path/to/file.csv' }
        })
      end
      
      it 'does not create additional sync log (CSV handles its own)' do
        # CSV connector creates its own sync log, so the one created by sync_now! should be destroyed
        expect {
          data_source.sync_now!
        }.not_to change { data_source.sync_logs.count }
      end
    end
  end
end