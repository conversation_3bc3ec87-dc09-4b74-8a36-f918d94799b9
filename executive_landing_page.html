<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DataReflow - Transform Your Business with Data-Driven Insights | SME Data Platform</title>
  <meta name="description" content="DataReflow helps SMEs unlock 10x ROI through automated data integration and real-time business intelligence. Turn scattered data into strategic insights in 30 minutes.">
  <meta name="keywords" content="SME data platform, business intelligence, ROI analytics, data integration, executive dashboard">
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://datareflow.com/">
  <meta property="og:title" content="DataReflow - Transform Your Business with Data-Driven Insights">
  <meta property="og:description" content="SME data platform delivering 10x ROI through automated analytics">
  <meta property="og:image" content="https://datareflow.com/images/og-executive.jpg">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://datareflow.com/">
  <meta property="twitter:title" content="DataReflow - Transform Your Business with Data-Driven Insights">
  <meta property="twitter:description" content="SME data platform delivering 10x ROI through automated analytics">
  <meta property="twitter:image" content="https://datareflow.com/images/og-executive.jpg">

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "DataReflow",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web",
    "description": "Data integration and analytics platform for SMEs",
    "offers": {
      "@type": "Offer",
      "price": "149",
      "priceCurrency": "USD"
    }
  }
  </script>

  <style>
    :root {
      --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      --glass-bg: rgba(255, 255, 255, 0.1);
      --glass-border: rgba(255, 255, 255, 0.2);
      --text-primary: #1a202c;
      --text-secondary: #4a5568;
      --text-light: #718096;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6;
      color: var(--text-primary);
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }

    .glass-morphism {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 20px;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    /* Header */
    .header {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(20px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding: 1rem 0;
      transition: all 0.3s ease;
    }

    .nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .logo {
      font-size: 1.8rem;
      font-weight: 700;
      color: white;
      text-decoration: none;
      background: linear-gradient(45deg, #fff, #f0f0f0);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .nav-links {
      display: flex;
      list-style: none;
      gap: 2rem;
    }

    .nav-links a {
      color: rgba(255, 255, 255, 0.9);
      text-decoration: none;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .nav-links a:hover {
      color: white;
      text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
    }

    .cta-button {
      background: var(--success-gradient);
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 50px;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.3s ease;
      border: none;
      cursor: pointer;
      display: inline-block;
    }

    .cta-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }

    /* Hero Section */
    .hero {
      padding: 8rem 0 6rem;
      text-align: center;
      color: white;
    }

    .hero h1 {
      font-size: 3.5rem;
      font-weight: 800;
      line-height: 1.1;
      margin-bottom: 1.5rem;
      background: linear-gradient(135deg, #fff 0%, #f0f0f0 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .hero .subtitle {
      font-size: 1.4rem;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 2rem;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
    }

    .hero-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 2rem;
      margin: 3rem 0;
    }

    .stat-item {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 15px;
      padding: 2rem;
      text-align: center;
    }

    .stat-number {
      font-size: 2.5rem;
      font-weight: 800;
      color: #4facfe;
      display: block;
    }

    .stat-label {
      color: rgba(255, 255, 255, 0.8);
      font-size: 0.9rem;
      margin-top: 0.5rem;
    }

    /* Main Content */
    .main-content {
      background: white;
      border-radius: 30px 30px 0 0;
      margin-top: 4rem;
      padding: 4rem 0;
    }

    .section {
      padding: 4rem 0;
    }

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      text-align: center;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    .section-subtitle {
      font-size: 1.2rem;
      color: var(--text-secondary);
      text-align: center;
      max-width: 600px;
      margin: 0 auto 3rem;
    }

    /* Features Grid */
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
      margin-top: 3rem;
    }

    .feature-card {
      background: white;
      border-radius: 20px;
      padding: 2.5rem;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .feature-icon {
      width: 60px;
      height: 60px;
      background: var(--primary-gradient);
      border-radius: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1.5rem;
      font-size: 1.5rem;
      color: white;
    }

    .feature-title {
      font-size: 1.4rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    .feature-description {
      color: var(--text-secondary);
      line-height: 1.6;
    }

    /* ROI Calculator */
    .roi-calculator {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 20px;
      padding: 3rem;
      margin: 4rem 0;
      text-align: center;
    }

    .calculator-inputs {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin: 2rem 0;
    }

    .input-group {
      text-align: left;
    }

    .input-group label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 600;
      color: var(--text-primary);
    }

    .input-group input {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #e2e8f0;
      border-radius: 10px;
      font-size: 1rem;
    }

    .roi-result {
      background: var(--success-gradient);
      color: white;
      padding: 2rem;
      border-radius: 15px;
      margin-top: 2rem;
    }

    /* Testimonials */
    .testimonials {
      background: #f8fafc;
    }

    .testimonial-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
    }

    .testimonial-card {
      background: white;
      padding: 2.5rem;
      border-radius: 20px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .testimonial-text {
      font-style: italic;
      margin-bottom: 1.5rem;
      color: var(--text-secondary);
      font-size: 1.1rem;
    }

    .testimonial-author {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .author-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: var(--primary-gradient);
    }

    .author-info h4 {
      font-weight: 600;
      color: var(--text-primary);
    }

    .author-info p {
      color: var(--text-light);
      font-size: 0.9rem;
    }

    /* Pricing */
    .pricing-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin-top: 3rem;
    }

    .pricing-card {
      background: white;
      border-radius: 20px;
      padding: 2.5rem;
      text-align: center;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(0, 0, 0, 0.05);
      position: relative;
    }

    .pricing-card.featured {
      border: 2px solid #667eea;
      transform: scale(1.05);
    }

    .pricing-badge {
      position: absolute;
      top: -10px;
      left: 50%;
      transform: translateX(-50%);
      background: var(--primary-gradient);
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 600;
    }

    .price {
      font-size: 3rem;
      font-weight: 800;
      color: var(--text-primary);
    }

    .price-period {
      color: var(--text-light);
      font-size: 1rem;
    }

    .features-list {
      list-style: none;
      margin: 2rem 0;
      text-align: left;
    }

    .features-list li {
      padding: 0.5rem 0;
      position: relative;
      padding-left: 1.5rem;
    }

    .features-list li:before {
      content: "✓";
      position: absolute;
      left: 0;
      color: #4facfe;
      font-weight: bold;
    }

    /* CTA Section */
    .cta-section {
      background: var(--primary-gradient);
      color: white;
      text-align: center;
      padding: 6rem 0;
    }

    .cta-section h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
    }

    .cta-section p {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      color: rgba(255, 255, 255, 0.9);
    }

    .cta-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    .btn-secondary {
      background: rgba(255, 255, 255, 0.2);
      border: 2px solid white;
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 50px;
      text-decoration: none;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .btn-secondary:hover {
      background: white;
      color: #667eea;
    }

    /* Footer */
    .footer {
      background: #1a202c;
      color: white;
      padding: 3rem 0 1rem;
    }

    .footer-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-bottom: 2rem;
    }

    .footer-section h3 {
      margin-bottom: 1rem;
      color: white;
    }

    .footer-section ul {
      list-style: none;
    }

    .footer-section ul li {
      margin-bottom: 0.5rem;
    }

    .footer-section ul li a {
      color: rgba(255, 255, 255, 0.7);
      text-decoration: none;
      transition: color 0.3s ease;
    }

    .footer-section ul li a:hover {
      color: white;
    }

    .footer-bottom {
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      padding-top: 2rem;
      text-align: center;
      color: rgba(255, 255, 255, 0.7);
    }

    /* Animations */
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .animate-on-scroll {
      opacity: 0;
      animation: fadeInUp 0.8s ease forwards;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .hero h1 {
        font-size: 2.5rem;
      }
      
      .hero .subtitle {
        font-size: 1.1rem;
      }
      
      .nav-links {
        display: none;
      }
      
      .features-grid {
        grid-template-columns: 1fr;
      }
      
      .pricing-card.featured {
        transform: none;
      }
      
      .calculator-inputs {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <!-- Header -->
  <header class="header" data-controller="navbar">
    <nav class="nav container">
      <a href="/" class="logo">DataReflow</a>
      <ul class="nav-links">
        <li><a href="#features">Features</a></li>
        <li><a href="#roi">ROI Calculator</a></li>
        <li><a href="#testimonials">Success Stories</a></li>
        <li><a href="#pricing">Pricing</a></li>
      </ul>
      <a href="#demo" class="cta-button">Get Demo</a>
    </nav>
  </header>

  <!-- Hero Section -->
  <section class="hero">
    <div class="container">
      <h1 data-controller="typewriter" data-typewriter-text-value="Transform Your Business with Data-Driven Insights">
        Transform Your Business with Data-Driven Insights
      </h1>
      <p class="subtitle">
        DataReflow helps SME executives unlock 10x ROI through automated data integration and real-time business intelligence. Turn scattered data into strategic insights in 30 minutes.
      </p>
      
      <div class="hero-stats">
        <div class="stat-item glass-morphism">
          <span class="stat-number" data-controller="counter" data-counter-target-value="10">0</span>
          <span class="stat-label">Average ROI Multiplier</span>
        </div>
        <div class="stat-item glass-morphism">
          <span class="stat-number" data-controller="counter" data-counter-target-value="87">0</span>
          <span class="stat-label">% Reduction in Manual Reporting</span>
        </div>
        <div class="stat-item glass-morphism">
          <span class="stat-number" data-controller="counter" data-counter-target-value="30">0</span>
          <span class="stat-label">Minutes to First Insights</span>
        </div>
      </div>
      
      <div class="cta-buttons">
        <a href="#demo" class="cta-button">Start Free Trial</a>
        <a href="#roi" class="btn-secondary">Calculate Your ROI</a>
      </div>
    </div>
  </section>

  <!-- Main Content -->
  <main class="main-content">
    <!-- Features Section -->
    <section id="features" class="section">
      <div class="container">
        <h2 class="section-title">Strategic Business Intelligence for Modern SMEs</h2>
        <p class="section-subtitle">
          Stop making decisions in the dark. DataReflow provides executive-level insights that drive growth, optimize operations, and maximize profitability.
        </p>
        
        <div class="features-grid">
          <div class="feature-card animate-on-scroll">
            <div class="feature-icon">📊</div>
            <h3 class="feature-title">Executive Dashboard</h3>
            <p class="feature-description">
              Real-time KPI monitoring with customizable executive dashboards. Track revenue, customer metrics, and operational efficiency in one unified view.
            </p>
          </div>
          
          <div class="feature-card animate-on-scroll">
            <div class="feature-icon">💰</div>
            <h3 class="feature-title">ROI Analytics</h3>
            <p class="feature-description">
              Measure the impact of every business decision. Track marketing ROI, customer lifetime value, and operational cost optimization in real-time.
            </p>
          </div>
          
          <div class="feature-card animate-on-scroll">
            <div class="feature-icon">🎯</div>
            <h3 class="feature-title">Predictive Insights</h3>
            <p class="feature-description">
              AI-powered forecasting for revenue, inventory, and customer behavior. Make proactive decisions with confidence in your projections.
            </p>
          </div>
          
          <div class="feature-card animate-on-scroll">
            <div class="feature-icon">⚡</div>
            <h3 class="feature-title">Automated Reporting</h3>
            <p class="feature-description">
              Eliminate manual reporting overhead. Automated daily, weekly, and monthly reports delivered to stakeholders and board members.
            </p>
          </div>
          
          <div class="feature-card animate-on-scroll">
            <div class="feature-icon">🔗</div>
            <h3 class="feature-title">Unified Data View</h3>
            <p class="feature-description">
              Connect all your business systems: CRM, accounting, marketing, and operations. See the complete picture of your business performance.
            </p>
          </div>
          
          <div class="feature-card animate-on-scroll">
            <div class="feature-icon">🚀</div>
            <h3 class="feature-title">Strategic Planning</h3>
            <p class="feature-description">
              Data-driven strategic planning tools. Set goals, track progress, and adjust strategies based on real performance data.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- ROI Calculator -->
    <section id="roi" class="section">
      <div class="container">
        <div class="roi-calculator" data-controller="roi-calculator">
          <h2 class="section-title">Calculate Your ROI</h2>
          <p class="section-subtitle">
            See how much DataReflow can save your business and boost your bottom line
          </p>
          
          <div class="calculator-inputs">
            <div class="input-group">
              <label for="employees">Number of Employees</label>
              <input type="number" id="employees" data-roi-calculator-target="employees" value="50" min="5" max="500">
            </div>
            
            <div class="input-group">
              <label for="revenue">Annual Revenue ($)</label>
              <input type="number" id="revenue" data-roi-calculator-target="revenue" value="5000000" min="100000" step="100000">
            </div>
            
            <div class="input-group">
              <label for="reporting-hours">Weekly Reporting Hours</label>
              <input type="number" id="reporting-hours" data-roi-calculator-target="reportingHours" value="20" min="5" max="80">
            </div>
            
            <div class="input-group">
              <label for="hourly-rate">Average Hourly Rate ($)</label>
              <input type="number" id="hourly-rate" data-roi-calculator-target="hourlyRate" value="75" min="25" max="200">
            </div>
          </div>
          
          <div class="roi-result">
            <h3>Your Potential Annual Savings</h3>
            <div style="font-size: 2.5rem; font-weight: 800; margin: 1rem 0;">
              $<span data-roi-calculator-target="savings">156,000</span>
            </div>
            <p>
              ROI: <span data-roi-calculator-target="roi" style="font-weight: 700;">2,890%</span> |
              Payback Period: <span data-roi-calculator-target="payback" style="font-weight: 700;">12 days</span>
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonials -->
    <section id="testimonials" class="section testimonials">
      <div class="container">
        <h2 class="section-title">Trusted by Forward-Thinking SME Leaders</h2>
        <p class="section-subtitle">
          See how DataReflow is transforming businesses across industries
        </p>
        
        <div class="testimonial-grid">
          <div class="testimonial-card animate-on-scroll">
            <p class="testimonial-text">
              "DataReflow transformed our decision-making process. We now have real-time visibility into every aspect of our business, leading to a 34% increase in profitability within six months."
            </p>
            <div class="testimonial-author">
              <div class="author-avatar"></div>
              <div class="author-info">
                <h4>Sarah Chen</h4>
                <p>CEO, TechFlow Solutions</p>
              </div>
            </div>
          </div>
          
          <div class="testimonial-card animate-on-scroll">
            <p class="testimonial-text">
              "The ROI was immediate. We eliminated 25 hours of weekly manual reporting and can now make strategic decisions based on real data instead of gut feeling."
            </p>
            <div class="testimonial-author">
              <div class="author-avatar"></div>
              <div class="author-info">
                <h4>Michael Rodriguez</h4>
                <p>COO, GreenSpace Manufacturing</p>
              </div>
            </div>
          </div>
          
          <div class="testimonial-card animate-on-scroll">
            <p class="testimonial-text">
              "Our board meetings went from 3 hours of reviewing spreadsheets to focused strategic discussions. DataReflow gave us the insights we needed to scale confidently."
            </p>
            <div class="testimonial-author">
              <div class="author-avatar"></div>
              <div class="author-info">
                <h4>Jennifer Walsh</h4>
                <p>Managing Partner, InnovateLaw</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing -->
    <section id="pricing" class="section">
      <div class="container">
        <h2 class="section-title">Enterprise-Grade Analytics at SME Prices</h2>
        <p class="section-subtitle">
          Transparent pricing that scales with your business growth
        </p>
        
        <div class="pricing-grid">
          <div class="pricing-card">
            <h3>Starter</h3>
            <div class="price">$149<span class="price-period">/month</span></div>
            <ul class="features-list">
              <li>5 data source connections</li>
              <li>Executive dashboard</li>
              <li>Basic reporting automation</li>
              <li>Email support</li>
              <li>10M records/month</li>
            </ul>
            <a href="#demo" class="cta-button">Start Free Trial</a>
          </div>
          
          <div class="pricing-card featured">
            <div class="pricing-badge">Most Popular</div>
            <h3>Professional</h3>
            <div class="price">$449<span class="price-period">/month</span></div>
            <ul class="features-list">
              <li>15 data source connections</li>
              <li>Advanced analytics & forecasting</li>
              <li>Custom dashboards</li>
              <li>Priority support & onboarding</li>
              <li>100M records/month</li>
              <li>API access</li>
            </ul>
            <a href="#demo" class="cta-button">Start Free Trial</a>
          </div>
          
          <div class="pricing-card">
            <h3>Business</h3>
            <div class="price">$899<span class="price-period">/month</span></div>
            <ul class="features-list">
              <li>Unlimited data sources</li>
              <li>White-label dashboards</li>
              <li>Advanced security & compliance</li>
              <li>Dedicated success manager</li>
              <li>500M records/month</li>
              <li>Custom integrations</li>
            </ul>
            <a href="#demo" class="cta-button">Start Free Trial</a>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- CTA Section -->
  <section class="cta-section">
    <div class="container">
      <h2>Ready to Transform Your Business?</h2>
      <p>Join 500+ SME leaders who've unlocked their data potential with DataReflow</p>
      <div class="cta-buttons">
        <a href="#demo" class="cta-button">Start Free 14-Day Trial</a>
        <a href="#contact" class="btn-secondary">Schedule Executive Demo</a>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <h3>DataReflow</h3>
          <p>Empowering SMEs with enterprise-grade data analytics</p>
        </div>
        
        <div class="footer-section">
          <h3>Product</h3>
          <ul>
            <li><a href="#features">Features</a></li>
            <li><a href="#integrations">Integrations</a></li>
            <li><a href="#security">Security</a></li>
            <li><a href="#api">API</a></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h3>Company</h3>
          <ul>
            <li><a href="#about">About</a></li>
            <li><a href="#careers">Careers</a></li>
            <li><a href="#contact">Contact</a></li>
            <li><a href="#press">Press</a></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h3>Resources</h3>
          <ul>
            <li><a href="#blog">Blog</a></li>
            <li><a href="#case-studies">Case Studies</a></li>
            <li><a href="#documentation">Documentation</a></li>
            <li><a href="#support">Support</a></li>
          </ul>
        </div>
      </div>
      
      <div class="footer-bottom">
        <p>&copy; 2025 DataReflow. All rights reserved.</p>
      </div>
    </div>
  </footer>

  <!-- Stimulus Controllers -->
  <script>
    // Counter Animation Controller
    class Counter {
      static targets = [""]
      
      connect() {
        this.animateCounter();
      }
      
      animateCounter() {
        const targetValue = parseInt(this.data.get("targetValue"));
        const duration = 2000;
        const stepTime = 50;
        const steps = duration / stepTime;
        const increment = targetValue / steps;
        let current = 0;
        
        const timer = setInterval(() => {
          current += increment;
          if (current >= targetValue) {
            current = targetValue;
            clearInterval(timer);
          }
          this.element.textContent = Math.floor(current);
        }, stepTime);
      }
    }
    
    // ROI Calculator Controller
    class ROICalculator {
      static targets = ["employees", "revenue", "reportingHours", "hourlyRate", "savings", "roi", "payback"]
      
      connect() {
        this.calculate();
        this.element.addEventListener("input", () => this.calculate());
      }
      
      calculate() {
        const employees = parseInt(this.employeesTarget.value) || 0;
        const revenue = parseInt(this.revenueTarget.value) || 0;
        const reportingHours = parseInt(this.reportingHoursTarget.value) || 0;
        const hourlyRate = parseInt(this.hourlyRateTarget.value) || 0;
        
        // Calculate annual cost of manual reporting
        const annualReportingCost = reportingHours * 52 * hourlyRate;
        
        // Calculate efficiency gains (87% reduction in manual work)
        const efficiencyGains = annualReportingCost * 0.87;
        
        // Calculate decision-making improvement (estimated 15% revenue increase)
        const revenueImprovement = revenue * 0.15;
        
        // Total annual savings
        const totalSavings = efficiencyGains + revenueImprovement;
        
        // DataReflow annual cost (estimated based on company size)
        let annualCost = 1788; // Starter plan
        if (employees > 50) annualCost = 5388; // Professional
        if (employees > 100) annualCost = 10788; // Business
        
        // Calculate ROI
        const roi = ((totalSavings - annualCost) / annualCost * 100);
        const paybackDays = Math.round((annualCost / totalSavings) * 365);
        
        // Update display
        this.savingsTarget.textContent = totalSavings.toLocaleString();
        this.roiTarget.textContent = Math.round(roi).toLocaleString() + "%";
        this.paybackTarget.textContent = paybackDays + " days";
      }
    }
    
    // Scroll Animation Controller
    class ScrollAnimation {
      connect() {
        this.observeElements();
      }
      
      observeElements() {
        const observer = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              entry.target.style.animationDelay = "0s";
              entry.target.classList.add("animate-on-scroll");
            }
          });
        });
        
        document.querySelectorAll(".animate-on-scroll").forEach(el => {
          observer.observe(el);
        });
      }
    }
    
    // Navbar Controller
    class Navbar {
      connect() {
        window.addEventListener("scroll", () => this.handleScroll());
      }
      
      handleScroll() {
        if (window.scrollY > 100) {
          this.element.style.background = "rgba(255, 255, 255, 0.95)";
          this.element.style.borderBottom = "1px solid rgba(0, 0, 0, 0.1)";
        } else {
          this.element.style.background = "rgba(255, 255, 255, 0.05)";
          this.element.style.borderBottom = "1px solid rgba(255, 255, 255, 0.1)";
        }
      }
    }
    
    // Register controllers
    window.Stimulus = {
      register: (name, controller) => {
        document.querySelectorAll(`[data-controller*="${name}"]`).forEach(element => {
          new controller().initialize(element);
        });
      }
    };
    
    // Initialize controllers
    document.addEventListener("DOMContentLoaded", () => {
      // Initialize counters
      document.querySelectorAll('[data-controller*="counter"]').forEach(element => {
        const counter = new Counter();
        counter.element = element;
        counter.data = {
          get: (key) => element.dataset[`counter${key.charAt(0).toUpperCase() + key.slice(1)}Value`]
        };
        counter.connect();
      });
      
      // Initialize ROI calculator
      document.querySelectorAll('[data-controller*="roi-calculator"]').forEach(element => {
        const calculator = new ROICalculator();
        calculator.element = element;
        calculator.employeesTarget = element.querySelector('[data-roi-calculator-target="employees"]');
        calculator.revenueTarget = element.querySelector('[data-roi-calculator-target="revenue"]');
        calculator.reportingHoursTarget = element.querySelector('[data-roi-calculator-target="reportingHours"]');
        calculator.hourlyRateTarget = element.querySelector('[data-roi-calculator-target="hourlyRate"]');
        calculator.savingsTarget = element.querySelector('[data-roi-calculator-target="savings"]');
        calculator.roiTarget = element.querySelector('[data-roi-calculator-target="roi"]');
        calculator.paybackTarget = element.querySelector('[data-roi-calculator-target="payback"]');
        calculator.connect();
      });
      
      // Initialize scroll animations
      const scrollAnimation = new ScrollAnimation();
      scrollAnimation.connect();
      
      // Initialize navbar
      document.querySelectorAll('[data-controller*="navbar"]').forEach(element => {
        const navbar = new Navbar();
        navbar.element = element;
        navbar.connect();
      });
    });
  </script>
</body>
</html>