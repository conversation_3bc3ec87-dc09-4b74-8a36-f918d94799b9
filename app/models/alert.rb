class Alert < ApplicationRecord
  include ActsAsTenant
  
  acts_as_tenant :organization
  
  belongs_to :monitoring_rule
  belongs_to :data_source, optional: true
  belongs_to :sync_log, optional: true
  belongs_to :acknowledged_by, class_name: 'User', optional: true
  belongs_to :resolved_by, class_name: 'User', optional: true
  
  STATUSES = %w[active acknowledged resolved].freeze
  SEVERITIES = %w[info warning critical].freeze
  
  validates :status, inclusion: { in: STATUSES }
  validates :severity, inclusion: { in: SEVERITIES }
  validates :alert_type, presence: true
  validates :title, presence: true
  validates :triggered_at, presence: true
  
  scope :active, -> { where(status: 'active') }
  scope :acknowledged, -> { where(status: 'acknowledged') }
  scope :resolved, -> { where(status: 'resolved') }
  scope :recent, -> { order(triggered_at: :desc) }
  scope :by_severity, ->(severity) { where(severity: severity) }
  scope :critical, -> { where(severity: 'critical') }
  scope :unresolved, -> { where.not(status: 'resolved') }
  
  after_create_commit :broadcast_alert
  after_update_commit :broadcast_alert_update
  
  def acknowledge!(user, notes: nil)
    return false if resolved?
    
    update!(
      status: 'acknowledged',
      acknowledged_at: Time.current,
      acknowledged_by: user,
      resolution_notes: notes
    )
  end
  
  def resolve!(user, notes: nil)
    update!(
      status: 'resolved',
      resolved_at: Time.current,
      resolved_by: user,
      resolution_notes: [resolution_notes, notes].compact.join("\n\n")
    )
  end
  
  def active?
    status == 'active'
  end
  
  def acknowledged?
    status == 'acknowledged'
  end
  
  def resolved?
    status == 'resolved'
  end
  
  def duration
    return nil unless resolved_at
    resolved_at - triggered_at
  end
  
  def time_to_acknowledge
    return nil unless acknowledged_at
    acknowledged_at - triggered_at
  end
  
  def time_to_resolve
    return nil unless resolved_at
    resolved_at - triggered_at
  end
  
  def severity_color
    case severity
    when 'critical'
      'red'
    when 'warning'
      'yellow'
    when 'info'
      'blue'
    else
      'gray'
    end
  end
  
  def severity_icon
    case severity
    when 'critical'
      'exclamation-circle'
    when 'warning'
      'exclamation-triangle'
    when 'info'
      'info-circle'
    else
      'question-circle'
    end
  end
  
  private
  
  def broadcast_alert
    # Broadcast to monitoring dashboard
    ActionCable.server.broadcast(
      "monitoring_#{organization_id}",
      {
        type: 'new_alert',
        alert: {
          id: id,
          title: title,
          message: message,
          severity: severity,
          alert_type: alert_type,
          triggered_at: triggered_at,
          data_source_name: data_source&.name
        }
      }
    )
    
    # Update alert counters
    broadcast_alert_counts
  end
  
  def broadcast_alert_update
    ActionCable.server.broadcast(
      "monitoring_#{organization_id}",
      {
        type: 'alert_updated',
        alert: {
          id: id,
          status: status,
          acknowledged_by: acknowledged_by&.full_name,
          resolved_by: resolved_by&.full_name
        }
      }
    )
    
    broadcast_alert_counts
  end
  
  def broadcast_alert_counts
    counts = organization.alerts.unresolved.group(:severity).count
    
    ActionCable.server.broadcast(
      "monitoring_#{organization_id}",
      {
        type: 'alert_counts',
        counts: {
          critical: counts['critical'] || 0,
          warning: counts['warning'] || 0,
          info: counts['info'] || 0,
          total: counts.values.sum
        }
      }
    )
  end
end