class PipelineRun < ApplicationRecord
  acts_as_tenant :organization
  
  # Associations
  belongs_to :pipeline
  belongs_to :organization
  has_many :step_executions, dependent: :destroy
  
  # Validations
  validates :status, presence: true
  validates :started_at, presence: true
  
  # Scopes
  scope :recent, -> { order(started_at: :desc) }
  scope :completed, -> { where(status: 'completed') }
  scope :failed, -> { where(status: 'failed') }
  scope :running, -> { where(status: 'running') }
  
  # Callbacks
  before_validation :set_defaults
  after_update :calculate_duration, if: :saved_change_to_completed_at?
  
  # Methods
  def duration
    return nil unless started_at
    (completed_at || Time.current) - started_at
  end
  
  def complete!
    update!(
      status: 'completed',
      completed_at: Time.current
    )
  end
  
  def fail!(error_message = nil)
    update!(
      status: 'failed',
      completed_at: Time.current,
      error_message: error_message
    )
  end
  
  private
  
  def set_defaults
    self.status ||= 'pending'
    self.organization ||= pipeline&.organization
  end
  
  def calculate_duration
    if started_at && completed_at
      update_column(:duration_seconds, (completed_at - started_at).to_i)
    end
  end
end