module DataSourceConstants
  extend ActiveSupport::Concern
  
  # Source types grouped by category
  DATABASE_SOURCES = %w[postgresql mysql sqlite sqlserver mongodb redis elasticsearch].freeze
  FILE_SOURCES = %w[csv excel json xml].freeze
  API_SOURCES = %w[api webhook stream].freeze
  CLOUD_STORAGE_SOURCES = %w[s3 gcs azure_blob sftp].freeze
  SAAS_SOURCES = %w[quickbooks stripe shopify salesforce].freeze
  
  # All source types
  SOURCE_TYPES = (DATABASE_SOURCES + FILE_SOURCES + API_SOURCES + CLOUD_STORAGE_SOURCES + SAAS_SOURCES).freeze
  
  # Status values
  STATUSES = %w[inactive active syncing error maintenance].freeze
  
  # Status groups
  ACTIVE_STATUSES = %w[active syncing].freeze
  ERROR_STATUSES = %w[error].freeze
  INACTIVE_STATUSES = %w[inactive maintenance].freeze
  
  # Sync frequencies
  SYNC_FREQUENCIES = %w[
    manual
    realtime
    every_5_minutes
    every_15_minutes
    every_30_minutes
    hourly
    every_6_hours
    daily
    weekly
    monthly
  ].freeze
  
  # Sensitive field patterns for masking
  SENSITIVE_FIELDS = %w[
    password
    api_key
    secret
    token
    private_key
    access_key
    secret_key
    credentials
    auth
  ].freeze
  
  # ID field patterns for external ID detection
  ID_FIELD_PATTERNS = %w[
    id
    ID
    Id
    _id
    uuid
    UUID
    guid
    GUID
    key
    KEY
    identifier
    external_id
    record_id
  ].freeze
  
  # Source type display names
  SOURCE_TYPE_NAMES = {
    'postgresql' => 'PostgreSQL',
    'mysql' => 'MySQL',
    'sqlite' => 'SQLite',
    'sqlserver' => 'SQL Server',
    'mongodb' => 'MongoDB',
    'redis' => 'Redis',
    'elasticsearch' => 'Elasticsearch',
    'csv' => 'CSV File',
    'excel' => 'Excel File',
    'json' => 'JSON File',
    'xml' => 'XML File',
    'api' => 'REST API',
    'webhook' => 'Webhook',
    'stream' => 'Data Stream',
    's3' => 'Amazon S3',
    'gcs' => 'Google Cloud Storage',
    'azure_blob' => 'Azure Blob Storage',
    'sftp' => 'SFTP',
    'quickbooks' => 'QuickBooks',
    'stripe' => 'Stripe',
    'shopify' => 'Shopify',
    'salesforce' => 'Salesforce'
  }.freeze
  
  # Sync frequency display names
  SYNC_FREQUENCY_NAMES = {
    'manual' => 'Manual',
    'realtime' => 'Real-time',
    'every_5_minutes' => 'Every 5 minutes',
    'every_15_minutes' => 'Every 15 minutes',
    'every_30_minutes' => 'Every 30 minutes',
    'hourly' => 'Hourly',
    'every_6_hours' => 'Every 6 hours',
    'daily' => 'Daily',
    'weekly' => 'Weekly',
    'monthly' => 'Monthly'
  }.freeze
  
  # Default values
  DEFAULTS = {
    delimiter: ',',
    encoding: 'UTF-8',
    has_headers: true,
    ssl_mode: 'prefer'
  }.freeze
end