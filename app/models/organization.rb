class Organization < ApplicationRecord
  # This model IS the tenant, not scoped by tenant
  # acts_as_tenant is used on other models to scope them to an organization
  
  has_many :users, dependent: :destroy
  has_many :data_sources, dependent: :destroy
  has_many :pipelines, dependent: :destroy
  has_many :monitoring_rules, dependent: :destroy
  has_many :alerts, dependent: :destroy
  has_many :performance_metrics, dependent: :destroy
  
  validates :name, presence: true
  validates :plan, inclusion: { in: %w[starter professional enterprise] }
  validates :subdomain, uniqueness: true, allow_blank: true, 
            format: { with: /\A[a-z0-9-]+\z/, message: "can only contain lowercase letters, numbers, and hyphens" }
  
  encrypts :settings
  
  before_save :set_monthly_row_limit
  
  def monthly_data_limit
    case plan
    when 'starter' then 10_000_000    # 10M rows
    when 'professional' then 100_000_000  # 100M rows
    when 'enterprise' then Float::INFINITY
    end
  end
  
  def data_limit_reached?
    return false if plan == 'enterprise'
    current_month_rows >= monthly_data_limit
  end
  
  def usage_percentage
    return 0 if plan == 'enterprise'
    ((current_month_rows.to_f / monthly_data_limit) * 100).round(2)
  end
  
  private
  
  def set_monthly_row_limit
    self.monthly_row_limit = monthly_data_limit
  end
end