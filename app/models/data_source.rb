class DataSource < ApplicationRecord
  include DataSourceConstants
  
  # Multi-tenancy
  acts_as_tenant :organization
  
  # Associations
  belongs_to :organization
  has_many :integrations, dependent: :destroy
  has_many :pipelines, dependent: :destroy
  has_many :sync_logs, dependent: :destroy
  has_many :imported_records, dependent: :destroy
  has_many :monitoring_rules, dependent: :destroy
  has_many :alerts, dependent: :destroy
  has_many :performance_metrics, dependent: :destroy
  
  # Encryption for sensitive connection config
  encrypts :connection_config
  
  # Validations
  validates :name, presence: true, 
            uniqueness: { scope: :organization_id, case_sensitive: false }
  validates :source_type, presence: true, inclusion: { in: SOURCE_TYPES }
  validates :status, presence: true, inclusion: { in: STATUSES }
  validates :sync_frequency, inclusion: { in: SYNC_FREQUENCIES }, allow_nil: true
  
  # Scopes
  scope :active, -> { where(status: 'active') }
  scope :inactive, -> { where(status: 'inactive') }
  scope :with_errors, -> { where(status: 'error') }
  scope :by_type, ->(type) { where(source_type: type) }
  scope :needs_sync, -> { 
    active.where(
      "last_sync_at IS NULL OR last_sync_at < ?", 
      DataSourceConfig.sync['needs_sync_threshold_hours'].hours.ago
    )
  }
  
  # Callbacks
  before_validation :normalize_name
  after_update :clear_error_if_fixed
  
  # Instance methods
  def active?
    status == 'active'
  end
  
  def inactive?
    status == 'inactive'
  end
  
  def has_error?
    status == 'error'
  end
  
  def syncing?
    status == 'syncing'
  end
  
  def can_sync?
    active? && !syncing?
  end
  
  def database_source?
    DATABASE_SOURCES.include?(source_type)
  end
  
  def file_source?
    FILE_SOURCES.include?(source_type)
  end
  
  def api_source?
    API_SOURCES.include?(source_type)
  end
  
  def cloud_storage_source?
    CLOUD_STORAGE_SOURCES.include?(source_type)
  end
  
  def saas_source?
    SAAS_SOURCES.include?(source_type)
  end
  
  # Connection configuration helpers
  def connection_settings
    return {} if connection_config.blank?
    
    begin
      parsed = JSON.parse(connection_config)
      # Use with_indifferent_access to allow both string and symbol access
      ActiveSupport::HashWithIndifferentAccess.new(parsed)
    rescue JSON::ParserError => e
      Rails.logger.error "Failed to parse connection_config: #{e.message}"
      Rails.logger.error "Connection config value: #{connection_config.inspect}"
      {}
    end
  end
  
  def update_connection_settings(settings)
    self.connection_config = settings.to_json
    save!
  end
  
  # Test connection
  def test_connection
    Rails.logger.info "DataSource.test_connection - ID: #{id}"
    Rails.logger.info "DataSource.test_connection - connection_settings: #{connection_settings.inspect}"
    
    connector = connector_class.new(self)
    result = connector.test_connection
    
    if result[:success]
      update!(status: 'active', error_message: nil)
    else
      update!(status: 'error', error_message: result[:error])
    end
    
    result
  rescue => e
    update!(status: 'error', error_message: e.message)
    { success: false, error: e.message }
  end
  
  # Sync methods
  def sync!
    return { success: false, error: 'Cannot sync inactive source' } unless can_sync?
    
    update!(status: 'syncing', error_message: nil)
    
    # Queue the sync job
    DataSourceSyncJob.perform_later(self)
    
    { success: true, message: 'Sync started' }
  rescue => e
    update!(status: 'error', error_message: e.message)
    { success: false, error: e.message }
  end
  
  def sync_now!
    return { success: false, error: 'Cannot sync inactive source' } unless can_sync?
    
    # Create sync log
    sync_log = sync_logs.create!(
      organization: organization,
      sync_type: 'full',
      status: 'running',
      started_at: Time.current,
      metadata: {
        source_type: source_type,
        connection_info: sanitized_connection_info
      }
    )
    
    update!(status: 'syncing', error_message: nil)
    
    connector = connector_class.new(self)
    
    # Check if connector is CSV - it handles its own sync logs
    if connector.is_a?(Connectors::CsvConnector)
      result = connector.sync
      # CSV connector already created its own sync log, so remove the one we created
      sync_log.destroy
    else
      # For non-CSV connectors, pass the sync log to track progress
      result = connector.sync
      
      # Update sync log based on result
      if result[:success]
        sync_log.update!(
          status: 'completed',
          completed_at: Time.current,
          records_processed: result[:row_count] || 0,
          records_imported: result[:row_count] || 0,
          duration_seconds: (Time.current - sync_log.started_at).round,
          metadata: sync_log.metadata.merge(result[:metadata] || {})
        )
      else
        sync_log.update!(
          status: 'failed',
          completed_at: Time.current,
          error_message: result[:error],
          error_details: result[:error_details] || {},
          duration_seconds: (Time.current - sync_log.started_at).round
        )
      end
    end
    
    if result[:success]
      update!(
        status: 'active',
        last_sync_at: Time.current,
        row_count: result[:row_count] || 0,
        error_message: nil,
        metadata: metadata.merge(last_sync_result: result[:metadata] || {})
      )
    else
      update!(
        status: 'error',
        error_message: result[:error],
        metadata: metadata.merge(last_sync_error: result[:error_details] || {})
      )
    end
    
    result
  rescue => e
    # Update sync log if it exists
    sync_log&.update!(
      status: 'failed',
      completed_at: Time.current,
      error_message: e.message,
      error_details: { backtrace: e.backtrace.first(10) },
      duration_seconds: (Time.current - sync_log.started_at).round
    ) if sync_log&.persisted?
    
    update!(status: 'error', error_message: e.message)
    { success: false, error: e.message }
  end
  
  # Get schema/structure
  def fetch_schema
    Rails.logger.info "DataSource.fetch_schema - ID: #{id}"
    Rails.logger.info "DataSource.fetch_schema - source_type: #{source_type}"
    Rails.logger.info "DataSource.fetch_schema - connection_settings: #{connection_settings.inspect}"
    
    connector = connector_class.new(self)
    Rails.logger.info "DataSource.fetch_schema - connector created: #{connector.class.name}"
    
    connector.fetch_schema
  rescue => e
    Rails.logger.error "DataSource.fetch_schema - error: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    { success: false, error: e.message }
  end
  
  # Get sample data
  def fetch_sample_data(limit: DataSourceConfig.ui['sample_data_limit'])
    connector = connector_class.new(self)
    connector.fetch_sample_data(limit: limit)
  rescue => e
    { success: false, error: e.message }
  end
  
  # Scheduling
  def schedule_next_sync
    return unless active? && sync_frequency != 'manual'
    
    next_sync_time = calculate_next_sync_time
    DataSourceSyncJob.set(wait_until: next_sync_time).perform_later(self)
  end
  
  def calculate_next_sync_time
    return nil if sync_frequency.blank? || sync_frequency == 'manual'
    
    seconds = DataSourceConfig.sync_frequencies[sync_frequency]
    seconds ? seconds.seconds.from_now : nil
  end
  
  def connection_config_value(key)
    return nil if connection_config.blank?
    
    config = connection_config.is_a?(String) ? JSON.parse(connection_config) : connection_config
    config ||= {}
    config[key.to_s]
  rescue JSON::ParserError
    nil
  end
  
  # Formatted helpers
  def formatted_row_count
    return I18n.t('data_source.no_data') if row_count.nil? || row_count.zero?
    
    number_with_delimiter(row_count)
  end
  
  def sync_frequency_text
    SYNC_FREQUENCY_NAMES[sync_frequency] || sync_frequency&.humanize || '-'
  end
  
  def source_type_text
    SOURCE_TYPE_NAMES[source_type] || source_type&.humanize || ''
  end
  
  private
  
  def normalize_name
    self.name = name&.strip
  end
  
  def sanitized_connection_info
    settings = connection_settings.dup
    
    # Remove sensitive fields
    %w[password api_key secret_key access_token refresh_token].each do |key|
      settings[key] = '[REDACTED]' if settings[key].present?
    end
    
    settings
  end
  
  def clear_error_if_fixed
    if saved_change_to_status? && status != 'error' && error_message.present?
      self.error_message = nil
      save
    end
  end
  
  def connector_class
    "Connectors::#{source_type.camelize}Connector".constantize
  rescue NameError
    raise NotImplementedError, "Connector not implemented for #{source_type}"
  end
  
  def number_with_delimiter(number)
    ActionController::Base.helpers.number_with_delimiter(number)
  end
end