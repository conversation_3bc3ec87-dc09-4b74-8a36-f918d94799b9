class Pipeline < ApplicationRecord
  acts_as_tenant :organization
  
  # Constants
  STATUSES = %w[inactive active running paused scheduled failed].freeze
  SCHEDULES = %w[manual hourly daily weekly monthly].freeze
  
  # Associations
  belongs_to :data_source
  belongs_to :organization
  has_many :pipeline_runs, dependent: :destroy
  has_many :pipeline_steps, dependent: :destroy
  
  # Validations
  validates :name, presence: true
  validates :status, presence: true, inclusion: { in: STATUSES }
  validates :schedule, inclusion: { in: SCHEDULES }, allow_nil: true
  
  # Scopes
  scope :active, -> { where(status: 'active') }
  scope :scheduled, -> { where.not(schedule: [nil, '', 'manual']) }
  scope :recent, -> { order(created_at: :desc) }
  
  # Callbacks
  before_validation :set_defaults
  
  # Methods
  def configuration_hash
    return {} if configuration.blank?
    JSON.parse(configuration)
  rescue JSON::ParserError
    {}
  end
  
  def scheduled?
    schedule.present? && schedule != 'manual'
  end
  
  def can_run?
    %w[active scheduled paused].include?(status) && !running?
  end
  
  def running?
    status == 'running'
  end
  
  def run!
    return { success: false, error: 'Pipeline cannot be run in current state' } unless can_run?
    
    # Create a new pipeline run
    run = pipeline_runs.create!(
      status: 'pending',
      started_at: Time.current
    )
    
    # Queue the pipeline execution job
    PipelineExecutionJob.perform_later(run)
    
    update!(status: 'running', last_run_at: Time.current)
    
    { success: true, run: run }
  rescue => e
    { success: false, error: e.message }
  end
  
  def next_scheduled_run_time
    return nil unless scheduled?
    
    case schedule
    when 'hourly'
      1.hour.from_now
    when 'daily'
      1.day.from_now
    when 'weekly'
      1.week.from_now
    when 'monthly'
      1.month.from_now
    else
      nil
    end
  end
  
  private
  
  def set_defaults
    self.status ||= 'inactive'
    self.schedule ||= 'manual'
    self.configuration ||= {}.to_json
  end
end
