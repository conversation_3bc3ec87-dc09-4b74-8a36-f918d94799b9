class MonitoringRule < ApplicationRecord
  include ActsAsTenant
  
  acts_as_tenant :organization
  
  belongs_to :data_source, optional: true
  has_many :alerts, dependent: :destroy
  
  RULE_TYPES = %w[sync_failure performance data_quality uptime].freeze
  SEVERITIES = %w[info warning critical].freeze
  
  validates :name, presence: true
  validates :rule_type, inclusion: { in: RULE_TYPES }
  validates :severity, inclusion: { in: SEVERITIES }
  validates :conditions, presence: true
  
  scope :enabled, -> { where(enabled: true) }
  scope :by_severity, ->(severity) { where(severity: severity) }
  scope :by_type, ->(type) { where(rule_type: type) }
  scope :recently_triggered, -> { where.not(last_triggered_at: nil).order(last_triggered_at: :desc) }
  
  def should_trigger?(context = {})
    return false unless enabled?
    return false if in_cooldown?
    
    evaluate_conditions(context)
  end
  
  def trigger!(context = {})
    return false unless should_trigger?(context)
    
    alert = alerts.create!(
      organization: organization,
      data_source: data_source,
      sync_log_id: context[:sync_log_id],
      severity: severity,
      alert_type: rule_type,
      title: generate_alert_title(context),
      message: generate_alert_message(context),
      details: context,
      triggered_at: Time.current
    )
    
    update!(last_triggered_at: Time.current)
    
    # Send notifications
    AlertNotificationJob.perform_later(alert) if notification_channels.present?
    
    alert
  end
  
  def in_cooldown?
    return false if last_triggered_at.nil?
    last_triggered_at > cooldown_minutes.minutes.ago
  end
  
  private
  
  def evaluate_conditions(context)
    case rule_type
    when 'sync_failure'
      evaluate_sync_failure_conditions(context)
    when 'performance'
      evaluate_performance_conditions(context)
    when 'data_quality'
      evaluate_data_quality_conditions(context)
    when 'uptime'
      evaluate_uptime_conditions(context)
    else
      false
    end
  end
  
  def evaluate_sync_failure_conditions(context)
    return false unless context[:sync_status] == 'failed'
    
    # Check consecutive failures
    if conditions['consecutive_failures'].present?
      consecutive_failures = data_source&.sync_logs&.recent&.limit(conditions['consecutive_failures'])&.pluck(:status) || []
      return consecutive_failures.all? { |status| status == 'failed' }
    end
    
    # Check failure rate
    if conditions['failure_rate_threshold'].present? && conditions['failure_rate_window_hours'].present?
      window = conditions['failure_rate_window_hours'].hours.ago
      total_syncs = data_source&.sync_logs&.where('started_at > ?', window)&.count || 0
      failed_syncs = data_source&.sync_logs&.where('started_at > ? AND status = ?', window, 'failed')&.count || 0
      
      return false if total_syncs.zero?
      
      failure_rate = (failed_syncs.to_f / total_syncs) * 100
      return failure_rate >= conditions['failure_rate_threshold']
    end
    
    true
  end
  
  def evaluate_performance_conditions(context)
    return false unless context[:metric_type] && context[:value]
    
    # Check duration threshold
    if conditions['duration_threshold_seconds'].present? && context[:metric_type] == 'sync_duration'
      return context[:value] > conditions['duration_threshold_seconds']
    end
    
    # Check memory usage
    if conditions['memory_threshold_mb'].present? && context[:metric_type] == 'memory_usage'
      return context[:value] > conditions['memory_threshold_mb'] * 1024 * 1024
    end
    
    # Check throughput
    if conditions['min_throughput_rows_per_second'].present? && context[:metric_type] == 'throughput'
      return context[:value] < conditions['min_throughput_rows_per_second']
    end
    
    false
  end
  
  def evaluate_data_quality_conditions(context)
    return false unless context[:sync_log_id]
    
    sync_log = SyncLog.find_by(id: context[:sync_log_id])
    return false unless sync_log
    
    # Check error rate
    if conditions['max_error_rate_percentage'].present?
      total_records = sync_log.records_processed
      return false if total_records.zero?
      
      error_rate = (sync_log.records_failed.to_f / total_records) * 100
      return error_rate > conditions['max_error_rate_percentage']
    end
    
    # Check for no data
    if conditions['alert_on_no_data'] && sync_log.records_imported.zero?
      return true
    end
    
    false
  end
  
  def evaluate_uptime_conditions(context)
    return false unless data_source
    
    # Check last successful sync time
    if conditions['max_time_since_last_sync_hours'].present?
      last_successful_sync = data_source.sync_logs.where(status: 'completed').order(started_at: :desc).first
      
      if last_successful_sync.nil? || last_successful_sync.started_at < conditions['max_time_since_last_sync_hours'].hours.ago
        return true
      end
    end
    
    false
  end
  
  def generate_alert_title(context)
    case rule_type
    when 'sync_failure'
      "Sync Failed: #{data_source&.name || 'Unknown Source'}"
    when 'performance'
      "Performance Issue: #{context[:metric_type]&.humanize || 'Unknown Metric'}"
    when 'data_quality'
      "Data Quality Issue: #{data_source&.name || 'Unknown Source'}"
    when 'uptime'
      "Source Offline: #{data_source&.name || 'Unknown Source'}"
    else
      "Alert: #{name}"
    end
  end
  
  def generate_alert_message(context)
    case rule_type
    when 'sync_failure'
      "The sync for #{data_source&.name} has failed. Error: #{context[:error_message]}"
    when 'performance'
      "Performance threshold exceeded. #{context[:metric_type]&.humanize}: #{context[:value]} #{context[:unit]}"
    when 'data_quality'
      sync_log = SyncLog.find_by(id: context[:sync_log_id])
      "Data quality issues detected. Records failed: #{sync_log&.records_failed || 0}, Error rate: #{context[:error_rate]}%"
    when 'uptime'
      "The data source #{data_source&.name} has not synced successfully in #{conditions['max_time_since_last_sync_hours']} hours"
    else
      description || "Alert triggered for #{name}"
    end
  end
end