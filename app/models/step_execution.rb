class StepExecution < ApplicationRecord
  acts_as_tenant :organization
  
  # Associations
  belongs_to :pipeline_run
  belongs_to :pipeline_step, optional: true
  belongs_to :organization
  
  # Validations
  validates :status, presence: true
  validates :step_name, presence: true
  
  # Scopes
  scope :ordered, -> { order(:position) }
  scope :completed, -> { where(status: 'completed') }
  scope :failed, -> { where(status: 'failed') }
  
  # Callbacks
  before_validation :set_defaults
  
  private
  
  def set_defaults
    self.status ||= 'pending'
    self.organization ||= pipeline_run&.organization
  end
end