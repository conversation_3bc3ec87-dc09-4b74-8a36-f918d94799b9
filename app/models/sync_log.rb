class SyncLog < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant :organization
  
  # Associations
  belongs_to :organization
  belongs_to :data_source
  has_many :imported_records, dependent: :destroy
  has_many :alerts, dependent: :nullify
  has_many :performance_metrics, dependent: :destroy
  
  # Constants
  STATUSES = %w[pending running completed failed cancelled].freeze
  SYNC_TYPES = %w[full incremental refresh].freeze
  
  # Validations
  validates :status, presence: true, inclusion: { in: STATUSES }
  validates :started_at, presence: true
  validates :sync_type, inclusion: { in: SYNC_TYPES }, allow_nil: true
  
  # Scopes
  scope :recent, -> { order(started_at: :desc) }
  scope :completed, -> { where(status: 'completed') }
  scope :failed, -> { where(status: 'failed') }
  scope :running, -> { where(status: 'running') }
  
  # Callbacks
  before_validation :set_defaults, on: :create
  after_update :calculate_duration, if: :saved_change_to_completed_at?
  after_update :check_monitoring_rules, if: :saved_change_to_status?
  after_create_commit :record_performance_metrics
  
  # Instance methods
  def completed?
    status == 'completed'
  end
  
  def failed?
    status == 'failed'
  end
  
  def running?
    status == 'running'
  end
  
  def duration
    return nil unless started_at
    end_time = completed_at || Time.current
    end_time - started_at
  end
  
  def success_rate
    return 0 if records_processed.zero?
    ((records_imported.to_f / records_processed) * 100).round(2)
  end
  
  def mark_as_running!
    update!(status: 'running', started_at: Time.current)
  end
  
  def mark_as_completed!
    update!(
      status: 'completed',
      completed_at: Time.current,
      duration_seconds: duration
    )
  end
  
  def mark_as_failed!(error_message, error_details = {})
    update!(
      status: 'failed',
      completed_at: Time.current,
      duration_seconds: duration,
      error_message: error_message,
      error_details: error_details
    )
  end
  
  def increment_processed!(count = 1)
    increment!(:records_processed, count)
  end
  
  def increment_imported!(count = 1)
    increment!(:records_imported, count)
  end
  
  def increment_failed!(count = 1)
    increment!(:records_failed, count)
  end
  
  def increment_skipped!(count = 1)
    increment!(:records_skipped, count)
  end
  
  def add_metadata(key, value)
    self.metadata ||= {}
    self.metadata[key] = value
    save!
  end
  
  private
  
  def set_defaults
    self.started_at ||= Time.current
    self.status ||= 'pending'
    self.metadata ||= {}
    self.error_details ||= {}
  end
  
  def calculate_duration
    self.duration_seconds = duration if completed_at && started_at
  end
  
  def check_monitoring_rules
    # Check monitoring rules when sync completes or fails
    if completed? || failed?
      MonitoringCheckJob.perform_later(self)
    end
  end
  
  def record_performance_metrics
    # Record performance metrics when sync completes
    if completed?
      PerformanceMetric.record_sync_metrics(self)
    end
  end
end