class ImportedRecord < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant :organization
  
  # Associations
  belongs_to :organization
  belongs_to :data_source
  belongs_to :sync_log, optional: true
  
  # Validations
  validates :record_data, presence: true
  validates :imported_at, presence: true
  validates :external_id, uniqueness: { scope: :data_source_id }, allow_nil: true
  
  # Scopes
  scope :recent, -> { order(imported_at: :desc) }
  scope :by_external_id, ->(id) { where(external_id: id) }
  scope :for_sync, ->(sync_log_id) { where(sync_log_id: sync_log_id) }
  
  # Callbacks
  before_validation :set_defaults, on: :create
  before_save :calculate_checksum
  
  # Class methods
  def self.upsert_record(data_source, record_data, external_id = nil, sync_log = nil)
    checksum = calculate_checksum_for(record_data)
    
    # Check if record already exists
    existing = where(data_source: data_source, external_id: external_id).first if external_id
    existing ||= where(data_source: data_source, checksum: checksum).first
    
    if existing
      # Update if data changed
      if existing.checksum != checksum
        existing.update!(
          record_data: record_data,
          checksum: checksum,
          imported_at: Time.current,
          sync_log: sync_log
        )
        :updated
      else
        :skipped
      end
    else
      # Create new record
      create!(
        organization: data_source.organization,
        data_source: data_source,
        record_data: record_data,
        external_id: external_id,
        checksum: checksum,
        imported_at: Time.current,
        sync_log: sync_log
      )
      :created
    end
  end
  
  def self.calculate_checksum_for(data)
    Digest::SHA256.hexdigest(data.to_json.sort)
  end
  
  # Instance methods
  def duplicate?
    self.class.where(data_source: data_source, checksum: checksum)
        .where.not(id: id)
        .exists?
  end
  
  # Search in JSONB data
  def self.search_in_data(query)
    where("record_data @> ?", query.to_json)
  end
  
  def self.where_data_field(field, value)
    where("record_data ->> ? = ?", field, value.to_s)
  end
  
  def self.where_data_field_like(field, pattern)
    where("record_data ->> ? ILIKE ?", field, "%#{pattern}%")
  end
  
  private
  
  def set_defaults
    self.imported_at ||= Time.current
    self.organization ||= data_source&.organization
  end
  
  def calculate_checksum
    self.checksum = self.class.calculate_checksum_for(record_data) if record_data_changed?
  end
end