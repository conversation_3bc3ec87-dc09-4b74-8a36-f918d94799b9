class PerformanceMetric < ApplicationRecord
  include ActsAsTenant
  
  acts_as_tenant :organization
  
  belongs_to :data_source, optional: true
  belongs_to :sync_log, optional: true
  
  METRIC_TYPES = %w[sync_duration memory_usage cpu_usage error_rate throughput response_time queue_depth].freeze
  RESOURCE_TYPES = %w[data_source sync system].freeze
  
  validates :metric_type, inclusion: { in: METRIC_TYPES }
  validates :value, presence: true, numericality: true
  validates :recorded_at, presence: true
  validates :resource_type, inclusion: { in: RESOURCE_TYPES }, allow_nil: true
  
  before_save :set_time_dimensions
  
  scope :recent, -> { order(recorded_at: :desc) }
  scope :by_type, ->(type) { where(metric_type: type) }
  scope :for_data_source, ->(source) { where(data_source: source) }
  scope :in_range, ->(start_time, end_time) { where(recorded_at: start_time..end_time) }
  
  class << self
    def record_sync_metrics(sync_log)
      return unless sync_log.completed?
      
      # Record sync duration
      create!(
        data_source: sync_log.data_source,
        sync_log: sync_log,
        metric_type: 'sync_duration',
        value: sync_log.duration_seconds,
        unit: 'seconds',
        recorded_at: sync_log.completed_at,
        resource_type: 'sync',
        resource_id: sync_log.id,
        metadata: {
          records_processed: sync_log.records_processed,
          records_imported: sync_log.records_imported
        }
      )
      
      # Record throughput
      if sync_log.duration_seconds > 0
        throughput = sync_log.records_processed.to_f / sync_log.duration_seconds
        create!(
          data_source: sync_log.data_source,
          sync_log: sync_log,
          metric_type: 'throughput',
          value: throughput,
          unit: 'rows_per_second',
          recorded_at: sync_log.completed_at,
          resource_type: 'sync',
          resource_id: sync_log.id
        )
      end
      
      # Record error rate
      if sync_log.records_processed > 0
        error_rate = (sync_log.records_failed.to_f / sync_log.records_processed) * 100
        create!(
          data_source: sync_log.data_source,
          sync_log: sync_log,
          metric_type: 'error_rate',
          value: error_rate,
          unit: 'percentage',
          recorded_at: sync_log.completed_at,
          resource_type: 'sync',
          resource_id: sync_log.id
        )
      end
    end
    
    def record_system_metric(metric_type, value, unit: nil, metadata: {})
      create!(
        metric_type: metric_type,
        value: value,
        unit: unit,
        recorded_at: Time.current,
        resource_type: 'system',
        metadata: metadata
      )
    end
    
    def percentile(metric_type, percentile, time_range: 24.hours.ago..Time.current)
      values = where(metric_type: metric_type, recorded_at: time_range)
        .order(:value)
        .pluck(:value)
      
      return nil if values.empty?
      
      index = (percentile / 100.0 * values.length).ceil - 1
      values[index]
    end
    
    def aggregate_by_period(metric_type, period: :hour, time_range: 24.hours.ago..Time.current)
      where(metric_type: metric_type, recorded_at: time_range)
        .group_by_period(period, :recorded_at)
        .average(:value)
    end
  end
  
  def anomaly_score
    # Simple z-score based anomaly detection
    return 0 if metadata['baseline_mean'].nil? || metadata['baseline_std'].nil?
    
    mean = metadata['baseline_mean']
    std = metadata['baseline_std']
    
    return 0 if std.zero?
    
    ((value - mean) / std).abs
  end
  
  def is_anomaly?(threshold: 3)
    anomaly_score > threshold
  end
  
  private
  
  def set_time_dimensions
    self.hour_of_day = recorded_at.hour
    self.day_of_week = recorded_at.wday
  end
end