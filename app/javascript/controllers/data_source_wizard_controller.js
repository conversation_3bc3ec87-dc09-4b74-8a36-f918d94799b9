import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "step1Content", "step2Content", "step3Content",
    "step1Indicator", "step2Indicator", "step3Indicator",
    "step1Connector", "step2Connector",
    "typeSearch", "typesGrid", "sourceType", "noResults",
    "categoryFilter", "selectedTypeName", "configurationForm"
  ]

  connect() {
    console.log("DataSourceWizard controller connected")
    this.currentStep = 1
    this.selectedType = null
  }

  searchTypes(event) {
    const searchTerm = event.target.value.toLowerCase()
    let hasVisibleItems = false

    this.sourceTypeTargets.forEach(item => {
      const name = item.dataset.name
      const type = item.dataset.type
      const isVisible = name.includes(searchTerm) || type.includes(searchTerm)
      
      item.style.display = isVisible ? "block" : "none"
      if (isVisible) hasVisibleItems = true
    })

    this.noResultsTarget.classList.toggle("hidden", hasVisibleItems)
  }

  filterByCategory(event) {
    const category = event.currentTarget.dataset.category

    // Update active state
    this.categoryFilterTargets.forEach(button => {
      if (button.dataset.category === category) {
        button.classList.add("border-primary-300", "bg-primary-50", "text-primary-700")
        button.classList.remove("border-neutral-300", "bg-white", "text-neutral-700")
      } else {
        button.classList.remove("border-primary-300", "bg-primary-50", "text-primary-700")
        button.classList.add("border-neutral-300", "bg-white", "text-neutral-700")
      }
    })

    // Filter items
    let hasVisibleItems = false
    this.sourceTypeTargets.forEach(item => {
      const itemCategory = item.dataset.category
      const isVisible = category === "all" || itemCategory === category
      
      item.style.display = isVisible ? "block" : "none"
      if (isVisible) hasVisibleItems = true
    })

    this.noResultsTarget.classList.toggle("hidden", hasVisibleItems)
  }

  selectType(event) {
    const typeElement = event.currentTarget
    this.selectedType = typeElement.dataset.type
    const typeName = typeElement.querySelector("h3").textContent

    console.log("Selected type:", this.selectedType, "Name:", typeName)

    // Update selected type name
    this.selectedTypeNameTarget.textContent = typeName

    // Move to step 2
    this.goToStep2()

    // Load configuration form
    this.loadConfigurationForm()
  }

  goToStep2() {
    // Hide step 1, show step 2
    this.step1ContentTarget.classList.add("hidden")
    this.step2ContentTarget.classList.remove("hidden")

    // Update progress indicators
    this.step2IndicatorTarget.classList.remove("bg-neutral-200", "text-neutral-500")
    this.step2IndicatorTarget.classList.add("bg-primary-600", "text-white")
    this.step1ConnectorTarget.classList.remove("bg-neutral-200")
    this.step1ConnectorTarget.classList.add("bg-primary-600")

    this.currentStep = 2
  }

  goBackToStep1() {
    // Show step 1, hide step 2
    this.step1ContentTarget.classList.remove("hidden")
    this.step2ContentTarget.classList.add("hidden")

    // Reset progress indicators
    this.step2IndicatorTarget.classList.add("bg-neutral-200", "text-neutral-500")
    this.step2IndicatorTarget.classList.remove("bg-primary-600", "text-white")
    this.step1ConnectorTarget.classList.add("bg-neutral-200")
    this.step1ConnectorTarget.classList.remove("bg-primary-600")

    this.currentStep = 1
  }

  async loadConfigurationForm() {
    try {
      console.log("Loading configuration form for:", this.selectedType)
      
      // Show loading state
      this.configurationFormTarget.innerHTML = `
        <div class="flex items-center justify-center py-12">
          <div class="text-center">
            <i class="fas fa-spinner fa-spin text-4xl text-primary-600 mb-4"></i>
            <p class="text-neutral-600">Loading configuration form...</p>
          </div>
        </div>
      `

      // Fetch the form partial
      const response = await fetch(`/dashboard/data_sources/connection_fields/${this.selectedType}`)
      
      if (!response.ok) {
        throw new Error(`Failed to load form: ${response.status}`)
      }

      const html = await response.text()
      
      // Create the form wrapper
      const formHtml = `
        <form action="/dashboard/data_sources" method="post" data-turbo="false" enctype="multipart/form-data">
          <input type="hidden" name="authenticity_token" value="${this.getAuthenticityToken()}" />
          <input type="hidden" name="data_source[source_type]" value="${this.selectedType}" />
          
          <!-- Name field -->
          <div class="mb-6">
            <label for="data_source_name" class="block text-sm font-medium text-neutral-700 mb-1">
              Data Source Name <span class="text-red-500">*</span>
            </label>
            <input type="text" 
                   id="data_source_name"
                   name="data_source[name]" 
                   class="form-input w-full" 
                   placeholder="e.g., Customer Data, Sales Report"
                   required>
            <p class="mt-1 text-sm text-neutral-500">A descriptive name to identify this data source</p>
          </div>
          
          ${html}
          
          <div class="mt-6 flex items-center justify-between">
            <button type="button"
                    class="px-4 py-2 border border-neutral-300 rounded-lg text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200"
                    data-action="click->data-source-wizard#goBackToStep1">
              <i class="fas fa-arrow-left mr-2"></i>
              Back
            </button>
            
            <div class="flex items-center gap-3">
              <button type="submit" 
                      name="commit" 
                      value="test"
                      class="px-4 py-2 border border-primary-300 rounded-lg text-sm font-medium text-primary-700 bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200">
                <i class="fas fa-plug mr-2"></i>
                Test Connection
              </button>
              
              <button type="submit"
                      name="commit"
                      value="save"
                      class="px-6 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200 shadow-md hover:shadow-lg">
                <i class="fas fa-save mr-2"></i>
                Save Data Source
              </button>
            </div>
          </div>
        </form>
      `
      
      this.configurationFormTarget.innerHTML = formHtml
      
      // Re-initialize any nested Stimulus controllers
      const application = this.application
      application.start()
      
    } catch (error) {
      console.error("Error loading configuration form:", error)
      this.configurationFormTarget.innerHTML = `
        <div class="rounded-lg bg-red-50 p-4 border border-red-200">
          <div class="flex">
            <div class="flex-shrink-0">
              <i class="fas fa-exclamation-triangle text-red-400"></i>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">Error loading form</h3>
              <p class="mt-2 text-sm text-red-700">${error.message}</p>
              <button type="button"
                      class="mt-3 text-sm font-medium text-red-800 hover:text-red-700"
                      data-action="click->data-source-wizard#loadConfigurationForm">
                Try again
              </button>
            </div>
          </div>
        </div>
      `
    }
  }

  getAuthenticityToken() {
    const token = document.querySelector('meta[name="csrf-token"]')
    return token ? token.content : ""
  }
}