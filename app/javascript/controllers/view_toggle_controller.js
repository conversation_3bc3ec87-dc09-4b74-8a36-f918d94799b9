import { Controller } from "@hotwired/stimulus"

// Controller for toggling between table and card views
export default class extends Controller {
  static targets = ["tableView", "cardView", "tableBtn", "cardBtn"]
  static values = { 
    currentView: { type: String, default: "table" }
  }
  
  connect() {
    this.updateView();
  }
  
  showTable() {
    this.currentViewValue = "table";
    this.updateView();
    this.dispatch("viewChanged", { detail: { view: "table" } });
  }
  
  showCards() {
    this.currentViewValue = "card";
    this.updateView();
    this.dispatch("viewChanged", { detail: { view: "card" } });
  }
  
  updateView() {
    if (this.currentViewValue === "table") {
      // Show table view
      this.tableViewTarget.classList.remove("hidden");
      this.tableViewTarget.classList.add("block");
      this.cardViewTarget.classList.remove("block");
      this.cardViewTarget.classList.add("hidden");
      
      // Update button states
      this.tableBtnTarget.classList.add("bg-white", "text-neutral-900", "shadow-sm");
      this.tableBtnTarget.classList.remove("text-neutral-600", "hover:text-neutral-900");
      this.tableBtnTarget.setAttribute("aria-selected", "true");
      
      this.cardBtnTarget.classList.remove("bg-white", "text-neutral-900", "shadow-sm");
      this.cardBtnTarget.classList.add("text-neutral-600", "hover:text-neutral-900");
      this.cardBtnTarget.setAttribute("aria-selected", "false");
    } else {
      // Show card view
      this.cardViewTarget.classList.remove("hidden");
      this.cardViewTarget.classList.add("block");
      this.tableViewTarget.classList.remove("block");
      this.tableViewTarget.classList.add("hidden");
      
      // Update button states
      this.cardBtnTarget.classList.add("bg-white", "text-neutral-900", "shadow-sm");
      this.cardBtnTarget.classList.remove("text-neutral-600", "hover:text-neutral-900");
      this.cardBtnTarget.setAttribute("aria-selected", "true");
      
      this.tableBtnTarget.classList.remove("bg-white", "text-neutral-900", "shadow-sm");
      this.tableBtnTarget.classList.add("text-neutral-600", "hover:text-neutral-900");
      this.tableBtnTarget.setAttribute("aria-selected", "false");
    }
  }
  
  // Auto-switch to card view on mobile
  currentViewValueChanged() {
    this.updateView();
  }
}
