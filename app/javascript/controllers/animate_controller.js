import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static values = { 
    animation: { type: String, default: "fadeIn" },
    delay: { type: Number, default: 0 },
    duration: { type: Number, default: 1000 }
  }
  
  connect() {
    // Add initial hidden state
    this.element.style.opacity = "0";
    
    // Set up intersection observer
    this.observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting && !this.animated) {
            setTimeout(() => {
              this.animate();
            }, this.delayValue);
            this.animated = true;
          }
        });
      },
      { 
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px"
      }
    );
    
    this.observer.observe(this.element);
  }
  
  disconnect() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
  
  animate() {
    // Add animation class
    this.element.style.opacity = "1";
    this.element.classList.add(`animate-${this.animationValue}`);
    
    // Define animations
    const animations = {
      fadeIn: [
        { opacity: 0 },
        { opacity: 1 }
      ],
      fadeInUp: [
        { opacity: 0, transform: "translateY(30px)" },
        { opacity: 1, transform: "translateY(0)" }
      ],
      fadeInDown: [
        { opacity: 0, transform: "translateY(-30px)" },
        { opacity: 1, transform: "translateY(0)" }
      ],
      fadeInLeft: [
        { opacity: 0, transform: "translateX(30px)" },
        { opacity: 1, transform: "translateX(0)" }
      ],
      fadeInRight: [
        { opacity: 0, transform: "translateX(-30px)" },
        { opacity: 1, transform: "translateX(0)" }
      ]
    };
    
    const animation = animations[this.animationValue] || animations.fadeIn;
    
    this.element.animate(animation, {
      duration: this.durationValue,
      easing: "cubic-bezier(0.16, 1, 0.3, 1)",
      fill: "forwards"
    });
  }
}
