import { Controller } from "@hotwired/stimulus"

// FAQ Accordion Controller
//
// Provides accessible accordion functionality for FAQ sections.
//
// Configuration:
// - data-faq-accordion-value="true" (default): Only one item can be open at a time
// - data-faq-accordion-value="false": Multiple items can be open simultaneously
// - data-faq-duration-value="300" (default): Animation duration in milliseconds
//
// Usage:
// <div data-controller="faq" data-faq-accordion-value="true">
//   <button data-action="click->faq#toggle" data-faq-target="trigger">Question</button>
//   <div data-faq-target="content">Answer content</div>
//   <i data-faq-target="icon">Icon</i>
// </div>

export default class extends Controller {
  static targets = ["trigger", "content", "icon"]
  static values = {
    duration: { type: Number, default: 300 },
    accordion: { type: Boolean, default: true }
  }
  
  connect() {
    this.setupInitialState();
  }
  
  setupInitialState() {
    // Set up initial ARIA attributes and states
    this.triggerTargets.forEach((trigger, index) => {
      const content = this.contentTargets[index];
      const icon = this.iconTargets[index];
      
      if (trigger && content) {
        // Generate unique IDs for accessibility
        const contentId = `faq-content-${index}`;
        const triggerId = `faq-trigger-${index}`;
        
        trigger.setAttribute('id', triggerId);
        trigger.setAttribute('aria-expanded', 'false');
        trigger.setAttribute('aria-controls', contentId);
        
        content.setAttribute('id', contentId);
        content.setAttribute('aria-labelledby', triggerId);
        content.setAttribute('role', 'region');
        
        // Set initial styles for smooth animation
        content.style.maxHeight = '0';
        content.style.overflow = 'hidden';
        content.style.transition = `max-height ${this.durationValue}ms ease-in-out, opacity ${this.durationValue}ms ease-in-out`;
        content.style.opacity = '0';
        content.classList.remove('hidden');
        
        // Set initial icon state
        if (icon) {
          icon.style.transition = `transform ${this.durationValue}ms ease-in-out`;
          icon.style.transform = 'rotate(0deg)';
        }
      }
    });
  }
  
  toggle(event) {
    event.preventDefault();

    const trigger = event.currentTarget;
    const triggerIndex = this.triggerTargets.indexOf(trigger);
    const content = this.contentTargets[triggerIndex];
    const icon = this.iconTargets[triggerIndex];

    if (!content) return;

    const isExpanded = trigger.getAttribute('aria-expanded') === 'true';

    // Close all other items first (accordion behavior) - only if accordion mode is enabled
    if (this.accordionValue) {
      this.triggerTargets.forEach((otherTrigger, otherIndex) => {
        if (otherIndex !== triggerIndex && otherTrigger.getAttribute('aria-expanded') === 'true') {
          const otherContent = this.contentTargets[otherIndex];
          const otherIcon = this.iconTargets[otherIndex];
          this.collapse(otherTrigger, otherContent, otherIcon);
        }
      });
    }

    // Toggle the clicked item
    if (isExpanded) {
      this.collapse(trigger, content, icon);
    } else {
      this.expand(trigger, content, icon);
    }

    // Announce state change for screen readers
    this.announceStateChange(isExpanded);
  }
  
  expand(trigger, content, icon) {
    // Update ARIA state
    trigger.setAttribute('aria-expanded', 'true');
    
    // Get the natural height
    const naturalHeight = content.scrollHeight;
    
    // Animate to natural height
    requestAnimationFrame(() => {
      content.style.maxHeight = naturalHeight + 'px';
      content.style.opacity = '1';
    });
    
    // Rotate icon
    if (icon) {
      icon.style.transform = 'rotate(180deg)';
    }
    
    // Clean up after animation completes
    setTimeout(() => {
      content.style.maxHeight = 'none';
    }, this.durationValue);
  }
  
  collapse(trigger, content, icon) {
    // Update ARIA state
    trigger.setAttribute('aria-expanded', 'false');
    
    // Set explicit height first for smooth animation
    content.style.maxHeight = content.scrollHeight + 'px';
    
    // Force reflow
    content.offsetHeight;
    
    // Animate to collapsed state
    requestAnimationFrame(() => {
      content.style.maxHeight = '0';
      content.style.opacity = '0';
    });
    
    // Rotate icon back
    if (icon) {
      icon.style.transform = 'rotate(0deg)';
    }
  }
  
  announceStateChange(wasExpanded) {
    const message = wasExpanded ? 'FAQ section collapsed' : 'FAQ section expanded';
    
    // Create a temporary element for screen reader announcement
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }
  
  // Keyboard navigation support
  keydown(event) {
    const trigger = event.currentTarget;
    
    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        this.toggle(event);
        break;
      case 'ArrowDown':
        event.preventDefault();
        this.focusNext(trigger);
        break;
      case 'ArrowUp':
        event.preventDefault();
        this.focusPrevious(trigger);
        break;
      case 'Home':
        event.preventDefault();
        this.focusFirst();
        break;
      case 'End':
        event.preventDefault();
        this.focusLast();
        break;
    }
  }
  
  focusNext(currentTrigger) {
    const currentIndex = this.triggerTargets.indexOf(currentTrigger);
    const nextIndex = (currentIndex + 1) % this.triggerTargets.length;
    this.triggerTargets[nextIndex].focus();
  }
  
  focusPrevious(currentTrigger) {
    const currentIndex = this.triggerTargets.indexOf(currentTrigger);
    const previousIndex = currentIndex === 0 ? this.triggerTargets.length - 1 : currentIndex - 1;
    this.triggerTargets[previousIndex].focus();
  }
  
  focusFirst() {
    this.triggerTargets[0].focus();
  }
  
  focusLast() {
    this.triggerTargets[this.triggerTargets.length - 1].focus();
  }
  
  // Public methods for external control
  expandAll() {
    this.triggerTargets.forEach((trigger, index) => {
      const content = this.contentTargets[index];
      const icon = this.iconTargets[index];
      
      if (trigger.getAttribute('aria-expanded') === 'false') {
        this.expand(trigger, content, icon);
      }
    });
  }
  
  collapseAll() {
    this.triggerTargets.forEach((trigger, index) => {
      const content = this.contentTargets[index];
      const icon = this.iconTargets[index];
      
      if (trigger.getAttribute('aria-expanded') === 'true') {
        this.collapse(trigger, content, icon);
      }
    });
  }
}
