import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["display"]
  static values = { 
    target: Number,
    duration: { type: Number, default: 2000 },
    prefix: { type: String, default: "" },
    suffix: { type: String, default: "" },
    decimals: { type: Number, default: 0 }
  }
  
  connect() {
    this.observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting && !this.animated) {
            this.animate();
            this.animated = true;
          }
        });
      },
      { threshold: 0.5 }
    );
    
    this.observer.observe(this.element);
  }
  
  disconnect() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
  
  animate() {
    const startTime = performance.now();
    const startValue = 0;
    const endValue = this.targetValue;
    
    const updateCounter = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / this.durationValue, 1);
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const currentValue = startValue + (endValue - startValue) * easeOutQuart;
      
      // Format the display value
      let displayValue = currentValue.toFixed(this.decimalsValue);
      if (this.decimalsValue === 0) {
        displayValue = Math.floor(currentValue).toString();
      }
      
      // Add prefix and suffix
      this.displayTarget.textContent = `${this.prefixValue}${displayValue}${this.suffixValue}`;
      
      if (progress < 1) {
        requestAnimationFrame(updateCounter);
      }
    };
    
    requestAnimationFrame(updateCounter);
  }
}
