import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["fileInput", "filePreview", "fileName", "fileSize", "tabButton", "tabContent"]

  connect() {
    console.log("Enhanced JSON form controller connected")
    
    // Set up drag and drop
    this.setupDragAndDrop()
    
    // Set up file input listener
    if (this.hasFileInputTarget) {
      this.fileInputTarget.addEventListener('change', this.handleFileChange.bind(this))
    }
  }

  // Tab switching with enhanced animations
  switchTab(event) {
    const tab = event.currentTarget.dataset.tab
    console.log("Switching to tab:", tab)
    
    // Update tab buttons with smooth transitions
    this.tabButtonTargets.forEach(btn => {
      const isActive = btn.dataset.tab === tab
      
      // Remove all state classes
      btn.classList.remove(
        'bg-white', 'text-primary-600', 'shadow-sm',
        'text-slate-600', 'hover:text-slate-800', 'hover:bg-slate-50'
      )
      
      if (isActive) {
        btn.classList.add('bg-white', 'text-primary-600', 'shadow-sm')
        btn.setAttribute('aria-selected', 'true')
      } else {
        btn.classList.add('text-slate-600', 'hover:text-slate-800', 'hover:bg-slate-50')
        btn.setAttribute('aria-selected', 'false')
      }
    })
    
    // Show/hide content with fade animation
    this.tabContentTargets.forEach(content => {
      const isTargetContent = content.id === `json-${tab}-panel`
      
      if (isTargetContent) {
        content.classList.remove('hidden')
        content.style.opacity = '0'
        content.style.transform = 'translateY(10px)'
        
        // Smooth fade in
        requestAnimationFrame(() => {
          content.style.transition = 'opacity 0.3s ease, transform 0.3s ease'
          content.style.opacity = '1'
          content.style.transform = 'translateY(0)'
        })
      } else {
        content.style.transition = 'opacity 0.2s ease'
        content.style.opacity = '0'
        
        setTimeout(() => {
          content.classList.add('hidden')
        }, 200)
      }
    })
  }

  // Enhanced file upload handling
  handleFileChange(event) {
    const file = event.target.files[0]
    if (file) {
      console.log("File selected:", file.name, file.size)
      
      // Validate file
      if (!this.validateFile(file)) {
        return
      }
      
      // Update preview
      this.updateFilePreview(file)
      
      // Show preview with animation
      if (this.hasFilePreviewTarget) {
        this.filePreviewTarget.classList.remove('hidden')
        this.filePreviewTarget.style.opacity = '0'
        this.filePreviewTarget.style.transform = 'translateY(10px)'
        
        requestAnimationFrame(() => {
          this.filePreviewTarget.style.transition = 'opacity 0.3s ease, transform 0.3s ease'
          this.filePreviewTarget.style.opacity = '1'
          this.filePreviewTarget.style.transform = 'translateY(0)'
        })
      }
      
      // Trigger file analysis
      this.analyzeFile(file)
    }
  }

  validateFile(file) {
    const maxSize = 100 * 1024 * 1024 // 100MB
    const allowedTypes = ['application/json', 'text/plain']
    const allowedExtensions = ['.json', '.jsonl', '.gz']
    
    // Check file size
    if (file.size > maxSize) {
      this.showFileError(`File size (${this.formatFileSize(file.size)}) exceeds the 100MB limit.`)
      return false
    }
    
    // Check file type and extension
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
    const isValidType = allowedTypes.includes(file.type) || allowedExtensions.includes(fileExtension)
    
    if (!isValidType) {
      this.showFileError(`File type not supported. Please upload a JSON, JSONL, or GZ file.`)
      return false
    }
    
    return true
  }

  updateFilePreview(file) {
    if (this.hasFileNameTarget) {
      this.fileNameTarget.textContent = file.name
    }
    if (this.hasFileSizeTarget) {
      this.fileSizeTarget.textContent = this.formatFileSize(file.size)
    }
  }

  analyzeFile(file) {
    // Only analyze JSON files
    if (!file.name.toLowerCase().match(/\.(json|jsonl)$/)) {
      return
    }
    
    console.log("Analyzing JSON file...")
    
    // Read first few KB to detect format
    const reader = new FileReader()
    reader.onload = (e) => {
      const text = e.target.result
      
      try {
        // Try to detect JSON format
        const format = this.detectJsonFormat(text)
        if (format) {
          this.updateFormatSelection(format)
        }
        
        // Show analysis results
        this.showAnalysisResults(format)
        
      } catch (error) {
        console.warn("Could not analyze JSON file:", error)
      }
    }
    
    // Read first 5KB of file
    const blob = file.slice(0, 5120)
    reader.readAsText(blob)
  }

  detectJsonFormat(text) {
    const trimmed = text.trim()
    
    // Check for JSON Lines format
    if (trimmed.includes('\n') && !trimmed.startsWith('[') && !trimmed.startsWith('{')) {
      const lines = trimmed.split('\n').filter(line => line.trim())
      if (lines.length > 1) {
        try {
          JSON.parse(lines[0])
          return 'lines'
        } catch (e) {
          // Not JSON Lines
        }
      }
    }
    
    // Check for array format
    if (trimmed.startsWith('[')) {
      return 'array'
    }
    
    // Check for nested object format
    if (trimmed.startsWith('{')) {
      try {
        const parsed = JSON.parse(trimmed)
        // Look for common nested patterns
        if (parsed.data || parsed.results || parsed.items || parsed.records) {
          return 'nested'
        }
        return 'array' // Default for single object
      } catch (e) {
        // Invalid JSON
      }
    }
    
    return 'array' // Default
  }

  updateFormatSelection(format) {
    const formatSelect = this.element.querySelector('select[name*="json_format"]')
    if (formatSelect) {
      formatSelect.value = format
      
      // Add visual feedback
      formatSelect.style.borderColor = '#10b981'
      setTimeout(() => {
        formatSelect.style.borderColor = ''
      }, 2000)
    }
  }

  showAnalysisResults(format) {
    // Create or update analysis results display
    let analysisDiv = this.element.querySelector('.json-analysis-results')
    
    if (!analysisDiv) {
      analysisDiv = document.createElement('div')
      analysisDiv.className = 'json-analysis-results mt-4 p-4 bg-blue-50 rounded-xl border border-blue-200'
      
      if (this.hasFilePreviewTarget) {
        this.filePreviewTarget.appendChild(analysisDiv)
      }
    }
    
    const formatNames = {
      'array': 'Array of Objects',
      'lines': 'JSON Lines (JSONL)',
      'nested': 'Nested Object'
    }
    
    analysisDiv.innerHTML = `
      <div class="flex items-start">
        <i class="fas fa-magic text-blue-600 mt-1 mr-3"></i>
        <div class="flex-1">
          <h4 class="font-semibold text-blue-900 mb-2">Auto-detected Settings</h4>
          <div class="text-sm text-blue-800">
            <div class="flex items-center">
              <i class="fas fa-list mr-2"></i>
              Format: <strong class="ml-1">${formatNames[format] || 'Unknown'}</strong>
            </div>
          </div>
          <p class="text-xs text-blue-700 mt-2">
            Settings have been automatically applied. You can adjust them if needed.
          </p>
        </div>
      </div>
    `
  }

  clearFile() {
    console.log("Clearing file")
    
    if (this.hasFileInputTarget) {
      this.fileInputTarget.value = ''
    }
    
    if (this.hasFilePreviewTarget) {
      // Animate out
      this.filePreviewTarget.style.transition = 'opacity 0.2s ease, transform 0.2s ease'
      this.filePreviewTarget.style.opacity = '0'
      this.filePreviewTarget.style.transform = 'translateY(-10px)'
      
      setTimeout(() => {
        this.filePreviewTarget.classList.add('hidden')
      }, 200)
    }
    
    // Clear analysis results
    const analysisDiv = this.element.querySelector('.json-analysis-results')
    if (analysisDiv) {
      analysisDiv.remove()
    }
  }

  showFileError(message) {
    // Clear file input
    if (this.hasFileInputTarget) {
      this.fileInputTarget.value = ''
    }
    
    // Show error message
    const errorDiv = document.createElement('div')
    errorDiv.className = 'mt-4 p-4 bg-red-50 rounded-xl border border-red-200'
    errorDiv.innerHTML = `
      <div class="flex items-start">
        <i class="fas fa-exclamation-triangle text-red-600 mt-1 mr-3"></i>
        <div>
          <h4 class="font-semibold text-red-900 mb-1">File Error</h4>
          <p class="text-sm text-red-800">${message}</p>
        </div>
      </div>
    `
    
    // Insert after file input
    const uploadArea = this.element.querySelector('label[for="json_file"]')
    if (uploadArea) {
      uploadArea.parentNode.insertBefore(errorDiv, uploadArea.nextSibling)
      
      // Remove error after 5 seconds
      setTimeout(() => {
        errorDiv.remove()
      }, 5000)
    }
  }

  setupDragAndDrop() {
    const uploadArea = this.element.querySelector('label[for="json_file"]')
    if (!uploadArea) return
    
    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      uploadArea.addEventListener(eventName, this.preventDefaults, false)
      document.body.addEventListener(eventName, this.preventDefaults, false)
    })
    
    // Highlight drop area when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
      uploadArea.addEventListener(eventName, () => this.highlight(uploadArea), false)
    })
    
    ['dragleave', 'drop'].forEach(eventName => {
      uploadArea.addEventListener(eventName, () => this.unhighlight(uploadArea), false)
    })
    
    // Handle dropped files
    uploadArea.addEventListener('drop', this.handleDrop.bind(this), false)
  }

  preventDefaults(e) {
    e.preventDefault()
    e.stopPropagation()
  }

  highlight(element) {
    element.classList.add('border-primary-500', 'bg-primary-100')
    element.classList.remove('border-slate-300')
  }

  unhighlight(element) {
    element.classList.remove('border-primary-500', 'bg-primary-100')
    element.classList.add('border-slate-300')
  }

  handleDrop(e) {
    const dt = e.dataTransfer
    const files = dt.files
    
    if (files.length > 0) {
      const file = files[0]
      
      // Update file input
      if (this.hasFileInputTarget) {
        // Create a new FileList-like object
        const dataTransfer = new DataTransfer()
        dataTransfer.items.add(file)
        this.fileInputTarget.files = dataTransfer.files
        
        // Trigger change event
        this.handleFileChange({ target: { files: [file] } })
      }
    }
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}
