import { Controller } from "@hotwired/stimulus"

// Enhanced data sources manager with search, filtering, and view management
export default class extends Controller {
  static targets = [
    "searchInput", "visibleCount", "listContainer", "gridContainer", 
    "sourceItem", "sourceCard", "filterButton", "filterLabel", 
    "activeFilterCount", "clearButton", "bulkActionsBar"
  ]
  static values = { 
    currentFilter: { type: String, default: "all" },
    currentType: { type: String, default: "all" },
    activeFilters: { type: Number, default: 0 }
  }
  
  connect() {
    this.originalItems = Array.from(this.sourceItemTargets);
    this.originalCards = Array.from(this.sourceCardTargets);
    this.updateVisibleCount();
    this.setupKeyboardShortcuts();
  }
  
  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Cmd/Ctrl + K for search focus
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        this.searchInputTarget.focus();
      }
      // Escape to clear search when focused
      if (e.key === 'Escape' && document.activeElement === this.searchInputTarget) {
        this.clearSearch();
      }
    });
  }
  
  // Search functionality
  search() {
    const query = this.searchInputTarget.value.toLowerCase().trim();
    this.filterItems(query);
    
    // Show/hide clear button
    if (this.hasClearButtonTarget) {
      this.clearButtonTarget.style.display = query ? 'flex' : 'none';
    }
  }
  
  clearSearch() {
    this.searchInputTarget.value = '';
    this.search();
    this.searchInputTarget.focus();
  }
  
  // Filter by status
  filterByStatus(event) {
    const status = event.currentTarget.dataset.status;
    this.currentFilterValue = status;
    this.applyFilters();
    this.updateFilterIndicators();
    
    // Close dropdown
    this.dispatch("filterChanged", { detail: { type: "status", value: status } });
  }
  
  // Filter by type
  filterByType(event) {
    const type = event.currentTarget.dataset.type;
    this.currentTypeValue = type;
    this.applyFilters();
    this.updateFilterIndicators();
    
    // Close dropdown
    this.dispatch("filterChanged", { detail: { type: "sourceType", value: type } });
  }
  
  // Apply all filters
  applyFilters() {
    const query = this.searchInputTarget.value.toLowerCase().trim();
    this.filterItems(query);
  }
  
  filterItems(query = "") {
    let visibleCount = 0;
    
    // Filter list items
    this.sourceItemTargets.forEach(item => {
      if (this.shouldShowItem(item, query)) {
        item.style.display = "";
        visibleCount++;
      } else {
        item.style.display = "none";
      }
    });
    
    // Filter grid cards
    this.sourceCardTargets.forEach(card => {
      if (this.shouldShowItem(card, query)) {
        card.style.display = "";
      } else {
        card.style.display = "none";
      }
    });
    
    this.updateVisibleCount(visibleCount);
  }
  
  shouldShowItem(item, query) {
    const name = item.dataset.name || "";
    const status = item.dataset.status || "";
    const type = item.dataset.type || "";
    
    // Check search query
    const matchesQuery = query === "" || name.includes(query);
    
    // Check status filter
    const matchesStatus = this.currentFilterValue === "all" || status === this.currentFilterValue;
    
    // Check type filter
    let matchesType = this.currentTypeValue === "all";
    if (!matchesType) {
      switch (this.currentTypeValue) {
        case "database":
          matchesType = ["postgresql", "mysql", "sqlite", "sqlserver", "mongodb", "redis", "elasticsearch"].includes(type);
          break;
        case "file":
          matchesType = ["csv", "excel", "json", "xml"].includes(type);
          break;
        case "api":
          matchesType = ["api", "webhook", "stream"].includes(type);
          break;
        case "cloud":
          matchesType = ["s3", "gcs", "azure_blob", "sftp"].includes(type);
          break;
        case "saas":
          matchesType = ["quickbooks", "stripe", "shopify", "salesforce"].includes(type);
          break;
        default:
          matchesType = true;
      }
    }
    
    return matchesQuery && matchesStatus && matchesType;
  }
  
  updateVisibleCount(count = null) {
    if (count === null) {
      // Count visible items
      count = this.sourceItemTargets.filter(item => item.style.display !== "none").length;
    }
    
    if (this.hasVisibleCountTarget) {
      this.visibleCountTarget.textContent = count;
    }
  }
  
  // Bulk operations
  selectAll() {
    this.sourceItemTargets.forEach(item => {
      const checkbox = item.querySelector('input[type="checkbox"]');
      if (checkbox && item.style.display !== "none") {
        checkbox.checked = true;
      }
    });
    
    this.updateBulkActions();
  }
  
  selectNone() {
    this.sourceItemTargets.forEach(item => {
      const checkbox = item.querySelector('input[type="checkbox"]');
      if (checkbox) {
        checkbox.checked = false;
      }
    });
    
    this.updateBulkActions();
  }
  
  updateBulkActions() {
    const selectedItems = this.getSelectedItems();
    const bulkActionsContainer = this.element.querySelector('[data-bulk-actions]');
    
    if (bulkActionsContainer) {
      if (selectedItems.length > 0) {
        bulkActionsContainer.classList.remove('hidden');
        const countElement = bulkActionsContainer.querySelector('[data-selected-count]');
        if (countElement) {
          countElement.textContent = selectedItems.length;
        }
      } else {
        bulkActionsContainer.classList.add('hidden');
      }
    }
  }
  
  getSelectedItems() {
    return this.sourceItemTargets.filter(item => {
      const checkbox = item.querySelector('input[type="checkbox"]');
      return checkbox && checkbox.checked;
    });
  }
  
  // Export functionality
  exportSelected() {
    const selectedItems = this.getSelectedItems();
    if (selectedItems.length === 0) {
      this.showNotification("Please select at least one data source to export", "warning");
      return;
    }
    
    const data = selectedItems.map(item => ({
      name: item.dataset.name,
      type: item.dataset.type,
      status: item.dataset.status
    }));
    
    this.downloadJSON(data, "data-sources.json");
  }
  
  downloadJSON(data, filename) {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
  
  showNotification(message, type = "info") {
    // Create a simple notification
    const notification = document.createElement("div");
    notification.className = `fixed top-4 right-4 px-4 py-2 rounded-lg shadow-lg z-50 ${
      type === "warning" ? "bg-yellow-100 text-yellow-800 border border-yellow-200" :
      type === "error" ? "bg-red-100 text-red-800 border border-red-200" :
      "bg-blue-100 text-blue-800 border border-blue-200"
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.remove();
    }, 3000);
  }
  
  // Reset all filters
  resetFilters() {
    this.currentFilterValue = "all";
    this.currentTypeValue = "all";
    this.searchInputTarget.value = "";
    this.applyFilters();
    this.updateFilterIndicators();
    if (this.hasClearButtonTarget) {
      this.clearButtonTarget.style.display = 'none';
    }
  }
  
  updateFilterIndicators() {
    let activeCount = 0;
    let filterText = "All Filters";
    
    if (this.currentFilterValue !== "all") {
      activeCount++;
      filterText = this.currentFilterValue.charAt(0).toUpperCase() + this.currentFilterValue.slice(1);
    }
    
    if (this.currentTypeValue !== "all") {
      activeCount++;
      if (filterText === "All Filters") {
        filterText = this.getTypeLabel(this.currentTypeValue);
      } else {
        filterText += " • " + this.getTypeLabel(this.currentTypeValue);
      }
    }
    
    if (this.hasFilterLabelTarget) {
      this.filterLabelTarget.textContent = filterText;
    }
    
    if (this.hasActiveFilterCountTarget) {
      if (activeCount > 0) {
        this.activeFilterCountTarget.textContent = activeCount;
        this.activeFilterCountTarget.classList.remove('hidden');
      } else {
        this.activeFilterCountTarget.classList.add('hidden');
      }
    }
  }
  
  getTypeLabel(type) {
    const labels = {
      'database': 'Databases',
      'file': 'Files',
      'api': 'APIs',
      'cloud': 'Cloud Storage',
      'saas': 'SaaS'
    };
    return labels[type] || type;
  }
  
  // Bulk operations
  toggleSelectAll(event) {
    const isChecked = event.target.checked;
    this.sourceItemTargets.forEach(item => {
      if (item.style.display !== "none") {
        const checkbox = item.querySelector('input[type="checkbox"]');
        if (checkbox) checkbox.checked = isChecked;
      }
    });
    this.updateBulkActions();
  }
  
  updateBulkActions() {
    const selectedCount = this.getSelectedItems().length;
    
    if (this.hasBulkActionsBarTarget) {
      if (selectedCount > 0) {
        this.bulkActionsBarTarget.classList.remove('hidden');
        const countElement = this.bulkActionsBarTarget.querySelector('[data-selected-count]');
        if (countElement) countElement.textContent = selectedCount;
      } else {
        this.bulkActionsBarTarget.classList.add('hidden');
      }
    }
  }
  
  getSelectedItems() {
    return this.sourceItemTargets.filter(item => {
      const checkbox = item.querySelector('input[type="checkbox"]');
      return checkbox && checkbox.checked;
    }).map(item => ({
      id: item.dataset.sourceId,
      name: item.dataset.name,
      type: item.dataset.type,
      status: item.dataset.status
    }));
  }
  
  // Bulk actions
  async bulkSync() {
    const selected = this.getSelectedItems();
    if (selected.length === 0) return;
    
    if (confirm(`Sync ${selected.length} data source(s)?`)) {
      this.showNotification(`Syncing ${selected.length} data sources...`, 'info');
      // Implementation would make API calls here
    }
  }
  
  async bulkActivate() {
    const selected = this.getSelectedItems();
    if (selected.length === 0) return;
    
    if (confirm(`Activate ${selected.length} data source(s)?`)) {
      this.showNotification(`Activating ${selected.length} data sources...`, 'info');
      // Implementation would make API calls here
    }
  }
  
  async bulkDeactivate() {
    const selected = this.getSelectedItems();
    if (selected.length === 0) return;
    
    if (confirm(`Deactivate ${selected.length} data source(s)?`)) {
      this.showNotification(`Deactivating ${selected.length} data sources...`, 'info');
      // Implementation would make API calls here
    }
  }
  
  async bulkDelete() {
    const selected = this.getSelectedItems();
    if (selected.length === 0) return;
    
    if (confirm(`Are you sure you want to delete ${selected.length} data source(s)? This action cannot be undone.`)) {
      this.showNotification(`Deleting ${selected.length} data sources...`, 'warning');
      // Implementation would make API calls here
    }
  }
  
  // Export functionality
  exportAll() {
    const allData = this.sourceItemTargets.map(item => ({
      id: item.dataset.sourceId,
      name: item.dataset.name,
      type: item.dataset.type,
      status: item.dataset.status
    }));
    
    this.downloadJSON(allData, `data-sources-${new Date().toISOString().split('T')[0]}.json`);
    this.showNotification('All data sources exported successfully', 'success');
  }
  
  showImportModal() {
    // Would open import modal
    this.showNotification('Import feature coming soon', 'info');
  }
  
  refreshAll() {
    this.showNotification('Refreshing all data sources...', 'info');
    // Would refresh status via API
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  }
}
