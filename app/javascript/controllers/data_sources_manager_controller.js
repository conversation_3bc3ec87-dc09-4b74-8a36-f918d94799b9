import { Controller } from "@hotwired/stimulus"

// Enhanced data sources manager with search, filtering, and view management
export default class extends Controller {
  static targets = [
    "searchInput", "visibleCount", "listContainer", "gridContainer", 
    "sourceItem", "sourceCard"
  ]
  static values = { 
    currentFilter: { type: String, default: "all" },
    currentType: { type: String, default: "all" }
  }
  
  connect() {
    this.originalItems = Array.from(this.sourceItemTargets);
    this.originalCards = Array.from(this.sourceCardTargets);
    this.updateVisibleCount();
  }
  
  // Search functionality
  search() {
    const query = this.searchInputTarget.value.toLowerCase().trim();
    this.filterItems(query);
  }
  
  // Filter by status
  filterByStatus(event) {
    const status = event.currentTarget.dataset.status;
    this.currentFilterValue = status;
    this.applyFilters();
    
    // Close dropdown
    this.dispatch("filterChanged", { detail: { type: "status", value: status } });
  }
  
  // Filter by type
  filterByType(event) {
    const type = event.currentTarget.dataset.type;
    this.currentTypeValue = type;
    this.applyFilters();
    
    // Close dropdown
    this.dispatch("filterChanged", { detail: { type: "sourceType", value: type } });
  }
  
  // Apply all filters
  applyFilters() {
    const query = this.searchInputTarget.value.toLowerCase().trim();
    this.filterItems(query);
  }
  
  filterItems(query = "") {
    let visibleCount = 0;
    
    // Filter list items
    this.sourceItemTargets.forEach(item => {
      if (this.shouldShowItem(item, query)) {
        item.style.display = "";
        visibleCount++;
      } else {
        item.style.display = "none";
      }
    });
    
    // Filter grid cards
    this.sourceCardTargets.forEach(card => {
      if (this.shouldShowItem(card, query)) {
        card.style.display = "";
      } else {
        card.style.display = "none";
      }
    });
    
    this.updateVisibleCount(visibleCount);
  }
  
  shouldShowItem(item, query) {
    const name = item.dataset.name || "";
    const status = item.dataset.status || "";
    const type = item.dataset.type || "";
    
    // Check search query
    const matchesQuery = query === "" || name.includes(query);
    
    // Check status filter
    const matchesStatus = this.currentFilterValue === "all" || status === this.currentFilterValue;
    
    // Check type filter
    let matchesType = this.currentTypeValue === "all";
    if (!matchesType) {
      switch (this.currentTypeValue) {
        case "database":
          matchesType = ["postgresql", "mysql", "sqlite", "sqlserver", "mongodb", "redis", "elasticsearch"].includes(type);
          break;
        case "file":
          matchesType = ["csv", "excel", "json", "xml"].includes(type);
          break;
        case "api":
          matchesType = ["api", "webhook", "stream"].includes(type);
          break;
        case "cloud":
          matchesType = ["s3", "gcs", "azure_blob", "sftp"].includes(type);
          break;
        case "saas":
          matchesType = ["quickbooks", "stripe", "shopify", "salesforce"].includes(type);
          break;
        default:
          matchesType = true;
      }
    }
    
    return matchesQuery && matchesStatus && matchesType;
  }
  
  updateVisibleCount(count = null) {
    if (count === null) {
      // Count visible items
      count = this.sourceItemTargets.filter(item => item.style.display !== "none").length;
    }
    
    if (this.hasVisibleCountTarget) {
      this.visibleCountTarget.textContent = count;
    }
  }
  
  // Bulk operations
  selectAll() {
    this.sourceItemTargets.forEach(item => {
      const checkbox = item.querySelector('input[type="checkbox"]');
      if (checkbox && item.style.display !== "none") {
        checkbox.checked = true;
      }
    });
    
    this.updateBulkActions();
  }
  
  selectNone() {
    this.sourceItemTargets.forEach(item => {
      const checkbox = item.querySelector('input[type="checkbox"]');
      if (checkbox) {
        checkbox.checked = false;
      }
    });
    
    this.updateBulkActions();
  }
  
  updateBulkActions() {
    const selectedItems = this.getSelectedItems();
    const bulkActionsContainer = this.element.querySelector('[data-bulk-actions]');
    
    if (bulkActionsContainer) {
      if (selectedItems.length > 0) {
        bulkActionsContainer.classList.remove('hidden');
        const countElement = bulkActionsContainer.querySelector('[data-selected-count]');
        if (countElement) {
          countElement.textContent = selectedItems.length;
        }
      } else {
        bulkActionsContainer.classList.add('hidden');
      }
    }
  }
  
  getSelectedItems() {
    return this.sourceItemTargets.filter(item => {
      const checkbox = item.querySelector('input[type="checkbox"]');
      return checkbox && checkbox.checked;
    });
  }
  
  // Export functionality
  exportSelected() {
    const selectedItems = this.getSelectedItems();
    if (selectedItems.length === 0) {
      this.showNotification("Please select at least one data source to export", "warning");
      return;
    }
    
    const data = selectedItems.map(item => ({
      name: item.dataset.name,
      type: item.dataset.type,
      status: item.dataset.status
    }));
    
    this.downloadJSON(data, "data-sources.json");
  }
  
  downloadJSON(data, filename) {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
  
  showNotification(message, type = "info") {
    // Create a simple notification
    const notification = document.createElement("div");
    notification.className = `fixed top-4 right-4 px-4 py-2 rounded-lg shadow-lg z-50 ${
      type === "warning" ? "bg-yellow-100 text-yellow-800 border border-yellow-200" :
      type === "error" ? "bg-red-100 text-red-800 border border-red-200" :
      "bg-blue-100 text-blue-800 border border-blue-200"
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.remove();
    }, 3000);
  }
  
  // Reset all filters
  resetFilters() {
    this.currentFilterValue = "all";
    this.currentTypeValue = "all";
    this.searchInputTarget.value = "";
    this.applyFilters();
  }
}
