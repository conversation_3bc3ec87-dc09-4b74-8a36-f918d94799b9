import { Controller } from "@hotwired/stimulus"

// Simple dropdown controller for menus
export default class extends Controller {
  static targets = ["menu"]
  static values = { 
    open: { type: Boolean, default: false }
  }
  
  connect() {
    this.boundCloseOnOutsideClick = this.closeOnOutsideClick.bind(this);
    this.boundCloseOnEscape = this.closeOnEscape.bind(this);
  }
  
  disconnect() {
    this.removeEventListeners();
  }
  
  toggle() {
    this.openValue = !this.openValue;
  }
  
  open() {
    this.openValue = true;
  }
  
  close() {
    this.openValue = false;
  }
  
  openValueChanged() {
    if (this.openValue) {
      this.showMenu();
      this.addEventListeners();
    } else {
      this.hideMenu();
      this.removeEventListeners();
    }
    
    // Update ARIA attributes
    const trigger = this.element.querySelector('[aria-haspopup]');
    if (trigger) {
      trigger.setAttribute('aria-expanded', this.openValue.toString());
    }
  }
  
  showMenu() {
    this.menuTarget.classList.remove('hidden');
    this.menuTarget.classList.add('block');
    
    // Focus first menu item for accessibility
    const firstItem = this.menuTarget.querySelector('button, a');
    if (firstItem) {
      firstItem.focus();
    }
  }
  
  hideMenu() {
    this.menuTarget.classList.remove('block');
    this.menuTarget.classList.add('hidden');
  }
  
  closeOnOutsideClick(event) {
    if (!this.element.contains(event.target)) {
      this.close();
    }
  }
  
  closeOnEscape(event) {
    if (event.key === 'Escape') {
      this.close();
      
      // Return focus to trigger
      const trigger = this.element.querySelector('[aria-haspopup]');
      if (trigger) {
        trigger.focus();
      }
    }
  }
  
  addEventListeners() {
    document.addEventListener('click', this.boundCloseOnOutsideClick);
    document.addEventListener('keydown', this.boundCloseOnEscape);
  }
  
  removeEventListeners() {
    document.removeEventListener('click', this.boundCloseOnOutsideClick);
    document.removeEventListener('keydown', this.boundCloseOnEscape);
  }
}
