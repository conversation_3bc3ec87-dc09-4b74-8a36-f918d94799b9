import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["mobileMenu", "mobileMenuButton", "userMenu", "userMenuButton", "menuLine", "notificationMenu", "notificationButton"]

  connect() {
    // Bind methods for event listeners
    this.handleClickOutside = this.handleClickOutside.bind(this);
    this.handleResize = this.handleResize.bind(this);

    // Add event listeners
    document.addEventListener('click', this.handleClickOutside);
    window.addEventListener('resize', this.handleResize);
  }

  disconnect() {
    // Remove event listeners
    document.removeEventListener('click', this.handleClickOutside);
    window.removeEventListener('resize', this.handleResize);
  }

  handleClickOutside(event) {
    // Close mobile menu if clicking outside
    if (this.hasMobileMenuTarget && !this.element.contains(event.target)) {
      this.closeMobileMenu();
    }
    
    // Close user menu if clicking outside
    if (this.hasUserMenuTarget && !this.userMenuTarget.contains(event.target) && !this.userMenuButtonTarget.contains(event.target)) {
      this.closeUserMenu();
    }

    // Close notification menu if clicking outside
    if (this.hasNotificationMenuTarget && !this.notificationMenuTarget.contains(event.target) && !this.notificationButtonTarget.contains(event.target)) {
      this.closeNotificationMenu();
    }
  }

  handleResize() {
    // Close mobile menu on resize to desktop
    if (window.innerWidth >= 768) {
      this.closeMobileMenu();
    }
  }

  toggleMobileMenu() {
    if (!this.hasMobileMenuTarget) return;

    const isOpen = !this.mobileMenuTarget.classList.contains('hidden');

    if (isOpen) {
      this.closeMobileMenu();
    } else {
      this.openMobileMenu();
    }
  }

  openMobileMenu() {
    this.mobileMenuTarget.classList.remove('hidden');
    
    // Update button state
    if (this.hasMobileMenuButtonTarget) {
      this.mobileMenuButtonTarget.setAttribute('aria-expanded', 'true');
    }
    
    // Animate hamburger to X
    this.animateMenuIcon(true);
    
    // Prevent body scroll on mobile
    if (window.innerWidth < 768) {
      document.body.style.overflow = 'hidden';
    }
  }

  closeMobileMenu() {
    if (!this.hasMobileMenuTarget) return;
    
    this.mobileMenuTarget.classList.add('hidden');
    
    // Update button state
    if (this.hasMobileMenuButtonTarget) {
      this.mobileMenuButtonTarget.setAttribute('aria-expanded', 'false');
    }
    
    // Animate X back to hamburger
    this.animateMenuIcon(false);
    
    // Restore body scroll
    document.body.style.overflow = '';
  }

  animateMenuIcon(isOpen) {
    if (!this.hasMenuLineTarget) return;

    const lines = this.menuLineTargets;

    if (isOpen) {
      // Transform to X
      lines[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
      lines[1].style.opacity = '0';
      lines[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
    } else {
      // Transform back to hamburger
      lines[0].style.transform = 'rotate(0) translate(0, 0)';
      lines[1].style.opacity = '1';
      lines[2].style.transform = 'rotate(0) translate(0, 0)';
    }
  }

  toggleUserMenu() {
    if (!this.hasUserMenuTarget) return;

    const isOpen = !this.userMenuTarget.classList.contains('hidden');

    if (isOpen) {
      this.closeUserMenu();
    } else {
      this.openUserMenu();
    }
  }

  openUserMenu() {
    this.userMenuTarget.classList.remove('hidden');

    // Update button state
    if (this.hasUserMenuButtonTarget) {
      this.userMenuButtonTarget.setAttribute('aria-expanded', 'true');
    }

    // Close other menus
    this.closeNotificationMenu();

    // Focus first menu item
    const firstMenuItem = this.userMenuTarget.querySelector('[role="menuitem"]');
    if (firstMenuItem) {
      setTimeout(() => firstMenuItem.focus(), 100);
    }
  }

  closeUserMenu() {
    if (!this.hasUserMenuTarget) return;

    this.userMenuTarget.classList.add('hidden');

    // Update button state
    if (this.hasUserMenuButtonTarget) {
      this.userMenuButtonTarget.setAttribute('aria-expanded', 'false');
    }
  }

  toggleNotificationMenu() {
    if (!this.hasNotificationMenuTarget) return;

    const isOpen = !this.notificationMenuTarget.classList.contains('hidden');

    if (isOpen) {
      this.closeNotificationMenu();
    } else {
      this.openNotificationMenu();
    }
  }

  openNotificationMenu() {
    this.notificationMenuTarget.classList.remove('hidden');

    // Update button state
    if (this.hasNotificationButtonTarget) {
      this.notificationButtonTarget.setAttribute('aria-expanded', 'true');
    }

    // Close other menus
    this.closeUserMenu();

    // Focus first notification item
    const firstNotification = this.notificationMenuTarget.querySelector('[role="menuitem"]');
    if (firstNotification) {
      setTimeout(() => firstNotification.focus(), 100);
    }
  }

  closeNotificationMenu() {
    if (!this.hasNotificationMenuTarget) return;

    this.notificationMenuTarget.classList.add('hidden');

    // Update button state
    if (this.hasNotificationButtonTarget) {
      this.notificationButtonTarget.setAttribute('aria-expanded', 'false');
    }
  }
}
