import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "step", "prevButton", "nextButton", "submitButton", 
    "connectionFields", "sourceTypeRadio", "typeDescription", "typeFeatures",
    "syncRecommendation", "testButton", "testResults"
  ]
  static values = { 
    currentStep: Number, 
    totalSteps: Number 
  }

  // Source type configurations
  sourceTypeConfigs = {
    postgresql: {
      name: "PostgreSQL",
      description: "Connect to PostgreSQL databases for real-time data access",
      icon: "fas fa-database",
      color: "blue",
      features: ["Real-time sync", "Schema detection", "Incremental updates", "SSL support"],
      syncOptions: ["realtime", "every_5_minutes", "every_15_minutes", "hourly", "daily"],
      requiresAuth: true
    },
    mysql: {
      name: "MySQL", 
      description: "Import data from MySQL and MariaDB databases",
      icon: "fas fa-database",
      color: "orange",
      features: ["Schema detection", "Bulk import", "Incremental sync", "Binary log support"],
      syncOptions: ["every_15_minutes", "hourly", "daily", "weekly"],
      requiresAuth: true
    },
    csv: {
      name: "CSV File",
      description: "Upload or connect to CSV files for easy data import",
      icon: "fas fa-file-csv", 
      color: "green",
      features: ["Auto-detect delimiter", "Header detection", "Large file support", "Remote URL support"],
      syncOptions: ["manual", "hourly", "daily", "weekly"],
      requiresAuth: false
    }
  }

  connect() {
    console.log("Enhanced data source form controller connected")
    console.log("Current step:", this.currentStepValue, "Total steps:", this.totalStepsValue)

    // Initialize form state
    this.updateStepVisibility()
    this.updateNavigationButtons()
    this.updateProgress()

    // Set up form validation
    this.setupFormValidation()

    // Initialize current source type if editing
    this.initializeSourceType()
  }



  // Initialize form defaults and ensure proper state
  initializeFormDefaults() {
    // Ensure sync frequency has a default if none selected
    const syncFreqRadios = this.element.querySelectorAll('[name="data_source[sync_frequency]"]')
    const hasSelectedFreq = this.element.querySelector('[name="data_source[sync_frequency]"]:checked')

    if (syncFreqRadios.length > 0 && !hasSelectedFreq) {
      const dailyOption = this.element.querySelector('[name="data_source[sync_frequency]"][value="daily"]')
      if (dailyOption) {
        dailyOption.checked = true
      }
    }

    // Ensure source type is properly selected for existing data sources
    const sourceTypeRadios = this.element.querySelectorAll('[name="data_source[source_type]"]')
    const hasSelectedType = this.element.querySelector('[name="data_source[source_type]"]:checked')

    if (sourceTypeRadios.length > 0 && !hasSelectedType) {
      const firstType = sourceTypeRadios[0]
      if (firstType) {
        firstType.checked = true
        firstType.dispatchEvent(new Event('change', { bubbles: true }))
      }
    }
  }

  // Step Navigation
  nextStep() {
    console.log("nextStep called - current step:", this.currentStepValue)

    if (this.validateCurrentStep()) {
      if (this.currentStepValue < this.totalStepsValue) {
        this.currentStepValue++
        console.log("Advanced to step:", this.currentStepValue)
        this.updateStepVisibility()
        this.updateNavigationButtons()
        this.updateProgress()
        this.announceStepChange()
      }
    } else {
      console.log("Validation failed for step:", this.currentStepValue)
    }
  }

  previousStep() {
    if (this.currentStepValue > 1) {
      this.currentStepValue--
      this.updateStepVisibility()
      this.updateNavigationButtons()
      this.updateProgress()
      this.announceStepChange()
    }
  }

  updateStepVisibility() {
    this.stepTargets.forEach((step, index) => {
      const stepNumber = parseInt(step.dataset.step)
      if (stepNumber === this.currentStepValue) {
        step.classList.remove('hidden')
        step.style.opacity = '0'
        step.style.transform = 'translateX(20px)'

        // Animate in
        requestAnimationFrame(() => {
          step.style.transition = 'opacity 300ms ease-out, transform 300ms ease-out'
          step.style.opacity = '1'
          step.style.transform = 'translateX(0)'
        })
      } else {
        step.classList.add('hidden')
      }
    })
  }

  updateNavigationButtons() {
    // Previous button
    if (this.hasPrevButtonTarget) {
      this.prevButtonTarget.disabled = this.currentStepValue === 1
    }

    // Next button
    if (this.hasNextButtonTarget) {
      if (this.currentStepValue === this.totalStepsValue) {
        this.nextButtonTarget.style.display = 'none'
      } else {
        this.nextButtonTarget.style.display = 'inline-flex'
      }
    }

    // Submit button
    if (this.hasSubmitButtonTarget) {
      if (this.currentStepValue === this.totalStepsValue) {
        this.submitButtonTarget.style.display = 'inline-flex'
      } else {
        this.submitButtonTarget.style.display = 'none'
      }
    }
  }

  updateProgress() {
    // Update modal progress if available
    const modal = this.element.closest('[data-controller*="data-source-edit-modal"]')
    if (modal) {
      const modalController = this.application.getControllerForElementAndIdentifier(modal, 'data-source-edit-modal')
      if (modalController) {
        modalController.updateProgress(this.currentStepValue, this.totalStepsValue)
      }
    }
  }

  announceStepChange() {
    // Announce step change for screen readers
    const announcement = `Step ${this.currentStepValue} of ${this.totalStepsValue}`
    const announcer = document.createElement('div')
    announcer.setAttribute('aria-live', 'polite')
    announcer.setAttribute('aria-atomic', 'true')
    announcer.className = 'sr-only'
    announcer.textContent = announcement
    document.body.appendChild(announcer)
    
    setTimeout(() => {
      document.body.removeChild(announcer)
    }, 1000)
  }

  // Form Validation
  validateCurrentStep() {
    const currentStep = this.stepTargets.find(step =>
      parseInt(step.dataset.step) === this.currentStepValue
    )

    if (!currentStep) return true

    const requiredFields = currentStep.querySelectorAll('[required]')
    let isValid = true

    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        this.showFieldError(field, 'This field is required')
        isValid = false
      } else {
        this.clearFieldError(field)
      }
    })

    // Step-specific validation
    switch(this.currentStepValue) {
      case 1:
        isValid = this.validateBasicInfo() && isValid
        break
      case 2:
        isValid = this.validateConnectionType() && isValid
        break
      case 3:
        isValid = this.validateConnectionSettings() && isValid
        break
      case 4:
        isValid = this.validateSyncSettings() && isValid
        break
    }

    return isValid
  }

  validateBasicInfo() {
    const nameField = this.element.querySelector('[name="data_source[name]"]')
    if (nameField && nameField.value.trim().length < 3) {
      this.showFieldError(nameField, 'Name must be at least 3 characters long')
      return false
    }
    return true
  }

  validateConnectionType() {
    const selectedType = this.element.querySelector('[name="data_source[source_type]"]:checked')
    if (!selectedType) {
      // Check if this is an edit form with existing source type
      const hiddenSourceType = this.element.querySelector('[name="data_source[source_type]"][type="hidden"]')
      if (hiddenSourceType && hiddenSourceType.value) {
        return true
      }
      this.showStepError(2, 'Please select a connection type')
      return false
    }
    return true
  }

  validateConnectionSettings() {
    // This will be implemented based on the selected connection type
    return true
  }

  validateSyncSettings() {
    const selectedFreq = this.element.querySelector('[name="data_source[sync_frequency]"]:checked')
    if (!selectedFreq) {
      // Check for hidden field or default value
      const hiddenFreq = this.element.querySelector('[name="data_source[sync_frequency]"][type="hidden"]')
      if (hiddenFreq && hiddenFreq.value) {
        return true
      }
      this.showStepError(4, 'Please select a sync frequency')
      return false
    }
    return true
  }

  showFieldError(field, message) {
    this.clearFieldError(field)
    
    field.classList.add('border-red-500', 'focus:border-red-500', 'focus:ring-red-500')
    field.classList.remove('border-slate-300', 'focus:border-primary-500', 'focus:ring-primary-500')
    
    const errorDiv = document.createElement('div')
    errorDiv.className = 'field-error mt-2 text-sm text-red-600 flex items-center'
    errorDiv.innerHTML = `<i class="fas fa-exclamation-circle mr-2"></i>${message}`
    
    field.parentNode.appendChild(errorDiv)
  }

  clearFieldError(field) {
    field.classList.remove('border-red-500', 'focus:border-red-500', 'focus:ring-red-500')
    field.classList.add('border-slate-300', 'focus:border-primary-500', 'focus:ring-primary-500')
    
    const existingError = field.parentNode.querySelector('.field-error')
    if (existingError) {
      existingError.remove()
    }
  }

  showStepError(stepNumber, message) {
    // Show error message for the step
    console.error(`Step ${stepNumber} validation error:`, message)
  }

  setupFormValidation() {
    // Real-time validation for required fields
    const requiredFields = this.element.querySelectorAll('[required]')
    requiredFields.forEach(field => {
      field.addEventListener('blur', () => {
        if (!field.value.trim()) {
          this.showFieldError(field, 'This field is required')
        } else {
          this.clearFieldError(field)
        }
      })
      
      field.addEventListener('input', () => {
        if (field.value.trim()) {
          this.clearFieldError(field)
        }
      })
    })
  }

  // Connection Type Handling
  updateConnectionType(event) {
    const sourceType = event.target.value
    console.log("Connection type changed to:", sourceType)
    
    this.updateTypeDescription(sourceType)
    this.updateTypeFeatures(sourceType)
    this.loadConnectionFields(sourceType)
  }

  initializeSourceType() {
    const selectedType = this.element.querySelector('[name="data_source[source_type]"]:checked')
    if (selectedType) {
      this.updateTypeDescription(selectedType.value)
      this.updateTypeFeatures(selectedType.value)
    }
  }

  updateTypeDescription(sourceType) {
    if (!this.hasTypeDescriptionTarget) return
    
    const config = this.sourceTypeConfigs[sourceType]
    if (config) {
      this.typeDescriptionTarget.innerHTML = `
        <div class="flex items-center">
          <div class="w-10 h-10 bg-primary-100 rounded-xl flex items-center justify-center">
            <i class="${config.icon} text-primary-600"></i>
          </div>
          <div class="ml-4">
            <h3 class="font-semibold text-primary-900">${config.name}</h3>
            <p class="text-sm text-primary-700">${config.description}</p>
          </div>
        </div>
      `
    }
  }

  updateTypeFeatures(sourceType) {
    if (!this.hasTypeFeaturesTarget) return
    
    const config = this.sourceTypeConfigs[sourceType]
    if (config && config.features) {
      const featuresList = document.getElementById('features-list')
      if (featuresList) {
        featuresList.innerHTML = config.features.map(feature => `
          <div class="flex items-center text-sm text-slate-700">
            <i class="fas fa-check text-emerald-500 mr-2"></i>
            ${feature}
          </div>
        `).join('')
      }
    }
  }

  loadConnectionFields(sourceType) {
    if (!this.hasConnectionFieldsTarget) return
    
    console.log("Loading connection fields for:", sourceType)
    
    // Show loading state
    this.connectionFieldsTarget.innerHTML = `
      <div class="flex items-center justify-center py-12">
        <div class="text-center">
          <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-spinner fa-spin text-primary-600"></i>
          </div>
          <p class="text-slate-600">Loading connection settings...</p>
        </div>
      </div>
    `
    
    // Fetch the enhanced partial
    const url = `/dashboard/data_sources/enhanced_connection_fields/${sourceType}`
    
    fetch(url)
      .then(response => {
        if (!response.ok) {
          throw new Error(`Connection fields not found: ${response.status}`)
        }
        return response.text()
      })
      .then(html => {
        this.connectionFieldsTarget.innerHTML = html
        this.initializeLoadedFields()
      })
      .catch(error => {
        console.error('Error loading connection fields:', error)
        const config = this.sourceTypeConfigs[sourceType] || {}
        this.connectionFieldsTarget.innerHTML = `
          <div class="text-center py-12">
            <div class="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-exclamation-triangle text-amber-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-slate-900 mb-2">Coming Soon</h3>
            <p class="text-slate-600 mb-6">
              ${config.name || sourceType} connector is under development. 
              Check back soon or contact support for early access.
            </p>
          </div>
        `
      })
  }

  initializeLoadedFields() {
    // Re-initialize any JavaScript components in the loaded partial
    const event = new CustomEvent('enhanced-data-source-fields-loaded', { 
      detail: { element: this.connectionFieldsTarget }
    })
    document.dispatchEvent(event)
  }

  // Sync Settings
  updateSyncRecommendation(event) {
    const frequency = event.target.value
    if (!this.hasSyncRecommendationTarget) return
    
    const recommendations = {
      manual: "Perfect for one-time imports or when you want full control over when data syncs.",
      realtime: "Best for critical data that needs immediate updates. Requires stable connection.",
      every_5_minutes: "High-frequency sync for rapidly changing data. Monitor performance impact.",
      every_15_minutes: "Good balance between freshness and performance for most use cases.",
      hourly: "Standard choice for business data that changes throughout the day.",
      daily: "Ideal for reports, analytics, or data that changes once per day."
    }
    
    const recommendation = recommendations[frequency] || "Choose based on your data update frequency and performance requirements."
    
    this.syncRecommendationTarget.innerHTML = `
      <div class="flex items-start">
        <i class="fas fa-lightbulb text-amber-500 mt-1 mr-3"></i>
        <div>
          <h4 class="font-semibold text-amber-900 mb-1">Recommendation</h4>
          <p class="text-sm text-amber-800">${recommendation}</p>
        </div>
      </div>
    `
  }

  // Connection Testing
  testConnection() {
    if (!this.hasTestButtonTarget || !this.hasTestResultsTarget) return

    console.log("Testing connection...")

    // Show loading state
    this.testButtonTarget.disabled = true
    this.testButtonTarget.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Testing...'

    this.testResultsTarget.classList.remove('hidden')
    this.testResultsTarget.innerHTML = `
      <div class="flex items-center text-blue-800">
        <i class="fas fa-spinner fa-spin mr-3"></i>
        Testing connection to data source...
      </div>
    `

    // Get form data for testing
    const form = this.element.querySelector('form')
    const formData = new FormData(form)

    // Make actual API call to test connection
    fetch('/dashboard/data_sources/test_connection_with_params', {
      method: 'POST',
      body: formData,
      headers: {
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content,
        'Accept': 'application/json'
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        this.testResultsTarget.innerHTML = `
          <div class="flex items-center text-emerald-800">
            <i class="fas fa-check-circle mr-3"></i>
            ${data.message || 'Connection successful! Ready to sync data.'}
          </div>
        `
        this.testResultsTarget.className = 'mt-4 p-4 bg-emerald-50 rounded-xl border border-emerald-200'
      } else {
        this.testResultsTarget.innerHTML = `
          <div class="flex items-start text-red-800">
            <i class="fas fa-exclamation-triangle mr-3 mt-0.5"></i>
            <div>
              <div class="font-semibold">Connection failed</div>
              <div class="text-sm mt-1">${data.error || 'Please check your connection settings and try again.'}</div>
            </div>
          </div>
        `
        this.testResultsTarget.className = 'mt-4 p-4 bg-red-50 rounded-xl border border-red-200'
      }
    })
    .catch(error => {
      console.error('Connection test error:', error)
      this.testResultsTarget.innerHTML = `
        <div class="flex items-start text-red-800">
          <i class="fas fa-exclamation-triangle mr-3 mt-0.5"></i>
          <div>
            <div class="font-semibold">Connection test failed</div>
            <div class="text-sm mt-1">Unable to test connection. Please try again.</div>
          </div>
        </div>
      `
      this.testResultsTarget.className = 'mt-4 p-4 bg-red-50 rounded-xl border border-red-200'
    })
    .finally(() => {
      // Restore button
      this.testButtonTarget.disabled = false
      this.testButtonTarget.innerHTML = '<i class="fas fa-play mr-2"></i>Test Connection'
    })
  }

  // Form submission handler
  handleSubmit(event) {
    event.preventDefault()
    console.log("Form submission intercepted")

    // Validate all steps before submission
    if (!this.validateAllSteps()) {
      console.log("Form validation failed")
      return
    }

    const form = event.target
    const formData = new FormData(form)

    // Show loading state
    this.showSubmissionLoading()

    // Submit via fetch (don't set Content-Type for multipart forms)
    fetch(form.action, {
      method: form.method,
      body: formData,
      headers: {
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content,
        'Accept': 'application/json'
        // Don't set Content-Type - let browser set it for multipart/form-data
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        this.handleSubmissionSuccess(data)
      } else {
        this.handleSubmissionError(data)
      }
    })
    .catch(error => {
      console.error('Form submission error:', error)
      this.handleSubmissionError({ errors: ['An unexpected error occurred. Please try again.'] })
    })
    .finally(() => {
      this.hideSubmissionLoading()
    })
  }

  validateAllSteps() {
    let isValid = true

    // Validate each step
    for (let step = 1; step <= this.totalStepsValue; step++) {
      const originalStep = this.currentStepValue
      this.currentStepValue = step

      if (!this.validateCurrentStep()) {
        isValid = false
        // Show the first invalid step
        if (step < originalStep) {
          this.updateStepVisibility()
          this.updateNavigationButtons()
          this.updateProgress()
          break
        }
      }

      this.currentStepValue = originalStep
    }

    return isValid
  }

  showSubmissionLoading() {
    if (this.hasSubmitButtonTarget) {
      this.submitButtonTarget.disabled = true
      this.submitButtonTarget.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...'
    }

    // Disable navigation buttons
    if (this.hasPrevButtonTarget) this.prevButtonTarget.disabled = true
    if (this.hasNextButtonTarget) this.nextButtonTarget.disabled = true
  }

  hideSubmissionLoading() {
    if (this.hasSubmitButtonTarget) {
      this.submitButtonTarget.disabled = false
      this.submitButtonTarget.innerHTML = '<i class="fas fa-save mr-2"></i>Update Data Source'
    }

    // Re-enable navigation buttons
    this.updateNavigationButtons()
  }

  handleSubmissionSuccess(data) {
    console.log("Form submission successful:", data)

    // Show success message
    this.showSuccessMessage(data.message || 'Data source updated successfully!')

    // Close modal and redirect after a short delay
    setTimeout(() => {
      this.closeModal()
      if (data.redirect_url) {
        window.location.href = data.redirect_url
      }
    }, 1500)
  }

  handleSubmissionError(data) {
    console.log("Form submission failed:", data)

    // Show error messages
    if (data.errors && data.errors.length > 0) {
      this.showErrorMessages(data.errors)
    }

    // If HTML is provided, update the form content
    if (data.html) {
      const formContainer = this.element.querySelector('.flex-1')
      if (formContainer) {
        formContainer.innerHTML = data.html
      }
    }
  }

  showSuccessMessage(message) {
    const successDiv = document.createElement('div')
    successDiv.className = 'fixed top-4 right-4 z-50 p-4 bg-emerald-50 border border-emerald-200 rounded-xl shadow-lg'
    successDiv.innerHTML = `
      <div class="flex items-center">
        <i class="fas fa-check-circle text-emerald-600 mr-3"></i>
        <span class="text-emerald-800 font-medium">${message}</span>
      </div>
    `

    document.body.appendChild(successDiv)

    // Animate in
    requestAnimationFrame(() => {
      successDiv.style.transform = 'translateX(0)'
      successDiv.style.opacity = '1'
    })

    // Remove after 3 seconds
    setTimeout(() => {
      successDiv.remove()
    }, 3000)
  }

  showErrorMessages(errors) {
    // Remove existing error messages
    const existingErrors = this.element.querySelectorAll('.form-submission-error')
    existingErrors.forEach(error => error.remove())

    // Create error container
    const errorDiv = document.createElement('div')
    errorDiv.className = 'form-submission-error mb-6 p-4 bg-red-50 border border-red-200 rounded-xl'
    errorDiv.innerHTML = `
      <div class="flex items-start">
        <i class="fas fa-exclamation-triangle text-red-600 mt-1 mr-3"></i>
        <div>
          <h3 class="font-semibold text-red-900 mb-2">Please fix the following errors:</h3>
          <ul class="space-y-1">
            ${errors.map(error => `
              <li class="text-red-800 text-sm flex items-center">
                <i class="fas fa-circle text-red-400 text-xs mr-2"></i>
                ${error}
              </li>
            `).join('')}
          </ul>
        </div>
      </div>
    `

    // Insert at the top of the form content
    const formContent = this.element.querySelector('.flex-1')
    if (formContent) {
      formContent.insertBefore(errorDiv, formContent.firstChild)
    }
  }

  closeModal() {
    // Find and trigger the modal close
    const modal = this.element.closest('[data-controller*="data-source-edit-modal"]')
    if (modal) {
      const modalController = this.application.getControllerForElementAndIdentifier(modal, 'data-source-edit-modal')
      if (modalController) {
        modalController.close()
      }
    }
  }
}
