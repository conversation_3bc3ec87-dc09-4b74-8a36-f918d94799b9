import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  openTrialModal(event) {
    event.preventDefault();
    const modal = document.getElementById('trial-modal');
    const modalController = this.application.getControllerForElementAndIdentifier(modal, 'modal');
    
    // Load trial form content
    const content = modal.querySelector('[data-modal-target="content"]');
    content.innerHTML = this.trialFormHTML();
    
    modalController.open(event);
    
    // Handle form submission
    const form = content.querySelector('form');
    form.addEventListener('submit', (e) => this.handleTrialSubmit(e, modalController));
  }
  
  openDemoModal(event) {
    event.preventDefault();
    const modal = document.getElementById('demo-modal');
    const modalController = this.application.getControllerForElementAndIdentifier(modal, 'modal');
    
    // Load demo form content
    const content = modal.querySelector('[data-modal-target="content"]');
    content.innerHTML = this.demoFormHTML();
    
    modalController.open(event);
    
    // Handle form submission
    const form = content.querySelector('form');
    form.addEventListener('submit', (e) => this.handleDemoSubmit(e, modalController));
  }
  
  async handleTrialSubmit(event, modalController) {
    event.preventDefault();
    const form = event.target;
    const formData = new FormData(form);
    
    try {
      const response = await fetch('/start-trial', {
        method: 'POST',
        headers: {
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content,
          'Accept': 'application/json'
        },
        body: formData
      });
      
      const data = await response.json();
      
      if (data.success) {
        modalController.close();
        this.showSuccessMessage(data.message);
        if (data.redirect_url) {
          setTimeout(() => {
            window.location.href = data.redirect_url;
          }, 2000);
        }
      } else {
        this.showErrors(form, data.errors);
      }
    } catch (error) {
      console.error('Error:', error);
      this.showErrors(form, ['An error occurred. Please try again.']);
    }
  }
  
  async handleDemoSubmit(event, modalController) {
    event.preventDefault();
    const form = event.target;
    const formData = new FormData(form);
    
    try {
      const response = await fetch('/request-demo', {
        method: 'POST',
        headers: {
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content,
          'Accept': 'application/json'
        },
        body: formData
      });
      
      const data = await response.json();
      
      if (data.success) {
        modalController.close();
        this.showSuccessMessage(data.message);
      } else {
        this.showErrors(form, data.errors);
      }
    } catch (error) {
      console.error('Error:', error);
      this.showErrors(form, ['An error occurred. Please try again.']);
    }
  }
  
  showSuccessMessage(message) {
    const successEl = document.createElement('div');
    successEl.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-4 rounded-lg shadow-lg z-50 animate-slideIn';
    successEl.innerHTML = `
      <div class="flex items-center">
        <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
        <span>${message}</span>
      </div>
    `;
    
    document.body.appendChild(successEl);
    
    setTimeout(() => {
      successEl.remove();
    }, 5000);
  }
  
  showErrors(form, errors) {
    // Remove existing errors
    form.querySelectorAll('.error-message').forEach(el => el.remove());
    
    // Add new errors
    const errorContainer = document.createElement('div');
    errorContainer.className = 'bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4 error-message';
    errorContainer.innerHTML = `
      <p class="font-medium mb-1">Please fix the following errors:</p>
      <ul class="list-disc list-inside text-sm">
        ${errors.map(error => `<li>${error}</li>`).join('')}
      </ul>
    `;
    
    form.insertBefore(errorContainer, form.firstChild);
  }
  
  trialFormHTML() {
    return `
      <h3 class="text-2xl font-bold text-gray-900 mb-2">Start Your Free Trial</h3>
      <p class="text-gray-600 mb-6">Get started with Data Reflow today. No credit card required.</p>
      
      <form class="space-y-4">
        <div>
          <label for="trial-email" class="block text-sm font-medium text-gray-700 mb-2">
            Business Email
          </label>
          <input type="email" 
                 id="trial-email" 
                 name="trial_registration[email]"
                 class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                 placeholder="<EMAIL>" 
                 required>
        </div>
        
        <div>
          <label for="trial-company" class="block text-sm font-medium text-gray-700 mb-2">
            Company Name
          </label>
          <input type="text" 
                 id="trial-company" 
                 name="trial_registration[company_name]"
                 class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                 placeholder="Your Company" 
                 required>
        </div>
        
        <div>
          <label for="trial-size" class="block text-sm font-medium text-gray-700 mb-2">
            Company Size
          </label>
          <select id="trial-size" 
                  name="trial_registration[company_size]"
                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                  required>
            <option value="">Select size</option>
            <option value="1-10">1-10 employees</option>
            <option value="11-50">11-50 employees</option>
            <option value="51-200">51-200 employees</option>
            <option value="201-500">201-500 employees</option>
            <option value="500+">500+ employees</option>
          </select>
        </div>
        
        <button type="submit" 
                class="w-full py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors font-semibold">
          Start Free Trial
        </button>
      </form>
    `;
  }
  
  demoFormHTML() {
    return `
      <h3 class="text-2xl font-bold text-gray-900 mb-2">Schedule a Demo</h3>
      <p class="text-gray-600 mb-6">See Data Reflow in action with a personalized demo.</p>
      
      <form class="space-y-4">
        <div>
          <label for="demo-email" class="block text-sm font-medium text-gray-700 mb-2">
            Business Email
          </label>
          <input type="email" 
                 id="demo-email" 
                 name="demo_request[email]"
                 class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                 placeholder="<EMAIL>" 
                 required>
        </div>
        
        <div>
          <label for="demo-name" class="block text-sm font-medium text-gray-700 mb-2">
            Full Name
          </label>
          <input type="text" 
                 id="demo-name" 
                 name="demo_request[name]"
                 class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                 placeholder="Your Name" 
                 required>
        </div>
        
        <div>
          <label for="demo-phone" class="block text-sm font-medium text-gray-700 mb-2">
            Phone Number
          </label>
          <input type="tel" 
                 id="demo-phone" 
                 name="demo_request[phone]"
                 class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                 placeholder="+44 ************" 
                 required>
        </div>
        
        <div>
          <label for="demo-time" class="block text-sm font-medium text-gray-700 mb-2">
            Preferred Time
          </label>
          <select id="demo-time" 
                  name="demo_request[preferred_time]"
                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                  required>
            <option value="">Select time</option>
            <option value="morning">Morning (9-12 PM)</option>
            <option value="afternoon">Afternoon (12-5 PM)</option>
            <option value="evening">Evening (5-8 PM)</option>
          </select>
        </div>
        
        <button type="submit" 
                class="w-full py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors font-semibold">
          Schedule Demo
        </button>
      </form>
    `;
  }
}
