import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["content", "trigger", "icon"]
  static values = { 
    expanded: { type: Boolean, default: false },
    duration: { type: Number, default: 300 }
  }
  
  connect() {
    this.setupInitialState();
  }
  
  setupInitialState() {
    if (this.hasContentTarget) {
      if (!this.expandedValue) {
        this.contentTarget.style.display = 'none';
        this.contentTarget.style.opacity = '0';
        this.contentTarget.style.maxHeight = '0';
        this.contentTarget.style.overflow = 'hidden';
        this.contentTarget.style.transition = `all ${this.durationValue}ms ease-in-out`;
      }
    }
    
    this.updateIcon();
  }
  
  toggle() {
    this.expandedValue = !this.expandedValue;
    
    if (this.expandedValue) {
      this.expand();
    } else {
      this.collapse();
    }
    
    this.updateIcon();
    this.announceStateChange();
  }
  
  expand() {
    if (!this.hasContentTarget) return;
    
    const content = this.contentTarget;
    
    // Show the element
    content.style.display = 'block';
    
    // Get the natural height
    const naturalHeight = content.scrollHeight;
    
    // Animate to natural height
    requestAnimationFrame(() => {
      content.style.maxHeight = naturalHeight + 'px';
      content.style.opacity = '1';
    });
    
    // Clean up after animation
    setTimeout(() => {
      content.style.maxHeight = 'none';
    }, this.durationValue);
    
    // Update ARIA attributes
    if (this.hasTriggerTarget) {
      this.triggerTarget.setAttribute('aria-expanded', 'true');
    }
  }
  
  collapse() {
    if (!this.hasContentTarget) return;
    
    const content = this.contentTarget;
    
    // Set explicit height first
    content.style.maxHeight = content.scrollHeight + 'px';
    
    // Force reflow
    content.offsetHeight;
    
    // Animate to collapsed state
    requestAnimationFrame(() => {
      content.style.maxHeight = '0';
      content.style.opacity = '0';
    });
    
    // Hide after animation
    setTimeout(() => {
      content.style.display = 'none';
    }, this.durationValue);
    
    // Update ARIA attributes
    if (this.hasTriggerTarget) {
      this.triggerTarget.setAttribute('aria-expanded', 'false');
    }
  }
  
  updateIcon() {
    if (this.hasIconTarget) {
      const icon = this.iconTarget;
      if (this.expandedValue) {
        icon.style.transform = 'rotate(180deg)';
      } else {
        icon.style.transform = 'rotate(0deg)';
      }
    }
  }
  
  announceStateChange() {
    const message = this.expandedValue ? 'Content expanded' : 'Content collapsed';
    if (window.announceToScreenReader) {
      window.announceToScreenReader(message);
    }
  }
  
  // Public methods for external control
  open() {
    if (!this.expandedValue) {
      this.toggle();
    }
  }
  
  close() {
    if (this.expandedValue) {
      this.toggle();
    }
  }
}
