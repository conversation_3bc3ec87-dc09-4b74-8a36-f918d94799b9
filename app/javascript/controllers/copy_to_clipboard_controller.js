import { Controller } from "@hotwired/stimulus"

// Controller for copying text to clipboard
export default class extends Controller {
  static values = { 
    text: String,
    successMessage: { type: String, default: "Copied!" }
  }
  
  copy() {
    if (!this.textValue) return;
    
    navigator.clipboard.writeText(this.textValue).then(() => {
      this.showSuccess();
    }).catch(() => {
      // Fallback for older browsers
      this.fallbackCopy();
    });
  }
  
  fallbackCopy() {
    const textArea = document.createElement("textarea");
    textArea.value = this.textValue;
    textArea.style.position = "fixed";
    textArea.style.left = "-999999px";
    textArea.style.top = "-999999px";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
      document.execCommand('copy');
      this.showSuccess();
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
    
    document.body.removeChild(textArea);
  }
  
  showSuccess() {
    const originalText = this.element.innerHTML;
    this.element.innerHTML = `<i class="fas fa-check mr-1" aria-hidden="true"></i>${this.successMessageValue}`;
    this.element.classList.add("text-green-600");
    
    setTimeout(() => {
      this.element.innerHTML = originalText;
      this.element.classList.remove("text-green-600");
    }, 2000);
    
    this.dispatch("copied", { detail: { text: this.textValue } });
  }
}
