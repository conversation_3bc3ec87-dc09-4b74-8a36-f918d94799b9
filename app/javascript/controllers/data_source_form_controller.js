import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["connectionFields", "sourceTypeRadio", "syncSettings", "typeDescription", "typeFeatures"]
  
  // Connection type configurations
  sourceTypeConfigs = {
    postgresql: {
      name: "PostgreSQL",
      description: "Connect to PostgreSQL databases for real-time data access",
      icon: "fas fa-database",
      color: "blue",
      features: ["Real-time sync", "Schema detection", "Incremental updates", "SSL support"],
      syncOptions: ["realtime", "every_5_minutes", "every_15_minutes", "hourly", "daily"],
      requiresAuth: true
    },
    mysql: {
      name: "MySQL",
      description: "Import data from MySQL and MariaDB databases",
      icon: "fas fa-database",
      color: "orange",
      features: ["Schema detection", "Bulk import", "Incremental sync", "Binary log support"],
      syncOptions: ["every_15_minutes", "hourly", "daily", "weekly"],
      requiresAuth: true
    },
    csv: {
      name: "CSV File",
      description: "Upload or connect to CSV files for easy data import",
      icon: "fas fa-file-csv",
      color: "green",
      features: ["Auto-detect delimiter", "Header detection", "Large file support", "Remote URL support"],
      syncOptions: ["manual", "hourly", "daily", "weekly"],
      requiresAuth: false
    },
    excel: {
      name: "Excel",
      description: "Import data from Excel spreadsheets (.xlsx, .xls)",
      icon: "fas fa-file-excel",
      color: "green",
      features: ["Multiple sheets", "Formula evaluation", "Pivot table support", "Named ranges"],
      syncOptions: ["manual", "daily", "weekly"],
      requiresAuth: false
    },
    api: {
      name: "REST API",
      description: "Connect to any REST API endpoint for dynamic data",
      icon: "fas fa-plug",
      color: "purple",
      features: ["OAuth support", "Rate limiting", "Pagination", "Custom headers"],
      syncOptions: ["realtime", "every_5_minutes", "every_15_minutes", "hourly"],
      requiresAuth: true
    },
    salesforce: {
      name: "Salesforce",
      description: "Sync CRM data from Salesforce",
      icon: "fab fa-salesforce",
      color: "blue",
      features: ["Object sync", "Custom fields", "Bulk API", "Change data capture"],
      syncOptions: ["realtime", "every_15_minutes", "hourly", "daily"],
      requiresAuth: true
    },
    stripe: {
      name: "Stripe",
      description: "Import payment and subscription data from Stripe",
      icon: "fab fa-stripe",
      color: "purple",
      features: ["Webhooks", "Historical data", "Test mode support", "Automatic reconciliation"],
      syncOptions: ["realtime", "hourly", "daily"],
      requiresAuth: true
    },
    s3: {
      name: "Amazon S3",
      description: "Connect to files stored in Amazon S3 buckets",
      icon: "fab fa-aws",
      color: "orange",
      features: ["Bucket browsing", "Large file support", "IAM roles", "Versioning support"],
      syncOptions: ["manual", "hourly", "daily"],
      requiresAuth: true
    }
  }
  
  connect() {
    console.log("DataSourceForm controller connected!", this.element)
    console.log("Targets available:", {
      connectionFields: this.hasConnectionFieldsTarget,
      typeDescription: this.hasTypeDescriptionTarget,
      syncSettings: this.hasSyncSettingsTarget
    })
    
    // Log all radio buttons
    const radios = this.element.querySelectorAll('input[type="radio"]')
    console.log("Total radio buttons found:", radios.length)
    
    // Check specifically for source type radios
    const sourceTypeRadios = this.element.querySelectorAll('input[name="data_source[source_type]"]')
    console.log("Source type radios found:", sourceTypeRadios.length)
    
    // Add direct event listeners as a fallback
    sourceTypeRadios.forEach((radio, index) => {
      console.log(`Radio ${index}: value="${radio.value}", checked=${radio.checked}`)
      
      // Add both change and click listeners
      radio.addEventListener('change', (e) => {
        console.log("Radio CHANGE event:", e.target.value)
        this.handleSourceTypeChange(e.target.value)
      })
      
      radio.addEventListener('click', (e) => {
        console.log("Radio CLICK event:", e.target.value)
        this.handleSourceTypeChange(e.target.value)
      })
    })
    
    // Check if a source type is already selected
    const selectedRadio = this.element.querySelector('input[name="data_source[source_type]"]:checked')
    if (selectedRadio) {
      console.log("Pre-selected radio value:", selectedRadio.value)
      this.updateFormForSourceType(selectedRadio.value)
    }
  }
  
  handleSourceTypeChange(sourceType) {
    console.log("handleSourceTypeChange called with:", sourceType)
    this.updateFormForSourceType(sourceType)
  }
  
  updateConnectionFields(event) {
    const sourceType = event.target.value
    console.log("updateConnectionFields called with:", sourceType)
    this.updateFormForSourceType(sourceType)
  }
  
  updateFormForSourceType(sourceType) {
    // Update connection fields
    this.loadConnectionFields(sourceType)
    
    // Update type information
    this.updateTypeInfo(sourceType)
    
    // Update sync settings based on type
    this.updateSyncSettings(sourceType)
    
    // Show/hide relevant sections
    this.toggleFormSections(sourceType)
  }
  
  loadConnectionFields(sourceType) {
    console.log("loadConnectionFields called for:", sourceType)
    const connectionSettings = document.getElementById('connection-settings')
    console.log("Found connection-settings element:", connectionSettings)
    if (!connectionSettings) {
      console.error("connection-settings element not found!")
      return
    }
    
    // Show loading message with spinner
    connectionSettings.innerHTML = `
      <div class="flex items-center justify-center py-8">
        <i class="fas fa-spinner fa-spin text-2xl text-primary-500 mr-3"></i>
        <p class="text-neutral-500">Loading connection settings...</p>
      </div>
    `
    
    // Fetch the partial for this source type
    const url = `/dashboard/data_sources/connection_fields/${sourceType}`
    console.log("Fetching from:", url)
    
    fetch(url)
      .then(response => {
        console.log("Response status:", response.status)
        if (!response.ok) {
          throw new Error(`Connection fields not found: ${response.status}`)
        }
        return response.text()
      })
      .then(html => {
        console.log("Received HTML, length:", html.length)
        connectionSettings.innerHTML = html
        // Re-initialize any JavaScript in the loaded partial
        this.initializeLoadedFields()
      })
      .catch(error => {
        console.error('Error loading connection fields:', error)
        const config = this.sourceTypeConfigs[sourceType] || {}
        connectionSettings.innerHTML = `
          <div class="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-lg">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
              <div class="ml-3">
                <p class="font-medium">Coming Soon</p>
                <p class="text-sm mt-1">
                  ${config.name || sourceType} connector is under development. 
                  Check back soon or contact support for early access.
                </p>
              </div>
            </div>
          </div>
        `
      })
  }
  
  updateTypeInfo(sourceType) {
    const config = this.sourceTypeConfigs[sourceType]
    if (!config) return
    
    // Update description if target exists
    if (this.hasTypeDescriptionTarget) {
      this.typeDescriptionTarget.innerHTML = `
        <div class="flex items-start space-x-3">
          <div class="flex-shrink-0">
            <i class="${config.icon} text-2xl text-${config.color}-500"></i>
          </div>
          <div>
            <h3 class="text-sm font-medium text-neutral-900">${config.name}</h3>
            <p class="text-sm text-neutral-600 mt-1">${config.description}</p>
          </div>
        </div>
      `
      this.typeDescriptionTarget.classList.remove('hidden')
    }
    
    // Update features if target exists
    if (this.hasTypeFeaturesTarget) {
      const featuresHtml = config.features.map(feature => `
        <li class="flex items-center text-sm text-neutral-600">
          <i class="fas fa-check-circle text-green-500 mr-2"></i>
          ${feature}
        </li>
      `).join('')
      
      this.typeFeaturesTarget.innerHTML = `
        <h4 class="text-xs font-medium text-neutral-700 uppercase tracking-wide mb-2">Key Features</h4>
        <ul class="space-y-1">${featuresHtml}</ul>
      `
      this.typeFeaturesTarget.classList.remove('hidden')
    }
  }
  
  updateSyncSettings(sourceType) {
    const config = this.sourceTypeConfigs[sourceType]
    if (!config || !this.hasSyncSettingsTarget) return
    
    // Find the sync frequency select
    const syncSelect = this.syncSettingsTarget.querySelector('select[name="data_source[sync_frequency]"]')
    if (!syncSelect) return
    
    // Get current value
    const currentValue = syncSelect.value
    
    // Build new options based on source type
    const options = config.syncOptions.map(freq => {
      const labels = {
        manual: "Manual Only",
        realtime: "Real-time",
        every_5_minutes: "Every 5 minutes",
        every_15_minutes: "Every 15 minutes",
        every_30_minutes: "Every 30 minutes",
        hourly: "Hourly",
        every_6_hours: "Every 6 hours",
        daily: "Daily",
        weekly: "Weekly",
        monthly: "Monthly"
      }
      return `<option value="${freq}">${labels[freq] || freq}</option>`
    }).join('')
    
    syncSelect.innerHTML = `<option value="">Select frequency...</option>${options}`
    
    // Restore previous value if it's still valid
    if (config.syncOptions.includes(currentValue)) {
      syncSelect.value = currentValue
    }
    
    // Show recommendation
    const recommendationDiv = this.syncSettingsTarget.querySelector('.sync-recommendation')
    if (recommendationDiv) {
      const recommended = config.syncOptions[0]
      recommendationDiv.innerHTML = `
        <p class="text-xs text-neutral-500 mt-1">
          <i class="fas fa-info-circle"></i>
          Recommended: ${this.formatSyncFrequency(recommended)} for ${config.name}
        </p>
      `
    }
  }
  
  toggleFormSections(sourceType) {
    const config = this.sourceTypeConfigs[sourceType]
    if (!config) return
    
    // Show/hide authentication section based on type
    const authSection = this.element.querySelector('[data-form-section="authentication"]')
    if (authSection) {
      authSection.style.display = config.requiresAuth ? 'block' : 'none'
    }
  }
  
  initializeLoadedFields() {
    // Re-initialize any JavaScript components in the loaded partial
    // For example, file upload handlers, date pickers, etc.
    const event = new CustomEvent('data-source-fields-loaded', { 
      detail: { element: this.element }
    })
    document.dispatchEvent(event)
  }
  
  formatSyncFrequency(freq) {
    const labels = {
      manual: "Manual sync",
      realtime: "Real-time sync",
      every_5_minutes: "Every 5 minutes",
      every_15_minutes: "Every 15 minutes",
      hourly: "Hourly sync",
      daily: "Daily sync",
      weekly: "Weekly sync"
    }
    return labels[freq] || freq
  }
}