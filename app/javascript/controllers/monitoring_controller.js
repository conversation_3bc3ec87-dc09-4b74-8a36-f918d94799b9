import { Controller } from "@hotwired/stimulus"
import Chart from "chart.js"

export default class extends Controller {
  static targets = ["executionTimeChart", "throughputChart", "resourceUsageChart", "errorRateChart"]
  
  connect() {
    console.log("Monitoring controller connected")
    this.initializeCharts()
    this.setupEventListeners()
    this.updateCharts()
    this.setupWebSocket()
  }

  disconnect() {
    // Clean up charts
    if (this.executionTimeChart) this.executionTimeChart.destroy()
    if (this.throughputChart) this.throughputChart.destroy()
    if (this.resourceUsageChart) this.resourceUsageChart.destroy()
    if (this.errorRateChart) this.errorRateChart.destroy()
  }

  initializeCharts() {
    this.executionTimeChart = this.initializeChart(
      this.executionTimeChartTarget, 
      'Execution Time (seconds)', 
      'line'
    )
    
    this.throughputChart = this.initializeChart(
      this.throughputChartTarget, 
      'Records Processed', 
      'line'
    )
    
    this.resourceUsageChart = this.initializeResourceChart(this.resourceUsageChartTarget)
    
    this.errorRateChart = this.initializeChart(
      this.errorRateChartTarget, 
      'Error Count', 
      'bar'
    )
  }

  initializeChart(canvas, label, type) {
    const ctx = canvas.getContext('2d')
    return new Chart(ctx, {
      type: type,
      data: {
        labels: [],
        datasets: [{
          label: label,
          data: [],
          borderColor: 'rgb(75, 192, 192)',
          backgroundColor: type === 'bar' ? 'rgba(75, 192, 192, 0.2)' : 'transparent',
          tension: 0.1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          x: {
            display: true
          },
          y: {
            display: true,
            beginAtZero: true
          }
        }
      }
    })
  }

  initializeResourceChart(canvas) {
    const ctx = canvas.getContext('2d')
    return new Chart(ctx, {
      type: 'line',
      data: {
        labels: [],
        datasets: [{
          label: 'CPU Usage (%)',
          data: [],
          borderColor: 'rgb(255, 99, 132)',
          backgroundColor: 'transparent',
          tension: 0.1
        }, {
          label: 'Memory Usage (MB)',
          data: [],
          borderColor: 'rgb(54, 162, 235)',
          backgroundColor: 'transparent',
          tension: 0.1,
          yAxisID: 'y1'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          x: {
            display: true
          },
          y: {
            display: true,
            beginAtZero: true,
            max: 100,
            title: {
              display: true,
              text: 'CPU %'
            }
          },
          y1: {
            display: true,
            position: 'right',
            beginAtZero: true,
            title: {
              display: true,
              text: 'Memory MB'
            },
            grid: {
              drawOnChartArea: false
            }
          }
        }
      }
    })
  }

  setupEventListeners() {
    const pipelineFilter = document.getElementById('pipeline-filter')
    const timeRange = document.getElementById('time-range')
    
    if (pipelineFilter) {
      pipelineFilter.addEventListener('change', () => this.updateCharts())
    }
    
    if (timeRange) {
      timeRange.addEventListener('change', () => this.updateCharts())
    }
  }

  updateCharts() {
    const pipelineId = document.getElementById('pipeline-filter')?.value
    const timeRange = document.getElementById('time-range')?.value || '24h'
    
    const params = new URLSearchParams()
    if (pipelineId) params.append('pipeline_id', pipelineId)
    params.append('from', this.getTimeRangeStart(timeRange))
    
    fetch(`/dashboard/monitoring/metrics?${params}`)
      .then(response => response.json())
      .then(data => {
        this.updateChartData(data.metrics)
      })
      .catch(error => console.error('Error fetching metrics:', error))
  }

  getTimeRangeStart(range) {
    const now = new Date()
    switch(range) {
      case '1h':
        return new Date(now - 60 * 60 * 1000).toISOString()
      case '6h':
        return new Date(now - 6 * 60 * 60 * 1000).toISOString()
      case '24h':
        return new Date(now - 24 * 60 * 60 * 1000).toISOString()
      case '7d':
        return new Date(now - 7 * 24 * 60 * 60 * 1000).toISOString()
      default:
        return new Date(now - 24 * 60 * 60 * 1000).toISOString()
    }
  }

  updateChartData(metrics) {
    const executionTimes = []
    const throughput = []
    const cpuUsage = []
    const memoryUsage = []
    const errors = []
    
    metrics.forEach(metric => {
      const time = new Date(metric.created_at).toLocaleTimeString()
      
      switch(metric.metric_type) {
        case 'execution_time':
          executionTimes.push({ x: time, y: metric.value })
          break
        case 'records_processed':
          throughput.push({ x: time, y: metric.value })
          break
        case 'cpu_usage':
          cpuUsage.push({ x: time, y: metric.value })
          break
        case 'memory_usage':
          memoryUsage.push({ x: time, y: metric.value })
          break
        case 'error_count':
          errors.push({ x: time, y: metric.value })
          break
      }
    })
    
    this.updateSingleChart(this.executionTimeChart, executionTimes)
    this.updateSingleChart(this.throughputChart, throughput)
    this.updateResourceChartData(this.resourceUsageChart, cpuUsage, memoryUsage)
    this.updateSingleChart(this.errorRateChart, errors)
  }

  updateSingleChart(chart, data) {
    if (!chart) return
    
    chart.data.labels = data.map(d => d.x)
    chart.data.datasets[0].data = data.map(d => d.y)
    chart.update()
  }

  updateResourceChartData(chart, cpuData, memoryData) {
    if (!chart) return
    
    const labels = [...new Set([...cpuData.map(d => d.x), ...memoryData.map(d => d.x)])]
    chart.data.labels = labels
    chart.data.datasets[0].data = cpuData.map(d => d.y)
    chart.data.datasets[1].data = memoryData.map(d => d.y)
    chart.update()
  }

  setupWebSocket() {
    // ActionCable setup would go here
    // For now, we'll use polling as a fallback
    setInterval(() => {
      this.updateCharts()
    }, 30000) // Update every 30 seconds
  }

  addDataPoint(chart, label, value, datasetIndex = 0) {
    if (!chart) return
    
    // Keep only last 50 data points
    if (chart.data.labels.length > 50) {
      chart.data.labels.shift()
      chart.data.datasets[datasetIndex].data.shift()
    }
    
    chart.data.labels.push(label)
    chart.data.datasets[datasetIndex].data.push(value)
    chart.update()
  }
}