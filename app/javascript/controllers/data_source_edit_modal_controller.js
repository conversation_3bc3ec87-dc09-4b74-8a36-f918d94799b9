import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["backdrop", "modal", "content", "progressBar", "progressText"]
  static values = { dataSourceId: Number }

  connect() {
    console.log("Data source edit modal controller connected")
    
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden'
    
    // Focus management
    this.previouslyFocusedElement = document.activeElement
    this.trapFocus()
    
    // Animate modal in
    this.animateIn()
  }

  disconnect() {
    // Restore body scroll
    document.body.style.overflow = ''
    
    // Restore focus
    if (this.previouslyFocusedElement) {
      this.previouslyFocusedElement.focus()
    }
  }

  animateIn() {
    // Initial state
    this.backdropTarget.style.opacity = '0'
    this.modalTarget.style.opacity = '0'
    this.modalTarget.style.transform = 'scale(0.95) translateY(20px)'
    
    // Animate in
    requestAnimationFrame(() => {
      this.backdropTarget.style.transition = 'opacity 300ms ease-out'
      this.modalTarget.style.transition = 'all 300ms ease-out'
      
      this.backdropTarget.style.opacity = '1'
      this.modalTarget.style.opacity = '1'
      this.modalTarget.style.transform = 'scale(1) translateY(0)'
    })
  }

  animateOut() {
    return new Promise((resolve) => {
      this.backdropTarget.style.transition = 'opacity 200ms ease-in'
      this.modalTarget.style.transition = 'all 200ms ease-in'
      
      this.backdropTarget.style.opacity = '0'
      this.modalTarget.style.opacity = '0'
      this.modalTarget.style.transform = 'scale(0.95) translateY(20px)'
      
      setTimeout(resolve, 200)
    })
  }

  close() {
    console.log("Closing modal")
    
    // Check for unsaved changes
    if (this.hasUnsavedChanges()) {
      if (!confirm('You have unsaved changes. Are you sure you want to close?')) {
        return
      }
    }
    
    this.animateOut().then(() => {
      // Navigate back to the data source show page
      window.location.href = `/dashboard/data_sources/${this.dataSourceIdValue}`
    })
  }

  clickOutside(event) {
    if (event.target === this.backdropTarget) {
      this.close()
    }
  }

  hasUnsavedChanges() {
    // Check if any form fields have been modified
    const form = this.element.querySelector('form')
    if (!form) return false
    
    const formData = new FormData(form)
    const originalData = this.getOriginalFormData()
    
    // Simple comparison - in production you might want more sophisticated change detection
    for (let [key, value] of formData.entries()) {
      if (originalData[key] !== value) {
        return true
      }
    }
    
    return false
  }

  getOriginalFormData() {
    // This would ideally be set when the modal loads with the original values
    // For now, return empty object
    return {}
  }

  trapFocus() {
    // Get all focusable elements within the modal
    const focusableElements = this.modalTarget.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )
    
    if (focusableElements.length === 0) return
    
    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]
    
    // Focus the first element
    firstElement.focus()
    
    // Add keydown listener for tab trapping
    this.keydownListener = (e) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          // Shift + Tab
          if (document.activeElement === firstElement) {
            e.preventDefault()
            lastElement.focus()
          }
        } else {
          // Tab
          if (document.activeElement === lastElement) {
            e.preventDefault()
            firstElement.focus()
          }
        }
      }
    }
    
    this.modalTarget.addEventListener('keydown', this.keydownListener)
  }

  updateProgress(step, totalSteps) {
    if (this.hasProgressBarTarget && this.hasProgressTextTarget) {
      const percentage = (step / totalSteps) * 100
      this.progressBarTarget.style.width = `${percentage}%`
      this.progressTextTarget.textContent = `Step ${step} of ${totalSteps}`
    }
  }

  // Handle form submission
  handleFormSubmit(event) {
    console.log("Form submission intercepted")
    
    // Add loading state
    const submitButton = event.target.querySelector('[type="submit"]')
    if (submitButton) {
      const originalText = submitButton.textContent
      submitButton.disabled = true
      submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...'
      
      // Restore button state after a delay (in case of errors)
      setTimeout(() => {
        submitButton.disabled = false
        submitButton.textContent = originalText
      }, 10000)
    }
  }

  // Show loading state
  showLoading(message = 'Loading...') {
    if (this.hasContentTarget) {
      this.contentTarget.innerHTML = `
        <div class="flex items-center justify-center py-16">
          <div class="text-center">
            <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-spinner fa-spin text-primary-600 text-2xl"></i>
            </div>
            <p class="text-slate-600 font-medium">${message}</p>
          </div>
        </div>
      `
    }
  }

  // Show error state
  showError(message = 'An error occurred') {
    if (this.hasContentTarget) {
      this.contentTarget.innerHTML = `
        <div class="flex items-center justify-center py-16">
          <div class="text-center max-w-md">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-slate-900 mb-2">Error</h3>
            <p class="text-slate-600 mb-6">${message}</p>
            <button type="button" 
                    class="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-xl hover:bg-primary-700 transition-colors duration-200"
                    data-action="click->data-source-edit-modal#close">
              <i class="fas fa-arrow-left mr-2"></i>
              Go Back
            </button>
          </div>
        </div>
      `
    }
  }

  // Keyboard shortcuts
  handleKeydown(event) {
    switch(event.key) {
      case 'Escape':
        event.preventDefault()
        this.close()
        break
      case 's':
        if (event.metaKey || event.ctrlKey) {
          event.preventDefault()
          this.saveForm()
        }
        break
    }
  }

  saveForm() {
    const form = this.element.querySelector('form')
    if (form) {
      form.requestSubmit()
    }
  }
}
