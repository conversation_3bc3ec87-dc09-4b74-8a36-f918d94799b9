// This file is auto-generated by ./bin/rails stimulus:manifest:update
// Run that command whenever you add a new controller or create them with
// ./bin/rails generate stimulus controllerName

import { application } from "./application"

import AnimateController from "./animate_controller"
application.register("animate", AnimateController)

import CopyToClipboardController from "./copy_to_clipboard_controller"
application.register("copy-to-clipboard", CopyToClipboardController)

import CounterController from "./counter_controller"
application.register("counter", CounterController)

import CsvFormController from "./csv_form_controller"
application.register("csv-form", CsvFormController)

import DashboardNavigationController from "./dashboard_navigation_controller"
application.register("dashboard-navigation", DashboardNavigationController)

import DataSourceEditModalController from "./data_source_edit_modal_controller"
application.register("data-source-edit-modal", DataSourceEditModalController)

import DataSourceFormController from "./data_source_form_controller"
application.register("data-source-form", DataSourceFormController)

import DataSourceModalTriggerController from "./data_source_modal_trigger_controller"
application.register("data-source-modal-trigger", DataSourceModalTriggerController)

import DataSourceWizardController from "./data_source_wizard_controller"
application.register("data-source-wizard", DataSourceWizardController)

import DataSourcesManagerController from "./data_sources_manager_controller"
application.register("data-sources-manager", DataSourcesManagerController)

import DataTableController from "./data_table_controller"
application.register("data-table", DataTableController)

import DropdownController from "./dropdown_controller"
application.register("dropdown", DropdownController)

import EnhancedCsvFormController from "./enhanced_csv_form_controller"
application.register("enhanced-csv-form", EnhancedCsvFormController)

import EnhancedDataSourceFormController from "./enhanced_data_source_form_controller"
application.register("enhanced-data-source-form", EnhancedDataSourceFormController)

import EnhancedJsonFormController from "./enhanced_json_form_controller"
application.register("enhanced-json-form", EnhancedJsonFormController)

import ExpandableController from "./expandable_controller"
application.register("expandable", ExpandableController)

import FaqController from "./faq_controller"
application.register("faq", FaqController)

import FlashMessageController from "./flash_message_controller"
application.register("flash-message", FlashMessageController)

import HelloController from "./hello_controller"
application.register("hello", HelloController)

import LandingController from "./landing_controller"
application.register("landing", LandingController)

import LandingPageController from "./landing_page_controller"
application.register("landing-page", LandingPageController)

import ModalController from "./modal_controller"
application.register("modal", ModalController)

import NavigationController from "./navigation_controller"
application.register("navigation", NavigationController)

import RoiCalculatorController from "./roi_calculator_controller"
application.register("roi-calculator", RoiCalculatorController)

import SyncHistoryController from "./sync_history_controller"
application.register("sync-history", SyncHistoryController)

import TestController from "./test_controller"
application.register("test", TestController)

import ToggleController from "./toggle_controller"
application.register("toggle", ToggleController)

import TooltipController from "./tooltip_controller"
application.register("tooltip", TooltipController)

import ViewToggleController from "./view_toggle_controller"
application.register("view-toggle", ViewToggleController)
