import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["fileInput", "filePreview", "fileName", "fileSize", "tabButton", "tabContent"]
  
  connect() {
    console.log("CSV form controller connected")
    
    // Set up file input listener if target exists
    if (this.hasFileInputTarget) {
      this.fileInputTarget.addEventListener('change', this.handleFileChange.bind(this))
    }
  }
  
  // Tab switching
  switchTab(event) {
    const tab = event.currentTarget.dataset.tab
    console.log("Switching to tab:", tab)
    
    // Update tab buttons
    this.tabButtonTargets.forEach(btn => {
      if (btn.dataset.tab === tab) {
        btn.classList.add('border-primary-500', 'text-primary-600')
        btn.classList.remove('border-transparent', 'text-neutral-500')
      } else {
        btn.classList.remove('border-primary-500', 'text-primary-600')
        btn.classList.add('border-transparent', 'text-neutral-500')
      }
    })
    
    // Show/hide content
    this.tabContentTargets.forEach(content => {
      if (content.id === `csv-${tab}-tab`) {
        content.classList.remove('hidden')
      } else {
        content.classList.add('hidden')
      }
    })
  }
  
  // File upload handling
  handleFileChange(event) {
    const file = event.target.files[0]
    if (file) {
      console.log("File selected:", file.name, file.size)
      if (this.hasFileNameTarget) {
        this.fileNameTarget.textContent = file.name
      }
      if (this.hasFileSizeTarget) {
        this.fileSizeTarget.textContent = this.formatFileSize(file.size)
      }
      if (this.hasFilePreviewTarget) {
        this.filePreviewTarget.classList.remove('hidden')
      }
    }
  }
  
  clearFile() {
    console.log("Clearing file")
    if (this.hasFileInputTarget) {
      this.fileInputTarget.value = ''
    }
    if (this.hasFilePreviewTarget) {
      this.filePreviewTarget.classList.add('hidden')
    }
  }
  
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}