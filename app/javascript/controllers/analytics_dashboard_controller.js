import { Controller } from "@hotwired/stimulus"
import Chart from "chart.js/auto"

export default class extends Controller {
  static targets = [
    "dateRangeLabel", "refreshIcon", "customDateModal",
    "customDateFrom", "customDateTo",
    "syncTimeline<PERSON>hart", "dataVolumeTrendChart", 
    "sourcePerformanceChart", "errorDistributionChart",
    "totalSyncs"
  ]
  
  static values = {
    dateRange: String
  }
  
  connect() {
    this.initializeCharts()
    this.startAutoRefresh()
    this.updateDateRangeLabel()
    this.setupWebSocket()
  }
  
  disconnect() {
    this.stopAutoRefresh()
    this.destroyCharts()
    if (this.subscription) {
      this.subscription.unsubscribe()
    }
  }
  
  initializeCharts() {
    // Sync Timeline Chart
    if (this.hasSyncTimelineChartTarget) {
      const data = JSON.parse(this.syncTimelineChartTarget.dataset.chartData)
      this.createSyncTimelineChart(data)
    }
    
    // Data Volume Trend Chart
    if (this.hasDataVolumeTrendChartTarget) {
      const data = JSON.parse(this.dataVolumeTrendChartTarget.dataset.chartData)
      this.createDataVolumeTrendChart(data)
    }
    
    // Source Performance Chart
    if (this.hasSourcePerformanceChartTarget) {
      const data = JSON.parse(this.sourcePerformanceChartTarget.dataset.chartData)
      this.createSourcePerformanceChart(data)
    }
    
    // Error Distribution Chart
    if (this.hasErrorDistributionChartTarget) {
      const data = JSON.parse(this.errorDistributionChartTarget.dataset.chartData)
      this.createErrorDistributionChart(data)
    }
  }
  
  createSyncTimelineChart(rawData) {
    // Transform data for Chart.js
    const timeLabels = [...new Set(Object.keys(rawData).map(k => rawData[k].time))].sort()
    const statusTypes = ['completed', 'failed', 'cancelled']
    
    const datasets = statusTypes.map(status => {
      const color = {
        completed: 'rgb(34, 197, 94)',
        failed: 'rgb(239, 68, 68)',
        cancelled: 'rgb(251, 191, 36)'
      }[status]
      
      return {
        label: status.charAt(0).toUpperCase() + status.slice(1),
        data: timeLabels.map(time => {
          const key = Object.keys(rawData).find(k => rawData[k].time === time && rawData[k].status === status)
          return key ? rawData[key] : 0
        }),
        backgroundColor: color,
        borderColor: color,
        tension: 0.4
      }
    })
    
    this.syncTimelineChart = new Chart(this.syncTimelineChartTarget, {
      type: 'line',
      data: {
        labels: timeLabels.map(t => new Date(t).toLocaleString()),
        datasets: datasets
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom'
          },
          tooltip: {
            mode: 'index',
            intersect: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              stepSize: 1
            }
          }
        }
      }
    })
  }
  
  createDataVolumeTrendChart(data) {
    const labels = Object.keys(data).map(date => new Date(date).toLocaleDateString())
    const values = Object.values(data)
    
    this.dataVolumeTrendChart = new Chart(this.dataVolumeTrendChartTarget, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          label: 'Records Imported',
          data: values,
          backgroundColor: 'rgba(147, 51, 234, 0.1)',
          borderColor: 'rgb(147, 51, 234)',
          borderWidth: 2,
          fill: true,
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return value.toLocaleString()
              }
            }
          }
        }
      }
    })
  }
  
  createSourcePerformanceChart(data) {
    const sortedData = Object.entries(data).sort(([,a], [,b]) => b - a).slice(0, 10)
    const labels = sortedData.map(([name]) => name)
    const values = sortedData.map(([, duration]) => duration)
    
    this.sourcePerformanceChart = new Chart(this.sourcePerformanceChartTarget, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [{
          label: 'Avg Duration (seconds)',
          data: values,
          backgroundColor: values.map(v => 
            v < 60 ? 'rgba(34, 197, 94, 0.8)' : 
            v < 300 ? 'rgba(251, 191, 36, 0.8)' : 
            'rgba(239, 68, 68, 0.8)'
          )
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          x: {
            ticks: {
              callback: function(val, index) {
                const label = this.getLabelForValue(val)
                return label.length > 15 ? label.substr(0, 15) + '...' : label
              }
            }
          },
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return value + 's'
              }
            }
          }
        }
      }
    })
  }
  
  createErrorDistributionChart(data) {
    const labels = Object.keys(data)
    const values = Object.values(data)
    
    this.errorDistributionChart = new Chart(this.errorDistributionChartTarget, {
      type: 'doughnut',
      data: {
        labels: labels.map(l => l.replace('_', ' ').toUpperCase()),
        datasets: [{
          data: values,
          backgroundColor: [
            'rgba(239, 68, 68, 0.8)',
            'rgba(251, 191, 36, 0.8)',
            'rgba(59, 130, 246, 0.8)',
            'rgba(147, 51, 234, 0.8)',
            'rgba(34, 197, 94, 0.8)'
          ]
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'right'
          }
        }
      }
    })
  }
  
  destroyCharts() {
    if (this.syncTimelineChart) this.syncTimelineChart.destroy()
    if (this.dataVolumeTrendChart) this.dataVolumeTrendChart.destroy()
    if (this.sourcePerformanceChart) this.sourcePerformanceChart.destroy()
    if (this.errorDistributionChart) this.errorDistributionChart.destroy()
  }
  
  async refresh() {
    // Animate refresh icon
    this.refreshIconTarget.classList.add('animate-spin')
    
    try {
      const response = await fetch(`/dashboard/analytics.json?date_range=${this.dateRangeValue}`, {
        headers: {
          'Accept': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        this.updateMetrics(data.metrics)
        this.updateCharts(data.charts)
        this.showNotification('Dashboard refreshed successfully', 'success')
      }
    } catch (error) {
      console.error('Failed to refresh analytics:', error)
      this.showNotification('Failed to refresh dashboard', 'error')
    } finally {
      this.refreshIconTarget.classList.remove('animate-spin')
    }
  }
  
  updateMetrics(metrics) {
    // Update metric values with animation
    if (this.hasTotalSyncsTarget) {
      this.animateValue(this.totalSyncsTarget, parseInt(this.totalSyncsTarget.textContent.replace(/,/g, '')), metrics.total_syncs)
    }
    
    // Update other metrics similarly...
  }
  
  updateCharts(chartsData) {
    // Update chart data
    if (this.syncTimelineChart && chartsData.sync_timeline) {
      // Update sync timeline chart data
      this.syncTimelineChart.data = this.transformSyncTimelineData(chartsData.sync_timeline)
      this.syncTimelineChart.update()
    }
    
    // Update other charts similarly...
  }
  
  changeDateRange(event) {
    const range = event.currentTarget.dataset.range
    this.dateRangeValue = range
    
    // Update URL and reload
    const url = new URL(window.location)
    url.searchParams.set('date_range', range)
    window.location.href = url.toString()
  }
  
  showCustomDatePicker() {
    this.customDateModalTarget.classList.remove('hidden')
  }
  
  closeCustomDatePicker() {
    this.customDateModalTarget.classList.add('hidden')
  }
  
  applyCustomDateRange() {
    const from = this.customDateFromTarget.value
    const to = this.customDateToTarget.value
    
    if (from && to) {
      const url = new URL(window.location)
      url.searchParams.set('date_range', 'custom')
      url.searchParams.set('date_from', from)
      url.searchParams.set('date_to', to)
      window.location.href = url.toString()
    }
  }
  
  updateDateRangeLabel() {
    const labels = {
      '24_hours': 'Last 24 Hours',
      '7_days': 'Last 7 Days',
      '30_days': 'Last 30 Days',
      '90_days': 'Last 90 Days',
      'custom': 'Custom Range'
    }
    
    this.dateRangeLabelTarget.textContent = labels[this.dateRangeValue] || 'Last 30 Days'
  }
  
  startAutoRefresh() {
    // Auto-refresh every 5 minutes
    this.autoRefreshTimer = setInterval(() => {
      this.refresh()
    }, 300000)
  }
  
  stopAutoRefresh() {
    if (this.autoRefreshTimer) {
      clearInterval(this.autoRefreshTimer)
    }
  }
  
  animateValue(element, start, end, duration = 1000) {
    const range = end - start
    const minTimer = 50
    let stepTime = Math.abs(Math.floor(duration / range))
    stepTime = Math.max(stepTime, minTimer)
    
    const startTime = new Date().getTime()
    const endTime = startTime + duration
    let timer
    
    function run() {
      const now = new Date().getTime()
      const remaining = Math.max((endTime - now) / duration, 0)
      const value = Math.round(end - (remaining * range))
      element.textContent = value.toLocaleString()
      
      if (value === end) {
        clearInterval(timer)
      }
    }
    
    timer = setInterval(run, stepTime)
    run()
  }
  
  showNotification(message, type = 'info') {
    const notification = document.createElement('div')
    notification.className = `fixed bottom-4 right-4 px-6 py-4 rounded-lg shadow-lg z-50 transform transition-all duration-300 ${
      type === 'success' ? 'bg-green-500 text-white' :
      type === 'error' ? 'bg-red-500 text-white' :
      'bg-blue-500 text-white'
    }`
    notification.innerHTML = `
      <div class="flex items-center">
        <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'} mr-3"></i>
        <span>${message}</span>
      </div>
    `
    
    document.body.appendChild(notification)
    
    // Animate in
    setTimeout(() => {
      notification.classList.add('translate-y-0')
    }, 100)
    
    // Remove after 3 seconds
    setTimeout(() => {
      notification.classList.add('translate-y-full', 'opacity-0')
      setTimeout(() => notification.remove(), 300)
    }, 3000)
  }
  
  setupWebSocket() {
    // Setup ActionCable subscription for real-time updates
    if (typeof App !== 'undefined' && App.cable) {
      this.subscription = App.cable.subscriptions.create(
        { channel: "AnalyticsChannel" },
        {
          received: (data) => {
            this.handleWebSocketMessage(data)
          }
        }
      )
    }
  }
  
  handleWebSocketMessage(data) {
    switch (data.type) {
      case 'analytics_update':
        this.handleAnalyticsUpdate(data.data)
        break
      case 'metric_update':
        this.handleMetricUpdate(data.metric, data.value)
        break
      case 'sync_update':
        this.handleSyncUpdate(data.source_id, data.status)
        break
    }
  }
  
  handleAnalyticsUpdate(data) {
    // Update all metrics and charts with new data
    if (data.metrics) {
      this.updateMetrics(data.metrics)
    }
    if (data.charts) {
      this.updateCharts(data.charts)
    }
  }
  
  handleMetricUpdate(metric, value) {
    // Update specific metric with animation
    const metricElement = document.querySelector(`[data-metric="${metric}"]`)
    if (metricElement) {
      const currentValue = parseInt(metricElement.textContent.replace(/[^0-9]/g, ''))
      this.animateValue(metricElement, currentValue, value)
      
      // Add pulse effect
      metricElement.classList.add('animate-pulse')
      setTimeout(() => metricElement.classList.remove('animate-pulse'), 1000)
    }
  }
  
  handleSyncUpdate(sourceId, status) {
    // Update sync-related metrics when a sync status changes
    if (status === 'completed' || status === 'failed') {
      // Refresh the dashboard data
      this.refresh()
    }
  }
}