import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  connect() {
    console.log("Data source modal trigger controller connected")
  }

  async openEditModal(event) {
    const dataSourceId = event.currentTarget.dataset.dataSourceId
    console.log("Opening edit modal for data source:", dataSourceId)
    
    if (!dataSourceId) {
      console.error("No data source ID provided")
      return
    }
    
    try {
      // Show loading state
      this.showLoadingState(event.currentTarget)
      
      // Fetch the edit modal content
      const response = await fetch(`/dashboard/data_sources/${dataSourceId}/edit`, {
        headers: {
          'Accept': 'text/html',
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const html = await response.text()
      
      // Create modal container and inject content
      this.createModalContainer(html)
      
    } catch (error) {
      console.error('Error loading edit modal:', error)
      this.showErrorState(error.message)
    } finally {
      // Restore button state
      this.restoreButtonState(event.currentTarget)
    }
  }

  showLoadingState(button) {
    // Store original content
    button.dataset.originalContent = button.innerHTML
    button.disabled = true
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2" aria-hidden="true"></i>Loading...'
  }

  restoreButtonState(button) {
    if (button.dataset.originalContent) {
      button.innerHTML = button.dataset.originalContent
      button.disabled = false
      delete button.dataset.originalContent
    }
  }

  createModalContainer(html) {
    // Remove any existing modal
    const existingModal = document.querySelector('[data-controller*="data-source-edit-modal"]')
    if (existingModal) {
      existingModal.remove()
    }
    
    // Create new modal container
    const modalContainer = document.createElement('div')
    modalContainer.innerHTML = html
    
    // Append to body
    document.body.appendChild(modalContainer.firstElementChild)
    
    // The modal controller will automatically initialize due to Stimulus
  }

  showErrorState(message) {
    // Create error modal
    const errorModal = document.createElement('div')
    errorModal.className = 'fixed inset-0 z-50 overflow-y-auto'
    errorModal.innerHTML = `
      <div class="fixed inset-0 bg-slate-900/75 backdrop-blur-sm"></div>
      <div class="flex min-h-full items-center justify-center p-4">
        <div class="relative w-full max-w-md bg-white rounded-2xl shadow-2xl p-8">
          <div class="text-center">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-slate-900 mb-2">Error Loading Editor</h3>
            <p class="text-slate-600 mb-6">${message}</p>
            <button type="button" 
                    class="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-xl hover:bg-primary-700 transition-colors duration-200"
                    onclick="this.closest('.fixed').remove()">
              <i class="fas fa-times mr-2"></i>
              Close
            </button>
          </div>
        </div>
      </div>
    `
    
    document.body.appendChild(errorModal)
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (errorModal.parentNode) {
        errorModal.remove()
      }
    }, 5000)
  }

  // Handle modal close events
  handleModalClose() {
    console.log("Modal closed, cleaning up...")
    // Any cleanup logic can go here
  }
}
