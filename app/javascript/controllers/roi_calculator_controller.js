import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["modal", "form", "results", "employees", "revenue", "industry"]
  static values = { 
    industry: { type: String, default: "general" },
    visible: { type: Boolean, default: false }
  }
  
  connect() {
    this.setupModal();
  }
  
  setupModal() {
    // Create modal if it doesn't exist
    if (!document.getElementById('roi-calculator-modal')) {
      this.createModal();
    }
  }
  
  createModal() {
    const modal = document.createElement('div');
    modal.id = 'roi-calculator-modal';
    modal.className = 'fixed inset-0 z-50 hidden';
    modal.setAttribute('data-controller', 'modal');
    modal.setAttribute('data-modal-target', 'container');
    
    modal.innerHTML = `
      <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" data-action="click->modal#close"></div>
      <div class="fixed inset-0 flex items-center justify-center p-4">
        <div class="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-screen overflow-y-auto">
          <div class="p-6">
            <div class="flex justify-between items-center mb-6">
              <h3 class="text-2xl font-bold text-neutral-800">ROI Calculator</h3>
              <button class="text-gray-400 hover:text-gray-600 transition-colors duration-200" data-action="click->modal#close">
                <i class="fas fa-times text-xl"></i>
              </button>
            </div>
            <div data-modal-target="content">
              ${this.getCalculatorHTML()}
            </div>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
  }
  
  getCalculatorHTML() {
    return `
      <form data-roi-calculator-target="form" data-action="submit->roi-calculator#calculate">
        <div class="grid md:grid-cols-2 gap-6 mb-6">
          <div>
            <label for="roi-employees" class="block text-sm font-medium text-gray-700 mb-2">
              Number of Employees
            </label>
            <input type="number" 
                   id="roi-employees" 
                   data-roi-calculator-target="employees"
                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                   placeholder="50" 
                   min="1"
                   value="50"
                   required>
          </div>
          
          <div>
            <label for="roi-revenue" class="block text-sm font-medium text-gray-700 mb-2">
              Annual Revenue (£)
            </label>
            <input type="number" 
                   id="roi-revenue" 
                   data-roi-calculator-target="revenue"
                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                   placeholder="1000000" 
                   min="10000"
                   value="1000000"
                   required>
          </div>
          
          <div class="md:col-span-2">
            <label for="roi-industry" class="block text-sm font-medium text-gray-700 mb-2">
              Industry
            </label>
            <select id="roi-industry" 
                    data-roi-calculator-target="industry"
                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    required>
              <option value="retail">Retail & E-commerce</option>
              <option value="manufacturing">Manufacturing</option>
              <option value="healthcare">Healthcare</option>
              <option value="financial">Financial Services</option>
              <option value="logistics">Logistics & Transportation</option>
              <option value="energy">Energy & Utilities</option>
              <option value="realestate">Real Estate</option>
              <option value="general">Other</option>
            </select>
          </div>
        </div>
        
        <button type="submit" 
                class="w-full bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-all duration-200 font-semibold">
          Calculate ROI
        </button>
      </form>
      
      <div data-roi-calculator-target="results" class="hidden mt-6">
        <!-- Results will be populated here -->
      </div>
    `;
  }
  
  open() {
    const modal = document.getElementById('roi-calculator-modal');
    if (modal) {
      const modalController = this.application.getControllerForElementAndIdentifier(modal, 'modal');
      if (modalController) {
        modalController.open();
      }
      
      // Set industry if specified
      if (this.industryValue && this.industryValue !== 'general') {
        const industrySelect = modal.querySelector('[data-roi-calculator-target="industry"]');
        if (industrySelect) {
          industrySelect.value = this.industryValue;
        }
      }
    }
  }
  
  calculate(event) {
    event.preventDefault();
    
    const employees = parseInt(this.employeesTarget.value) || 50;
    const revenue = parseInt(this.revenueTarget.value) || 1000000;
    const industry = this.industryTarget.value || 'general';
    
    // Industry-specific multipliers
    const industryMultipliers = {
      retail: { efficiency: 0.25, cost_reduction: 0.15, roi: 3.4 },
      manufacturing: { efficiency: 0.30, cost_reduction: 0.20, roi: 2.8 },
      healthcare: { efficiency: 0.35, cost_reduction: 0.25, roi: 4.2 },
      financial: { efficiency: 0.28, cost_reduction: 0.18, roi: 3.8 },
      logistics: { efficiency: 0.32, cost_reduction: 0.22, roi: 3.2 },
      energy: { efficiency: 0.40, cost_reduction: 0.25, roi: 2.9 },
      realestate: { efficiency: 0.18, cost_reduction: 0.12, roi: 2.6 },
      general: { efficiency: 0.25, cost_reduction: 0.15, roi: 3.0 }
    };
    
    const multiplier = industryMultipliers[industry] || industryMultipliers.general;
    
    // Calculations
    const avgSalary = 45000; // Average UK salary
    const totalSalaryCost = employees * avgSalary;
    const efficiencyGain = totalSalaryCost * multiplier.efficiency;
    const costReduction = revenue * multiplier.cost_reduction;
    const totalSavings = efficiencyGain + costReduction;
    const monthlySubscription = this.getSubscriptionCost(employees);
    const annualSubscription = monthlySubscription * 12;
    const netBenefit = totalSavings - annualSubscription;
    const roiPercentage = ((netBenefit / annualSubscription) * 100);
    const paybackMonths = (annualSubscription / (totalSavings / 12));
    
    this.displayResults({
      totalSavings,
      efficiencyGain,
      costReduction,
      annualSubscription,
      netBenefit,
      roiPercentage,
      paybackMonths,
      industry
    });
  }
  
  getSubscriptionCost(employees) {
    if (employees <= 25) return 99;
    if (employees <= 100) return 299;
    return 699;
  }
  
  displayResults(data) {
    const resultsHTML = `
      <div class="bg-gradient-to-r from-primary-50 to-primary-100 rounded-lg p-6">
        <h4 class="text-lg font-bold text-neutral-800 mb-4">Your Projected ROI</h4>
        
        <div class="grid grid-cols-2 gap-4 mb-6">
          <div class="bg-white rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-primary-600">£${data.totalSavings.toLocaleString()}</div>
            <div class="text-sm text-neutral-600">Annual Savings</div>
          </div>
          <div class="bg-white rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-green-600">${data.roiPercentage.toFixed(0)}%</div>
            <div class="text-sm text-neutral-600">ROI</div>
          </div>
          <div class="bg-white rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-blue-600">${data.paybackMonths.toFixed(1)}</div>
            <div class="text-sm text-neutral-600">Payback (months)</div>
          </div>
          <div class="bg-white rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-purple-600">£${data.netBenefit.toLocaleString()}</div>
            <div class="text-sm text-neutral-600">Net Benefit</div>
          </div>
        </div>
        
        <div class="space-y-3 mb-6">
          <div class="flex justify-between items-center">
            <span class="text-neutral-700">Efficiency Gains:</span>
            <span class="font-semibold text-neutral-800">£${data.efficiencyGain.toLocaleString()}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-neutral-700">Cost Reduction:</span>
            <span class="font-semibold text-neutral-800">£${data.costReduction.toLocaleString()}</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-neutral-700">Annual Subscription:</span>
            <span class="font-semibold text-neutral-800">£${data.annualSubscription.toLocaleString()}</span>
          </div>
        </div>
        
        <div class="flex flex-col sm:flex-row gap-3">
          <a href="/users/sign_up"
             class="flex-1 bg-primary-600 text-white px-4 py-3 rounded-lg hover:bg-primary-700 transition-all duration-200 font-semibold text-center">
            Start Free Trial
          </a>
          <a href="#demo" 
             class="flex-1 border border-primary-600 text-primary-600 px-4 py-3 rounded-lg hover:bg-primary-50 transition-all duration-200 font-semibold text-center"
             data-action="click->landing#openDemoModal">
            Schedule Demo
          </a>
        </div>
      </div>
    `;
    
    this.resultsTarget.innerHTML = resultsHTML;
    this.resultsTarget.classList.remove('hidden');
    
    // Scroll to results
    this.resultsTarget.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
  }
  
  toggle() {
    this.open();
  }
}
