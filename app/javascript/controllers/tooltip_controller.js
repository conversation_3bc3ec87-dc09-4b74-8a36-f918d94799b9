import { Controller } from "@hotwired/stimulus"

// Simple tooltip controller for help text
export default class extends Controller {
  static values = { 
    content: String,
    position: { type: String, default: "top" }
  }
  
  connect() {
    this.tooltip = null;
    this.element.addEventListener("mouseenter", this.show.bind(this));
    this.element.addEventListener("mouseleave", this.hide.bind(this));
    this.element.addEventListener("focus", this.show.bind(this));
    this.element.addEventListener("blur", this.hide.bind(this));
  }
  
  disconnect() {
    this.hide();
    this.element.removeEventListener("mouseenter", this.show.bind(this));
    this.element.removeEventListener("mouseleave", this.hide.bind(this));
    this.element.removeEventListener("focus", this.show.bind(this));
    this.element.removeEventListener("blur", this.hide.bind(this));
  }
  
  show() {
    if (this.tooltip || !this.contentValue) return;
    
    this.tooltip = document.createElement("div");
    this.tooltip.className = "absolute z-50 px-3 py-2 text-sm text-white bg-neutral-900 rounded-lg shadow-lg max-w-xs";
    this.tooltip.textContent = this.contentValue;
    this.tooltip.setAttribute("role", "tooltip");
    
    // Position the tooltip
    document.body.appendChild(this.tooltip);
    this.positionTooltip();
    
    // Animate in
    this.tooltip.style.opacity = "0";
    this.tooltip.style.transform = "translateY(4px)";
    this.tooltip.style.transition = "opacity 150ms ease-out, transform 150ms ease-out";
    
    requestAnimationFrame(() => {
      this.tooltip.style.opacity = "1";
      this.tooltip.style.transform = "translateY(0)";
    });
  }
  
  hide() {
    if (!this.tooltip) return;
    
    this.tooltip.style.opacity = "0";
    this.tooltip.style.transform = "translateY(4px)";
    
    setTimeout(() => {
      if (this.tooltip && this.tooltip.parentNode) {
        this.tooltip.parentNode.removeChild(this.tooltip);
      }
      this.tooltip = null;
    }, 150);
  }
  
  positionTooltip() {
    if (!this.tooltip) return;
    
    const rect = this.element.getBoundingClientRect();
    const tooltipRect = this.tooltip.getBoundingClientRect();
    
    let top, left;
    
    switch (this.positionValue) {
      case "bottom":
        top = rect.bottom + window.scrollY + 8;
        left = rect.left + window.scrollX + (rect.width / 2) - (tooltipRect.width / 2);
        break;
      case "left":
        top = rect.top + window.scrollY + (rect.height / 2) - (tooltipRect.height / 2);
        left = rect.left + window.scrollX - tooltipRect.width - 8;
        break;
      case "right":
        top = rect.top + window.scrollY + (rect.height / 2) - (tooltipRect.height / 2);
        left = rect.right + window.scrollX + 8;
        break;
      default: // top
        top = rect.top + window.scrollY - tooltipRect.height - 8;
        left = rect.left + window.scrollX + (rect.width / 2) - (tooltipRect.width / 2);
    }
    
    // Keep tooltip within viewport
    const padding = 8;
    left = Math.max(padding, Math.min(left, window.innerWidth - tooltipRect.width - padding));
    top = Math.max(padding, top);
    
    this.tooltip.style.position = "absolute";
    this.tooltip.style.top = `${top}px`;
    this.tooltip.style.left = `${left}px`;
  }
}
