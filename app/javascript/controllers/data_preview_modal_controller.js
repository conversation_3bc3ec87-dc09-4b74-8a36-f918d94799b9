import { Controller } from "@hotwired/stimulus"

// Controller for data source preview modal
export default class extends Controller {
  static targets = ["modal", "modalContent", "loadingSpinner", "dataTable", "errorMessage"]
  static values = { 
    sourceId: Number,
    sourceName: String
  }
  
  connect() {
    // Close modal on escape key
    this.handleEscape = (e) => {
      if (e.key === 'Escape') this.close();
    };
    document.addEventListener('keydown', this.handleEscape);
  }
  
  disconnect() {
    document.removeEventListener('keydown', this.handleEscape);
  }
  
  async open(event) {
    event.preventDefault();
    const sourceId = event.currentTarget.dataset.sourceId;
    const sourceName = event.currentTarget.dataset.sourceName;
    
    this.sourceIdValue = sourceId;
    this.sourceNameValue = sourceName;
    
    // Show modal
    this.modalTarget.classList.remove('hidden');
    document.body.classList.add('overflow-hidden');
    
    // Show loading state
    this.showLoading();
    
    // Fetch preview data
    try {
      const response = await fetch(`/dashboard/data_sources/${sourceId}/sample_data.json`, {
        headers: {
          'Accept': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        this.displayData(data);
      } else {
        const error = await response.text();
        this.showError(error || 'Failed to load preview data');
      }
    } catch (error) {
      this.showError('Network error: ' + error.message);
    }
  }
  
  close() {
    this.modalTarget.classList.add('hidden');
    document.body.classList.remove('overflow-hidden');
    this.clearContent();
  }
  
  closeOnBackdrop(event) {
    if (event.target === this.modalTarget) {
      this.close();
    }
  }
  
  showLoading() {
    this.loadingSpinnerTarget.classList.remove('hidden');
    this.dataTableTarget.classList.add('hidden');
    this.errorMessageTarget.classList.add('hidden');
  }
  
  showError(message) {
    this.loadingSpinnerTarget.classList.add('hidden');
    this.dataTableTarget.classList.add('hidden');
    this.errorMessageTarget.classList.remove('hidden');
    this.errorMessageTarget.innerHTML = `
      <div class="flex items-center">
        <i class="fas fa-exclamation-triangle text-red-500 mr-3 text-xl"></i>
        <div>
          <p class="font-semibold text-red-900">Error loading preview</p>
          <p class="text-red-700 text-sm mt-1">${message}</p>
        </div>
      </div>
    `;
  }
  
  displayData(response) {
    this.loadingSpinnerTarget.classList.add('hidden');
    this.errorMessageTarget.classList.add('hidden');
    this.dataTableTarget.classList.remove('hidden');
    
    if (!response.data || response.data.length === 0) {
      this.dataTableTarget.innerHTML = `
        <div class="text-center py-12">
          <i class="fas fa-inbox text-gray-400 text-5xl mb-4"></i>
          <p class="text-gray-600">No data available for preview</p>
        </div>
      `;
      return;
    }
    
    const columns = response.columns || Object.keys(response.data[0]);
    const data = response.data;
    
    // Build table HTML
    let tableHtml = `
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
    `;
    
    // Add headers
    columns.forEach(col => {
      tableHtml += `
        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
          ${this.escapeHtml(col)}
        </th>
      `;
    });
    
    tableHtml += `
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
    `;
    
    // Add rows
    data.forEach((row, index) => {
      tableHtml += `
        <tr class="${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}">
      `;
      
      columns.forEach(col => {
        const value = row[col];
        const displayValue = this.formatCellValue(value);
        tableHtml += `
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
            ${displayValue}
          </td>
        `;
      });
      
      tableHtml += `
        </tr>
      `;
    });
    
    tableHtml += `
          </tbody>
        </table>
      </div>
    `;
    
    // Add row count info
    tableHtml += `
      <div class="px-6 py-3 bg-gray-50 border-t border-gray-200">
        <p class="text-sm text-gray-600">
          Showing ${data.length} of ${response.total_count || data.length} rows
        </p>
      </div>
    `;
    
    this.dataTableTarget.innerHTML = tableHtml;
  }
  
  formatCellValue(value) {
    if (value === null || value === undefined) {
      return '<span class="text-gray-400 italic">null</span>';
    }
    
    if (typeof value === 'boolean') {
      return `<span class="px-2 py-1 text-xs rounded-full ${value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">${value}</span>`;
    }
    
    if (typeof value === 'object') {
      return `<code class="text-xs bg-gray-100 px-1 py-0.5 rounded">${this.escapeHtml(JSON.stringify(value))}</code>`;
    }
    
    const strValue = String(value);
    if (strValue.length > 50) {
      return `
        <span title="${this.escapeHtml(strValue)}">
          ${this.escapeHtml(strValue.substring(0, 50))}...
        </span>
      `;
    }
    
    return this.escapeHtml(strValue);
  }
  
  escapeHtml(unsafe) {
    return unsafe
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#039;");
  }
  
  clearContent() {
    this.dataTableTarget.innerHTML = '';
    this.errorMessageTarget.innerHTML = '';
  }
  
  downloadData() {
    window.location.href = `/dashboard/data_sources/${this.sourceIdValue}/sample_data.csv`;
  }
}