import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["mobileMenu", "navLink", "mobileNavLink", "menuButton", "menuLine"]
  static values = {
    activeSection: { type: String, default: "" },
    scrollOffset: { type: Number, default: 80 }
  }

  connect() {
    // Bind methods
    this.handleScroll = this.handleScroll.bind(this);
    this.handleResize = this.handleResize.bind(this);
    this.handleClickOutside = this.handleClickOutside.bind(this);

    // Add event listeners
    window.addEventListener('scroll', this.handleScroll);
    window.addEventListener('resize', this.handleResize);
    document.addEventListener('click', this.handleClickOutside);

    // Initialize
    this.handleScroll(); // Check initial state
    this.updateActiveSection();
    this.setupIntersectionObserver();
  }

  disconnect() {
    window.removeEventListener('scroll', this.handleScroll);
    window.removeEventListener('resize', this.handleResize);
    document.removeEventListener('click', this.handleClickOutside);

    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }
  }

  handleScroll() {
    const header = this.element;
    const scrollY = window.scrollY;

    // Header background effect
    if (scrollY > 50) {
      header.classList.add('shadow-md', 'bg-white');
      header.classList.remove('bg-white/95');
    } else {
      header.classList.remove('shadow-md', 'bg-white');
      header.classList.add('bg-white/95');
    }

    // Update active section
    this.updateActiveSection();
  }

  handleResize() {
    // Close mobile menu on resize to desktop
    if (window.innerWidth >= 1024) {
      this.closeMobileMenu();
    }
  }

  handleClickOutside(event) {
    if (!this.element.contains(event.target) && this.hasMobileMenuTarget) {
      this.closeMobileMenu();
    }
  }

  setupIntersectionObserver() {
    const options = {
      root: null,
      rootMargin: `-${this.scrollOffsetValue}px 0px -50% 0px`,
      threshold: 0.1
    };

    this.intersectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.id;
          this.activeSectionValue = sectionId;
          this.updateNavigationState(sectionId);
        }
      });
    }, options);

    // Observe all sections
    const sections = document.querySelectorAll('section[id]');
    sections.forEach(section => {
      this.intersectionObserver.observe(section);
    });
  }

  updateActiveSection() {
    const sections = document.querySelectorAll('section[id]');
    const scrollPosition = window.scrollY + this.scrollOffsetValue;

    let currentSection = '';

    sections.forEach(section => {
      const sectionTop = section.offsetTop;
      const sectionHeight = section.offsetHeight;

      if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
        currentSection = section.id;
      }
    });

    if (currentSection !== this.activeSectionValue) {
      this.activeSectionValue = currentSection;
      this.updateNavigationState(currentSection);
    }
  }

  updateNavigationState(activeSection) {
    // Update desktop navigation
    if (this.hasNavLinkTarget) {
      this.navLinkTargets.forEach(link => {
        const section = link.dataset.section;
        const isActive = section === activeSection;

        this.updateLinkState(link, isActive);
      });
    }

    // Update mobile navigation
    if (this.hasMobileNavLinkTarget) {
      this.mobileNavLinkTargets.forEach(link => {
        const section = link.dataset.section;
        const isActive = section === activeSection;

        this.updateMobileLinkState(link, isActive);
      });
    }
  }

  updateLinkState(link, isActive) {
    const underline = link.querySelector('div');

    if (isActive) {
      link.classList.add('text-primary-600', 'bg-primary-50');
      link.classList.remove('text-neutral-700');
      if (underline) {
        underline.style.width = '100%';
      }
    } else {
      link.classList.remove('text-primary-600', 'bg-primary-50');
      link.classList.add('text-neutral-700');
      if (underline) {
        underline.style.width = '0';
      }
    }
  }

  updateMobileLinkState(link, isActive) {
    if (isActive) {
      link.classList.add('text-primary-600', 'bg-primary-50', 'border-l-4', 'border-primary-600');
      link.classList.remove('text-neutral-700');
    } else {
      link.classList.remove('text-primary-600', 'bg-primary-50', 'border-l-4', 'border-primary-600');
      link.classList.add('text-neutral-700');
    }
  }

  // Navigation methods
  navigateToSection(event) {
    event.preventDefault();

    const link = event.currentTarget;
    const sectionId = link.dataset.section;
    const targetSection = document.getElementById(sectionId);

    if (targetSection) {
      const headerHeight = this.element.offsetHeight;
      const targetPosition = targetSection.offsetTop - headerHeight - 20;

      // Smooth scroll to section
      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });

      // Close mobile menu if open
      this.closeMobileMenu();

      // Update URL hash without triggering scroll
      if (history.pushState) {
        history.pushState(null, null, `#${sectionId}`);
      }

      // Announce to screen readers
      if (window.announceToScreenReader) {
        window.announceToScreenReader(`Navigated to ${link.textContent} section`);
      }
    }
  }

  toggleMobile() {
    const isOpen = !this.mobileMenuTarget.classList.contains('hidden');

    if (isOpen) {
      this.closeMobileMenu();
    } else {
      this.openMobileMenu();
    }
  }

  openMobileMenu() {
    this.mobileMenuTarget.classList.remove('hidden');

    // Update button state
    if (this.hasMenuButtonTarget) {
      this.menuButtonTarget.setAttribute('aria-expanded', 'true');
    }

    // Animate hamburger to X
    this.animateMenuIcon(true);

    // Prevent body scroll
    document.body.style.overflow = 'hidden';

    // Focus first menu item
    const firstMenuItem = this.mobileMenuTarget.querySelector('[role="menuitem"]');
    if (firstMenuItem) {
      setTimeout(() => firstMenuItem.focus(), 100);
    }
  }

  closeMobileMenu() {
    if (!this.hasMobileMenuTarget) return;

    this.mobileMenuTarget.classList.add('hidden');

    // Update button state
    if (this.hasMenuButtonTarget) {
      this.menuButtonTarget.setAttribute('aria-expanded', 'false');
    }

    // Animate X back to hamburger
    this.animateMenuIcon(false);

    // Restore body scroll
    document.body.style.overflow = '';
  }

  animateMenuIcon(isOpen) {
    if (!this.hasMenuLineTarget) return;

    const lines = this.menuLineTargets;

    if (isOpen) {
      // Transform to X
      lines[0].style.transform = 'rotate(45deg) translate(5px, 5px)';
      lines[1].style.opacity = '0';
      lines[2].style.transform = 'rotate(-45deg) translate(7px, -6px)';
    } else {
      // Transform back to hamburger
      lines[0].style.transform = 'rotate(0) translate(0, 0)';
      lines[1].style.opacity = '1';
      lines[2].style.transform = 'rotate(0) translate(0, 0)';
    }
  }
