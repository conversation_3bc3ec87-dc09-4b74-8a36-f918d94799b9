import { Controller } from "@hotwired/stimulus"

// Enhanced sync history controller with filtering and details management
export default class extends Controller {
  static targets = [
    "tableBody", "syncRow", "detailsRow"
  ]
  static values = { 
    currentFilter: { type: String, default: "all" }
  }
  
  connect() {
    this.originalRows = Array.from(this.syncRowTargets);
    this.updateVisibleCount();
  }
  
  // Filter by status
  filterByStatus(event) {
    const status = event.currentTarget.dataset.status;
    this.currentFilterValue = status;
    this.applyFilters();
    
    // Close dropdown
    this.dispatch("filterChanged", { detail: { type: "status", value: status } });
  }
  
  // Apply filters
  applyFilters() {
    let visibleCount = 0;
    
    this.syncRowTargets.forEach(row => {
      if (this.shouldShowRow(row)) {
        row.style.display = "";
        visibleCount++;
      } else {
        row.style.display = "none";
        // Also hide associated details row
        const syncId = row.dataset.syncId;
        const detailsRow = document.getElementById(`sync-details-${syncId}`);
        if (detailsRow) {
          detailsRow.style.display = "none";
          detailsRow.classList.add("hidden");
        }
      }
    });
    
    this.updateVisibleCount(visibleCount);
  }
  
  shouldShowRow(row) {
    const status = row.dataset.status || "";
    
    // Check status filter
    return this.currentFilterValue === "all" || status === this.currentFilterValue;
  }
  
  updateVisibleCount(count = null) {
    if (count === null) {
      // Count visible rows
      count = this.syncRowTargets.filter(row => row.style.display !== "none").length;
    }
    
    // Update any visible count displays
    const countElements = document.querySelectorAll('[data-sync-count]');
    countElements.forEach(element => {
      element.textContent = count;
    });
  }
  
  // Toggle details for a specific sync
  toggleDetails(event) {
    const button = event.currentTarget;
    const syncId = button.dataset.syncId;
    const detailsRow = document.getElementById(`sync-details-${syncId}`);
    
    if (!detailsRow) return;
    
    const isHidden = detailsRow.classList.contains('hidden');
    
    if (isHidden) {
      // Show details
      detailsRow.classList.remove('hidden');
      button.setAttribute('aria-expanded', 'true');
      button.querySelector('i').classList.remove('fa-eye');
      button.querySelector('i').classList.add('fa-eye-slash');
      
      // Smooth scroll to details if needed
      setTimeout(() => {
        detailsRow.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'nearest' 
        });
      }, 100);
    } else {
      // Hide details
      detailsRow.classList.add('hidden');
      button.setAttribute('aria-expanded', 'false');
      button.querySelector('i').classList.remove('fa-eye-slash');
      button.querySelector('i').classList.add('fa-eye');
    }
  }
  
  // Expand all details
  expandAll() {
    this.syncRowTargets.forEach(row => {
      if (row.style.display !== "none") {
        const syncId = row.dataset.syncId;
        const detailsRow = document.getElementById(`sync-details-${syncId}`);
        const button = row.querySelector('[data-action*="toggleDetails"]');
        
        if (detailsRow && button) {
          detailsRow.classList.remove('hidden');
          button.setAttribute('aria-expanded', 'true');
          button.querySelector('i').classList.remove('fa-eye');
          button.querySelector('i').classList.add('fa-eye-slash');
        }
      }
    });
  }
  
  // Collapse all details
  collapseAll() {
    this.detailsRowTargets.forEach(detailsRow => {
      detailsRow.classList.add('hidden');
    });
    
    // Update all buttons
    const buttons = document.querySelectorAll('[data-action*="toggleDetails"]');
    buttons.forEach(button => {
      button.setAttribute('aria-expanded', 'false');
      button.querySelector('i').classList.remove('fa-eye-slash');
      button.querySelector('i').classList.add('fa-eye');
    });
  }
  
  // Export visible sync data
  exportVisible() {
    const visibleRows = this.syncRowTargets.filter(row => row.style.display !== "none");
    
    if (visibleRows.length === 0) {
      this.showNotification("No sync records to export", "warning");
      return;
    }
    
    const data = visibleRows.map(row => {
      const syncId = row.dataset.syncId;
      const cells = row.querySelectorAll('td');
      
      return {
        id: syncId,
        started_at: cells[0]?.textContent.trim(),
        type: cells[1]?.textContent.trim(),
        status: cells[2]?.textContent.trim(),
        duration: cells[3]?.textContent.trim(),
        records: cells[4]?.textContent.trim(),
        success_rate: cells[5]?.textContent.trim()
      };
    });
    
    this.downloadJSON(data, "sync-history.json");
  }
  
  downloadJSON(data, filename) {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
  
  showNotification(message, type = "info") {
    // Create a simple notification
    const notification = document.createElement("div");
    notification.className = `fixed top-4 right-4 px-4 py-2 rounded-lg shadow-lg z-50 ${
      type === "warning" ? "bg-yellow-100 text-yellow-800 border border-yellow-200" :
      type === "error" ? "bg-red-100 text-red-800 border border-red-200" :
      "bg-blue-100 text-blue-800 border border-blue-200"
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.remove();
    }, 3000);
  }
  
  // Reset all filters
  resetFilters() {
    this.currentFilterValue = "all";
    this.applyFilters();
  }
  
  // Auto-refresh functionality (if needed)
  startAutoRefresh(intervalSeconds = 30) {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }
    
    this.refreshInterval = setInterval(() => {
      // Only refresh if there are running syncs
      const runningRows = this.syncRowTargets.filter(row => 
        row.dataset.status === "running" && row.style.display !== "none"
      );
      
      if (runningRows.length > 0) {
        window.location.reload();
      }
    }, intervalSeconds * 1000);
  }
  
  stopAutoRefresh() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }
  }
  
  disconnect() {
    this.stopAutoRefresh();
  }
}
