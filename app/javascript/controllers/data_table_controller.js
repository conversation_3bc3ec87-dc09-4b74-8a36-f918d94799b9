import { Controller } from "@hotwired/stimulus"

// Enhanced data table controller with search, sort, and export functionality
export default class extends Controller {
  static targets = [
    "searchInput", "table", "tableBody", "tableRow", "cardContainer", "cardRow",
    "filteredCount", "filteredCountText"
  ]
  static values = { 
    sortColumn: { type: Number, default: -1 },
    sortDirection: { type: String, default: "asc" }
  }
  
  connect() {
    this.originalRows = Array.from(this.tableRowTargets);
    this.originalCards = Array.from(this.cardRowTargets);
    this.allData = this.extractTableData();
  }
  
  // Search functionality
  search() {
    const query = this.searchInputTarget.value.toLowerCase().trim();
    
    if (query === "") {
      this.showAllRows();
      this.hideFilteredCount();
      return;
    }
    
    let visibleCount = 0;
    
    // Filter table rows
    this.tableRowTargets.forEach(row => {
      const text = row.textContent.toLowerCase();
      if (text.includes(query)) {
        row.style.display = "";
        visibleCount++;
      } else {
        row.style.display = "none";
      }
    });
    
    // Filter card rows
    this.cardRowTargets.forEach(card => {
      const text = card.textContent.toLowerCase();
      if (text.includes(query)) {
        card.style.display = "";
      } else {
        card.style.display = "none";
      }
    });
    
    this.showFilteredCount(visibleCount);
  }
  
  // Sort functionality
  sortColumn(event) {
    const columnIndex = parseInt(event.currentTarget.dataset.column);
    const columnName = event.currentTarget.dataset.columnName;
    
    // Toggle sort direction if same column
    if (this.sortColumnValue === columnIndex) {
      this.sortDirectionValue = this.sortDirectionValue === "asc" ? "desc" : "asc";
    } else {
      this.sortColumnValue = columnIndex;
      this.sortDirectionValue = "asc";
    }
    
    this.updateSortIcons(columnIndex);
    this.performSort(columnIndex);
    
    // Announce sort change for accessibility
    this.announceSortChange(columnName, this.sortDirectionValue);
  }
  
  performSort(columnIndex) {
    const tbody = this.tableBodyTarget;
    const rows = Array.from(this.tableRowTargets);
    
    rows.sort((a, b) => {
      const cellA = a.cells[columnIndex]?.textContent.trim() || "";
      const cellB = b.cells[columnIndex]?.textContent.trim() || "";
      
      // Try to parse as numbers first
      const numA = parseFloat(cellA);
      const numB = parseFloat(cellB);
      
      let comparison = 0;
      
      if (!isNaN(numA) && !isNaN(numB)) {
        // Numeric comparison
        comparison = numA - numB;
      } else {
        // String comparison
        comparison = cellA.localeCompare(cellB);
      }
      
      return this.sortDirectionValue === "asc" ? comparison : -comparison;
    });
    
    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));
    
    // Update row striping
    this.updateRowStriping();
  }
  
  updateSortIcons(activeColumn) {
    // Reset all sort icons
    const headers = this.tableTarget.querySelectorAll('th[data-column]');
    headers.forEach((header, index) => {
      const icon = header.querySelector('[data-data-table-target*="sortIcon"]');
      if (icon) {
        icon.className = "fas fa-sort text-neutral-300 text-xs";
        header.setAttribute("aria-sort", "none");
      }
    });
    
    // Update active column icon
    const activeHeader = this.tableTarget.querySelector(`th[data-column="${activeColumn}"]`);
    if (activeHeader) {
      const icon = activeHeader.querySelector('[data-data-table-target*="sortIcon"]');
      if (icon) {
        const iconClass = this.sortDirectionValue === "asc" ? "fa-sort-up" : "fa-sort-down";
        icon.className = `fas ${iconClass} text-neutral-600 text-xs`;
        activeHeader.setAttribute("aria-sort", this.sortDirectionValue === "asc" ? "ascending" : "descending");
      }
    }
  }
  
  updateRowStriping() {
    this.tableRowTargets.forEach((row, index) => {
      row.className = row.className.replace(/bg-(white|neutral-50)/g, "");
      row.classList.add(index % 2 === 0 ? "bg-white" : "bg-neutral-50");
    });
  }
  
  // Export functionality
  exportCSV() {
    const data = this.getVisibleData();
    const csv = this.convertToCSV(data);
    this.downloadFile(csv, "sample-data.csv", "text/csv");
  }
  
  exportJSON() {
    const data = this.getVisibleData();
    const json = JSON.stringify(data, null, 2);
    this.downloadFile(json, "sample-data.json", "application/json");
  }
  
  // Helper methods
  extractTableData() {
    const headers = Array.from(this.tableTarget.querySelectorAll('thead th')).map(th => 
      th.textContent.trim()
    );
    
    return this.tableRowTargets.map(row => {
      const cells = Array.from(row.cells);
      const rowData = {};
      headers.forEach((header, index) => {
        rowData[header] = cells[index]?.textContent.trim() || "";
      });
      return rowData;
    });
  }
  
  getVisibleData() {
    const headers = Array.from(this.tableTarget.querySelectorAll('thead th')).map(th => 
      th.textContent.trim()
    );
    
    return this.tableRowTargets
      .filter(row => row.style.display !== "none")
      .map(row => {
        const cells = Array.from(row.cells);
        const rowData = {};
        headers.forEach((header, index) => {
          rowData[header] = cells[index]?.textContent.trim() || "";
        });
        return rowData;
      });
  }
  
  convertToCSV(data) {
    if (data.length === 0) return "";
    
    const headers = Object.keys(data[0]);
    const csvRows = [headers.join(",")];
    
    data.forEach(row => {
      const values = headers.map(header => {
        const value = row[header] || "";
        // Escape quotes and wrap in quotes if contains comma
        return value.includes(",") || value.includes('"') 
          ? `"${value.replace(/"/g, '""')}"` 
          : value;
      });
      csvRows.push(values.join(","));
    });
    
    return csvRows.join("\n");
  }
  
  downloadFile(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
  
  showAllRows() {
    this.tableRowTargets.forEach(row => row.style.display = "");
    this.cardRowTargets.forEach(card => card.style.display = "");
  }
  
  showFilteredCount(count) {
    this.filteredCountTarget.classList.remove("hidden");
    this.filteredCountTextTarget.textContent = `${count} rows`;
  }
  
  hideFilteredCount() {
    this.filteredCountTarget.classList.add("hidden");
  }
  
  announceSortChange(columnName, direction) {
    const message = `Table sorted by ${columnName} in ${direction}ending order`;
    const announcement = document.createElement("div");
    announcement.setAttribute("aria-live", "polite");
    announcement.setAttribute("aria-atomic", "true");
    announcement.className = "sr-only";
    announcement.textContent = message;
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  }
}
