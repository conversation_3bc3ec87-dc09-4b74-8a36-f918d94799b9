import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["container", "content"]
  
  connect() {
    // Close on escape key
    this.handleKeydown = this.handleKeydown.bind(this);
    document.addEventListener('keydown', this.handleKeydown);
  }
  
  disconnect() {
    document.removeEventListener('keydown', this.handleKeydown);
  }
  
  handleKeydown(event) {
    if (event.key === 'Escape' && !this.element.classList.contains('hidden')) {
      this.close();
    }
  }

  open(event) {
    if (event) event.preventDefault();
    this.element.classList.remove('hidden');
    document.body.style.overflow = 'hidden';

    // Focus management for accessibility
    const firstFocusable = this.element.querySelector('input, button, select, textarea, [tabindex]:not([tabindex="-1"])');
    if (firstFocusable) {
      firstFocusable.focus();
    }
  }

  close(event) {
    if (event) event.preventDefault();
    this.element.classList.add('hidden');
    document.body.style.overflow = '';
  }

  // Close when clicking outside the modal content
  clickOutside(event) {
    if (event.target.classList.contains('fixed') && event.target.classList.contains('inset-0')) {
      this.close();
    }
  }
}
