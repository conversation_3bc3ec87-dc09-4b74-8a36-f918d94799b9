import { Controller } from "@hotwired/stimulus"

// Controller for real-time data source status updates
export default class extends Controller {
  static targets = ["statusBadge", "lastSync", "rowCount", "syncButton"]
  static values = { 
    sourceId: Number,
    refreshInterval: { type: Number, default: 30000 } // 30 seconds
  }
  
  connect() {
    this.startPolling();
    this.setupWebSocketIfAvailable();
  }
  
  disconnect() {
    this.stopPolling();
    if (this.channel) {
      this.channel.unsubscribe();
    }
  }
  
  startPolling() {
    // Poll for status updates
    this.pollTimer = setInterval(() => {
      this.fetchStatus();
    }, this.refreshIntervalValue);
  }
  
  stopPolling() {
    if (this.pollTimer) {
      clearInterval(this.pollTimer);
      this.pollTimer = null;
    }
  }
  
  async fetchStatus() {
    try {
      const response = await fetch(`/dashboard/data_sources/${this.sourceIdValue}/status.json`, {
        headers: {
          'Accept': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        this.updateStatus(data);
      }
    } catch (error) {
      console.error('Failed to fetch status:', error);
    }
  }
  
  updateStatus(data) {
    // Update status badge
    if (this.hasStatusBadgeTarget) {
      this.updateStatusBadge(data.status);
    }
    
    // Update last sync time
    if (this.hasLastSyncTarget && data.last_sync_at) {
      this.lastSyncTarget.textContent = data.last_sync_relative;
    }
    
    // Update row count
    if (this.hasRowCountTarget && data.row_count !== undefined) {
      this.rowCountTarget.textContent = data.formatted_row_count;
    }
    
    // Update sync button state
    if (this.hasSyncButtonTarget) {
      this.syncButtonTarget.disabled = data.status === 'syncing';
      if (data.status === 'syncing') {
        this.syncButtonTarget.innerHTML = '<i class="fas fa-sync-alt animate-spin mr-2"></i>Syncing...';
      } else {
        this.syncButtonTarget.innerHTML = '<i class="fas fa-sync-alt mr-2"></i>Sync Now';
      }
    }
  }
  
  updateStatusBadge(status) {
    const badgeConfigs = {
      'active': {
        classes: 'bg-green-100 text-green-800 border-green-200',
        icon: '<span class="w-1.5 h-1.5 bg-green-500 rounded-full mr-2 animate-pulse"></span>',
        text: 'Active'
      },
      'syncing': {
        classes: 'bg-blue-100 text-blue-800 border-blue-200',
        icon: '<i class="fas fa-sync-alt animate-spin mr-2"></i>',
        text: 'Syncing'
      },
      'error': {
        classes: 'bg-red-100 text-red-800 border-red-200',
        icon: '<i class="fas fa-exclamation-triangle mr-2"></i>',
        text: 'Error'
      },
      'inactive': {
        classes: 'bg-gray-100 text-gray-800 border-gray-200',
        icon: '<i class="fas fa-pause-circle mr-2"></i>',
        text: 'Inactive'
      }
    };
    
    const config = badgeConfigs[status] || badgeConfigs['inactive'];
    
    // Remove all status classes
    Object.values(badgeConfigs).forEach(cfg => {
      this.statusBadgeTarget.classList.remove(...cfg.classes.split(' '));
    });
    
    // Add new status classes
    this.statusBadgeTarget.classList.add(...config.classes.split(' '));
    this.statusBadgeTarget.innerHTML = config.icon + config.text;
  }
  
  setupWebSocketIfAvailable() {
    // Setup ActionCable subscription if available
    if (typeof App !== 'undefined' && App.cable) {
      this.channel = App.cable.subscriptions.create(
        { 
          channel: "DataSourceStatusChannel",
          data_source_id: this.sourceIdValue
        },
        {
          received: (data) => {
            this.updateStatus(data);
          }
        }
      );
    }
  }
  
  // Manual refresh action
  refresh() {
    this.fetchStatus();
    
    // Show refresh indicator
    const refreshBtn = event.currentTarget;
    const originalContent = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="fas fa-sync-alt animate-spin"></i>';
    refreshBtn.disabled = true;
    
    setTimeout(() => {
      refreshBtn.innerHTML = originalContent;
      refreshBtn.disabled = false;
    }, 1000);
  }
}