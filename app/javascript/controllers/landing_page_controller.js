import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["backToTop", "animatedElement", "form"]
  
  connect() {
    console.log("Landing page controller connected")
    
    // Initialize smooth scrolling for anchor links
    this.initSmoothScrolling()
    
    // Initialize back to top button
    this.initBackToTop()
    
    // Initialize intersection observer for animations
    this.initScrollAnimations()
    
    // Initialize form enhancements
    this.initFormEnhancements()
    
    // Initialize accessibility features
    this.initAccessibility()
  }
  
  initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', (e) => {
        e.preventDefault()
        const target = document.querySelector(anchor.getAttribute('href'))
        if (target) {
          const headerOffset = 80
          const elementPosition = target.getBoundingClientRect().top
          const offsetPosition = elementPosition + window.pageYOffset - headerOffset
          
          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
          })
        }
      })
    })
  }
  
  initBackToTop() {
    if (!this.hasBackToTopTarget) return
    
    window.addEventListener('scroll', () => {
      if (window.pageYOffset > 300) {
        this.backToTopTarget.classList.remove('hidden')
      } else {
        this.backToTopTarget.classList.add('hidden')
      }
    })
    
    this.backToTopTarget.addEventListener('click', (e) => {
      e.preventDefault()
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    })
  }
  
  initScrollAnimations() {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -100px 0px'
    }
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in')
          observer.unobserve(entry.target)
        }
      })
    }, observerOptions)
    
    // Observe all elements with animation classes
    document.querySelectorAll('.fade-in-up, .fade-in, .slide-in-left, .slide-in-right').forEach(el => {
      observer.observe(el)
    })
  }
  
  initFormEnhancements() {
    // Find all forms with email inputs
    document.querySelectorAll('form').forEach(form => {
      const emailInput = form.querySelector('input[type="email"]')
      if (emailInput) {
        // Add real-time validation
        emailInput.addEventListener('blur', () => {
          this.validateEmail(emailInput)
        })
        
        // Handle form submission
        form.addEventListener('submit', (e) => {
          e.preventDefault()
          if (this.validateEmail(emailInput)) {
            this.handleFormSubmission(form)
          }
        })
      }
    })
  }
  
  validateEmail(input) {
    const email = input.value
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    
    if (!email) {
      this.showError(input, 'Email is required')
      return false
    } else if (!emailRegex.test(email)) {
      this.showError(input, 'Please enter a valid email address')
      return false
    } else {
      this.clearError(input)
      return true
    }
  }
  
  showError(input, message) {
    const formGroup = input.closest('.form-group') || input.parentElement
    let errorElement = formGroup.querySelector('.error-message')
    
    if (!errorElement) {
      errorElement = document.createElement('p')
      errorElement.className = 'error-message text-red-500 text-sm mt-1'
      formGroup.appendChild(errorElement)
    }
    
    errorElement.textContent = message
    input.classList.add('border-red-500')
  }
  
  clearError(input) {
    const formGroup = input.closest('.form-group') || input.parentElement
    const errorElement = formGroup.querySelector('.error-message')
    
    if (errorElement) {
      errorElement.remove()
    }
    
    input.classList.remove('border-red-500')
  }
  
  handleFormSubmission(form) {
    const submitButton = form.querySelector('button[type="submit"]')
    const originalText = submitButton.textContent
    
    // Show loading state
    submitButton.disabled = true
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...'
    
    // Simulate form submission
    setTimeout(() => {
      // Show success message
      const successMessage = document.createElement('div')
      successMessage.className = 'success-message bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mt-4'
      successMessage.innerHTML = `
        <div class="flex items-center">
          <i class="fas fa-check-circle mr-2"></i>
          <p>Thank you! We'll be in touch soon.</p>
        </div>
      `
      
      form.appendChild(successMessage)
      
      // Reset form
      form.reset()
      submitButton.disabled = false
      submitButton.textContent = originalText
      
      // Remove success message after 5 seconds
      setTimeout(() => {
        successMessage.remove()
      }, 5000)
    }, 1500)
  }
  
  initAccessibility() {
    // Skip to main content link
    const skipLink = document.createElement('a')
    skipLink.href = '#main'
    skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-600 text-white px-4 py-2 rounded-md'
    skipLink.textContent = 'Skip to main content'
    document.body.insertBefore(skipLink, document.body.firstChild)
    
    // Ensure all images have alt text
    document.querySelectorAll('img:not([alt])').forEach(img => {
      img.alt = 'Decorative image'
    })
    
    // Add ARIA labels to icon-only buttons
    document.querySelectorAll('button').forEach(button => {
      if (!button.textContent.trim() && !button.getAttribute('aria-label')) {
        const icon = button.querySelector('i')
        if (icon) {
          const iconClass = icon.className
          if (iconClass.includes('times')) {
            button.setAttribute('aria-label', 'Close')
          } else if (iconClass.includes('bars')) {
            button.setAttribute('aria-label', 'Menu')
          }
        }
      }
    })
  }
}