import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["message", "progressBar"]
  static values = { 
    autoDismiss: { type: Boolean, default: true },
    timeout: { type: Number, default: 5000 },
    type: String
  }

  connect() {
    // Set up auto-dismiss if enabled
    if (this.autoDismissValue && this.timeoutValue > 0) {
      this.startAutoDismiss();
    }

    // Add entrance animation
    this.element.classList.add('flash-message-enter');

    // Trigger entrance animation after a brief delay
    requestAnimationFrame(() => {
      this.element.classList.add('flash-message-enter-active');
    });

    // Set up keyboard accessibility
    this.element.setAttribute('tabindex', '0');
    this.element.addEventListener('keydown', this.handleKeydown.bind(this));

    // Ensure close button is clickable
    const closeButton = this.element.querySelector('[data-action*="manualDismiss"]');
    if (closeButton) {
      closeButton.addEventListener('click', (event) => {
        event.preventDefault();
        event.stopPropagation();
        this.manualDismiss(event);
      });
    }
  }

  disconnect() {
    this.clearTimers();
    this.element.removeEventListener('keydown', this.handleKeydown.bind(this));
  }

  startAutoDismiss() {
    // Clear any existing timers
    this.clearTimers();

    // Start progress bar animation if present
    if (this.hasProgressBarTarget) {
      this.progressBarTarget.style.animationDuration = `${this.timeoutValue}ms`;
      this.progressBarTarget.classList.add('flash-progress-active');
    }

    // Set timeout for auto-dismiss
    this.dismissTimer = setTimeout(() => {
      this.dismiss();
    }, this.timeoutValue);
  }

  pauseAutoDismiss() {
    if (this.dismissTimer) {
      clearTimeout(this.dismissTimer);
      this.dismissTimer = null;
    }

    if (this.hasProgressBarTarget) {
      this.progressBarTarget.style.animationPlayState = 'paused';
    }
  }

  resumeAutoDismiss() {
    if (this.autoDismissValue && !this.dismissTimer) {
      const remainingTime = this.getRemainingTime();
      
      if (remainingTime > 0) {
        this.dismissTimer = setTimeout(() => {
          this.dismiss();
        }, remainingTime);

        if (this.hasProgressBarTarget) {
          this.progressBarTarget.style.animationPlayState = 'running';
        }
      }
    }
  }

  getRemainingTime() {
    if (!this.hasProgressBarTarget) return this.timeoutValue;
    
    const progressBar = this.progressBarTarget;
    const computedStyle = window.getComputedStyle(progressBar);
    const animationDuration = parseFloat(computedStyle.animationDuration) * 1000;
    const animationDelay = parseFloat(computedStyle.animationDelay) * 1000;
    
    // Calculate remaining time based on animation progress
    // This is a simplified calculation - in a real implementation,
    // you might want to track the actual elapsed time
    return Math.max(0, animationDuration - animationDelay);
  }

  dismiss() {
    // Add exit animation
    this.element.classList.add('flash-message-exit');
    this.element.classList.add('flash-message-exit-active');

    // Remove element after animation completes
    setTimeout(() => {
      if (this.element.parentNode) {
        this.element.remove();
      }
    }, 300); // Match CSS transition duration

    // Clear timers
    this.clearTimers();

    // Dispatch custom event for analytics or other listeners
    this.dispatch('dismissed', { 
      detail: { 
        type: this.typeValue,
        method: 'auto'
      } 
    });
  }

  manualDismiss(event) {
    // Prevent event bubbling
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Add exit animation
    this.element.classList.add('flash-message-exit');
    this.element.classList.add('flash-message-exit-active');

    // Remove element after animation completes
    setTimeout(() => {
      if (this.element.parentNode) {
        this.element.remove();
      }
    }, 300);

    // Clear timers
    this.clearTimers();

    // Dispatch custom event
    this.dispatch('dismissed', {
      detail: {
        type: this.typeValue,
        method: 'manual'
      }
    });
  }

  handleKeydown(event) {
    // Allow dismissing with Escape key
    if (event.key === 'Escape') {
      event.preventDefault();
      this.manualDismiss();
    }
  }

  clearTimers() {
    if (this.dismissTimer) {
      clearTimeout(this.dismissTimer);
      this.dismissTimer = null;
    }
  }

  // Event handlers for mouse interactions
  mouseEnter() {
    if (this.autoDismissValue) {
      this.pauseAutoDismiss();
    }
  }

  mouseLeave() {
    if (this.autoDismissValue) {
      this.resumeAutoDismiss();
    }
  }

  // Focus handlers for accessibility
  focus() {
    if (this.autoDismissValue) {
      this.pauseAutoDismiss();
    }
  }

  blur() {
    if (this.autoDismissValue) {
      this.resumeAutoDismiss();
    }
  }
}
