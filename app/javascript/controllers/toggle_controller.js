import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["switch", "indicator"]
  static values = { 
    enabled: { type: Boolean, default: false },
    onText: { type: String, default: "On" },
    offText: { type: String, default: "Off" }
  }
  
  connect() {
    this.updateState();
  }
  
  toggle() {
    this.enabledValue = !this.enabledValue;
    this.updateState();
    this.dispatch("toggled", { detail: { enabled: this.enabledValue } });
  }
  
  updateState() {
    const indicator = this.indicatorTarget;
    const switchElement = this.switchTarget;
    
    if (this.enabledValue) {
      // Enabled state
      indicator.classList.remove("translate-x-1");
      indicator.classList.add("translate-x-6");
      switchElement.classList.remove("bg-gray-200");
      switchElement.classList.add("bg-primary-600");
      switchElement.setAttribute("aria-checked", "true");
    } else {
      // Disabled state
      indicator.classList.remove("translate-x-6");
      indicator.classList.add("translate-x-1");
      switchElement.classList.remove("bg-primary-600");
      switchElement.classList.add("bg-gray-200");
      switchElement.setAttribute("aria-checked", "false");
    }
    
    // Update aria-label
    const label = this.enabledValue ? this.onTextValue : this.offTextValue;
    switchElement.setAttribute("aria-label", label);
  }
  
  // Method to programmatically set state
  enable() {
    this.enabledValue = true;
    this.updateState();
  }
  
  disable() {
    this.enabledValue = false;
    this.updateState();
  }
}
