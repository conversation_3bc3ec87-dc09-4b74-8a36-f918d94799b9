import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["fileInput", "filePreview", "fileName", "fileSize", "tabButton", "tabContent"]

  connect() {
    console.log("Enhanced CSV form controller connected")
    
    // Set up drag and drop
    this.setupDragAndDrop()
    
    // Set up file input listener
    if (this.hasFileInputTarget) {
      this.fileInputTarget.addEventListener('change', this.handleFileChange.bind(this))
    }
  }

  // Tab switching with enhanced animations
  switchTab(event) {
    const tab = event.currentTarget.dataset.tab
    console.log("Switching to tab:", tab)
    
    // Update tab buttons with smooth transitions
    this.tabButtonTargets.forEach(btn => {
      const isActive = btn.dataset.tab === tab
      
      // Remove all state classes
      btn.classList.remove(
        'bg-white', 'text-primary-600', 'shadow-sm',
        'text-slate-600', 'hover:text-slate-800', 'hover:bg-slate-50'
      )
      
      if (isActive) {
        btn.classList.add('bg-white', 'text-primary-600', 'shadow-sm')
        btn.setAttribute('aria-selected', 'true')
      } else {
        btn.classList.add('text-slate-600', 'hover:text-slate-800', 'hover:bg-slate-50')
        btn.setAttribute('aria-selected', 'false')
      }
    })
    
    // Show/hide content with fade animation
    this.tabContentTargets.forEach(content => {
      const isTargetContent = content.id === `csv-${tab}-panel`
      
      if (isTargetContent) {
        content.classList.remove('hidden')
        content.style.opacity = '0'
        content.style.transform = 'translateY(10px)'
        
        // Smooth fade in
        requestAnimationFrame(() => {
          content.style.transition = 'opacity 0.3s ease, transform 0.3s ease'
          content.style.opacity = '1'
          content.style.transform = 'translateY(0)'
        })
      } else {
        content.style.transition = 'opacity 0.2s ease'
        content.style.opacity = '0'
        
        setTimeout(() => {
          content.classList.add('hidden')
        }, 200)
      }
    })
  }

  // Enhanced file upload handling
  handleFileChange(event) {
    const file = event.target.files[0]
    if (file) {
      console.log("File selected:", file.name, file.size)
      
      // Validate file
      if (!this.validateFile(file)) {
        return
      }
      
      // Update preview
      this.updateFilePreview(file)
      
      // Show preview with animation
      if (this.hasFilePreviewTarget) {
        this.filePreviewTarget.classList.remove('hidden')
        this.filePreviewTarget.style.opacity = '0'
        this.filePreviewTarget.style.transform = 'translateY(10px)'
        
        requestAnimationFrame(() => {
          this.filePreviewTarget.style.transition = 'opacity 0.3s ease, transform 0.3s ease'
          this.filePreviewTarget.style.opacity = '1'
          this.filePreviewTarget.style.transform = 'translateY(0)'
        })
      }
      
      // Trigger file analysis
      this.analyzeFile(file)
    }
  }

  validateFile(file) {
    const maxSize = 500 * 1024 * 1024 // 500MB
    const allowedTypes = ['text/csv', 'text/plain', 'application/csv']
    const allowedExtensions = ['.csv', '.txt', '.gz']
    
    // Check file size
    if (file.size > maxSize) {
      this.showFileError(`File size (${this.formatFileSize(file.size)}) exceeds the 500MB limit.`)
      return false
    }
    
    // Check file type and extension
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
    const isValidType = allowedTypes.includes(file.type) || allowedExtensions.includes(fileExtension)
    
    if (!isValidType) {
      this.showFileError(`File type not supported. Please upload a CSV, TXT, or GZ file.`)
      return false
    }
    
    return true
  }

  updateFilePreview(file) {
    if (this.hasFileNameTarget) {
      this.fileNameTarget.textContent = file.name
    }
    if (this.hasFileSizeTarget) {
      this.fileSizeTarget.textContent = this.formatFileSize(file.size)
    }
  }

  analyzeFile(file) {
    // Only analyze CSV files
    if (!file.name.toLowerCase().endsWith('.csv')) {
      return
    }
    
    console.log("Analyzing CSV file...")
    
    // Read first few lines to detect delimiter and headers
    const reader = new FileReader()
    reader.onload = (e) => {
      const text = e.target.result
      const lines = text.split('\n').slice(0, 5) // First 5 lines
      
      // Detect delimiter
      const delimiter = this.detectDelimiter(lines[0])
      if (delimiter) {
        this.updateDelimiterSelection(delimiter)
      }
      
      // Detect headers
      const hasHeaders = this.detectHeaders(lines)
      this.updateHeadersSelection(hasHeaders)
      
      // Show analysis results
      this.showAnalysisResults(lines, delimiter, hasHeaders)
    }
    
    // Read first 1KB of file
    const blob = file.slice(0, 1024)
    reader.readAsText(blob)
  }

  detectDelimiter(firstLine) {
    const delimiters = [',', ';', '\t', '|']
    let bestDelimiter = ','
    let maxCount = 0
    
    delimiters.forEach(delimiter => {
      const count = (firstLine.match(new RegExp(delimiter === '\t' ? '\\t' : `\\${delimiter}`, 'g')) || []).length
      if (count > maxCount) {
        maxCount = count
        bestDelimiter = delimiter
      }
    })
    
    return maxCount > 0 ? bestDelimiter : null
  }

  detectHeaders(lines) {
    if (lines.length < 2) return true // Default to true if we can't determine
    
    const firstLine = lines[0]
    const secondLine = lines[1]
    
    // Simple heuristic: if first line has more text and second line has more numbers
    const firstLineNumbers = (firstLine.match(/\d+/g) || []).length
    const secondLineNumbers = (secondLine.match(/\d+/g) || []).length
    
    return firstLineNumbers < secondLineNumbers
  }

  updateDelimiterSelection(delimiter) {
    const delimiterSelect = this.element.querySelector('select[name*="delimiter"]')
    if (delimiterSelect) {
      delimiterSelect.value = delimiter
      
      // Add visual feedback
      delimiterSelect.style.borderColor = '#10b981'
      setTimeout(() => {
        delimiterSelect.style.borderColor = ''
      }, 2000)
    }
  }

  updateHeadersSelection(hasHeaders) {
    const headersCheckbox = this.element.querySelector('input[name*="has_headers"]')
    if (headersCheckbox) {
      headersCheckbox.checked = hasHeaders
      
      // Add visual feedback
      const label = headersCheckbox.closest('label')
      if (label) {
        label.style.backgroundColor = '#f0fdf4'
        setTimeout(() => {
          label.style.backgroundColor = ''
        }, 2000)
      }
    }
  }

  showAnalysisResults(lines, delimiter, hasHeaders) {
    // Create or update analysis results display
    let analysisDiv = this.element.querySelector('.csv-analysis-results')
    
    if (!analysisDiv) {
      analysisDiv = document.createElement('div')
      analysisDiv.className = 'csv-analysis-results mt-4 p-4 bg-blue-50 rounded-xl border border-blue-200'
      
      if (this.hasFilePreviewTarget) {
        this.filePreviewTarget.appendChild(analysisDiv)
      }
    }
    
    analysisDiv.innerHTML = `
      <div class="flex items-start">
        <i class="fas fa-magic text-blue-600 mt-1 mr-3"></i>
        <div class="flex-1">
          <h4 class="font-semibold text-blue-900 mb-2">Auto-detected Settings</h4>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
            <div class="flex items-center text-blue-800">
              <i class="fas fa-columns mr-2"></i>
              Delimiter: <strong class="ml-1">${this.getDelimiterName(delimiter)}</strong>
            </div>
            <div class="flex items-center text-blue-800">
              <i class="fas fa-header mr-2"></i>
              Headers: <strong class="ml-1">${hasHeaders ? 'Yes' : 'No'}</strong>
            </div>
          </div>
          <p class="text-xs text-blue-700 mt-2">
            Settings have been automatically applied. You can adjust them if needed.
          </p>
        </div>
      </div>
    `
  }

  getDelimiterName(delimiter) {
    const names = {
      ',': 'Comma (,)',
      ';': 'Semicolon (;)',
      '\t': 'Tab',
      '|': 'Pipe (|)'
    }
    return names[delimiter] || 'Unknown'
  }

  clearFile() {
    console.log("Clearing file")
    
    if (this.hasFileInputTarget) {
      this.fileInputTarget.value = ''
    }
    
    if (this.hasFilePreviewTarget) {
      // Animate out
      this.filePreviewTarget.style.transition = 'opacity 0.2s ease, transform 0.2s ease'
      this.filePreviewTarget.style.opacity = '0'
      this.filePreviewTarget.style.transform = 'translateY(-10px)'
      
      setTimeout(() => {
        this.filePreviewTarget.classList.add('hidden')
      }, 200)
    }
    
    // Clear analysis results
    const analysisDiv = this.element.querySelector('.csv-analysis-results')
    if (analysisDiv) {
      analysisDiv.remove()
    }
  }

  showFileError(message) {
    // Clear file input
    if (this.hasFileInputTarget) {
      this.fileInputTarget.value = ''
    }
    
    // Show error message
    const errorDiv = document.createElement('div')
    errorDiv.className = 'mt-4 p-4 bg-red-50 rounded-xl border border-red-200'
    errorDiv.innerHTML = `
      <div class="flex items-start">
        <i class="fas fa-exclamation-triangle text-red-600 mt-1 mr-3"></i>
        <div>
          <h4 class="font-semibold text-red-900 mb-1">File Error</h4>
          <p class="text-sm text-red-800">${message}</p>
        </div>
      </div>
    `
    
    // Insert after file input
    const uploadArea = this.element.querySelector('label[for="csv_file"]')
    if (uploadArea) {
      uploadArea.parentNode.insertBefore(errorDiv, uploadArea.nextSibling)
      
      // Remove error after 5 seconds
      setTimeout(() => {
        errorDiv.remove()
      }, 5000)
    }
  }

  setupDragAndDrop() {
    const uploadArea = this.element.querySelector('label[for="csv_file"]')
    if (!uploadArea) return
    
    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      uploadArea.addEventListener(eventName, this.preventDefaults, false)
      document.body.addEventListener(eventName, this.preventDefaults, false)
    })
    
    // Highlight drop area when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
      uploadArea.addEventListener(eventName, () => this.highlight(uploadArea), false)
    })
    
    ['dragleave', 'drop'].forEach(eventName => {
      uploadArea.addEventListener(eventName, () => this.unhighlight(uploadArea), false)
    })
    
    // Handle dropped files
    uploadArea.addEventListener('drop', this.handleDrop.bind(this), false)
  }

  preventDefaults(e) {
    e.preventDefault()
    e.stopPropagation()
  }

  highlight(element) {
    element.classList.add('border-primary-500', 'bg-primary-100')
    element.classList.remove('border-slate-300')
  }

  unhighlight(element) {
    element.classList.remove('border-primary-500', 'bg-primary-100')
    element.classList.add('border-slate-300')
  }

  handleDrop(e) {
    const dt = e.dataTransfer
    const files = dt.files
    
    if (files.length > 0) {
      const file = files[0]
      
      // Update file input
      if (this.hasFileInputTarget) {
        // Create a new FileList-like object
        const dataTransfer = new DataTransfer()
        dataTransfer.items.add(file)
        this.fileInputTarget.files = dataTransfer.files
        
        // Trigger change event
        this.handleFileChange({ target: { files: [file] } })
      }
    }
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}
