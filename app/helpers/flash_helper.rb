module FlashHelper
  # Helper methods for setting flash messages with consistent types
  
  def flash_success(message)
    flash[:success] = message
  end
  
  def flash_error(message)
    flash[:error] = message
  end
  
  def flash_warning(message)
    flash[:warning] = message
  end
  
  def flash_info(message)
    flash[:notice] = message
  end
  
  def flash_notice(message)
    flash[:notice] = message
  end
  
  # Helper for adding action links to flash messages
  def flash_with_action(type, message, action_text, action_url, action_options = {})
    action_link = link_to(action_text, action_url, {
      class: "font-medium underline hover:no-underline ml-2",
      **action_options
    })
    
    flash[type] = "#{message} #{action_link}".html_safe
  end
  
  # Convenience methods for flash messages with actions
  def flash_success_with_action(message, action_text, action_url, action_options = {})
    flash_with_action(:success, message, action_text, action_url, action_options)
  end
  
  def flash_error_with_action(message, action_text, action_url, action_options = {})
    flash_with_action(:error, message, action_text, action_url, action_options)
  end
  
  def flash_warning_with_action(message, action_text, action_url, action_options = {})
    flash_with_action(:warning, message, action_text, action_url, action_options)
  end
  
  def flash_info_with_action(message, action_text, action_url, action_options = {})
    flash_with_action(:notice, message, action_text, action_url, action_options)
  end
  
  # Helper for checking if flash messages exist
  def flash_messages_present?
    flash.any? { |type, message| message.present? }
  end
  
  # Helper for getting flash message count
  def flash_message_count
    flash.count { |type, message| message.present? }
  end
  
  # Helper for clearing all flash messages
  def clear_flash_messages
    flash.clear
  end
  
  # Helper for flash message types with icons
  def flash_icon_for_type(type)
    case type.to_s
    when 'success'
      'fas fa-check-circle'
    when 'error', 'alert'
      'fas fa-exclamation-circle'
    when 'warning'
      'fas fa-exclamation-triangle'
    when 'notice'
      'fas fa-info-circle'
    else
      'fas fa-info-circle'
    end
  end
  
  # Helper for flash message colors
  def flash_color_classes_for_type(type)
    case type.to_s
    when 'success'
      {
        bg: 'bg-green-50 border-green-200',
        text: 'text-green-800',
        icon: 'text-green-400',
        button: 'text-green-400 hover:text-green-600'
      }
    when 'error', 'alert'
      {
        bg: 'bg-red-50 border-red-200',
        text: 'text-red-800',
        icon: 'text-red-400',
        button: 'text-red-400 hover:text-red-600'
      }
    when 'warning'
      {
        bg: 'bg-yellow-50 border-yellow-200',
        text: 'text-yellow-800',
        icon: 'text-yellow-400',
        button: 'text-yellow-400 hover:text-yellow-600'
      }
    when 'notice'
      {
        bg: 'bg-blue-50 border-blue-200',
        text: 'text-blue-800',
        icon: 'text-blue-400',
        button: 'text-blue-400 hover:text-blue-600'
      }
    else
      {
        bg: 'bg-gray-50 border-gray-200',
        text: 'text-gray-800',
        icon: 'text-gray-400',
        button: 'text-gray-400 hover:text-gray-600'
      }
    end
  end
end
