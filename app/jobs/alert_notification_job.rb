class AlertNotificationJob < ApplicationJob
  queue_as :critical
  
  def perform(alert)
    return unless alert.active?
    
    monitoring_rule = alert.monitoring_rule
    return unless monitoring_rule.notification_channels.present?
    
    # Send notifications based on configured channels
    monitoring_rule.notification_channels.each do |channel_type, config|
      case channel_type
      when 'email'
        send_email_notification(alert, config)
      when 'webhook'
        send_webhook_notification(alert, config)
      when 'slack'
        send_slack_notification(alert, config)
      else
        Rails.logger.warn "Unknown notification channel: #{channel_type}"
      end
    end
  rescue => e
    Rails.logger.error "Failed to send alert notification: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
  end
  
  private
  
  def send_email_notification(alert, config)
    return unless config['enabled'] && config['recipients'].present?
    
    recipients = parse_email_recipients(alert, config['recipients'])
    return if recipients.empty?
    
    AlertMailer.alert_triggered(alert, recipients).deliver_later
  end
  
  def send_webhook_notification(alert, config)
    return unless config['enabled'] && config['url'].present?
    
    payload = build_webhook_payload(alert)
    headers = config['headers'] || {}
    
    response = Faraday.post(config['url']) do |req|
      req.headers['Content-Type'] = 'application/json'
      headers.each { |k, v| req.headers[k] = v }
      req.body = payload.to_json
    end
    
    unless response.success?
      Rails.logger.error "Webhook notification failed: #{response.status} - #{response.body}"
    end
  rescue => e
    Rails.logger.error "Failed to send webhook notification: #{e.message}"
  end
  
  def send_slack_notification(alert, config)
    return unless config['enabled'] && config['webhook_url'].present?
    
    payload = build_slack_payload(alert)
    
    response = Faraday.post(config['webhook_url']) do |req|
      req.headers['Content-Type'] = 'application/json'
      req.body = payload.to_json
    end
    
    unless response.success?
      Rails.logger.error "Slack notification failed: #{response.status} - #{response.body}"
    end
  rescue => e
    Rails.logger.error "Failed to send Slack notification: #{e.message}"
  end
  
  def parse_email_recipients(alert, recipients_config)
    recipients = []
    
    # Add specific email addresses
    if recipients_config['emails'].present?
      recipients += Array(recipients_config['emails'])
    end
    
    # Add users by role
    if recipients_config['roles'].present?
      roles = Array(recipients_config['roles'])
      recipients += alert.organization.users.where(role: roles).pluck(:email)
    end
    
    # Add specific users
    if recipients_config['user_ids'].present?
      user_ids = Array(recipients_config['user_ids'])
      recipients += alert.organization.users.where(id: user_ids).pluck(:email)
    end
    
    recipients.uniq.select { |email| email.present? }
  end
  
  def build_webhook_payload(alert)
    {
      id: alert.id,
      organization_id: alert.organization_id,
      severity: alert.severity,
      alert_type: alert.alert_type,
      title: alert.title,
      message: alert.message,
      details: alert.details,
      triggered_at: alert.triggered_at,
      data_source: alert.data_source&.slice(:id, :name, :source_type),
      monitoring_rule: alert.monitoring_rule.slice(:id, :name, :rule_type),
      dashboard_url: Rails.application.routes.url_helpers.dashboard_alert_url(alert, host: Rails.application.config.action_mailer.default_url_options[:host])
    }
  end
  
  def build_slack_payload(alert)
    color = case alert.severity
            when 'critical' then 'danger'
            when 'warning' then 'warning'
            else 'good'
            end
    
    {
      text: "Alert: #{alert.title}",
      attachments: [
        {
          color: color,
          title: alert.title,
          text: alert.message,
          fields: [
            {
              title: "Severity",
              value: alert.severity.capitalize,
              short: true
            },
            {
              title: "Type",
              value: alert.alert_type.humanize,
              short: true
            },
            {
              title: "Data Source",
              value: alert.data_source&.name || 'N/A',
              short: true
            },
            {
              title: "Triggered At",
              value: alert.triggered_at.strftime('%Y-%m-%d %H:%M:%S'),
              short: true
            }
          ],
          actions: [
            {
              type: "button",
              text: "View Alert",
              url: Rails.application.routes.url_helpers.dashboard_alert_url(alert, host: Rails.application.config.action_mailer.default_url_options[:host])
            }
          ]
        }
      ]
    }
  end
end