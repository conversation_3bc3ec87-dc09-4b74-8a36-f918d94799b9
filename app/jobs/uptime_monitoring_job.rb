class UptimeMonitoringJob < ApplicationJob
  queue_as :monitoring
  
  def perform
    # Run uptime checks for all organizations
    Organization.find_each do |organization|
      check_organization_uptime(organization)
    end
  end
  
  private
  
  def check_organization_uptime(organization)
    # Get all uptime monitoring rules
    uptime_rules = organization.monitoring_rules.enabled.by_type('uptime')
    
    uptime_rules.find_each do |rule|
      check_uptime_rule(rule)
    end
  rescue => e
    Rails.logger.error "Failed to check uptime for organization #{organization.id}: #{e.message}"
  end
  
  def check_uptime_rule(rule)
    context = {
      rule_type: 'uptime',
      checked_at: Time.current
    }
    
    # Trigger the rule - it will check conditions internally
    rule.trigger!(context)
  end
end