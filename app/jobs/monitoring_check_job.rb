class MonitoringCheckJob < ApplicationJob
  queue_as :monitoring
  
  def perform(sync_log)
    organization = sync_log.organization
    data_source = sync_log.data_source
    
    # Find applicable monitoring rules
    rules = organization.monitoring_rules.enabled
    rules = rules.where(data_source: [data_source, nil]) # Rules for this source or global rules
    
    rules.find_each do |rule|
      check_rule(rule, sync_log)
    end
  end
  
  private
  
  def check_rule(rule, sync_log)
    context = build_context(sync_log)
    
    case rule.rule_type
    when 'sync_failure'
      check_sync_failure_rule(rule, sync_log, context)
    when 'performance'
      check_performance_rule(rule, sync_log, context)
    when 'data_quality'
      check_data_quality_rule(rule, sync_log, context)
    when 'uptime'
      check_uptime_rule(rule, sync_log, context)
    end
  rescue => e
    Rails.logger.error "Failed to check monitoring rule #{rule.id}: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
  end
  
  def build_context(sync_log)
    {
      sync_log_id: sync_log.id,
      sync_status: sync_log.status,
      error_message: sync_log.error_message,
      duration: sync_log.duration_seconds,
      records_processed: sync_log.records_processed,
      records_imported: sync_log.records_imported,
      records_failed: sync_log.records_failed,
      error_rate: sync_log.records_processed > 0 ? 
        ((sync_log.records_failed.to_f / sync_log.records_processed) * 100).round(2) : 0
    }
  end
  
  def check_sync_failure_rule(rule, sync_log, context)
    return unless sync_log.failed?
    
    rule.trigger!(context)
  end
  
  def check_performance_rule(rule, sync_log, context)
    return unless sync_log.completed?
    
    # Check duration
    if sync_log.duration_seconds
      context[:metric_type] = 'sync_duration'
      context[:value] = sync_log.duration_seconds
      context[:unit] = 'seconds'
      rule.trigger!(context)
    end
    
    # Check throughput
    if sync_log.duration_seconds && sync_log.duration_seconds > 0
      throughput = sync_log.records_processed.to_f / sync_log.duration_seconds
      context[:metric_type] = 'throughput'
      context[:value] = throughput
      context[:unit] = 'rows_per_second'
      rule.trigger!(context)
    end
  end
  
  def check_data_quality_rule(rule, sync_log, context)
    return unless sync_log.completed? || sync_log.failed?
    
    rule.trigger!(context)
  end
  
  def check_uptime_rule(rule, sync_log, context)
    # Uptime rules are checked separately by a scheduled job
    # This is just triggered when a sync completes to potentially clear alerts
    return unless sync_log.completed?
    
    # Check if there are active uptime alerts that should be resolved
    active_uptime_alerts = rule.alerts.active.where(alert_type: 'uptime')
    active_uptime_alerts.each do |alert|
      alert.resolve!(nil, notes: "Data source successfully synced")
    end
  end
end