class DataSourceSyncJob < ApplicationJob
  queue_as :default

  def perform(data_source)
    Rails.logger.info "Starting sync for DataSource #{data_source.id} - #{data_source.name}"
    
    # Perform the sync
    result = data_source.sync_now!
    
    if result[:success]
      Rails.logger.info "Sync completed successfully for DataSource #{data_source.id}"
      # Schedule next sync if applicable
      data_source.schedule_next_sync
    else
      Rails.logger.error "Sync failed for DataSource #{data_source.id}: #{result[:error]}"
    end
  end
end