<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center">
      <a href="<%= dashboard_pipeline_path(@pipeline) %>" class="mr-4 text-gray-400 hover:text-gray-600">
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
      </a>
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Pipeline Logs</h1>
        <p class="mt-1 text-sm text-gray-600"><%= @pipeline.name %></p>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="bg-white shadow sm:rounded-lg mb-6">
    <div class="px-4 py-5 sm:p-6">
      <%= form_with url: logs_dashboard_pipeline_path(@pipeline), method: :get, local: true, class: "grid grid-cols-1 gap-4 sm:grid-cols-3" do |f| %>
        <div>
          <label class="block text-sm font-medium text-gray-700">Status</label>
          <%= select_tag :status, 
              options_for_select([['All', ''], ['Completed', 'completed'], ['Failed', 'failed'], ['Running', 'running']], params[:status]),
              class: "mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md" %>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700">Date Range</label>
          <%= select_tag :date_range, 
              options_for_select([['Last 24 hours', '1'], ['Last 7 days', '7'], ['Last 30 days', '30'], ['All time', '']], params[:date_range] || '7'),
              class: "mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md" %>
        </div>
        
        <div class="flex items-end">
          <%= submit_tag "Filter", class: "w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Logs Table -->
  <div class="bg-white shadow overflow-hidden sm:rounded-md">
    <% if @runs.any? %>
      <ul class="divide-y divide-gray-200">
        <% @runs.each do |run| %>
          <li>
            <div class="px-4 py-4 sm:px-6">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <% case run.status %>
                    <% when 'completed' %>
                      <span class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-green-100">
                        <svg class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                      </span>
                    <% when 'failed' %>
                      <span class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-red-100">
                        <svg class="h-5 w-5 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </span>
                    <% when 'running' %>
                      <span class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-blue-100">
                        <svg class="h-5 w-5 text-blue-600 animate-spin" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      </span>
                    <% end %>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">
                      Run #<%= run.id %>
                    </div>
                    <div class="text-sm text-gray-500">
                      Started: <%= run.started_at.strftime("%B %d, %Y at %I:%M %p") %>
                    </div>
                  </div>
                </div>
                <div class="ml-2 flex-shrink-0 flex">
                  <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                     <%= run.status == 'completed' ? 'bg-green-100 text-green-800' : 
                         run.status == 'failed' ? 'bg-red-100 text-red-800' : 
                         'bg-blue-100 text-blue-800' %>">
                    <%= run.status.capitalize %>
                  </p>
                </div>
              </div>
              
              <div class="mt-2 sm:flex sm:justify-between">
                <div class="sm:flex">
                  <p class="flex items-center text-sm text-gray-500">
                    <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Duration: <%= distance_of_time_in_words(run.duration || 0) %>
                  </p>
                  <% if run.records_processed %>
                    <p class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                      <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      Records: <%= number_with_delimiter(run.records_processed) %>
                    </p>
                  <% end %>
                </div>
              </div>
              
              <% if run.error_message.present? %>
                <div class="mt-2">
                  <p class="text-sm text-red-600"><%= truncate(run.error_message, length: 200) %></p>
                </div>
              <% end %>
              
              <% if run.steps_completed.present? && run.total_steps.present? %>
                <div class="mt-3">
                  <div class="flex items-center">
                    <div class="flex-1">
                      <div class="bg-gray-200 rounded-full h-2">
                        <div class="bg-indigo-600 h-2 rounded-full" style="width: <%= (run.steps_completed.to_f / run.total_steps * 100).round %>%"></div>
                      </div>
                    </div>
                    <span class="ml-3 text-sm text-gray-500">
                      <%= run.steps_completed %> / <%= run.total_steps %> steps
                    </span>
                  </div>
                </div>
              <% end %>
            </div>
          </li>
        <% end %>
      </ul>
      
      <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <%= paginate @runs %>
      </div>
    <% else %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No logs found</h3>
        <p class="mt-1 text-sm text-gray-500">This pipeline hasn't been run yet.</p>
      </div>
    <% end %>
  </div>
</div>