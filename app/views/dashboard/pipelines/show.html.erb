<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <a href="<%= dashboard_pipelines_path %>" class="mr-4 text-gray-400 hover:text-gray-600">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
        </a>
        <div>
          <h1 class="text-2xl font-bold text-gray-900"><%= @pipeline.name %></h1>
          <p class="mt-1 text-sm text-gray-600">
            Data Source: <%= link_to @pipeline.data_source.name, dashboard_data_source_path(@pipeline.data_source), class: "text-indigo-600 hover:text-indigo-500" %>
          </p>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <%= pipeline_status_class(@pipeline.status) %>">
          <%= @pipeline.status.capitalize %>
        </span>
        <%= link_to edit_dashboard_pipeline_path(@pipeline), class: "text-gray-400 hover:text-gray-600" do %>
          <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
          </svg>
        <% end %>
      </div>
    </div>
  </div>

  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content -->
    <div class="lg:col-span-2 space-y-6">
      <!-- Pipeline Details -->
      <div class="bg-white shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Pipeline Details</h3>
          
          <% if @pipeline.description.present? %>
            <div class="mb-4">
              <p class="text-sm text-gray-600"><%= @pipeline.description %></p>
            </div>
          <% end %>

          <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-gray-500">Status</dt>
              <dd class="mt-1 text-sm text-gray-900">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= pipeline_status_class(@pipeline.status) %>">
                  <%= @pipeline.status.capitalize %>
                </span>
              </dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500">Schedule</dt>
              <dd class="mt-1 text-sm text-gray-900">
                <%= @pipeline.schedule.present? ? @pipeline.schedule.humanize : 'Manual' %>
              </dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500">Last Run</dt>
              <dd class="mt-1 text-sm text-gray-900">
                <%= @pipeline.last_run_at ? time_ago_in_words(@pipeline.last_run_at) + ' ago' : 'Never' %>
              </dd>
            </div>
            
            <% if @next_run_time %>
              <div>
                <dt class="text-sm font-medium text-gray-500">Next Run</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  <%= @next_run_time.strftime("%B %d, %Y at %I:%M %p") %>
                </dd>
              </div>
            <% end %>
          </dl>
        </div>
      </div>

      <!-- Configuration -->
      <div class="bg-white shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Configuration</h3>
          
          <div class="bg-gray-50 rounded-lg p-4">
            <pre class="text-sm text-gray-700 whitespace-pre-wrap"><%= JSON.pretty_generate(@pipeline.configuration_hash) %></pre>
          </div>
        </div>
      </div>

      <!-- Error Message -->
      <% if @pipeline.error_message.present? %>
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">Last Error</h3>
              <div class="mt-2 text-sm text-red-700">
                <%= @pipeline.error_message %>
              </div>
            </div>
          </div>
        </div>
      <% end %>

      <!-- Recent Runs -->
      <div class="bg-white shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Runs</h3>
          
          <% if @recent_runs.any? %>
            <div class="flow-root">
              <ul class="-my-5 divide-y divide-gray-200">
                <% @recent_runs.each do |run| %>
                  <li class="py-4">
                    <div class="flex items-center space-x-4">
                      <div class="flex-shrink-0">
                        <% if run.status == 'completed' %>
                          <span class="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                            <svg class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                          </span>
                        <% else %>
                          <span class="h-8 w-8 rounded-full bg-red-100 flex items-center justify-center">
                            <svg class="h-5 w-5 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </span>
                        <% end %>
                      </div>
                      <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">
                          <%= run.started_at.strftime("%B %d, %Y at %I:%M %p") %>
                        </p>
                        <p class="text-sm text-gray-500 truncate">
                          Duration: <%= distance_of_time_in_words(run.duration) %>
                        </p>
                      </div>
                    </div>
                  </li>
                <% end %>
              </ul>
            </div>
          <% else %>
            <p class="text-sm text-gray-500">No runs yet</p>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Sidebar -->
    <div class="lg:col-span-1 space-y-6">
      <!-- Actions -->
      <div class="bg-white shadow sm:rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Actions</h3>
          
          <div class="space-y-3">
            <% if @pipeline.can_run? %>
              <%= button_to "Run Now", run_dashboard_pipeline_path(@pipeline), method: :post, class: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
            <% end %>
            
            <% if @pipeline.status == 'active' || @pipeline.status == 'scheduled' %>
              <%= button_to "Pause", pause_dashboard_pipeline_path(@pipeline), method: :post, class: "w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
            <% elsif @pipeline.status == 'paused' %>
              <%= button_to "Resume", schedule_dashboard_pipeline_path(@pipeline), method: :post, params: { pipeline: { schedule: @pipeline.schedule } }, class: "w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
            <% end %>
            
            <%= link_to "Edit Pipeline", edit_dashboard_pipeline_path(@pipeline), class: "w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
            
            <%= button_to "Delete Pipeline", dashboard_pipeline_path(@pipeline), method: :delete, data: { confirm: "Are you sure you want to delete this pipeline?" }, class: "w-full flex justify-center py-2 px-4 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" %>
          </div>
        </div>
      </div>

      <!-- Schedule -->
      <% if @pipeline.scheduled? %>
        <div class="bg-white shadow sm:rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Schedule Settings</h3>
            
            <%= form_with model: @pipeline, url: schedule_dashboard_pipeline_path(@pipeline), method: :post, local: true do |form| %>
              <div class="mb-4">
                <%= form.label :schedule, class: "block text-sm font-medium text-gray-700" %>
                <%= form.select :schedule, options_for_select(Pipeline::SCHEDULES.map { |s| [s.humanize, s] }, @pipeline.schedule), 
                    { prompt: "Select schedule" }, 
                    class: "mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md" %>
              </div>
              
              <div>
                <%= form.submit "Update Schedule", class: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>