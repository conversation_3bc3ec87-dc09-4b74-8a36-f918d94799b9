<div class="pipelines-index">
  <div class="page-header mb-6">
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Pipelines</h1>
        <p class="mt-1 text-sm text-gray-600">Manage your data transformation pipelines</p>
      </div>
      <div>
        <%= link_to new_dashboard_pipeline_path, class: "btn btn-primary" do %>
          <i class="fas fa-plus mr-2"></i>New Pipeline
        <% end %>
      </div>
    </div>
  </div>

  <% if @pipelines.any? %>
    <div class="grid gap-4">
      <% @pipelines.each do |pipeline| %>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <div class="flex items-center gap-3 mb-2">
                <h3 class="text-lg font-semibold text-gray-900">
                  <%= link_to pipeline.name, dashboard_pipeline_path(pipeline), class: "hover:text-primary-600" %>
                </h3>
                <span class="px-2 py-1 text-xs font-medium rounded-full bg-<%= pipeline_status_color(pipeline.status) %>-100 text-<%= pipeline_status_color(pipeline.status) %>-800">
                  <%= pipeline.status.capitalize %>
                </span>
              </div>
              
              <p class="text-sm text-gray-600 mb-3">
                <%= pipeline.description.presence || "No description provided" %>
              </p>
              
              <div class="flex items-center gap-4 text-sm text-gray-500">
                <div>
                  <i class="fas fa-database mr-1"></i>
                  <%= pipeline.data_source&.name || "No data source" %>
                </div>
                <div>
                  <i class="fas fa-clock mr-1"></i>
                  <%= pipeline.schedule&.capitalize || "Manual" %>
                </div>
                <% if pipeline.last_run_at %>
                  <div>
                    <i class="fas fa-history mr-1"></i>
                    Last run <%= time_ago_in_words(pipeline.last_run_at) %> ago
                  </div>
                <% end %>
              </div>
            </div>
            
            <div class="flex gap-2 ml-4">
              <% if pipeline.can_run? %>
                <%= link_to run_dashboard_pipeline_path(pipeline), method: :post, 
                    class: "btn btn-sm btn-secondary", 
                    data: { confirm: "Run this pipeline?" } do %>
                  <i class="fas fa-play"></i>
                <% end %>
              <% end %>
              
              <%= link_to edit_dashboard_pipeline_path(pipeline), class: "btn btn-sm btn-outline" do %>
                <i class="fas fa-edit"></i>
              <% end %>
              
              <%= link_to dashboard_pipeline_path(pipeline), method: :delete, 
                  class: "btn btn-sm btn-outline-danger", 
                  data: { confirm: "Are you sure you want to delete this pipeline?" } do %>
                <i class="fas fa-trash"></i>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <div class="bg-gray-50 rounded-lg p-12 text-center">
      <i class="fas fa-stream text-6xl text-gray-300 mb-4"></i>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No pipelines yet</h3>
      <p class="text-gray-600 mb-6">Create your first pipeline to start transforming your data</p>
      <%= link_to new_dashboard_pipeline_path, class: "btn btn-primary" do %>
        <i class="fas fa-plus mr-2"></i>Create Pipeline
      <% end %>
    </div>
  <% end %>
</div>

<style>
  .btn {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }
  
  .btn-outline {
    @apply bg-white text-gray-700 border-gray-300 hover:bg-gray-50 focus:ring-gray-500;
  }
  
  .btn-outline-danger {
    @apply bg-white text-red-600 border-gray-300 hover:bg-red-50 focus:ring-red-500;
  }
  
  .btn-sm {
    @apply px-3 py-1 text-xs;
  }
</style>