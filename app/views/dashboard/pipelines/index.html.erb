<<<<<<< HEAD
<div class="pipelines-index">
  <div class="page-header mb-6">
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Pipelines</h1>
        <p class="mt-1 text-sm text-gray-600">Manage your data transformation pipelines</p>
      </div>
      <div>
        <%= link_to new_dashboard_pipeline_path, class: "btn btn-primary" do %>
          <i class="fas fa-plus mr-2"></i>New Pipeline
=======
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Data Pipelines</h1>
        <p class="mt-1 text-sm text-gray-600">Automate your data transformation workflows</p>
      </div>
      <div>
        <%= link_to new_dashboard_pipeline_path, class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
          <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          New Pipeline
>>>>>>> feature/enhanced-data-sources-dashboard
        <% end %>
      </div>
    </div>
  </div>

<<<<<<< HEAD
  <% if @pipelines.any? %>
    <div class="grid gap-4">
      <% @pipelines.each do |pipeline| %>
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <div class="flex items-center gap-3 mb-2">
                <h3 class="text-lg font-semibold text-gray-900">
                  <%= link_to pipeline.name, dashboard_pipeline_path(pipeline), class: "hover:text-primary-600" %>
                </h3>
                <span class="px-2 py-1 text-xs font-medium rounded-full bg-<%= pipeline_status_color(pipeline.status) %>-100 text-<%= pipeline_status_color(pipeline.status) %>-800">
                  <%= pipeline.status.capitalize %>
                </span>
              </div>
              
              <p class="text-sm text-gray-600 mb-3">
                <%= pipeline.description.presence || "No description provided" %>
              </p>
              
              <div class="flex items-center gap-4 text-sm text-gray-500">
                <div>
                  <i class="fas fa-database mr-1"></i>
                  <%= pipeline.data_source&.name || "No data source" %>
                </div>
                <div>
                  <i class="fas fa-clock mr-1"></i>
                  <%= pipeline.schedule&.capitalize || "Manual" %>
                </div>
                <% if pipeline.last_run_at %>
                  <div>
                    <i class="fas fa-history mr-1"></i>
                    Last run <%= time_ago_in_words(pipeline.last_run_at) %> ago
                  </div>
                <% end %>
              </div>
            </div>
            
            <div class="flex gap-2 ml-4">
              <% if pipeline.can_run? %>
                <%= link_to run_dashboard_pipeline_path(pipeline), method: :post, 
                    class: "btn btn-sm btn-secondary", 
                    data: { confirm: "Run this pipeline?" } do %>
                  <i class="fas fa-play"></i>
                <% end %>
              <% end %>
              
              <%= link_to edit_dashboard_pipeline_path(pipeline), class: "btn btn-sm btn-outline" do %>
                <i class="fas fa-edit"></i>
              <% end %>
              
              <%= link_to dashboard_pipeline_path(pipeline), method: :delete, 
                  class: "btn btn-sm btn-outline-danger", 
                  data: { confirm: "Are you sure you want to delete this pipeline?" } do %>
                <i class="fas fa-trash"></i>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <div class="bg-gray-50 rounded-lg p-12 text-center">
      <i class="fas fa-stream text-6xl text-gray-300 mb-4"></i>
      <h3 class="text-lg font-medium text-gray-900 mb-2">No pipelines yet</h3>
      <p class="text-gray-600 mb-6">Create your first pipeline to start transforming your data</p>
      <%= link_to new_dashboard_pipeline_path, class: "btn btn-primary" do %>
        <i class="fas fa-plus mr-2"></i>Create Pipeline
      <% end %>
    </div>
  <% end %>
</div>

<style>
  .btn {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }
  
  .btn-outline {
    @apply bg-white text-gray-700 border-gray-300 hover:bg-gray-50 focus:ring-gray-500;
  }
  
  .btn-outline-danger {
    @apply bg-white text-red-600 border-gray-300 hover:bg-red-50 focus:ring-red-500;
  }
  
  .btn-sm {
    @apply px-3 py-1 text-xs;
  }
</style>
=======
  <!-- Stats -->
  <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
            </svg>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Total Pipelines</dt>
              <dd class="text-lg font-medium text-gray-900"><%= @stats[:total] %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Active</dt>
              <dd class="text-lg font-medium text-gray-900"><%= @stats[:active] %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Running</dt>
              <dd class="text-lg font-medium text-gray-900"><%= @stats[:running] %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-6 w-6 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">Scheduled</dt>
              <dd class="text-lg font-medium text-gray-900"><%= @stats[:scheduled] %></dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Pipelines List -->
  <div class="bg-white shadow overflow-hidden sm:rounded-md">
    <% if @pipelines.any? %>
      <ul class="divide-y divide-gray-200">
        <% @pipelines.each do |pipeline| %>
          <li>
            <%= link_to dashboard_pipeline_path(pipeline), class: "block hover:bg-gray-50" do %>
              <div class="px-4 py-4 sm:px-6">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="flex-shrink-0">
                      <% case pipeline.status %>
                      <% when 'active' %>
                        <span class="inline-flex items-center justify-center h-10 w-10 rounded-full bg-green-100">
                          <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                          </svg>
                        </span>
                      <% when 'running' %>
                        <span class="inline-flex items-center justify-center h-10 w-10 rounded-full bg-blue-100">
                          <svg class="h-6 w-6 text-blue-600 animate-spin" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        </span>
                      <% when 'failed' %>
                        <span class="inline-flex items-center justify-center h-10 w-10 rounded-full bg-red-100">
                          <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </span>
                      <% else %>
                        <span class="inline-flex items-center justify-center h-10 w-10 rounded-full bg-gray-100">
                          <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </span>
                      <% end %>
                    </div>
                    <div class="ml-4">
                      <p class="text-sm font-medium text-gray-900"><%= pipeline.name %></p>
                      <p class="text-sm text-gray-500">
                        Data Source: <%= pipeline.data_source.name %>
                      </p>
                    </div>
                  </div>
                  <div class="ml-2 flex items-center text-sm text-gray-500">
                    <% if pipeline.schedule.present? && pipeline.schedule != 'manual' %>
                      <svg class="mr-1.5 h-5 w-5 flex-shrink-0 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <%= pipeline.schedule.humanize %>
                    <% end %>
                    <% if pipeline.last_run_at %>
                      <span class="ml-4">
                        Last run: <%= time_ago_in_words(pipeline.last_run_at) %> ago
                      </span>
                    <% end %>
                  </div>
                </div>
                <% if pipeline.description.present? %>
                  <div class="mt-2">
                    <p class="text-sm text-gray-600"><%= truncate(pipeline.description, length: 100) %></p>
                  </div>
                <% end %>
                <div class="mt-2">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <%= pipeline_status_class(pipeline.status) %>">
                    <%= pipeline.status.capitalize %>
                  </span>
                </div>
              </div>
            <% end %>
          </li>
        <% end %>
      </ul>
    <% else %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No pipelines</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by creating a new pipeline.</p>
        <div class="mt-6">
          <%= link_to new_dashboard_pipeline_path, class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
            <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            New Pipeline
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>
>>>>>>> feature/enhanced-data-sources-dashboard
