<%= form_with(model: pipeline, url: url, method: method, local: true) do |form| %>
  <% if pipeline.errors.any? %>
    <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">
            There were <%= pluralize(pipeline.errors.count, "error") %> with your submission
          </h3>
          <div class="mt-2 text-sm text-red-700">
            <ul class="list-disc pl-5 space-y-1">
              <% pipeline.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:p-6 space-y-6">
      <!-- Basic Information -->
      <div>
        <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Basic Information</h3>
        
        <div class="grid grid-cols-1 gap-6">
          <div>
            <%= form.label :name, class: "block text-sm font-medium text-gray-700" %>
            <%= form.text_field :name, 
                class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md",
                placeholder: "e.g., Daily Sales Report" %>
          </div>

          <div>
            <%= form.label :data_source_id, "Data Source", class: "block text-sm font-medium text-gray-700" %>
            <%= form.select :data_source_id, 
                options_for_select(data_sources.map { |ds| [ds.name, ds.id] }, pipeline.data_source_id),
                { prompt: "Select a data source" },
                class: "mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md" %>
            <p class="mt-1 text-sm text-gray-500">Choose the data source this pipeline will process</p>
          </div>

          <div>
            <%= form.label :description, class: "block text-sm font-medium text-gray-700" %>
            <%= form.text_area :description, 
                rows: 3,
                class: "mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md",
                placeholder: "Describe what this pipeline does..." %>
          </div>
        </div>
      </div>

      <!-- Schedule Settings -->
      <div class="border-t pt-6">
        <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Schedule Settings</h3>
        
        <div class="grid grid-cols-1 gap-6">
          <div>
            <%= form.label :schedule, class: "block text-sm font-medium text-gray-700" %>
            <%= form.select :schedule, 
                options_for_select(Pipeline::SCHEDULES.map { |s| [s.humanize, s] }, pipeline.schedule),
                {},
                class: "mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md" %>
            <p class="mt-1 text-sm text-gray-500">How often should this pipeline run?</p>
          </div>

          <div>
            <%= form.label :status, class: "block text-sm font-medium text-gray-700" %>
            <%= form.select :status, 
                options_for_select(Pipeline::STATUSES.map { |s| [s.capitalize, s] }, pipeline.status || 'inactive'),
                {},
                class: "mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md" %>
            <p class="mt-1 text-sm text-gray-500">Set to 'Active' to enable the pipeline</p>
          </div>
        </div>
      </div>

      <!-- Configuration -->
      <div class="border-t pt-6">
        <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Configuration</h3>
        
        <div>
          <%= form.label :configuration, "Pipeline Configuration (JSON)", class: "block text-sm font-medium text-gray-700" %>
          <%= form.text_area :configuration, 
              rows: 10,
              value: pipeline.persisted? ? JSON.pretty_generate(pipeline.configuration_hash) : nil,
              class: "mt-1 font-mono text-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm border-gray-300 rounded-md",
              placeholder: '{\n  "steps": [\n    {\n      "name": "transform",\n      "type": "transformation",\n      "config": {}\n    }\n  ]\n}' %>
          <p class="mt-1 text-sm text-gray-500">Define the pipeline steps and configuration in JSON format</p>
        </div>
      </div>

      <% if pipeline.persisted? %>
        <!-- Pipeline Info -->
        <div class="border-t pt-6">
          <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Pipeline Information</h3>
          
          <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
            <div>
              <dt class="text-sm font-medium text-gray-500">Last Run</dt>
              <dd class="mt-1 text-sm text-gray-900">
                <%= pipeline.last_run_at ? time_ago_in_words(pipeline.last_run_at) + ' ago' : 'Never' %>
              </dd>
            </div>
            
            <div>
              <dt class="text-sm font-medium text-gray-500">Created</dt>
              <dd class="mt-1 text-sm text-gray-900">
                <%= pipeline.created_at.strftime("%B %d, %Y") %>
              </dd>
            </div>
            
            <% if pipeline.error_message.present? %>
              <div class="sm:col-span-2">
                <dt class="text-sm font-medium text-gray-500">Last Error</dt>
                <dd class="mt-1 text-sm text-red-600">
                  <%= pipeline.error_message %>
                </dd>
              </div>
            <% end %>
          </dl>
        </div>
      <% end %>
    </div>

    <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
      <%= link_to "Cancel", cancel_path, class: "inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
      <%= form.submit submit_text, class: "ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
    </div>
  </div>
<% end %>