<div class="monitoring-dashboard" data-controller="monitoring">
  <div class="page-header">
    <h1>Performance Monitoring</h1>
    <div class="header-actions">
      <%= link_to "Configure Alerts", "#", class: "btn btn-primary", data: { action: "click->monitoring#showRuleModal" } %>
    </div>
  </div>

  <!-- Active Alerts Section -->
  <% if @active_alerts.any? %>
    <div class="alerts-section mb-4">
      <h2>Active Alerts</h2>
      <div class="alert-list">
        <% @active_alerts.each do |alert| %>
          <div class="alert-item alert-<%= alert.severity %>" data-alert-id="<%= alert.id %>">
            <div class="alert-header">
              <span class="alert-severity"><%= alert.severity.capitalize %></span>
              <span class="alert-pipeline"><%= alert.pipeline&.name || "System" %></span>
              <span class="alert-time"><%= time_ago_in_words(alert.triggered_at) %> ago</span>
            </div>
            <div class="alert-body">
              <p class="alert-message"><%= alert.message %></p>
              <p class="alert-rule">Rule: <%= alert.monitoring_rule&.name %></p>
            </div>
            <div class="alert-actions">
              <% if alert.status == 'triggered' %>
                <%= link_to "Acknowledge", acknowledge_alert_monitoring_path(alert), 
                    method: :post, 
                    class: "btn btn-sm btn-warning",
                    data: { confirm: "Acknowledge this alert?" } %>
              <% end %>
              <% if alert.status != 'resolved' %>
                <%= link_to "Resolve", resolve_alert_monitoring_path(alert), 
                    method: :post, 
                    class: "btn btn-sm btn-success",
                    data: { confirm: "Resolve this alert?" } %>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  <% end %>

  <!-- Aggregate Metrics -->
  <div class="metrics-overview mb-4">
    <h2>Performance Overview (Last 24 Hours)</h2>
    <div class="metrics-grid">
      <div class="metric-card">
        <div class="metric-label">Avg Execution Time</div>
        <div class="metric-value">
          <%= @aggregate_metrics[:avg_execution_time] || 'N/A' %>
          <span class="metric-unit">seconds</span>
        </div>
      </div>
      <div class="metric-card">
        <div class="metric-label">Records Processed</div>
        <div class="metric-value">
          <%= number_with_delimiter(@aggregate_metrics[:total_records_processed] || 0) %>
        </div>
      </div>
      <div class="metric-card">
        <div class="metric-label">Avg CPU Usage</div>
        <div class="metric-value">
          <%= @aggregate_metrics[:avg_cpu_usage] || 'N/A' %>
          <span class="metric-unit">%</span>
        </div>
      </div>
      <div class="metric-card">
        <div class="metric-label">Avg Memory Usage</div>
        <div class="metric-value">
          <%= @aggregate_metrics[:avg_memory_usage] || 'N/A' %>
          <span class="metric-unit">MB</span>
        </div>
      </div>
      <div class="metric-card">
        <div class="metric-label">Total Errors</div>
        <div class="metric-value error-metric">
          <%= @aggregate_metrics[:total_errors] || 0 %>
        </div>
      </div>
    </div>
  </div>

  <!-- Real-time Metrics Charts -->
  <div class="charts-section mb-4">
    <h2>Real-time Metrics</h2>
    <div class="chart-controls mb-3">
      <select id="pipeline-filter" class="form-control" style="width: 200px; display: inline-block;">
        <option value="">All Pipelines</option>
        <% @organization.pipelines.each do |pipeline| %>
          <option value="<%= pipeline.id %>"><%= pipeline.name %></option>
        <% end %>
      </select>
      <select id="time-range" class="form-control ml-2" style="width: 150px; display: inline-block;">
        <option value="1h">Last Hour</option>
        <option value="6h">Last 6 Hours</option>
        <option value="24h" selected>Last 24 Hours</option>
        <option value="7d">Last 7 Days</option>
      </select>
    </div>
    
    <div class="charts-grid">
      <div class="chart-container">
        <canvas data-monitoring-target="executionTimeChart"></canvas>
      </div>
      <div class="chart-container">
        <canvas data-monitoring-target="throughputChart"></canvas>
      </div>
      <div class="chart-container">
        <canvas data-monitoring-target="resourceUsageChart"></canvas>
      </div>
      <div class="chart-container">
        <canvas data-monitoring-target="errorRateChart"></canvas>
      </div>
    </div>
  </div>

  <!-- Pipeline Status Overview -->
  <div class="pipeline-status-section mb-4">
    <h2>Pipeline Status</h2>
    <div class="status-grid">
      <% @pipeline_statuses.each do |status, count| %>
        <div class="status-card status-<%= status %>">
          <div class="status-label"><%= status.humanize %></div>
          <div class="status-count"><%= count %></div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Alert History -->
  <div class="alert-history-section">
    <h2>Recent Alert History</h2>
    <div class="table-responsive">
      <table class="table table-striped">
        <thead>
          <tr>
            <th>Time</th>
            <th>Pipeline</th>
            <th>Rule</th>
            <th>Severity</th>
            <th>Message</th>
            <th>Status</th>
            <th>Duration</th>
          </tr>
        </thead>
        <tbody>
          <% @alert_history.each do |alert| %>
            <tr>
              <td><%= alert.triggered_at.strftime("%m/%d %H:%M") %></td>
              <td><%= alert.pipeline&.name || "System" %></td>
              <td><%= alert.monitoring_rule&.name %></td>
              <td>
                <span class="badge badge-<%= alert.severity %>">
                  <%= alert.severity.capitalize %>
                </span>
              </td>
              <td><%= truncate(alert.message, length: 60) %></td>
              <td>
                <span class="badge badge-<%= alert.status %>">
                  <%= alert.status.capitalize %>
                </span>
              </td>
              <td>
                <% if alert.resolved_at %>
                  <%= distance_of_time_in_words(alert.triggered_at, alert.resolved_at) %>
                <% else %>
                  Ongoing
                <% end %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  </div>
</div>

<style>
  .monitoring-dashboard {
    padding: 20px;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
  }

  .alerts-section {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  .alert-list {
    display: grid;
    gap: 15px;
  }

  .alert-item {
    border-left: 4px solid;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 4px;
  }

  .alert-item.alert-critical {
    border-color: #dc3545;
    background: #f8d7da;
  }

  .alert-item.alert-warning {
    border-color: #ffc107;
    background: #fff3cd;
  }

  .alert-item.alert-info {
    border-color: #17a2b8;
    background: #d1ecf1;
  }

  .alert-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .alert-severity {
    font-weight: bold;
    text-transform: uppercase;
    font-size: 12px;
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
  }

  .metric-card {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
  }

  .metric-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
  }

  .metric-value {
    font-size: 32px;
    font-weight: bold;
    color: #333;
  }

  .metric-unit {
    font-size: 16px;
    color: #666;
    font-weight: normal;
  }

  .error-metric {
    color: #dc3545;
  }

  .charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
  }

  .chart-container {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    height: 300px;
  }

  .status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
  }

  .status-card {
    background: #fff;
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    border: 2px solid;
  }

  .status-card.status-active {
    border-color: #28a745;
    background: #d4edda;
  }

  .status-card.status-paused {
    border-color: #ffc107;
    background: #fff3cd;
  }

  .status-card.status-error {
    border-color: #dc3545;
    background: #f8d7da;
  }

  .status-label {
    font-size: 14px;
    margin-bottom: 5px;
  }

  .status-count {
    font-size: 24px;
    font-weight: bold;
  }

  .badge-critical { background-color: #dc3545; }
  .badge-warning { background-color: #ffc107; }
  .badge-info { background-color: #17a2b8; }
  .badge-triggered { background-color: #dc3545; }
  .badge-acknowledged { background-color: #ffc107; }
  .badge-resolved { background-color: #28a745; }
</style>

