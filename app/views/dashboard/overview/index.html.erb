<% content_for :title, "Dashboard Overview" %>
<% content_for :description, "Get insights into your data analytics performance and key metrics." %>

<!-- Flash Message Demo Section -->
<div class="bg-white shadow rounded-lg p-6 mb-6">
  <h2 class="text-lg font-medium text-gray-900 mb-4">Flash Message System Demo - Compact & Improved</h2>
  <p class="text-sm text-gray-600 mb-6">Test the updated compact flash message system with improved dismiss functionality.</p>

  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
    <%= link_to "Success Message", dashboard_root_path(demo_flash: 'success'),
        class: "inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200" %>

    <%= link_to "Error Message", dashboard_root_path(demo_flash: 'error'),
        class: "inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200" %>

    <%= link_to "Warning Message", dashboard_root_path(demo_flash: 'warning'),
        class: "inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors duration-200" %>

    <%= link_to "Info Message", dashboard_root_path(demo_flash: 'info'),
        class: "inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200" %>

    <%= link_to "Multiple Messages", dashboard_root_path(demo_flash: 'multiple'),
        class: "inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors duration-200" %>
  </div>

  <div class="mt-4 p-4 bg-gray-50 rounded-md">
    <h4 class="text-sm font-medium text-gray-900 mb-2">Updated Features (Compact Design):</h4>
    <ul class="text-sm text-gray-600 space-y-1">
      <li>• <strong>Smaller Size:</strong> Reduced padding, font size, and overall dimensions</li>
      <li>• <strong>Fixed Dismiss:</strong> Close button (X) and Escape key now work properly</li>
      <li>• <strong>Auto-dismiss:</strong> Success (4s), Warning/Info (6s), Error (manual only)</li>
      <li>• <strong>Progress Bar:</strong> Thinner visual indicator (0.5px height)</li>
      <li>• <strong>Hover Pause:</strong> Auto-dismiss pauses on hover/focus</li>
      <li>• <strong>Accessibility:</strong> WCAG 2.1 AA compliant with ARIA attributes</li>
      <li>• <strong>Mobile Ready:</strong> Touch-friendly with responsive design</li>
    </ul>

    <div class="mt-3 p-3 bg-blue-50 rounded border border-blue-200">
      <h5 class="text-xs font-medium text-blue-900 mb-1">Testing Instructions:</h5>
      <p class="text-xs text-blue-800">
        1. Click "Error Message" to test manual dismiss (no auto-dismiss)<br>
        2. Try clicking the X button or pressing Escape key<br>
        3. Test "Multiple Messages" to see compact stacking<br>
        4. Hover over auto-dismissing messages to pause countdown
      </p>
    </div>
  </div>
</div>

<!-- Welcome Section -->
<div class="bg-white overflow-hidden shadow rounded-lg mb-6">
  <div class="px-4 py-5 sm:p-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div class="mb-4 sm:mb-0">
        <h2 class="text-lg sm:text-xl font-medium text-gray-900">
          Welcome back, <%= current_user&.first_name || 'there' %>!
        </h2>
        <p class="mt-1 text-sm text-gray-500">
          Here's what's happening with your data analytics today.
        </p>
      </div>
      <div class="flex-shrink-0">
        <div class="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
          <div class="flex items-center text-sm text-gray-500">
            <i class="fas fa-building mr-2 text-primary-600" aria-hidden="true"></i>
            <span class="truncate"><%= current_organization&.name %></span>
          </div>
          <div class="flex items-center text-sm text-gray-500">
            <i class="fas fa-clock mr-2 text-primary-600" aria-hidden="true"></i>
            <span class="whitespace-nowrap">Last updated: <%= Time.current.strftime("%I:%M %p") %></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Key Metrics -->
<div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
  <!-- Total Revenue -->
  <div class="bg-white overflow-hidden shadow rounded-lg">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
            <i class="fas fa-dollar-sign text-green-600" aria-hidden="true"></i>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
            <dd class="text-lg font-medium text-gray-900">£<%= number_with_delimiter(@metrics[:total_revenue]) %></dd>
          </dl>
        </div>
      </div>
    </div>
    <div class="bg-gray-50 px-5 py-3">
      <div class="text-sm">
        <span class="text-green-600 font-medium">+12.5%</span>
        <span class="text-gray-500">from last month</span>
      </div>
    </div>
  </div>

  <!-- Active Customers -->
  <div class="bg-white overflow-hidden shadow rounded-lg">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <i class="fas fa-users text-blue-600" aria-hidden="true"></i>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 truncate">Active Customers</dt>
            <dd class="text-lg font-medium text-gray-900"><%= number_with_delimiter(@metrics[:active_customers]) %></dd>
          </dl>
        </div>
      </div>
    </div>
    <div class="bg-gray-50 px-5 py-3">
      <div class="text-sm">
        <span class="text-blue-600 font-medium">+8.2%</span>
        <span class="text-gray-500">from last month</span>
      </div>
    </div>
  </div>

  <!-- Data Processed -->
  <div class="bg-white overflow-hidden shadow rounded-lg">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
            <i class="fas fa-database text-purple-600" aria-hidden="true"></i>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 truncate">Data Processed</dt>
            <dd class="text-lg font-medium text-gray-900"><%= @metrics[:data_processed] %>GB</dd>
          </dl>
        </div>
      </div>
    </div>
    <div class="bg-gray-50 px-5 py-3">
      <div class="text-sm">
        <span class="text-purple-600 font-medium">+15.3%</span>
        <span class="text-gray-500">from last month</span>
      </div>
    </div>
  </div>

  <!-- Conversion Rate -->
  <div class="bg-white overflow-hidden shadow rounded-lg">
    <div class="p-5">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
            <i class="fas fa-chart-line text-yellow-600" aria-hidden="true"></i>
          </div>
        </div>
        <div class="ml-5 w-0 flex-1">
          <dl>
            <dt class="text-sm font-medium text-gray-500 truncate">Conversion Rate</dt>
            <dd class="text-lg font-medium text-gray-900"><%= @metrics[:conversion_rate] %>%</dd>
          </dl>
        </div>
      </div>
    </div>
    <div class="bg-gray-50 px-5 py-3">
      <div class="text-sm">
        <span class="text-yellow-600 font-medium">+3.1%</span>
        <span class="text-gray-500">from last month</span>
      </div>
    </div>
  </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
  <!-- Recent Activity -->
  <div class="lg:col-span-2 order-2 lg:order-1">
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
        <div class="flow-root">
          <ul role="list" class="-mb-8">
            <% @recent_activities.each_with_index do |activity, index| %>
              <li>
                <div class="relative pb-8">
                  <% unless index == @recent_activities.length - 1 %>
                    <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                  <% end %>
                  <div class="relative flex space-x-3">
                    <div>
                      <span class="h-8 w-8 rounded-full <%= activity[:status] == 'completed' ? 'bg-green-500' : activity[:status] == 'warning' ? 'bg-yellow-500' : 'bg-gray-400' %> flex items-center justify-center ring-8 ring-white">
                        <% case activity[:type] %>
                        <% when 'data_sync' %>
                          <i class="fas fa-sync-alt h-4 w-4 text-white" aria-hidden="true"></i>
                        <% when 'report_generated' %>
                          <i class="fas fa-file-alt h-4 w-4 text-white" aria-hidden="true"></i>
                        <% when 'pipeline_run' %>
                          <i class="fas fa-cogs h-4 w-4 text-white" aria-hidden="true"></i>
                        <% when 'alert' %>
                          <i class="fas fa-exclamation-triangle h-4 w-4 text-white" aria-hidden="true"></i>
                        <% end %>
                      </span>
                    </div>
                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                      <div>
                        <p class="text-sm text-gray-500"><%= activity[:description] %></p>
                      </div>
                      <div class="text-right text-sm whitespace-nowrap text-gray-500">
                        <time datetime="<%= activity[:timestamp].iso8601 %>">
                          <%= time_ago_in_words(activity[:timestamp]) %> ago
                        </time>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            <% end %>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Stats & Actions -->
  <div class="space-y-6 order-1 lg:order-2">
    <!-- Quick Stats -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Stats</h3>
        <dl class="space-y-4">
          <div class="flex justify-between">
            <dt class="text-sm font-medium text-gray-500">Data Sources</dt>
            <dd class="text-sm text-gray-900"><%= @quick_stats[:data_sources_connected] %></dd>
          </div>
          <div class="flex justify-between">
            <dt class="text-sm font-medium text-gray-500">Active Pipelines</dt>
            <dd class="text-sm text-gray-900"><%= @quick_stats[:active_pipelines] %></dd>
          </div>
          <div class="flex justify-between">
            <dt class="text-sm font-medium text-gray-500">Reports This Month</dt>
            <dd class="text-sm text-gray-900"><%= @quick_stats[:reports_this_month] %></dd>
          </div>
          <div class="flex justify-between">
            <dt class="text-sm font-medium text-gray-500">Team Members</dt>
            <dd class="text-sm text-gray-900"><%= @quick_stats[:team_members] %></dd>
          </div>
        </dl>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div class="space-y-3">
          <button type="button" class="w-full bg-primary-600 text-white px-4 py-3 rounded-lg text-sm font-medium hover:bg-primary-700 transition-all duration-200 flex items-center justify-center space-x-2 shadow-md hover:shadow-lg transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
            <i class="fas fa-plus" aria-hidden="true"></i>
            <span>Connect Data Source</span>
          </button>
          <button type="button" class="w-full bg-white border border-gray-300 text-gray-700 px-4 py-3 rounded-lg text-sm font-medium hover:bg-gray-50 transition-all duration-200 flex items-center justify-center space-x-2 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
            <i class="fas fa-chart-bar" aria-hidden="true"></i>
            <span>Create Report</span>
          </button>
          <button type="button" class="w-full bg-white border border-gray-300 text-gray-700 px-4 py-3 rounded-lg text-sm font-medium hover:bg-gray-50 transition-all duration-200 flex items-center justify-center space-x-2 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
            <i class="fas fa-users" aria-hidden="true"></i>
            <span>Invite Team Member</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Getting Started -->
    <div class="bg-gradient-to-r from-primary-600 to-primary-700 shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-lg leading-6 font-medium text-white mb-2">Getting Started</h3>
        <p class="text-primary-100 text-sm mb-4">
          Complete your setup to unlock the full potential of Data Reflow.
        </p>
        <div class="space-y-2">
          <div class="flex items-center text-sm text-primary-100">
            <i class="fas fa-check-circle mr-2 text-green-400" aria-hidden="true"></i>
            Account created
          </div>
          <div class="flex items-center text-sm text-primary-100">
            <i class="fas fa-circle mr-2 text-primary-300" aria-hidden="true"></i>
            Connect your first data source
          </div>
          <div class="flex items-center text-sm text-primary-100">
            <i class="fas fa-circle mr-2 text-primary-300" aria-hidden="true"></i>
            Create your first dashboard
          </div>
        </div>
        <button type="button" class="mt-4 w-full bg-white text-primary-600 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
          Continue Setup
        </button>
      </div>
    </div>
  </div>
</div>
