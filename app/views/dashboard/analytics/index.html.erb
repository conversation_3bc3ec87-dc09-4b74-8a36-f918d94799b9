<% content_for :title, "Analytics Dashboard - Data Reflow" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50"
     data-controller="analytics-dashboard"
     data-analytics-dashboard-date-range-value="<%= @date_range %>">
  
  <!-- Header Section -->
  <div class="bg-white/80 backdrop-blur-sm border-b border-slate-200/60 shadow-sm sticky top-0 z-40">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <!-- Title and Description -->
        <div>
          <h1 class="text-2xl font-bold text-neutral-900 tracking-tight flex items-center gap-3">
            <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
              <i class="fas fa-chart-line text-white text-lg"></i>
            </div>
            Analytics Dashboard
          </h1>
          <p class="text-neutral-600 mt-1">Monitor performance, track trends, and gain insights</p>
        </div>
        
        <!-- Controls -->
        <div class="flex flex-wrap items-center gap-3">
          <!-- Date Range Selector -->
          <div class="relative" data-controller="dropdown">
            <button type="button"
                    class="inline-flex items-center px-4 py-2.5 border border-neutral-200 rounded-xl text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all duration-200 shadow-sm"
                    data-action="click->dropdown#toggle">
              <i class="fas fa-calendar-alt mr-2 text-neutral-500"></i>
              <span data-analytics-dashboard-target="dateRangeLabel">Last 30 Days</span>
              <i class="fas fa-chevron-down ml-2 text-neutral-400 text-xs"></i>
            </button>
            
            <div class="hidden absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-xl border border-neutral-200 z-50"
                 data-dropdown-target="menu">
              <div class="py-2">
                <button type="button"
                        class="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100"
                        data-action="click->analytics-dashboard#changeDateRange"
                        data-range="24_hours">
                  <i class="fas fa-clock mr-2 text-neutral-500"></i>
                  Last 24 Hours
                </button>
                <button type="button"
                        class="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100"
                        data-action="click->analytics-dashboard#changeDateRange"
                        data-range="7_days">
                  <i class="fas fa-calendar-week mr-2 text-neutral-500"></i>
                  Last 7 Days
                </button>
                <button type="button"
                        class="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100"
                        data-action="click->analytics-dashboard#changeDateRange"
                        data-range="30_days">
                  <i class="fas fa-calendar-alt mr-2 text-neutral-500"></i>
                  Last 30 Days
                </button>
                <button type="button"
                        class="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100"
                        data-action="click->analytics-dashboard#changeDateRange"
                        data-range="90_days">
                  <i class="fas fa-calendar mr-2 text-neutral-500"></i>
                  Last 90 Days
                </button>
                <div class="border-t border-neutral-200 my-2"></div>
                <button type="button"
                        class="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100"
                        data-action="click->analytics-dashboard#showCustomDatePicker">
                  <i class="fas fa-calendar-plus mr-2 text-neutral-500"></i>
                  Custom Range
                </button>
              </div>
            </div>
          </div>
          
          <!-- Refresh Button -->
          <button type="button"
                  class="inline-flex items-center px-4 py-2.5 border border-neutral-200 rounded-xl text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all duration-200 shadow-sm"
                  data-action="click->analytics-dashboard#refresh">
            <i class="fas fa-sync-alt mr-2 text-neutral-500" data-analytics-dashboard-target="refreshIcon"></i>
            Refresh
          </button>
          
          <!-- Export Dropdown -->
          <div class="relative" data-controller="dropdown">
            <button type="button"
                    class="inline-flex items-center px-4 py-2.5 border border-transparent rounded-xl text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-all duration-200 shadow-lg"
                    data-action="click->dropdown#toggle">
              <i class="fas fa-download mr-2"></i>
              Export
              <i class="fas fa-chevron-down ml-2 text-xs"></i>
            </button>
            
            <div class="hidden absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-xl border border-neutral-200 z-50"
                 data-dropdown-target="menu">
              <div class="py-2">
                <%= link_to export_dashboard_analytics_path(format_type: 'csv', date_range: @date_range),
                    class: "block w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100" do %>
                  <i class="fas fa-file-csv mr-2 text-green-500"></i>
                  Export as CSV
                <% end %>
                <%= link_to export_dashboard_analytics_path(format_type: 'pdf', date_range: @date_range),
                    class: "block w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100" do %>
                  <i class="fas fa-file-pdf mr-2 text-red-500"></i>
                  Export as PDF
                <% end %>
                <%= link_to export_dashboard_analytics_path(format_type: 'json', date_range: @date_range),
                    class: "block w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100" do %>
                  <i class="fas fa-file-code mr-2 text-blue-500"></i>
                  Export as JSON
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Key Metrics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-8">
      <!-- Total Syncs -->
      <div class="bg-white rounded-xl shadow-sm border border-neutral-200 p-5 hover:shadow-md transition-all duration-200 group">
        <div class="flex items-center justify-between mb-3">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
            <i class="fas fa-sync-alt text-white"></i>
          </div>
          <span class="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded-full">Total</span>
        </div>
        <p class="text-2xl font-bold text-neutral-900" data-analytics-dashboard-target="totalSyncs">
          <%= number_with_delimiter(@metrics[:total_syncs]) %>
        </p>
        <p class="text-sm text-neutral-600 mt-1">Total Syncs</p>
      </div>
      
      <!-- Success Rate -->
      <div class="bg-white rounded-xl shadow-sm border border-neutral-200 p-5 hover:shadow-md transition-all duration-200 group">
        <div class="flex items-center justify-between mb-3">
          <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
            <i class="fas fa-check-circle text-white"></i>
          </div>
          <% if @metrics[:success_rate] >= 95 %>
            <span class="text-xs font-medium text-green-600 bg-green-50 px-2 py-1 rounded-full">Excellent</span>
          <% elsif @metrics[:success_rate] >= 85 %>
            <span class="text-xs font-medium text-yellow-600 bg-yellow-50 px-2 py-1 rounded-full">Good</span>
          <% else %>
            <span class="text-xs font-medium text-red-600 bg-red-50 px-2 py-1 rounded-full">Needs Attention</span>
          <% end %>
        </div>
        <p class="text-2xl font-bold text-neutral-900">
          <%= @metrics[:success_rate] %>%
        </p>
        <p class="text-sm text-neutral-600 mt-1">Success Rate</p>
      </div>
      
      <!-- Data Volume -->
      <div class="bg-white rounded-xl shadow-sm border border-neutral-200 p-5 hover:shadow-md transition-all duration-200 group">
        <div class="flex items-center justify-between mb-3">
          <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
            <i class="fas fa-database text-white"></i>
          </div>
          <span class="text-xs font-medium text-purple-600 bg-purple-50 px-2 py-1 rounded-full">Volume</span>
        </div>
        <p class="text-2xl font-bold text-neutral-900">
          <%= @metrics[:total_data_volume] %>
        </p>
        <p class="text-sm text-neutral-600 mt-1">Data Processed</p>
      </div>
      
      <!-- Avg Duration -->
      <div class="bg-white rounded-xl shadow-sm border border-neutral-200 p-5 hover:shadow-md transition-all duration-200 group">
        <div class="flex items-center justify-between mb-3">
          <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
            <i class="fas fa-clock text-white"></i>
          </div>
          <% if @metrics[:avg_sync_duration] < 60 %>
            <span class="text-xs font-medium text-green-600 bg-green-50 px-2 py-1 rounded-full">Fast</span>
          <% elsif @metrics[:avg_sync_duration] < 300 %>
            <span class="text-xs font-medium text-yellow-600 bg-yellow-50 px-2 py-1 rounded-full">Normal</span>
          <% else %>
            <span class="text-xs font-medium text-red-600 bg-red-50 px-2 py-1 rounded-full">Slow</span>
          <% end %>
        </div>
        <p class="text-2xl font-bold text-neutral-900">
          <%= @metrics[:avg_sync_duration] %>s
        </p>
        <p class="text-sm text-neutral-600 mt-1">Avg Duration</p>
      </div>
      
      <!-- Active Sources -->
      <div class="bg-white rounded-xl shadow-sm border border-neutral-200 p-5 hover:shadow-md transition-all duration-200 group">
        <div class="flex items-center justify-between mb-3">
          <div class="w-10 h-10 bg-gradient-to-br from-teal-500 to-teal-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
            <i class="fas fa-plug text-white"></i>
          </div>
          <span class="text-xs font-medium text-teal-600 bg-teal-50 px-2 py-1 rounded-full">Sources</span>
        </div>
        <p class="text-2xl font-bold text-neutral-900">
          <%= @metrics[:active_sources] %>
        </p>
        <p class="text-sm text-neutral-600 mt-1">Active Sources</p>
      </div>
      
      <!-- Error Rate -->
      <div class="bg-white rounded-xl shadow-sm border border-neutral-200 p-5 hover:shadow-md transition-all duration-200 group">
        <div class="flex items-center justify-between mb-3">
          <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
            <i class="fas fa-exclamation-triangle text-white"></i>
          </div>
          <% if @metrics[:error_rate] < 5 %>
            <span class="text-xs font-medium text-green-600 bg-green-50 px-2 py-1 rounded-full">Low</span>
          <% elsif @metrics[:error_rate] < 15 %>
            <span class="text-xs font-medium text-yellow-600 bg-yellow-50 px-2 py-1 rounded-full">Medium</span>
          <% else %>
            <span class="text-xs font-medium text-red-600 bg-red-50 px-2 py-1 rounded-full">High</span>
          <% end %>
        </div>
        <p class="text-2xl font-bold text-neutral-900">
          <%= @metrics[:error_rate] %>%
        </p>
        <p class="text-sm text-neutral-600 mt-1">Error Rate</p>
      </div>
    </div>
    
    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Sync Timeline Chart -->
      <div class="bg-white rounded-xl shadow-sm border border-neutral-200 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-neutral-900 flex items-center gap-2">
            <i class="fas fa-chart-area text-indigo-500"></i>
            Sync Activity Timeline
          </h3>
          <button type="button"
                  class="text-neutral-400 hover:text-neutral-600 transition-colors duration-200"
                  data-controller="tooltip"
                  data-tooltip-content-value="Shows sync activity over time, grouped by status">
            <i class="fas fa-info-circle"></i>
          </button>
        </div>
        <div class="h-64">
          <canvas id="syncTimelineChart" 
                  data-analytics-dashboard-target="syncTimelineChart"
                  data-chart-data="<%= @sync_timeline_data.to_json %>"></canvas>
        </div>
      </div>
      
      <!-- Data Volume Trend Chart -->
      <div class="bg-white rounded-xl shadow-sm border border-neutral-200 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-neutral-900 flex items-center gap-2">
            <i class="fas fa-chart-line text-purple-500"></i>
            Data Volume Trend
          </h3>
          <button type="button"
                  class="text-neutral-400 hover:text-neutral-600 transition-colors duration-200"
                  data-controller="tooltip"
                  data-tooltip-content-value="Shows the volume of data processed over time">
            <i class="fas fa-info-circle"></i>
          </button>
        </div>
        <div class="h-64">
          <canvas id="dataVolumeTrendChart" 
                  data-analytics-dashboard-target="dataVolumeTrendChart"
                  data-chart-data="<%= @data_volume_trend.to_json %>"></canvas>
        </div>
      </div>
      
      <!-- Source Performance Chart -->
      <div class="bg-white rounded-xl shadow-sm border border-neutral-200 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-neutral-900 flex items-center gap-2">
            <i class="fas fa-chart-bar text-green-500"></i>
            Source Performance
          </h3>
          <button type="button"
                  class="text-neutral-400 hover:text-neutral-600 transition-colors duration-200"
                  data-controller="tooltip"
                  data-tooltip-content-value="Average sync duration by data source">
            <i class="fas fa-info-circle"></i>
          </button>
        </div>
        <div class="h-64">
          <canvas id="sourcePerformanceChart" 
                  data-analytics-dashboard-target="sourcePerformanceChart"
                  data-chart-data="<%= @source_performance_data.to_json %>"></canvas>
        </div>
      </div>
      
      <!-- Error Distribution Chart -->
      <div class="bg-white rounded-xl shadow-sm border border-neutral-200 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-neutral-900 flex items-center gap-2">
            <i class="fas fa-chart-pie text-red-500"></i>
            Error Distribution
          </h3>
          <button type="button"
                  class="text-neutral-400 hover:text-neutral-600 transition-colors duration-200"
                  data-controller="tooltip"
                  data-tooltip-content-value="Distribution of errors by source type">
            <i class="fas fa-info-circle"></i>
          </button>
        </div>
        <div class="h-64 flex items-center justify-center">
          <% if @error_distribution.any? %>
            <canvas id="errorDistributionChart" 
                    data-analytics-dashboard-target="errorDistributionChart"
                    data-chart-data="<%= @error_distribution.to_json %>"></canvas>
          <% else %>
            <div class="text-center">
              <i class="fas fa-check-circle text-green-500 text-5xl mb-3"></i>
              <p class="text-neutral-600">No errors in this period!</p>
            </div>
          <% end %>
        </div>
      </div>
    </div>
    
    <!-- Insights Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Top Performing Sources -->
      <div class="bg-white rounded-xl shadow-sm border border-neutral-200 p-6">
        <h3 class="text-lg font-semibold text-neutral-900 flex items-center gap-2 mb-4">
          <i class="fas fa-trophy text-yellow-500"></i>
          Top Performing Sources
        </h3>
        <div class="space-y-3">
          <% @top_sources.each_with_index do |source, index| %>
            <div class="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-transparent rounded-lg">
              <div class="flex items-center gap-3">
                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span class="text-sm font-semibold text-green-700"><%= index + 1 %></span>
                </div>
                <div>
                  <p class="font-medium text-neutral-900"><%= source[:name] %></p>
                  <p class="text-xs text-neutral-600"><%= source[:sync_count] %> successful syncs</p>
                </div>
              </div>
              <span class="text-sm font-medium text-green-600"><%= source[:avg_duration] %>s avg</span>
            </div>
          <% end %>
          <% if @top_sources.empty? %>
            <p class="text-center text-neutral-500 py-4">No sync data available</p>
          <% end %>
        </div>
      </div>
      
      <!-- Problematic Sources -->
      <div class="bg-white rounded-xl shadow-sm border border-neutral-200 p-6">
        <h3 class="text-lg font-semibold text-neutral-900 flex items-center gap-2 mb-4">
          <i class="fas fa-exclamation-circle text-red-500"></i>
          Sources Needing Attention
        </h3>
        <div class="space-y-3">
          <% @problematic_sources.each do |source| %>
            <div class="flex items-center justify-between p-3 bg-gradient-to-r from-red-50 to-transparent rounded-lg">
              <div>
                <p class="font-medium text-neutral-900"><%= source[:name] %></p>
                <p class="text-xs text-red-600"><%= source[:error_count] %> errors</p>
              </div>
              <%= link_to dashboard_data_source_path(current_organization.data_sources.find_by(name: source[:name])),
                  class: "text-sm text-red-600 hover:text-red-700 font-medium" do %>
                View <i class="fas fa-arrow-right ml-1"></i>
              <% end %>
            </div>
          <% end %>
          <% if @problematic_sources.empty? %>
            <div class="text-center py-4">
              <i class="fas fa-check-circle text-green-500 text-3xl mb-2"></i>
              <p class="text-neutral-600">All sources running smoothly!</p>
            </div>
          <% end %>
        </div>
      </div>
      
      <!-- Recent Errors -->
      <div class="bg-white rounded-xl shadow-sm border border-neutral-200 p-6">
        <h3 class="text-lg font-semibold text-neutral-900 flex items-center gap-2 mb-4">
          <i class="fas fa-history text-orange-500"></i>
          Recent Errors
        </h3>
        <div class="space-y-2 max-h-96 overflow-y-auto">
          <% @recent_errors.each do |error| %>
            <div class="p-3 border border-neutral-200 rounded-lg hover:border-red-300 transition-colors duration-200">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <p class="font-medium text-neutral-900 text-sm"><%= error[:source_name] %></p>
                  <p class="text-xs text-red-600 mt-1 line-clamp-2"><%= error[:error_message] %></p>
                </div>
                <span class="text-xs text-neutral-500 whitespace-nowrap ml-2">
                  <%= time_ago_in_words(error[:occurred_at]) %> ago
                </span>
              </div>
            </div>
          <% end %>
          <% if @recent_errors.empty? %>
            <p class="text-center text-neutral-500 py-4">No recent errors</p>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Custom Date Range Modal -->
<div class="hidden fixed inset-0 z-50 overflow-y-auto" 
     data-analytics-dashboard-target="customDateModal">
  <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" 
         data-action="click->analytics-dashboard#closeCustomDatePicker"></div>
    
    <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
      <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Select Custom Date Range</h3>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
            <input type="date" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500"
                   data-analytics-dashboard-target="customDateFrom">
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
            <input type="date" 
                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500"
                   data-analytics-dashboard-target="customDateTo">
          </div>
        </div>
      </div>
      
      <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
        <button type="button"
                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
                data-action="click->analytics-dashboard#applyCustomDateRange">
          Apply
        </button>
        <button type="button"
                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                data-action="click->analytics-dashboard#closeCustomDatePicker">
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>