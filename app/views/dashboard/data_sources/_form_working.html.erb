<%= form_with(model: [:dashboard, data_source], local: true, class: "space-y-6", html: { multipart: true }) do |form| %>
  <% if data_source.errors.any? %>
    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
      <h3 class="font-medium mb-2">Please fix the following errors:</h3>
      <ul class="list-disc list-inside text-sm">
        <% data_source.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <!-- Basic Information -->
  <div class="bg-white shadow rounded-lg p-6">
    <h2 class="text-lg font-medium text-neutral-900 mb-4">Basic Information</h2>
    
    <div class="space-y-4">
      <div>
        <%= form.label :name, class: "block text-sm font-medium text-neutral-700 mb-1" %>
        <%= form.text_field :name, class: "form-input w-full", placeholder: "e.g., Production Database" %>
        <p class="mt-1 text-sm text-neutral-500">A descriptive name for this data source</p>
      </div>

      <div>
        <%= form.label :description, class: "block text-sm font-medium text-neutral-700 mb-1" %>
        <%= form.text_area :description, rows: 3, class: "form-input w-full", placeholder: "Optional description..." %>
      </div>
    </div>
  </div>

  <!-- Connection Type -->
  <div class="bg-white shadow rounded-lg p-6">
    <h2 class="text-lg font-medium text-neutral-900 mb-4">Connection Type</h2>
    
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mb-6">
      <% DataSource::SOURCE_TYPES.each do |type| %>
        <label class="relative group">
          <%= form.radio_button :source_type, type, 
              class: "sr-only peer",
              onchange: "updateConnectionFields('#{type}')" %>
          <div class="flex flex-col p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 
                      border-neutral-200 hover:border-neutral-300 hover:shadow-sm
                      peer-checked:border-primary-600 peer-checked:bg-primary-50">
            <div class="flex items-center mb-2">
              <i class="<%= data_source_icon(type) %> text-xl mr-3 text-neutral-400 peer-checked:text-primary-600 group-hover:text-neutral-600"></i>
              <span class="text-sm font-medium text-neutral-900"><%= type.humanize %></span>
            </div>
            <span class="text-xs text-neutral-500">
              <%= case type
                  when 'postgresql', 'mysql' then 'Database connector'
                  when 'csv', 'excel', 'json', 'xml' then 'File import'
                  when 'api', 'webhook' then 'API integration'
                  when 's3', 'gcs', 'azure_blob' then 'Cloud storage'
                  when 'salesforce', 'stripe', 'shopify', 'quickbooks' then 'SaaS connector'
                  else 'Data source'
                  end %>
            </span>
          </div>
          <div class="absolute top-2 right-2 opacity-0 peer-checked:opacity-100 transition-opacity">
            <i class="fas fa-check-circle text-primary-600"></i>
          </div>
        </label>
      <% end %>
    </div>
  </div>

  <!-- Connection Settings -->
  <div class="bg-white shadow rounded-lg p-6">
    <h2 class="text-lg font-medium text-neutral-900 mb-4">Connection Settings</h2>
    
    <div id="connection-settings" class="space-y-4">
      <% if data_source.persisted? && data_source.source_type.present? %>
        <%= render partial: "dashboard/data_sources/connection_fields/#{data_source.source_type}", locals: { form: form, data_source: data_source } %>
      <% else %>
        <p class="text-neutral-500 text-sm">Select a connection type above to configure settings.</p>
      <% end %>
    </div>
  </div>

  <!-- Sync Settings -->
  <div class="bg-white shadow rounded-lg p-6">
    <h2 class="text-lg font-medium text-neutral-900 mb-4">Sync Settings</h2>
    
    <div class="space-y-4">
      <div>
        <%= form.label :sync_frequency, class: "block text-sm font-medium text-neutral-700 mb-1" %>
        <%= form.select :sync_frequency, 
            options_for_select([
              ['Manual Only', 'manual'],
              ['Real-time', 'realtime'],
              ['Every 5 minutes', 'every_5_minutes'],
              ['Every 15 minutes', 'every_15_minutes'],
              ['Every 30 minutes', 'every_30_minutes'],
              ['Hourly', 'hourly'],
              ['Every 6 hours', 'every_6_hours'],
              ['Daily', 'daily'],
              ['Weekly', 'weekly'],
              ['Monthly', 'monthly']
            ], data_source.sync_frequency),
            { prompt: 'Select frequency...' },
            class: "form-input w-full" %>
      </div>

      <div>
        <label class="flex items-center cursor-pointer">
          <%= form.check_box :active, class: "form-checkbox mr-2" %>
          <span class="text-sm text-neutral-700">Enable automatic syncing</span>
        </label>
        <p class="mt-1 text-sm text-neutral-500 ml-6">When enabled, data will sync automatically based on the frequency above</p>
      </div>
    </div>
  </div>

  <!-- Form Actions -->
  <div class="flex justify-end space-x-3">
    <%= link_to "Cancel", dashboard_data_sources_path, class: "btn btn--secondary" %>
    <%= form.submit data_source.persisted? ? "Update Data Source" : "Create Data Source", class: "btn btn--primary" %>
  </div>
<% end %>

<%= javascript_tag nonce: true do %>
  function updateConnectionFields(sourceType) {
    console.log('Updating connection fields for:', sourceType);
    const connectionSettings = document.getElementById('connection-settings');
    
    if (!connectionSettings) {
      console.error('Connection settings div not found');
      return;
    }
    
    // Show loading state
    connectionSettings.innerHTML = `
      <div class="flex items-center justify-center py-8">
        <div class="text-center">
          <i class="fas fa-spinner fa-spin text-2xl text-primary-500 mb-2"></i>
          <p class="text-neutral-500">Loading connection settings...</p>
        </div>
      </div>
    `;
    
    // Fetch the partial
    fetch(`/dashboard/data_sources/connection_fields/${sourceType}`)
      .then(response => {
        if (!response.ok) {
          throw new Error('Connection fields not found');
        }
        return response.text();
      })
      .then(html => {
        connectionSettings.innerHTML = html;
      })
      .catch(error => {
        console.error('Error loading connection fields:', error);
        connectionSettings.innerHTML = `
          <div class="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-lg">
            <p class="font-medium">Coming Soon</p>
            <p class="text-sm mt-1">${sourceType.charAt(0).toUpperCase() + sourceType.slice(1)} connector is under development.</p>
          </div>
        `;
      });
  }
<% end %>