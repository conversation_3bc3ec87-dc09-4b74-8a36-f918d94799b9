<div class="mb-8 bg-gradient-to-r from-amber-50 to-amber-50/50 border border-amber-200 rounded-2xl p-8 shadow-sm" role="alert" aria-live="polite">
  <div class="flex items-start">
    <div class="flex-shrink-0">
      <div class="w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center">
        <i class="fas fa-exclamation-triangle text-amber-600 text-lg" aria-hidden="true"></i>
      </div>
    </div>
    <div class="ml-6 flex-1">
      <h3 class="text-xl font-bold text-amber-900 mb-3">Configuration Issue Detected</h3>

      <% if @data_source.source_type == 'csv' %>
        <% if @data_source.connection_config_value('file_path').blank? %>
          <div class="bg-white rounded-xl p-6 mb-6 border border-amber-200">
            <h4 class="font-semibold text-amber-900 mb-3 flex items-center">
              <i class="fas fa-file-csv mr-2" aria-hidden="true"></i>
              Missing CSV File Path
            </h4>
            <p class="text-amber-800 leading-relaxed mb-4">
              The CSV file path is missing from the configuration. This might have occurred during the upload process
              or due to a temporary storage issue.
            </p>

            <% if @data_source.connection_config_value('original_filename').present? %>
              <div class="bg-amber-50 rounded-lg p-4 mb-4">
                <p class="text-sm font-medium text-amber-900 mb-1">Original File Detected:</p>
                <p class="text-amber-800 font-mono text-sm">
                  <%= @data_source.connection_config_value('original_filename') %>
                </p>
              </div>
            <% end %>

            <div class="flex flex-col sm:flex-row gap-3">
              <%= link_to edit_dashboard_data_source_path(@data_source),
                  class: "inline-flex items-center justify-center px-6 py-3 bg-amber-600 text-white font-semibold rounded-xl shadow-sm hover:bg-amber-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2",
                  'aria-label': 'Re-upload CSV file to fix configuration' do %>
                <i class="fas fa-upload mr-2" aria-hidden="true"></i>
                Re-upload CSV File
              <% end %>
            </div>
          </div>
        <% else %>
          <div class="bg-white rounded-xl p-6 mb-6 border border-amber-200">
            <h4 class="font-semibold text-amber-900 mb-3 flex items-center">
              <i class="fas fa-check-circle mr-2 text-emerald-500" aria-hidden="true"></i>
              File Path Configuration
            </h4>
            <div class="bg-slate-50 rounded-lg p-4 mb-4">
              <p class="text-sm font-medium text-slate-700 mb-1">Current File Path:</p>
              <p class="text-slate-900 font-mono text-sm break-all">
                <%= @data_source.connection_config_value('file_path') %>
              </p>
            </div>

            <% if @data_source.status == 'inactive' %>
              <p class="text-amber-800 mb-4">
                The file path exists but the connection is inactive. Try activating the connection to resolve this issue.
              </p>
              <div class="flex flex-col sm:flex-row gap-3">
                <%= button_to test_connection_dashboard_data_source_path(@data_source),
                    method: :post,
                    class: "inline-flex items-center justify-center px-6 py-3 bg-emerald-600 text-white font-semibold rounded-xl shadow-sm hover:bg-emerald-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2",
                    'aria-label': 'Test and activate the data source connection' do %>
                  <i class="fas fa-plug mr-2" aria-hidden="true"></i>
                  Activate Connection
                <% end %>
              </div>
            <% end %>
          </div>
        <% end %>
      <% end %>

      <!-- Advanced Configuration Details -->
      <details class="bg-white rounded-xl border border-amber-200 overflow-hidden">
        <summary class="px-6 py-4 cursor-pointer hover:bg-amber-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-inset">
          <span class="font-semibold text-amber-900 flex items-center">
            <i class="fas fa-code mr-2" aria-hidden="true"></i>
            View Raw Configuration
            <i class="fas fa-chevron-down ml-auto transition-transform duration-200" aria-hidden="true"></i>
          </span>
        </summary>
        <div class="px-6 pb-6">
          <div class="bg-slate-900 rounded-lg p-4 overflow-x-auto">
            <pre class="text-green-400 text-sm font-mono leading-relaxed"><%= JSON.pretty_generate(@data_source.connection_settings) rescue @data_source.connection_config %></pre>
          </div>
        </div>
      </details>
    </div>
  </div>
</div>