<% content_for :title, "New Data Source - Data Reflow" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50" data-controller="data-source-wizard">
  <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Enhanced Header -->
    <div class="mb-8">
      <!-- Breadcrumb Navigation -->
      <nav class="flex items-center text-sm mb-4" aria-label="Breadcrumb">
        <div class="flex items-center gap-2">
          <i class="fas fa-home text-neutral-400" aria-hidden="true"></i>
          <%= link_to "Dashboard", dashboard_root_path,
              class: "text-neutral-500 hover:text-primary-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-sm" %>
          <i class="fas fa-chevron-right mx-2 text-neutral-400 text-xs" aria-hidden="true"></i>
          <%= link_to "Data Sources", dashboard_data_sources_path,
              class: "text-neutral-500 hover:text-primary-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-sm" %>
          <i class="fas fa-chevron-right mx-2 text-neutral-400 text-xs" aria-hidden="true"></i>
          <span class="text-neutral-900 font-medium">New Data Source</span>
        </div>
      </nav>

      <!-- Page Header -->
      <div class="flex items-center gap-4 mb-6">
        <div class="flex-shrink-0">
          <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center shadow-lg">
            <i class="fas fa-plus text-white text-xl" aria-hidden="true"></i>
          </div>
        </div>
        <div>
          <h1 class="text-3xl font-bold text-neutral-900 tracking-tight">Connect a New Data Source</h1>
          <p class="text-neutral-600 mt-1">Choose from our library of connectors to start syncing your data</p>
        </div>
      </div>

      <!-- Progress Steps -->
      <div class="bg-white rounded-xl shadow-sm border border-neutral-200 p-6 mb-8">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <!-- Step 1: Choose Type -->
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white text-sm font-semibold"
                   data-data-source-wizard-target="step1Indicator">
                1
              </div>
              <div>
                <p class="text-sm font-medium text-neutral-900">Choose Type</p>
                <p class="text-xs text-neutral-500">Select data source type</p>
              </div>
            </div>

            <div class="w-12 h-0.5 bg-neutral-200" data-data-source-wizard-target="step1Connector"></div>

            <!-- Step 2: Configure -->
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-neutral-200 rounded-full flex items-center justify-center text-neutral-500 text-sm font-semibold"
                   data-data-source-wizard-target="step2Indicator">
                2
              </div>
              <div>
                <p class="text-sm font-medium text-neutral-500">Configure</p>
                <p class="text-xs text-neutral-400">Set up connection</p>
              </div>
            </div>

            <div class="w-12 h-0.5 bg-neutral-200" data-data-source-wizard-target="step2Connector"></div>

            <!-- Step 3: Test & Save -->
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-neutral-200 rounded-full flex items-center justify-center text-neutral-500 text-sm font-semibold"
                   data-data-source-wizard-target="step3Indicator">
                3
              </div>
              <div>
                <p class="text-sm font-medium text-neutral-500">Test & Save</p>
                <p class="text-xs text-neutral-400">Verify and complete</p>
              </div>
            </div>
          </div>

          <!-- Help Button -->
          <button type="button"
                  class="inline-flex items-center px-3 py-2 border border-neutral-300 rounded-lg text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200"
                  data-controller="tooltip"
                  data-tooltip-content-value="Need help choosing a data source? Check our documentation for detailed guides and examples."
                  aria-label="Get help with data source setup">
            <i class="fas fa-question-circle mr-2 text-neutral-500 text-sm" aria-hidden="true"></i>
            Need Help?
          </button>
        </div>
      </div>
    </div>

    <!-- Debug Script and Fallback -->
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        console.log('Page loaded');
        console.log('Stimulus loaded?', typeof Stimulus !== 'undefined');
        
        // Fallback implementation if Stimulus isn't working
        function initializeWizard() {
          const step1Content = document.querySelector('[data-data-source-wizard-target="step1Content"]');
          const step2Content = document.querySelector('[data-data-source-wizard-target="step2Content"]');
          const selectedTypeNameEl = document.querySelector('[data-data-source-wizard-target="selectedTypeName"]');
          const configFormEl = document.querySelector('[data-data-source-wizard-target="configurationForm"]');
          
          // Add click handlers to all source types
          document.querySelectorAll('[data-data-source-wizard-target="sourceType"]').forEach(function(el) {
            el.addEventListener('click', function() {
              const type = this.dataset.type;
              const name = this.querySelector('h3').textContent;
              
              console.log('Selected:', type, name);
              
              // Update UI
              selectedTypeNameEl.textContent = name;
              step1Content.classList.add('hidden');
              step2Content.classList.remove('hidden');
              
              // Update progress indicators
              document.querySelector('[data-data-source-wizard-target="step2Indicator"]').classList.remove('bg-neutral-200', 'text-neutral-500');
              document.querySelector('[data-data-source-wizard-target="step2Indicator"]').classList.add('bg-primary-600', 'text-white');
              document.querySelector('[data-data-source-wizard-target="step1Connector"]').classList.remove('bg-neutral-200');
              document.querySelector('[data-data-source-wizard-target="step1Connector"]').classList.add('bg-primary-600');
              
              // Load form
              loadConfigForm(type);
            });
          });
          
          // Back button
          document.querySelector('[data-action="click->data-source-wizard#goBackToStep1"]').addEventListener('click', function() {
            step2Content.classList.add('hidden');
            step1Content.classList.remove('hidden');
            
            // Reset progress
            document.querySelector('[data-data-source-wizard-target="step2Indicator"]').classList.add('bg-neutral-200', 'text-neutral-500');
            document.querySelector('[data-data-source-wizard-target="step2Indicator"]').classList.remove('bg-primary-600', 'text-white');
            document.querySelector('[data-data-source-wizard-target="step1Connector"]').classList.add('bg-neutral-200');
            document.querySelector('[data-data-source-wizard-target="step1Connector"]').classList.remove('bg-primary-600');
          });
          
          function loadConfigForm(type) {
            configFormEl.innerHTML = '<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-4xl text-primary-600"></i><p class="mt-4">Loading configuration form...</p></div>';
            
            fetch('/dashboard/data_sources/connection_fields/' + type)
              .then(response => response.text())
              .then(html => {
                const token = document.querySelector('meta[name="csrf-token"]').content;
                configFormEl.innerHTML = `
                  <form action="/dashboard/data_sources" method="post" data-turbo="false" enctype="multipart/form-data">
                    <input type="hidden" name="authenticity_token" value="${token}" />
                    <input type="hidden" name="data_source[source_type]" value="${type}" />
                    
                    <!-- Name field -->
                    <div class="mb-6">
                      <label for="data_source_name" class="block text-sm font-medium text-neutral-700 mb-1">
                        Data Source Name <span class="text-red-500">*</span>
                      </label>
                      <input type="text" 
                             id="data_source_name"
                             name="data_source[name]" 
                             class="form-input w-full" 
                             placeholder="e.g., Customer Data, Sales Report"
                             required>
                      <p class="mt-1 text-sm text-neutral-500">A descriptive name to identify this data source</p>
                    </div>
                    
                    ${html}
                    
                    <div class="mt-6 flex justify-end gap-3">
                      <button type="submit" name="commit" value="test" class="px-4 py-2 border border-primary-300 rounded-lg text-sm font-medium text-primary-700 bg-primary-50 hover:bg-primary-100">
                        <i class="fas fa-plug mr-2"></i>Test Connection
                      </button>
                      <button type="submit" name="commit" value="save" class="px-6 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800">
                        <i class="fas fa-save mr-2"></i>Save Data Source
                      </button>
                    </div>
                  </form>
                `;
              })
              .catch(error => {
                configFormEl.innerHTML = '<div class="text-red-600 p-4">Error loading form: ' + error.message + '</div>';
              });
          }
        }
        
        // Initialize after a short delay
        setTimeout(initializeWizard, 100);
      });
    </script>

    <!-- Data Source Type Selection -->
    <div class="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden" data-data-source-wizard-target="step1Content">
      <div class="px-6 py-5 border-b border-neutral-200 bg-gradient-to-r from-neutral-50 to-white">
        <h2 class="text-lg font-semibold text-neutral-900">Choose Your Data Source Type</h2>
        <p class="text-neutral-600 mt-1">Select the type of data source you want to connect</p>
      </div>

      <div class="p-6">
        <!-- Search and Filter -->
        <div class="mb-6">
          <div class="relative">
            <input type="text"
                   placeholder="Search data source types..."
                   class="w-full pl-12 pr-4 py-3 border border-neutral-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                   data-data-source-wizard-target="typeSearch"
                   data-action="input->data-source-wizard#searchTypes"
                   aria-label="Search data source types">
            <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
              <i class="fas fa-search text-neutral-400 text-sm" aria-hidden="true"></i>
            </div>
          </div>
        </div>

        <!-- Category Filters -->
        <div class="flex flex-wrap gap-2 mb-8">
          <button type="button"
                  class="px-4 py-2 text-sm font-medium rounded-lg border border-primary-300 bg-primary-50 text-primary-700"
                  data-data-source-wizard-target="categoryFilter"
                  data-category="all"
                  data-action="click->data-source-wizard#filterByCategory">
            All Sources
          </button>
          <button type="button"
                  class="px-4 py-2 text-sm font-medium rounded-lg border border-neutral-300 bg-white text-neutral-700 hover:bg-neutral-50"
                  data-data-source-wizard-target="categoryFilter"
                  data-category="database"
                  data-action="click->data-source-wizard#filterByCategory">
            <i class="fas fa-database mr-2 text-blue-500 text-sm" aria-hidden="true"></i>
            Databases
          </button>
          <button type="button"
                  class="px-4 py-2 text-sm font-medium rounded-lg border border-neutral-300 bg-white text-neutral-700 hover:bg-neutral-50"
                  data-data-source-wizard-target="categoryFilter"
                  data-category="file"
                  data-action="click->data-source-wizard#filterByCategory">
            <i class="fas fa-file-alt mr-2 text-orange-500 text-sm" aria-hidden="true"></i>
            Files
          </button>
          <button type="button"
                  class="px-4 py-2 text-sm font-medium rounded-lg border border-neutral-300 bg-white text-neutral-700 hover:bg-neutral-50"
                  data-data-source-wizard-target="categoryFilter"
                  data-category="api"
                  data-action="click->data-source-wizard#filterByCategory">
            <i class="fas fa-plug mr-2 text-purple-500 text-sm" aria-hidden="true"></i>
            APIs
          </button>
          <button type="button"
                  class="px-4 py-2 text-sm font-medium rounded-lg border border-neutral-300 bg-white text-neutral-700 hover:bg-neutral-50"
                  data-data-source-wizard-target="categoryFilter"
                  data-category="cloud"
                  data-action="click->data-source-wizard#filterByCategory">
            <i class="fas fa-cloud mr-2 text-cyan-500 text-sm" aria-hidden="true"></i>
            Cloud Storage
          </button>
          <button type="button"
                  class="px-4 py-2 text-sm font-medium rounded-lg border border-neutral-300 bg-white text-neutral-700 hover:bg-neutral-50"
                  data-data-source-wizard-target="categoryFilter"
                  data-category="saas"
                  data-action="click->data-source-wizard#filterByCategory">
            <i class="fas fa-store mr-2 text-pink-500 text-sm" aria-hidden="true"></i>
            SaaS Platforms
          </button>
        </div>

        <!-- Data Source Types Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" data-data-source-wizard-target="typesGrid">
          <!-- Database Sources -->
          <%
            database_sources = [
              { type: 'postgresql', name: 'PostgreSQL', icon: 'fa-database', color: 'from-blue-500 to-blue-600', description: 'Popular open-source relational database' },
              { type: 'mysql', name: 'MySQL', icon: 'fa-database', color: 'from-blue-500 to-blue-600', description: 'World\'s most popular open source database' },
              { type: 'sqlite', name: 'SQLite', icon: 'fa-database', color: 'from-blue-500 to-blue-600', description: 'Lightweight file-based database' },
              { type: 'sqlserver', name: 'SQL Server', icon: 'fa-database', color: 'from-blue-500 to-blue-600', description: 'Microsoft\'s enterprise database system' },
              { type: 'mongodb', name: 'MongoDB', icon: 'fa-server', color: 'from-green-500 to-green-600', description: 'Popular NoSQL document database' },
              { type: 'redis', name: 'Redis', icon: 'fa-server', color: 'from-red-500 to-red-600', description: 'In-memory data structure store' }
            ]

            file_sources = [
              { type: 'csv', name: 'CSV File', icon: 'fa-file-csv', color: 'from-orange-500 to-orange-600', description: 'Comma-separated values files' },
              { type: 'excel', name: 'Excel File', icon: 'fa-file-excel', color: 'from-green-500 to-green-600', description: 'Microsoft Excel spreadsheets' },
              { type: 'json', name: 'JSON File', icon: 'fa-file-code', color: 'from-yellow-500 to-yellow-600', description: 'JavaScript Object Notation files' },
              { type: 'xml', name: 'XML File', icon: 'fa-file-code', color: 'from-purple-500 to-purple-600', description: 'Extensible Markup Language files' }
            ]

            api_sources = [
              { type: 'api', name: 'REST API', icon: 'fa-plug', color: 'from-purple-500 to-purple-600', description: 'RESTful web service endpoints' },
              { type: 'webhook', name: 'Webhook', icon: 'fa-bolt', color: 'from-yellow-500 to-yellow-600', description: 'Real-time HTTP callbacks' }
            ]

            cloud_sources = [
              { type: 's3', name: 'Amazon S3', icon: 'fa-cloud', color: 'from-orange-500 to-orange-600', description: 'Amazon Simple Storage Service' },
              { type: 'gcs', name: 'Google Cloud Storage', icon: 'fa-cloud', color: 'from-blue-500 to-blue-600', description: 'Google\'s object storage service' },
              { type: 'azure_blob', name: 'Azure Blob Storage', icon: 'fa-cloud', color: 'from-blue-500 to-blue-600', description: 'Microsoft\'s object storage solution' }
            ]

            saas_sources = [
              { type: 'stripe', name: 'Stripe', icon: 'fa-credit-card', color: 'from-purple-500 to-purple-600', description: 'Payment processing platform' },
              { type: 'shopify', name: 'Shopify', icon: 'fa-shopping-cart', color: 'from-green-500 to-green-600', description: 'E-commerce platform' },
              { type: 'salesforce', name: 'Salesforce', icon: 'fa-cloud', color: 'from-blue-500 to-blue-600', description: 'Customer relationship management' }
            ]

            all_sources = database_sources + file_sources + api_sources + cloud_sources + saas_sources
          %>

          <% all_sources.each do |source| %>
            <div class="border border-neutral-200 rounded-lg p-4 hover:border-primary-300 hover:shadow-md transition-all duration-200 cursor-pointer"
                 data-data-source-wizard-target="sourceType"
                 data-type="<%= source[:type] %>"
                 data-category="<%= case source[:type]
                   when 'postgresql', 'mysql', 'sqlite', 'sqlserver', 'mongodb', 'redis', 'elasticsearch'
                     'database'
                   when 'csv', 'excel', 'json', 'xml'
                     'file'
                   when 'api', 'webhook'
                     'api'
                   when 's3', 'gcs', 'azure_blob'
                     'cloud'
                   when 'stripe', 'shopify', 'salesforce', 'quickbooks'
                     'saas'
                   end %>"
                 data-name="<%= source[:name].downcase %>"
                 data-action="click->data-source-wizard#selectType"
                 onclick="console.log('Clicked on <%= source[:type] %>')">

              <div class="flex items-start gap-3">
                <div class="w-10 h-10 bg-gradient-to-br <%= source[:color] %> rounded-lg flex items-center justify-center shadow-sm">
                  <i class="fas <%= source[:icon] %> text-white text-lg" aria-hidden="true"></i>
                </div>
                <div class="flex-1 min-w-0">
                  <h3 class="font-semibold text-neutral-900 text-sm"><%= source[:name] %></h3>
                  <p class="text-xs text-neutral-600 mt-1 leading-relaxed"><%= source[:description] %></p>

                  <!-- Popular badge for common sources -->
                  <% if ['postgresql', 'csv', 'api', 's3'].include?(source[:type]) %>
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 mt-2">
                      <i class="fas fa-star mr-1" aria-hidden="true"></i>
                      Popular
                    </span>
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>
        </div>

        <!-- No Results Message -->
        <div class="text-center py-12 hidden" data-data-source-wizard-target="noResults">
          <i class="fas fa-search text-4xl text-neutral-300 mb-4" aria-hidden="true"></i>
          <h3 class="text-lg font-medium text-neutral-900 mb-2">No matching data sources</h3>
          <p class="text-neutral-600">Try adjusting your search or filter criteria</p>
        </div>
      </div>
    </div>

    <!-- Configuration Step (Hidden initially) -->
    <div class="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden hidden" data-data-source-wizard-target="step2Content">
      <div class="px-6 py-5 border-b border-neutral-200 bg-gradient-to-r from-neutral-50 to-white">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-lg font-semibold text-neutral-900">Configure Connection</h2>
            <p class="text-neutral-600 mt-1">Set up your <span data-data-source-wizard-target="selectedTypeName"></span> connection</p>
          </div>
          <button type="button"
                  class="inline-flex items-center px-3 py-2 border border-neutral-300 rounded-lg text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200"
                  data-action="click->data-source-wizard#goBackToStep1">
            <i class="fas fa-arrow-left mr-2 text-sm" aria-hidden="true"></i>
            Back to Type Selection
          </button>
        </div>
      </div>

      <div class="p-6">
        <!-- Dynamic form content will be loaded here -->
        <div data-data-source-wizard-target="configurationForm">
          <!-- Form content will be dynamically loaded based on selected type -->
        </div>
      </div>
    </div>
  </div>
</div>
