<% content_for :title, "Data Sources - Data Reflow" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50" data-controller="data-sources-manager">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Enhanced Header with Search and Actions -->
    <div class="mb-8">
      <!-- Breadcrumb Navigation -->
      <nav class="flex items-center text-sm mb-4" aria-label="Breadcrumb">
        <div class="flex items-center gap-2">
          <i class="fas fa-home text-neutral-400" aria-hidden="true"></i>
          <%= link_to "Dashboard", dashboard_root_path,
              class: "text-neutral-500 hover:text-primary-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-sm" %>
          <i class="fas fa-chevron-right mx-2 text-neutral-400 text-xs" aria-hidden="true"></i>
          <span class="text-neutral-900 font-medium">Data Sources</span>
        </div>
      </nav>

      <!-- Page Header -->
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div class="flex items-center gap-4">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center shadow-lg">
              <i class="fas fa-database text-white text-xl" aria-hidden="true"></i>
            </div>
          </div>
          <div>
            <h1 class="text-3xl font-bold text-neutral-900 tracking-tight">Data Sources</h1>
            <p class="text-neutral-600 mt-1">Connect, manage, and monitor your data connections</p>
          </div>
        </div>

        <!-- Header Actions -->
        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          <!-- Search -->
          <div class="relative w-full sm:w-80">
            <input type="text"
                   placeholder="Search data sources..."
                   class="w-full pl-12 pr-4 py-3 border border-neutral-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white shadow-sm"
                   data-data-sources-manager-target="searchInput"
                   data-action="input->data-sources-manager#search"
                   aria-label="Search data sources">
            <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
              <i class="fas fa-search text-neutral-400 text-sm" aria-hidden="true"></i>
            </div>
          </div>

          <!-- Filter Dropdown -->
          <div class="relative" data-controller="dropdown">
            <button type="button"
                    class="inline-flex items-center px-4 py-3 border border-neutral-300 rounded-lg text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200 shadow-sm"
                    data-action="click->dropdown#toggle"
                    aria-haspopup="true"
                    aria-expanded="false">
              <i class="fas fa-filter mr-2 text-neutral-500 text-sm" aria-hidden="true"></i>
              Filter
              <i class="fas fa-chevron-down ml-2 text-neutral-400 text-xs" aria-hidden="true"></i>
            </button>

            <div class="hidden absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-neutral-200 z-20"
                 data-dropdown-target="menu">
              <div class="py-2">
                <div class="px-4 py-2 text-xs font-semibold text-neutral-500 uppercase tracking-wider border-b border-neutral-100">
                  Filter by Status
                </div>
                <button type="button"
                        class="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100"
                        data-action="click->data-sources-manager#filterByStatus"
                        data-status="all">
                  <i class="fas fa-list mr-2 text-neutral-500 text-sm" aria-hidden="true"></i>
                  All Sources
                </button>
                <button type="button"
                        class="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100"
                        data-action="click->data-sources-manager#filterByStatus"
                        data-status="active">
                  <i class="fas fa-check-circle mr-2 text-green-500 text-sm" aria-hidden="true"></i>
                  Active
                </button>
                <button type="button"
                        class="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100"
                        data-action="click->data-sources-manager#filterByStatus"
                        data-status="error">
                  <i class="fas fa-exclamation-triangle mr-2 text-red-500 text-sm" aria-hidden="true"></i>
                  With Errors
                </button>
                <button type="button"
                        class="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100"
                        data-action="click->data-sources-manager#filterByStatus"
                        data-status="syncing">
                  <i class="fas fa-sync-alt mr-2 text-blue-500 text-sm" aria-hidden="true"></i>
                  Syncing
                </button>
                <div class="border-t border-neutral-100 mt-2 pt-2">
                  <div class="px-4 py-2 text-xs font-semibold text-neutral-500 uppercase tracking-wider">
                    Filter by Type
                  </div>
                  <button type="button"
                          class="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100"
                          data-action="click->data-sources-manager#filterByType"
                          data-type="database">
                    <i class="fas fa-database mr-2 text-blue-500 text-sm" aria-hidden="true"></i>
                    Databases
                  </button>
                  <button type="button"
                          class="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100"
                          data-action="click->data-sources-manager#filterByType"
                          data-type="file">
                    <i class="fas fa-file-alt mr-2 text-green-500 text-sm" aria-hidden="true"></i>
                    Files
                  </button>
                  <button type="button"
                          class="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100"
                          data-action="click->data-sources-manager#filterByType"
                          data-type="api">
                    <i class="fas fa-plug mr-2 text-purple-500 text-sm" aria-hidden="true"></i>
                    APIs
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Add Data Source Button -->
          <%= link_to new_dashboard_data_source_path,
              class: "inline-flex items-center px-6 py-3 border border-transparent rounded-lg text-sm font-semibold text-white bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl",
              'aria-label': "Add new data source" do %>
            <i class="fas fa-plus mr-2 text-sm" aria-hidden="true"></i>
            Add Data Source
          <% end %>
        </div>
      </div>
    </div>

    <!-- Enhanced Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Total Sources -->
      <div class="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden hover:shadow-md transition-shadow duration-200">
        <div class="p-6">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center gap-3 mb-2">
                <div class="w-10 h-10 bg-gradient-to-br from-slate-500 to-slate-600 rounded-lg flex items-center justify-center">
                  <i class="fas fa-database text-white text-lg" aria-hidden="true"></i>
                </div>
                <div>
                  <p class="text-sm font-medium text-neutral-600">Total Sources</p>
                  <p class="text-2xl font-bold text-neutral-900"><%= @stats[:total] %></p>
                </div>
              </div>
            </div>
            <button type="button"
                    class="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full"
                    data-controller="tooltip"
                    data-tooltip-content-value="Total number of configured data sources"
                    aria-label="Help: Total sources information">
              <i class="fas fa-info-circle text-sm" aria-hidden="true"></i>
            </button>
          </div>
          <div class="mt-3 flex items-center text-sm">
            <span class="text-neutral-500">All configured connections</span>
          </div>
        </div>
      </div>

      <!-- Active Sources -->
      <div class="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden hover:shadow-md transition-shadow duration-200">
        <div class="p-6">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center gap-3 mb-2">
                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                  <i class="fas fa-check-circle text-white text-lg" aria-hidden="true"></i>
                </div>
                <div>
                  <p class="text-sm font-medium text-neutral-600">Active</p>
                  <p class="text-2xl font-bold text-neutral-900"><%= @stats[:active] %></p>
                </div>
              </div>
            </div>
            <button type="button"
                    class="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full"
                    data-controller="tooltip"
                    data-tooltip-content-value="Data sources that are connected and operational"
                    aria-label="Help: Active sources information">
              <i class="fas fa-info-circle text-sm" aria-hidden="true"></i>
            </button>
          </div>
          <div class="mt-3 flex items-center text-sm">
            <% if @stats[:total] > 0 %>
              <% percentage = (@stats[:active].to_f / @stats[:total] * 100).round(1) %>
              <span class="text-green-600 font-medium"><%= percentage %>%</span>
              <span class="text-neutral-500 ml-1">operational</span>
            <% else %>
              <span class="text-neutral-500">No sources yet</span>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Syncing Sources -->
      <div class="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden hover:shadow-md transition-shadow duration-200">
        <div class="p-6">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center gap-3 mb-2">
                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <i class="fas fa-sync-alt text-white text-lg <%= 'animate-spin' if @stats[:syncing] > 0 %>" aria-hidden="true"></i>
                </div>
                <div>
                  <p class="text-sm font-medium text-neutral-600">Syncing</p>
                  <p class="text-2xl font-bold text-neutral-900"><%= @stats[:syncing] %></p>
                </div>
              </div>
            </div>
            <button type="button"
                    class="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full"
                    data-controller="tooltip"
                    data-tooltip-content-value="Data sources currently synchronizing data"
                    aria-label="Help: Syncing sources information">
              <i class="fas fa-info-circle text-sm" aria-hidden="true"></i>
            </button>
          </div>
          <div class="mt-3 flex items-center text-sm">
            <% if @stats[:syncing] > 0 %>
              <span class="text-blue-600 font-medium">In progress</span>
            <% else %>
              <span class="text-neutral-500">All up to date</span>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Error Sources -->
      <div class="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden hover:shadow-md transition-shadow duration-200">
        <div class="p-6">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center gap-3 mb-2">
                <div class="w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                  <i class="fas fa-exclamation-triangle text-white text-lg" aria-hidden="true"></i>
                </div>
                <div>
                  <p class="text-sm font-medium text-neutral-600">Errors</p>
                  <p class="text-2xl font-bold text-neutral-900"><%= @stats[:with_errors] %></p>
                </div>
              </div>
            </div>
            <button type="button"
                    class="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full"
                    data-controller="tooltip"
                    data-tooltip-content-value="Data sources with connection or sync errors"
                    aria-label="Help: Error sources information">
              <i class="fas fa-info-circle text-sm" aria-hidden="true"></i>
            </button>
          </div>
          <div class="mt-3 flex items-center text-sm">
            <% if @stats[:with_errors] > 0 %>
              <span class="text-red-600 font-medium">Need attention</span>
            <% else %>
              <span class="text-neutral-500">All healthy</span>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Data Sources List with Dual View -->
    <div class="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden" data-controller="view-toggle">
      <% if @data_sources.any? %>
        <!-- List Header with View Toggle -->
        <div class="px-6 py-4 border-b border-neutral-200 bg-gradient-to-r from-neutral-50 to-white">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div class="flex items-center gap-3">
              <h2 class="text-lg font-semibold text-neutral-900">
                Your Data Sources
                <span class="inline-flex items-center ml-2 px-2 py-1 bg-primary-100 text-primary-800 text-xs font-medium rounded-full">
                  <span data-data-sources-manager-target="visibleCount"><%= @data_sources.length %></span> sources
                </span>
              </h2>
            </div>

            <!-- View Toggle -->
            <div class="flex items-center gap-2">
              <span class="text-sm text-neutral-600">View:</span>
              <div class="bg-neutral-100 rounded-lg p-1 flex" role="tablist" aria-label="Data source view options">
                <button type="button"
                        class="px-3 py-1 text-sm font-medium rounded-md bg-white text-neutral-900 shadow-sm"
                        role="tab"
                        aria-selected="true"
                        aria-controls="list-view"
                        data-view-toggle-target="tableBtn"
                        data-action="click->view-toggle#showTable">
                  <i class="fas fa-list mr-1 text-xs" aria-hidden="true"></i>
                  List
                </button>
                <button type="button"
                        class="px-3 py-1 text-sm font-medium rounded-md text-neutral-600 hover:text-neutral-900"
                        role="tab"
                        aria-selected="false"
                        aria-controls="grid-view"
                        data-view-toggle-target="cardBtn"
                        data-action="click->view-toggle#showCards">
                  <i class="fas fa-th-large mr-1 text-xs" aria-hidden="true"></i>
                  Grid
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- List View -->
        <div id="list-view" data-view-toggle-target="tableView" class="block">
          <div class="divide-y divide-neutral-200" data-data-sources-manager-target="listContainer">
            <% @data_sources.each do |data_source| %>
              <div class="hover:bg-neutral-50 transition-colors duration-150"
                   data-data-sources-manager-target="sourceItem"
                   data-status="<%= data_source.status %>"
                   data-type="<%= data_source.source_type %>"
                   data-name="<%= data_source.name.downcase %>">
                <%= link_to dashboard_data_source_path(data_source),
                    class: "block px-6 py-5 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-inset",
                    'aria-label': "View details for #{data_source.name}" do %>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-4 flex-1 min-w-0">
                      <!-- Enhanced Icon -->
                      <div class="flex-shrink-0">
                        <%
                          icon_config = case data_source.source_type
                          when 'postgresql', 'mysql', 'sqlite', 'sqlserver'
                            { icon: 'fa-database', color: 'from-blue-500 to-blue-600' }
                          when 'mongodb', 'redis', 'elasticsearch'
                            { icon: 'fa-server', color: 'from-green-500 to-green-600' }
                          when 'csv', 'excel', 'json', 'xml'
                            { icon: 'fa-file-alt', color: 'from-orange-500 to-orange-600' }
                          when 'api', 'webhook'
                            { icon: 'fa-plug', color: 'from-purple-500 to-purple-600' }
                          when 's3', 'gcs', 'azure_blob'
                            { icon: 'fa-cloud', color: 'from-cyan-500 to-cyan-600' }
                          when 'quickbooks', 'stripe', 'shopify', 'salesforce'
                            { icon: 'fa-store', color: 'from-pink-500 to-pink-600' }
                          else
                            { icon: 'fa-database', color: 'from-gray-500 to-gray-600' }
                          end
                        %>
                        <div class="w-12 h-12 bg-gradient-to-br <%= icon_config[:color] %> rounded-lg flex items-center justify-center shadow-sm">
                          <i class="fas <%= icon_config[:icon] %> text-white text-lg" aria-hidden="true"></i>
                        </div>
                      </div>

                      <!-- Source Info -->
                      <div class="flex-1 min-w-0">
                        <div class="flex items-center gap-3 mb-1">
                          <h3 class="text-lg font-semibold text-neutral-900 truncate">
                            <%= data_source.name %>
                          </h3>
                          <!-- Enhanced Status Badge -->
                          <% case data_source.status %>
                          <% when 'active' %>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-800 border border-green-200">
                              <span class="w-1.5 h-1.5 bg-green-500 rounded-full mr-2 animate-pulse" aria-hidden="true"></span>
                              Active
                            </span>
                          <% when 'syncing' %>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-blue-100 text-blue-800 border border-blue-200">
                              <i class="fas fa-sync-alt animate-spin mr-2" aria-hidden="true"></i>
                              Syncing
                            </span>
                          <% when 'error' %>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-red-100 text-red-800 border border-red-200">
                              <i class="fas fa-exclamation-triangle mr-2" aria-hidden="true"></i>
                              Error
                            </span>
                          <% when 'inactive' %>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gray-100 text-gray-800 border border-gray-200">
                              <i class="fas fa-pause-circle mr-2" aria-hidden="true"></i>
                              Inactive
                            </span>
                          <% when 'maintenance' %>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-yellow-100 text-yellow-800 border border-yellow-200">
                              <i class="fas fa-tools mr-2" aria-hidden="true"></i>
                              Maintenance
                            </span>
                          <% end %>
                        </div>

                        <div class="flex flex-wrap items-center gap-4 text-sm text-neutral-600">
                          <div class="flex items-center gap-1">
                            <i class="fas fa-tag text-neutral-400" aria-hidden="true"></i>
                            <span><%= data_source.source_type_text %></span>
                          </div>
                          <div class="flex items-center gap-1">
                            <i class="fas fa-clock text-neutral-400" aria-hidden="true"></i>
                            <span><%= data_source.sync_frequency_text %></span>
                          </div>
                          <div class="flex items-center gap-1">
                            <i class="fas fa-chart-bar text-neutral-400" aria-hidden="true"></i>
                            <span><%= data_source.formatted_row_count %> rows</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Right Side Info -->
                    <div class="flex items-center gap-4">
                      <div class="text-right">
                        <div class="text-sm font-medium text-neutral-900">
                          <% if data_source.last_sync_at %>
                            Last sync
                          <% else %>
                            Never synced
                          <% end %>
                        </div>
                        <div class="text-sm text-neutral-500">
                          <% if data_source.last_sync_at %>
                            <%= time_ago_in_words(data_source.last_sync_at) %> ago
                          <% else %>
                            <span class="text-yellow-600">Setup required</span>
                          <% end %>
                        </div>
                      </div>

                      <div class="flex-shrink-0">
                        <i class="fas fa-chevron-right text-neutral-400" aria-hidden="true"></i>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Grid View -->
        <div id="grid-view" data-view-toggle-target="cardView" class="hidden">
          <div class="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" data-data-sources-manager-target="gridContainer">
            <% @data_sources.each do |data_source| %>
              <div class="bg-white border border-neutral-200 rounded-lg hover:shadow-md transition-all duration-200 hover:border-primary-300"
                   data-data-sources-manager-target="sourceCard"
                   data-status="<%= data_source.status %>"
                   data-type="<%= data_source.source_type %>"
                   data-name="<%= data_source.name.downcase %>">
                <%= link_to dashboard_data_source_path(data_source),
                    class: "block p-6 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg",
                    'aria-label': "View details for #{data_source.name}" do %>

                  <!-- Card Header -->
                  <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center gap-3">
                      <%
                        icon_config = case data_source.source_type
                        when 'postgresql', 'mysql', 'sqlite', 'sqlserver'
                          { icon: 'fa-database', color: 'from-blue-500 to-blue-600' }
                        when 'mongodb', 'redis', 'elasticsearch'
                          { icon: 'fa-server', color: 'from-green-500 to-green-600' }
                        when 'csv', 'excel', 'json', 'xml'
                          { icon: 'fa-file-alt', color: 'from-orange-500 to-orange-600' }
                        when 'api', 'webhook'
                          { icon: 'fa-plug', color: 'from-purple-500 to-purple-600' }
                        when 's3', 'gcs', 'azure_blob'
                          { icon: 'fa-cloud', color: 'from-cyan-500 to-cyan-600' }
                        when 'quickbooks', 'stripe', 'shopify', 'salesforce'
                          { icon: 'fa-store', color: 'from-pink-500 to-pink-600' }
                        else
                          { icon: 'fa-database', color: 'from-gray-500 to-gray-600' }
                        end
                      %>
                      <div class="w-10 h-10 bg-gradient-to-br <%= icon_config[:color] %> rounded-lg flex items-center justify-center shadow-sm">
                        <i class="fas <%= icon_config[:icon] %> text-white text-lg" aria-hidden="true"></i>
                      </div>
                      <div>
                        <h3 class="font-semibold text-neutral-900 text-lg"><%= data_source.name %></h3>
                        <p class="text-sm text-neutral-600"><%= data_source.source_type_text %></p>
                      </div>
                    </div>

                    <!-- Status Badge -->
                    <% case data_source.status %>
                    <% when 'active' %>
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-green-100 text-green-800 border border-green-200">
                        <span class="w-1.5 h-1.5 bg-green-500 rounded-full mr-1 animate-pulse" aria-hidden="true"></span>
                        Active
                      </span>
                    <% when 'syncing' %>
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-blue-100 text-blue-800 border border-blue-200">
                        <i class="fas fa-sync-alt animate-spin mr-1" aria-hidden="true"></i>
                        Syncing
                      </span>
                    <% when 'error' %>
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-red-100 text-red-800 border border-red-200">
                        <i class="fas fa-exclamation-triangle mr-1" aria-hidden="true"></i>
                        Error
                      </span>
                    <% when 'inactive' %>
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-gray-100 text-gray-800 border border-gray-200">
                        <i class="fas fa-pause-circle mr-1" aria-hidden="true"></i>
                        Inactive
                      </span>
                    <% when 'maintenance' %>
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-yellow-100 text-yellow-800 border border-yellow-200">
                        <i class="fas fa-tools mr-1" aria-hidden="true"></i>
                        Maintenance
                      </span>
                    <% end %>
                  </div>

                  <!-- Card Content -->
                  <div class="space-y-3">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p class="text-neutral-500 mb-1">Sync Frequency</p>
                        <p class="font-medium text-neutral-900"><%= data_source.sync_frequency_text %></p>
                      </div>
                      <div>
                        <p class="text-neutral-500 mb-1">Row Count</p>
                        <p class="font-medium text-neutral-900"><%= data_source.formatted_row_count %></p>
                      </div>
                    </div>

                    <div class="pt-3 border-t border-neutral-100">
                      <div class="flex items-center justify-between text-sm">
                        <span class="text-neutral-500">Last sync:</span>
                        <span class="font-medium text-neutral-900">
                          <% if data_source.last_sync_at %>
                            <%= time_ago_in_words(data_source.last_sync_at) %> ago
                          <% else %>
                            <span class="text-yellow-600">Never</span>
                          <% end %>
                        </span>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      <% else %>
        <!-- Enhanced Empty State -->
        <div class="text-center py-16">
          <div class="w-20 h-20 bg-gradient-to-br from-primary-100 to-primary-200 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-database text-3xl text-primary-600" aria-hidden="true"></i>
          </div>
          <h3 class="text-xl font-semibold text-neutral-900 mb-2">No Data Sources Yet</h3>
          <p class="text-neutral-600 mb-8 max-w-md mx-auto">
            Connect your first data source to start syncing and managing your data.
            Choose from databases, files, APIs, and popular SaaS platforms.
          </p>

          <!-- Quick Start Options -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <%= link_to new_dashboard_data_source_path,
                class: "inline-flex items-center px-6 py-3 border border-transparent rounded-lg text-sm font-semibold text-white bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl" do %>
              <i class="fas fa-plus mr-2 text-sm" aria-hidden="true"></i>
              Add Your First Data Source
            <% end %>

            <button type="button"
                    class="inline-flex items-center px-6 py-3 border border-neutral-300 rounded-lg text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200"
                    data-controller="tooltip"
                    data-tooltip-content-value="Learn about supported data sources and connection types">
              <i class="fas fa-book mr-2 text-neutral-500 text-sm" aria-hidden="true"></i>
              View Documentation
            </button>
          </div>

          <!-- Popular Source Types -->
          <div class="mt-12">
            <p class="text-sm font-medium text-neutral-700 mb-4">Popular data sources:</p>
            <div class="flex flex-wrap justify-center gap-3">
              <div class="flex items-center gap-2 px-3 py-2 bg-blue-50 border border-blue-200 rounded-lg">
                <i class="fas fa-database text-blue-600" aria-hidden="true"></i>
                <span class="text-sm text-blue-800">PostgreSQL</span>
              </div>
              <div class="flex items-center gap-2 px-3 py-2 bg-orange-50 border border-orange-200 rounded-lg">
                <i class="fas fa-file-csv text-orange-600" aria-hidden="true"></i>
                <span class="text-sm text-orange-800">CSV Files</span>
              </div>
              <div class="flex items-center gap-2 px-3 py-2 bg-purple-50 border border-purple-200 rounded-lg">
                <i class="fas fa-plug text-purple-600" aria-hidden="true"></i>
                <span class="text-sm text-purple-800">REST API</span>
              </div>
              <div class="flex items-center gap-2 px-3 py-2 bg-cyan-50 border border-cyan-200 rounded-lg">
                <i class="fas fa-cloud text-cyan-600" aria-hidden="true"></i>
                <span class="text-sm text-cyan-800">Amazon S3</span>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>