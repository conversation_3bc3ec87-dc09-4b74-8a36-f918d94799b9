<% content_for :title, "Simple CSV Test" %>

<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <h1 class="text-2xl font-bold mb-4">Simple CSV Connection Test</h1>
  
  <div class="bg-white shadow rounded-lg p-6">
    <h2 class="text-lg font-medium mb-4">Select Connection Type</h2>
    
    <div class="space-y-3">
      <label class="flex items-center">
        <input type="radio" name="source_type" value="csv" class="mr-2" id="csv-radio">
        <span>CSV File</span>
      </label>
      
      <label class="flex items-center">
        <input type="radio" name="source_type" value="postgresql" class="mr-2" id="pg-radio">
        <span>PostgreSQL</span>
      </label>
    </div>
    
    <div id="connection-fields" class="mt-6 p-4 border rounded" style="display: none;">
      <!-- Fields will appear here -->
    </div>
  </div>
</div>

<%= javascript_tag nonce: true do %>
  document.addEventListener('DOMContentLoaded', function() {
    console.log('Simple test loaded');
    
    const csvRadio = document.getElementById('csv-radio');
    const pgRadio = document.getElementById('pg-radio');
    const fieldsDiv = document.getElementById('connection-fields');
    
    function showCSVFields() {
      console.log('CSV selected');
      fieldsDiv.style.display = 'block';
      fieldsDiv.innerHTML = `
        <h3 class="font-medium mb-3">CSV Connection Settings</h3>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium mb-1">Upload CSV File</label>
            <input type="file" accept=".csv" class="form-input w-full">
          </div>
          <div>
            <label class="block text-sm font-medium mb-1">Or provide URL</label>
            <input type="text" placeholder="https://example.com/data.csv" class="form-input w-full">
          </div>
          <div>
            <label class="block text-sm font-medium mb-1">Delimiter</label>
            <select class="form-input w-full">
              <option value=",">Comma (,)</option>
              <option value=";">Semicolon (;)</option>
              <option value="\t">Tab</option>
            </select>
          </div>
        </div>
      `;
    }
    
    function showPGFields() {
      console.log('PostgreSQL selected');
      fieldsDiv.style.display = 'block';
      fieldsDiv.innerHTML = `
        <h3 class="font-medium mb-3">PostgreSQL Connection Settings</h3>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium mb-1">Host</label>
            <input type="text" placeholder="localhost" class="form-input w-full">
          </div>
          <div>
            <label class="block text-sm font-medium mb-1">Port</label>
            <input type="number" placeholder="5432" class="form-input w-full">
          </div>
          <div>
            <label class="block text-sm font-medium mb-1">Database</label>
            <input type="text" placeholder="mydb" class="form-input w-full">
          </div>
        </div>
      `;
    }
    
    csvRadio.addEventListener('click', showCSVFields);
    pgRadio.addEventListener('click', showPGFields);
    
    console.log('Event listeners added');
  });
<% end %>