<% content_for :title, "Edit #{@data_source.name}" %>

<div class="max-w-4xl mx-auto">
  <!-- Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-slate-900">Edit Data Source</h1>
        <p class="mt-2 text-slate-600">Update the configuration for <%= @data_source.name %></p>
      </div>

      <%= link_to dashboard_data_source_path(@data_source),
          class: "inline-flex items-center px-4 py-2 border border-slate-300 text-slate-700 bg-white rounded-lg hover:bg-slate-50 transition-colors duration-200" do %>
        <i class="fas fa-arrow-left mr-2" aria-hidden="true"></i>
        Back to Data Source
      <% end %>
    </div>
  </div>

  <!-- Edit Form -->
  <div class="bg-white rounded-xl shadow-sm border border-slate-200">
    <div class="p-8">
      <%= form_with(model: [:dashboard, @data_source], local: true, class: "space-y-6") do |form| %>

        <!-- Error Messages -->
        <% if @data_source.errors.any? %>
          <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fas fa-exclamation-circle text-red-400" aria-hidden="true"></i>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  <%= pluralize(@data_source.errors.count, "error") %> prohibited this data source from being saved:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    <% @data_source.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Name Field -->
        <div>
          <%= form.label :name, class: "block text-sm font-medium text-slate-700 mb-2" %>
          <%= form.text_field :name,
              class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" %>
        </div>

        <!-- Description Field -->
        <div>
          <%= form.label :description, class: "block text-sm font-medium text-slate-700 mb-2" %>
          <%= form.text_area :description,
              rows: 3,
              class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" %>
        </div>

        <!-- Connection Configuration -->
        <div>
          <h3 class="text-lg font-medium text-slate-900 mb-4">Connection Configuration</h3>
          <div id="connection-fields">
            <%= render partial: "dashboard/data_sources/connection_fields/#{@data_source.source_type}", locals: { form: form, data_source: @data_source } %>
          </div>
        </div>

        <!-- Sync Settings -->
        <div>
          <h3 class="text-lg font-medium text-slate-900 mb-4">Sync Settings</h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Sync Frequency -->
            <div>
              <%= form.label :sync_frequency, class: "block text-sm font-medium text-slate-700 mb-2" %>
              <%= form.select :sync_frequency,
                  options_for_select([
                    ['Manual', 'manual'],
                    ['Every 5 minutes', 'every_5_minutes'],
                    ['Every 15 minutes', 'every_15_minutes'],
                    ['Every 30 minutes', 'every_30_minutes'],
                    ['Hourly', 'hourly'],
                    ['Every 6 hours', 'every_6_hours'],
                    ['Daily', 'daily'],
                    ['Weekly', 'weekly'],
                    ['Monthly', 'monthly']
                  ], @data_source.sync_frequency),
                  { prompt: 'Select frequency' },
                  { class: "w-full px-3 py-2 border border-slate-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" } %>
            </div>

            <!-- Active Status -->
            <div class="flex items-center">
              <%= form.check_box :active, class: "h-4 w-4 text-primary-600 focus:ring-primary-500 border-slate-300 rounded" %>
              <%= form.label :active, "Enable automatic syncing", class: "ml-2 block text-sm text-slate-700" %>
            </div>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-4 pt-6 border-t border-slate-200">
          <%= link_to dashboard_data_source_path(@data_source),
              class: "px-4 py-2 border border-slate-300 text-slate-700 bg-white rounded-lg hover:bg-slate-50 transition-colors duration-200" do %>
            Cancel
          <% end %>

          <%= form.submit "Update Data Source",
              class: "px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2" %>
        </div>
      <% end %>
    </div>
  </div>
</div>
