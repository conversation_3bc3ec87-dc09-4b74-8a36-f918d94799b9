<% content_for :title, "Stimulus Test" %>

<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <h1>Testing Stimulus</h1>
  
  <div data-controller="test" class="p-4 mt-4">
    If you see this text, Stimulus is NOT working.
  </div>
  
  <div class="mt-8 p-4 border">
    <h2>Manual Test</h2>
    <button onclick="alert('Basic JS works')" class="btn btn--primary">Test Basic JS</button>
  </div>
  
  <div class="mt-8 p-4 border">
    <h2>Check Console</h2>
    <p>Open browser console (F12) and check for:</p>
    <ul>
      <li>- Stimulus application started!</li>
      <li>- Analytics module loaded</li>
      <li>- Forms module loaded</li>
    </ul>
  </div>
</div>

<%= javascript_tag nonce: true do %>
  document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded - checking for Stimulus...');
    if (typeof Stimulus !== 'undefined') {
      console.log('Stimulus is available:', Stimulus);
    } else {
      console.error('Stimulus is NOT available!');
    }
  });
<% end %>