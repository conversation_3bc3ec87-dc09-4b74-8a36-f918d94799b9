<% content_for :title, "Schema - #{@data_source.name}" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Enhanced Header with Help -->
  <div class="mb-8">
    <!-- Breadcrumb Navigation -->
    <nav class="flex items-center text-sm mb-4" aria-label="Breadcrumb">
      <%= link_to "Data Sources", dashboard_data_sources_path,
          class: "text-neutral-500 hover:text-primary-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-sm",
          'aria-label': "Navigate to Data Sources list" %>
      <i class="fas fa-chevron-right mx-2 text-neutral-400 text-xs" aria-hidden="true"></i>
      <%= link_to @data_source.name, dashboard_data_source_path(@data_source),
          class: "text-neutral-500 hover:text-primary-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-sm",
          'aria-label': "Navigate to #{@data_source.name} details" %>
      <i class="fas fa-chevron-right mx-2 text-neutral-400 text-xs" aria-hidden="true"></i>
      <span class="text-neutral-900 font-medium">Schema</span>
    </nav>

    <!-- Page Header with Help -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div class="flex items-center gap-3">
        <div class="flex-shrink-0">
          <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
            <i class="fas fa-table text-white text-lg" aria-hidden="true"></i>
          </div>
        </div>
        <div>
          <h1 class="text-2xl font-bold text-neutral-900">Data Schema</h1>
          <p class="text-sm text-neutral-600 mt-1">Structure and format of your data fields</p>
        </div>
      </div>

      <!-- Help Button -->
      <button type="button"
              class="inline-flex items-center px-3 py-2 border border-neutral-300 rounded-lg text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200"
              data-controller="tooltip"
              data-tooltip-content-value="Schema shows the structure of your data including column names, data types, and sample values. This helps you understand how your data is organized."
              aria-label="Get help about data schema">
        <i class="fas fa-question-circle mr-2 text-neutral-500" aria-hidden="true"></i>
        What is Schema?
      </button>
    </div>
  </div>

  <% if @schema_info[:success] && @schema.present? %>
    <!-- Enhanced File Info (for CSV sources) -->
    <% if @data_source.source_type == 'csv' && @schema[:file_info].present? %>
      <div class="bg-white shadow-sm border border-neutral-200 rounded-xl p-6 mb-8">
        <div class="flex items-center gap-3 mb-6">
          <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
            <i class="fas fa-file-csv text-white text-sm" aria-hidden="true"></i>
          </div>
          <h2 class="text-lg font-semibold text-neutral-900">File Information</h2>
        </div>

        <dl class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <div class="bg-neutral-50 rounded-lg p-4 border border-neutral-100">
            <dt class="text-xs font-medium text-neutral-500 uppercase tracking-wider mb-2 flex items-center gap-2">
              <i class="fas fa-weight-hanging text-neutral-400 text-xs" aria-hidden="true"></i>
              File Size
              <button type="button"
                      class="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full"
                      data-controller="tooltip"
                      data-tooltip-content-value="The total size of your uploaded file"
                      aria-label="Help: File size information">
                <i class="fas fa-info-circle text-xs" aria-hidden="true"></i>
              </button>
            </dt>
            <dd class="text-lg font-semibold text-neutral-900">
              <%= number_to_human_size(@schema[:file_info][:size]) %>
            </dd>
          </div>

          <div class="bg-neutral-50 rounded-lg p-4 border border-neutral-100">
            <dt class="text-xs font-medium text-neutral-500 uppercase tracking-wider mb-2 flex items-center gap-2">
              <i class="fas fa-font text-neutral-400 text-xs" aria-hidden="true"></i>
              Encoding
              <button type="button"
                      class="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full"
                      data-controller="tooltip"
                      data-tooltip-content-value="Character encoding format used in your file (e.g., UTF-8 supports international characters)"
                      aria-label="Help: File encoding information">
                <i class="fas fa-info-circle text-xs" aria-hidden="true"></i>
              </button>
            </dt>
            <dd class="text-lg font-semibold text-neutral-900">
              <%= @schema[:file_info][:encoding] %>
            </dd>
          </div>

          <div class="bg-neutral-50 rounded-lg p-4 border border-neutral-100">
            <dt class="text-xs font-medium text-neutral-500 uppercase tracking-wider mb-2 flex items-center gap-2">
              <i class="fas fa-grip-lines-vertical text-neutral-400 text-xs" aria-hidden="true"></i>
              Delimiter
              <button type="button"
                      class="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full"
                      data-controller="tooltip"
                      data-tooltip-content-value="Character used to separate columns in your CSV file (comma, tab, semicolon, etc.)"
                      aria-label="Help: CSV delimiter information">
                <i class="fas fa-info-circle text-xs" aria-hidden="true"></i>
              </button>
            </dt>
            <dd class="text-lg font-semibold text-neutral-900">
              <span class="inline-flex items-center px-2 py-1 bg-white border border-neutral-200 rounded font-mono text-sm">
                <%= @schema[:file_info][:delimiter] == "\t" ? "TAB" : "'#{@schema[:file_info][:delimiter]}'" %>
              </span>
            </dd>
          </div>

          <div class="bg-neutral-50 rounded-lg p-4 border border-neutral-100">
            <dt class="text-xs font-medium text-neutral-500 uppercase tracking-wider mb-2 flex items-center gap-2">
              <i class="fas fa-list-ol text-neutral-400 text-xs" aria-hidden="true"></i>
              Estimated Rows
              <button type="button"
                      class="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full"
                      data-controller="tooltip"
                      data-tooltip-content-value="Approximate number of data rows in your file (excluding header)"
                      aria-label="Help: Row count information">
                <i class="fas fa-info-circle text-xs" aria-hidden="true"></i>
              </button>
            </dt>
            <dd class="text-lg font-semibold text-neutral-900">
              <% if @schema[:row_count_estimate] %>
                <%= number_with_delimiter(@schema[:row_count_estimate]) %>
              <% else %>
                <span class="text-neutral-500">Unknown</span>
              <% end %>
            </dd>
          </div>
        </dl>
      </div>
    <% end %>

    <!-- Enhanced Column Schema -->
    <div class="bg-white shadow-sm border border-neutral-200 rounded-xl overflow-hidden">
      <!-- Header with Summary and Controls -->
      <div class="px-6 py-5 border-b border-neutral-200 bg-gradient-to-r from-neutral-50 to-white">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
              <i class="fas fa-columns text-white text-sm" aria-hidden="true"></i>
            </div>
            <div>
              <h2 class="text-lg font-semibold text-neutral-900">
                Data Columns
                <span class="inline-flex items-center ml-2 px-2 py-1 bg-primary-100 text-primary-800 text-xs font-medium rounded-full">
                  <%= @schema[:columns].length %> columns
                </span>
              </h2>
              <p class="text-sm text-neutral-600 mt-1">Structure and data types of your columns</p>
            </div>
          </div>

          <!-- View Toggle for Mobile/Desktop -->
          <div class="flex items-center gap-2">
            <span class="text-sm text-neutral-600">View:</span>
            <div class="bg-neutral-100 rounded-lg p-1 flex" role="tablist" aria-label="Column view options">
              <button type="button"
                      class="px-3 py-1 text-sm font-medium rounded-md bg-white text-neutral-900 shadow-sm"
                      role="tab"
                      aria-selected="true"
                      aria-controls="table-view"
                      data-controller="view-toggle"
                      data-view-toggle-target="tableBtn"
                      data-action="click->view-toggle#showTable">
                <i class="fas fa-table mr-1" aria-hidden="true"></i>
                Table
              </button>
              <button type="button"
                      class="px-3 py-1 text-sm font-medium rounded-md text-neutral-600 hover:text-neutral-900"
                      role="tab"
                      aria-selected="false"
                      aria-controls="card-view"
                      data-view-toggle-target="cardBtn"
                      data-action="click->view-toggle#showCards">
                <i class="fas fa-th-large mr-1" aria-hidden="true"></i>
                Cards
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Data Type Legend -->
      <div class="px-6 py-4 bg-neutral-50 border-b border-neutral-200">
        <div class="flex flex-wrap items-center gap-4">
          <span class="text-sm font-medium text-neutral-700">Data Types:</span>
          <div class="flex flex-wrap gap-2">
            <div class="flex items-center gap-1">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                integer
              </span>
              <button type="button"
                      class="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full"
                      data-controller="tooltip"
                      data-tooltip-content-value="Whole numbers (e.g., 1, 42, -15)"
                      aria-label="Help: Integer data type">
                <i class="fas fa-info-circle text-xs" aria-hidden="true"></i>
              </button>
            </div>
            <div class="flex items-center gap-1">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-cyan-100 text-cyan-800">
                decimal
              </span>
              <button type="button"
                      class="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full"
                      data-controller="tooltip"
                      data-tooltip-content-value="Numbers with decimal points (e.g., 3.14, 99.99)"
                      aria-label="Help: Decimal data type">
                <i class="fas fa-info-circle text-xs" aria-hidden="true"></i>
              </button>
            </div>
            <div class="flex items-center gap-1">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                boolean
              </span>
              <button type="button"
                      class="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full"
                      data-controller="tooltip"
                      data-tooltip-content-value="True/False values (e.g., yes/no, active/inactive)"
                      aria-label="Help: Boolean data type">
                <i class="fas fa-info-circle text-xs" aria-hidden="true"></i>
              </button>
            </div>
            <div class="flex items-center gap-1">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                date/time
              </span>
              <button type="button"
                      class="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full"
                      data-controller="tooltip"
                      data-tooltip-content-value="Date and time values (e.g., 2024-01-15, 14:30:00)"
                      aria-label="Help: Date/time data type">
                <i class="fas fa-info-circle text-xs" aria-hidden="true"></i>
              </button>
            </div>
            <div class="flex items-center gap-1">
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                text
              </span>
              <button type="button"
                      class="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full"
                      data-controller="tooltip"
                      data-tooltip-content-value="Text and string values (e.g., names, descriptions)"
                      aria-label="Help: Text data type">
                <i class="fas fa-info-circle text-xs" aria-hidden="true"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Table View -->
      <div id="table-view" data-view-toggle-target="tableView" class="block">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-neutral-200" role="table" aria-label="Data columns information">
            <thead class="bg-neutral-50">
              <tr role="row">
                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-neutral-700 uppercase tracking-wider">
                  <div class="flex items-center gap-2">
                    <i class="fas fa-tag text-neutral-400" aria-hidden="true"></i>
                    Column Name
                  </div>
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-neutral-700 uppercase tracking-wider">
                  <div class="flex items-center gap-2">
                    <i class="fas fa-code text-neutral-400" aria-hidden="true"></i>
                    Data Type
                  </div>
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-neutral-700 uppercase tracking-wider">
                  <div class="flex items-center gap-2">
                    <i class="fas fa-question-circle text-neutral-400" aria-hidden="true"></i>
                    Can be Empty
                    <button type="button"
                            class="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full"
                            data-controller="tooltip"
                            data-tooltip-content-value="Whether this column can contain empty (null) values"
                            aria-label="Help: Nullable column information">
                      <i class="fas fa-info-circle text-xs" aria-hidden="true"></i>
                    </button>
                  </div>
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-neutral-700 uppercase tracking-wider">
                  <div class="flex items-center gap-2">
                    <i class="fas fa-eye text-neutral-400" aria-hidden="true"></i>
                    Sample Data
                  </div>
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-neutral-200">
              <% @schema[:columns].each_with_index do |column, index| %>
                <tr role="row" class="<%= index.even? ? 'bg-white' : 'bg-neutral-50' %> hover:bg-neutral-100 transition-colors duration-150">
                  <!-- Column Name -->
                  <td class="px-6 py-4 text-sm" role="gridcell">
                    <div class="flex items-center gap-2">
                      <span class="font-semibold text-neutral-900"><%= column[:name] %></span>
                      <% if column[:name].length > 20 %>
                        <button type="button"
                                class="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full"
                                data-controller="tooltip"
                                data-tooltip-content-value="<%= column[:name] %>"
                                aria-label="Full column name: <%= column[:name] %>">
                          <i class="fas fa-expand-alt text-xs" aria-hidden="true"></i>
                        </button>
                      <% end %>
                    </div>
                  </td>

                  <!-- Data Type -->
                  <td class="px-6 py-4 text-sm" role="gridcell">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border
                      <%= case column[:type]
                        when 'integer' then 'bg-blue-50 text-blue-700 border-blue-200'
                        when 'decimal' then 'bg-cyan-50 text-cyan-700 border-cyan-200'
                        when 'boolean' then 'bg-green-50 text-green-700 border-green-200'
                        when 'date', 'datetime' then 'bg-purple-50 text-purple-700 border-purple-200'
                        else 'bg-gray-50 text-gray-700 border-gray-200'
                        end %>"
                      aria-label="Data type: <%= column[:type] %>">
                      <i class="fas fa-<%= case column[:type]
                        when 'integer' then 'hashtag'
                        when 'decimal' then 'calculator'
                        when 'boolean' then 'toggle-on'
                        when 'date', 'datetime' then 'calendar'
                        else 'font'
                        end %> mr-1" aria-hidden="true"></i>
                      <%= column[:type] %>
                    </span>
                  </td>

                  <!-- Nullable -->
                  <td class="px-6 py-4 text-sm" role="gridcell">
                    <% if column[:nullable] %>
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-50 text-yellow-700 border border-yellow-200"
                            aria-label="This column can be empty">
                        <i class="fas fa-exclamation-triangle mr-1" aria-hidden="true"></i>
                        Yes
                      </span>
                    <% else %>
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-50 text-green-700 border border-green-200"
                            aria-label="This column must have a value">
                        <i class="fas fa-check-circle mr-1" aria-hidden="true"></i>
                        No
                      </span>
                    <% end %>
                  </td>

                  <!-- Sample Values -->
                  <td class="px-6 py-4 text-sm" role="gridcell">
                    <% if column[:sample_values].present? %>
                      <div class="max-w-xs">
                        <div class="text-neutral-700 truncate" title="<%= column[:sample_values].join(', ') %>">
                          <%= column[:sample_values].map { |v| truncate(v.to_s, length: 15) }.join(', ') %>
                        </div>
                        <% if column[:sample_values].length > 3 %>
                          <button type="button"
                                  class="text-xs text-primary-600 hover:text-primary-700 mt-1 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded"
                                  data-controller="tooltip"
                                  data-tooltip-content-value="<%= column[:sample_values].join(', ') %>"
                                  aria-label="View all sample values for <%= column[:name] %>">
                            View all (<%= column[:sample_values].length %> samples)
                          </button>
                        <% end %>
                      </div>
                    <% else %>
                      <span class="text-neutral-400 italic">No sample data available</span>
                    <% end %>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Card View (Mobile-Friendly) -->
      <div id="card-view" data-view-toggle-target="cardView" class="hidden">
        <div class="p-6 space-y-4">
          <% @schema[:columns].each_with_index do |column, index| %>
            <div class="bg-neutral-50 border border-neutral-200 rounded-lg p-4 hover:shadow-sm transition-shadow duration-150">
              <!-- Column Header -->
              <div class="flex items-start justify-between mb-3">
                <div class="flex items-center gap-2">
                  <h3 class="font-semibold text-neutral-900 text-base"><%= column[:name] %></h3>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border
                    <%= case column[:type]
                      when 'integer' then 'bg-blue-50 text-blue-700 border-blue-200'
                      when 'decimal' then 'bg-cyan-50 text-cyan-700 border-cyan-200'
                      when 'boolean' then 'bg-green-50 text-green-700 border-green-200'
                      when 'date', 'datetime' then 'bg-purple-50 text-purple-700 border-purple-200'
                      else 'bg-gray-50 text-gray-700 border-gray-200'
                      end %>"
                    aria-label="Data type: <%= column[:type] %>">
                    <i class="fas fa-<%= case column[:type]
                      when 'integer' then 'hashtag'
                      when 'decimal' then 'calculator'
                      when 'boolean' then 'toggle-on'
                      when 'date', 'datetime' then 'calendar'
                      else 'font'
                      end %> mr-1" aria-hidden="true"></i>
                    <%= column[:type] %>
                  </span>
                </div>

                <!-- Nullable Status -->
                <% if column[:nullable] %>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-50 text-yellow-700 border border-yellow-200"
                        aria-label="This column can be empty">
                    <i class="fas fa-exclamation-triangle mr-1" aria-hidden="true"></i>
                    Can be empty
                  </span>
                <% else %>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-50 text-green-700 border border-green-200"
                        aria-label="This column must have a value">
                    <i class="fas fa-check-circle mr-1" aria-hidden="true"></i>
                    Required
                  </span>
                <% end %>
              </div>

              <!-- Sample Values -->
              <% if column[:sample_values].present? %>
                <div class="mt-3">
                  <h4 class="text-xs font-medium text-neutral-500 uppercase tracking-wider mb-2 flex items-center gap-1">
                    <i class="fas fa-eye text-neutral-400" aria-hidden="true"></i>
                    Sample Data
                  </h4>
                  <div class="flex flex-wrap gap-1">
                    <% column[:sample_values].take(5).each do |value| %>
                      <span class="inline-block px-2 py-1 bg-white border border-neutral-200 rounded text-xs text-neutral-700 font-mono">
                        <%= truncate(value.to_s, length: 20) %>
                      </span>
                    <% end %>
                    <% if column[:sample_values].length > 5 %>
                      <button type="button"
                              class="inline-block px-2 py-1 bg-primary-50 border border-primary-200 rounded text-xs text-primary-700 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                              data-controller="tooltip"
                              data-tooltip-content-value="<%= column[:sample_values].join(', ') %>"
                              aria-label="View all sample values for <%= column[:name] %>">
                        +<%= column[:sample_values].length - 5 %> more
                      </button>
                    <% end %>
                  </div>
                </div>
              <% else %>
                <div class="mt-3">
                  <h4 class="text-xs font-medium text-neutral-500 uppercase tracking-wider mb-2 flex items-center gap-1">
                    <i class="fas fa-eye text-neutral-400" aria-hidden="true"></i>
                    Sample Data
                  </h4>
                  <span class="text-neutral-400 italic text-sm">No sample data available</span>
                </div>
              <% end %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  <% else %>
    <!-- Enhanced Error State -->
    <div class="bg-red-50 border border-red-200 rounded-xl p-8 text-center">
      <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <i class="fas fa-exclamation-triangle text-red-600 text-2xl" aria-hidden="true"></i>
      </div>
      <h2 class="text-xl font-semibold text-red-900 mb-2">Unable to Load Schema</h2>
      <p class="text-red-700 mb-4 max-w-md mx-auto">
        <%= @schema_info[:error] || "We encountered an issue while analyzing your data structure. This might be due to file format issues or connectivity problems." %>
      </p>
      <div class="flex flex-col sm:flex-row gap-3 justify-center">
        <%= link_to dashboard_data_source_path(@data_source),
            class: "inline-flex items-center px-4 py-2 border border-red-300 rounded-lg text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-200" do %>
          <i class="fas fa-arrow-left mr-2" aria-hidden="true"></i>
          Back to Data Source
        <% end %>
        <button type="button"
                class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-200"
                onclick="window.location.reload()">
          <i class="fas fa-redo mr-2" aria-hidden="true"></i>
          Try Again
        </button>
      </div>
    </div>
  <% end %>

  <!-- Enhanced Footer Navigation -->
  <div class="mt-8 pt-6 border-t border-neutral-200">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <!-- Back Navigation -->
      <div class="flex items-center gap-3">
        <%= link_to dashboard_data_source_path(@data_source),
            class: "inline-flex items-center px-4 py-2 border border-neutral-300 rounded-lg text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200" do %>
          <i class="fas fa-arrow-left mr-2" aria-hidden="true"></i>
          Back to Data Source
        <% end %>

        <% if @data_source.source_type == 'csv' %>
          <%= link_to sample_data_dashboard_data_source_path(@data_source),
              class: "inline-flex items-center px-4 py-2 border border-primary-300 rounded-lg text-sm font-medium text-primary-700 bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200" do %>
            <i class="fas fa-table mr-2" aria-hidden="true"></i>
            View Sample Data
          <% end %>
        <% end %>
      </div>

      <!-- Quick Actions -->
      <div class="flex items-center gap-2 text-sm text-neutral-600">
        <span>Quick actions:</span>
        <button type="button"
                class="text-primary-600 hover:text-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded"
                onclick="window.print()"
                aria-label="Print schema information">
          <i class="fas fa-print mr-1" aria-hidden="true"></i>
          Print
        </button>
        <span class="text-neutral-300">|</span>
        <button type="button"
                class="text-primary-600 hover:text-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded"
                data-controller="copy-to-clipboard"
                data-copy-to-clipboard-text-value="<%= @schema[:columns].map { |c| "#{c[:name]} (#{c[:type]})" }.join(', ') %>"
                aria-label="Copy column names to clipboard">
          <i class="fas fa-copy mr-1" aria-hidden="true"></i>
          Copy Columns
        </button>
      </div>
    </div>

    <!-- Help Text -->
    <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <div class="flex items-start gap-3">
        <div class="flex-shrink-0">
          <i class="fas fa-lightbulb text-blue-600 mt-0.5" aria-hidden="true"></i>
        </div>
        <div class="text-sm">
          <h3 class="font-medium text-blue-900 mb-1">Understanding Your Schema</h3>
          <p class="text-blue-700 leading-relaxed">
            This schema shows how your data is structured. Each column has a specific data type that determines what kind of information it can store.
            Use this information to understand your data better and plan any transformations or integrations.
          </p>
          <% if @schema[:columns].any? { |c| c[:nullable] } %>
            <p class="text-blue-700 mt-2">
              <i class="fas fa-info-circle mr-1" aria-hidden="true"></i>
              Some columns can be empty - consider this when setting up data validation rules.
            </p>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>