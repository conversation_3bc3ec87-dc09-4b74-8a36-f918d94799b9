<% content_for :title, "Debug Data Source" %>

<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <h1 class="text-2xl font-bold mb-4">Debug Data Source: <%= @data_source.name %></h1>
  
  <div class="bg-white shadow rounded-lg p-6 mb-4">
    <h2 class="text-lg font-medium mb-2">Basic Info</h2>
    <dl class="space-y-2">
      <div>
        <dt class="font-medium">ID:</dt>
        <dd><%= @data_source.id %></dd>
      </div>
      <div>
        <dt class="font-medium">Source Type:</dt>
        <dd><%= @data_source.source_type %></dd>
      </div>
      <div>
        <dt class="font-medium">Status:</dt>
        <dd><%= @data_source.status %></dd>
      </div>
    </dl>
  </div>
  
  <div class="bg-white shadow rounded-lg p-6 mb-4">
    <h2 class="text-lg font-medium mb-2">Connection Config (Raw)</h2>
    <pre class="bg-gray-100 p-4 rounded overflow-x-auto"><%= @data_source.connection_config %></pre>
  </div>
  
  <div class="bg-white shadow rounded-lg p-6 mb-4">
    <h2 class="text-lg font-medium mb-2">Connection Settings (Parsed)</h2>
    <pre class="bg-gray-100 p-4 rounded overflow-x-auto"><%= JSON.pretty_generate(@data_source.connection_settings) rescue @data_source.connection_settings.inspect %></pre>
  </div>
  
  <div class="bg-white shadow rounded-lg p-6 mb-4">
    <h2 class="text-lg font-medium mb-2">File Path Value</h2>
    <p>connection_config_value('file_path'): <%= @data_source.connection_config_value('file_path').inspect %></p>
    <p>connection_settings['file_path']: <%= @data_source.connection_settings['file_path'].inspect %></p>
  </div>
  
  <div class="bg-white shadow rounded-lg p-6">
    <h2 class="text-lg font-medium mb-2">Connector Test</h2>
    <% begin %>
      <% connector = @data_source.connector_class.new(@data_source) %>
      <p>Connector class: <%= connector.class.name %></p>
      <p>Connection settings in connector: <%= connector.connection_settings.inspect %></p>
    <% rescue => e %>
      <p class="text-red-600">Error: <%= e.message %></p>
    <% end %>
  </div>
  
  <div class="mt-4">
    <%= link_to "Back to Data Source", dashboard_data_source_path(@data_source), class: "btn btn--secondary" %>
  </div>
</div>