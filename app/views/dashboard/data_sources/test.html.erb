<div data-controller="data-source-form">
  <h1>Test Page</h1>
  <p>Check the console - you should see "DataSourceForm controller connected"</p>
  
  <div style="margin: 20px 0;">
    <label>
      <input type="radio" name="test" value="csv" data-action="change->data-source-form#updateConnectionFields"> CSV
    </label>
    <label style="margin-left: 20px;">
      <input type="radio" name="test" value="postgresql" data-action="change->data-source-form#updateConnectionFields"> PostgreSQL
    </label>
  </div>
  
  <div id="connection-settings" style="border: 1px solid #ccc; padding: 20px; min-height: 100px;">
    Settings will appear here
  </div>
  
  <div style="margin-top: 20px;">
    <p>If nothing happens when clicking the radio buttons, check the browser console for errors.</p>
  </div>
</div>