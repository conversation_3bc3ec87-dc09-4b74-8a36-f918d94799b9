<div class="space-y-8">
  <!-- API Endpoint -->
  <div class="group">
    <label class="block text-sm font-semibold text-slate-700 mb-3">
      <i class="fas fa-link mr-2 text-primary-500" aria-hidden="true"></i>
      API Endpoint URL <span class="text-red-500">*</span>
    </label>
    <input type="url" 
           name="data_source[connection_config][endpoint_url]" 
           value="<%= data_source.connection_config_value('endpoint_url') %>"
           class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
           placeholder="https://api.example.com/v1/data"
           required
           aria-describedby="endpoint-help">
    <p id="endpoint-help" class="mt-2 text-sm text-slate-600 flex items-center">
      <i class="fas fa-info-circle mr-2 text-blue-500" aria-hidden="true"></i>
      The full URL to your API endpoint that returns data
    </p>
  </div>

  <!-- HTTP Method -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div class="group">
      <label class="block text-sm font-semibold text-slate-700 mb-3">
        <i class="fas fa-exchange-alt mr-2 text-primary-500" aria-hidden="true"></i>
        HTTP Method
      </label>
      <select name="data_source[connection_config][http_method]" 
              class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200">
        <option value="GET" <%= 'selected' if data_source.connection_config_value('http_method') == 'GET' || data_source.connection_config_value('http_method').nil? %>>
          GET - Retrieve data
        </option>
        <option value="POST" <%= 'selected' if data_source.connection_config_value('http_method') == 'POST' %>>
          POST - Send data to retrieve response
        </option>
        <option value="PUT" <%= 'selected' if data_source.connection_config_value('http_method') == 'PUT' %>>
          PUT - Update and retrieve data
        </option>
      </select>
    </div>

    <!-- Response Format -->
    <div class="group">
      <label class="block text-sm font-semibold text-slate-700 mb-3">
        <i class="fas fa-file-code mr-2 text-primary-500" aria-hidden="true"></i>
        Response Format
      </label>
      <select name="data_source[connection_config][response_format]" 
              class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200">
        <option value="json" <%= 'selected' if data_source.connection_config_value('response_format') == 'json' || data_source.connection_config_value('response_format').nil? %>>
          JSON
        </option>
        <option value="xml" <%= 'selected' if data_source.connection_config_value('response_format') == 'xml' %>>
          XML
        </option>
        <option value="csv" <%= 'selected' if data_source.connection_config_value('response_format') == 'csv' %>>
          CSV
        </option>
        <option value="text" <%= 'selected' if data_source.connection_config_value('response_format') == 'text' %>>
          Plain Text
        </option>
      </select>
    </div>
  </div>

  <!-- Authentication -->
  <div class="p-6 bg-slate-50 rounded-2xl border border-slate-200">
    <h3 class="text-lg font-semibold text-slate-900 mb-4 flex items-center">
      <i class="fas fa-shield-alt mr-3 text-emerald-500" aria-hidden="true"></i>
      Authentication
    </h3>
    
    <div class="space-y-6">
      <!-- Auth Type -->
      <div class="group">
        <label class="block text-sm font-semibold text-slate-700 mb-3">
          <i class="fas fa-key mr-2 text-primary-500" aria-hidden="true"></i>
          Authentication Type
        </label>
        <select name="data_source[connection_config][auth_type]" 
                class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                onchange="toggleAuthFields(this.value)">
          <option value="none" <%= 'selected' if data_source.connection_config_value('auth_type') == 'none' || data_source.connection_config_value('auth_type').nil? %>>
            None - No authentication
          </option>
          <option value="api_key" <%= 'selected' if data_source.connection_config_value('auth_type') == 'api_key' %>>
            API Key
          </option>
          <option value="bearer_token" <%= 'selected' if data_source.connection_config_value('auth_type') == 'bearer_token' %>>
            Bearer Token
          </option>
          <option value="basic_auth" <%= 'selected' if data_source.connection_config_value('auth_type') == 'basic_auth' %>>
            Basic Authentication
          </option>
          <option value="oauth2" <%= 'selected' if data_source.connection_config_value('auth_type') == 'oauth2' %>>
            OAuth 2.0
          </option>
        </select>
      </div>

      <!-- API Key Fields -->
      <div id="api-key-fields" class="auth-fields space-y-4" style="display: none;">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-semibold text-slate-700 mb-3">
              <i class="fas fa-tag mr-2 text-primary-500" aria-hidden="true"></i>
              API Key Name
            </label>
            <input type="text" 
                   name="data_source[connection_config][api_key_name]" 
                   value="<%= data_source.connection_config_value('api_key_name') %>"
                   class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                   placeholder="X-API-Key or api_key">
          </div>
          <div>
            <label class="block text-sm font-semibold text-slate-700 mb-3">
              <i class="fas fa-key mr-2 text-primary-500" aria-hidden="true"></i>
              API Key Value
            </label>
            <input type="password" 
                   name="data_source[connection_config][api_key_value]" 
                   value="<%= data_source.connection_config_value('api_key_value') %>"
                   class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                   placeholder="Your API key">
          </div>
        </div>
      </div>

      <!-- Bearer Token Fields -->
      <div id="bearer-token-fields" class="auth-fields space-y-4" style="display: none;">
        <div>
          <label class="block text-sm font-semibold text-slate-700 mb-3">
            <i class="fas fa-shield-alt mr-2 text-primary-500" aria-hidden="true"></i>
            Bearer Token
          </label>
          <input type="password" 
                 name="data_source[connection_config][bearer_token]" 
                 value="<%= data_source.connection_config_value('bearer_token') %>"
                 class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                 placeholder="Your bearer token">
        </div>
      </div>

      <!-- Basic Auth Fields -->
      <div id="basic-auth-fields" class="auth-fields space-y-4" style="display: none;">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-semibold text-slate-700 mb-3">
              <i class="fas fa-user mr-2 text-primary-500" aria-hidden="true"></i>
              Username
            </label>
            <input type="text" 
                   name="data_source[connection_config][basic_username]" 
                   value="<%= data_source.connection_config_value('basic_username') %>"
                   class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                   placeholder="Username">
          </div>
          <div>
            <label class="block text-sm font-semibold text-slate-700 mb-3">
              <i class="fas fa-lock mr-2 text-primary-500" aria-hidden="true"></i>
              Password
            </label>
            <input type="password" 
                   name="data_source[connection_config][basic_password]" 
                   value="<%= data_source.connection_config_value('basic_password') %>"
                   class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                   placeholder="Password">
          </div>
        </div>
      </div>

      <!-- OAuth2 Fields -->
      <div id="oauth2-fields" class="auth-fields space-y-4" style="display: none;">
        <div class="p-4 bg-blue-50 rounded-xl border border-blue-200">
          <div class="flex items-start">
            <i class="fas fa-info-circle text-blue-600 mt-1 mr-3" aria-hidden="true"></i>
            <div>
              <h4 class="font-semibold text-blue-900 mb-1">OAuth 2.0 Setup</h4>
              <p class="text-sm text-blue-800">
                OAuth 2.0 requires additional configuration. Please contact support for assistance with OAuth setup.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Request Configuration -->
  <details class="group/request">
    <summary class="flex items-center justify-between p-4 bg-slate-100 rounded-xl cursor-pointer hover:bg-slate-200 transition-colors duration-200">
      <div class="flex items-center">
        <i class="fas fa-cogs mr-3 text-slate-600" aria-hidden="true"></i>
        <span class="font-semibold text-slate-900">Request Configuration</span>
      </div>
      <i class="fas fa-chevron-down text-slate-500 transition-transform duration-200 group-open/request:rotate-180" aria-hidden="true"></i>
    </summary>
    
    <div class="mt-4 p-6 bg-white rounded-xl border border-slate-200 space-y-6">
      <!-- Custom Headers -->
      <div>
        <label class="block text-sm font-semibold text-slate-700 mb-3">
          <i class="fas fa-list mr-2 text-primary-500" aria-hidden="true"></i>
          Custom Headers (JSON)
        </label>
        <textarea name="data_source[connection_config][custom_headers]" 
                  rows="4" 
                  class="w-full px-4 py-3 bg-slate-900 text-green-400 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 font-mono text-sm" 
                  placeholder='{"Content-Type": "application/json", "Accept": "application/json"}'><%= data_source.connection_config_value('custom_headers')&.to_json %></textarea>
        <p class="mt-2 text-sm text-slate-600">
          Additional HTTP headers to send with requests (JSON format)
        </p>
      </div>

      <!-- Request Body -->
      <div>
        <label class="block text-sm font-semibold text-slate-700 mb-3">
          <i class="fas fa-file-code mr-2 text-primary-500" aria-hidden="true"></i>
          Request Body (for POST/PUT)
        </label>
        <textarea name="data_source[connection_config][request_body]" 
                  rows="6" 
                  class="w-full px-4 py-3 bg-slate-900 text-green-400 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 font-mono text-sm" 
                  placeholder='{"query": {"limit": 100, "offset": 0}}'><%= data_source.connection_config_value('request_body') %></textarea>
        <p class="mt-2 text-sm text-slate-600">
          Request body to send with POST/PUT requests (JSON format)
        </p>
      </div>

      <!-- Timeout Settings -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-semibold text-slate-700 mb-3">
            <i class="fas fa-clock mr-2 text-primary-500" aria-hidden="true"></i>
            Connection Timeout (seconds)
          </label>
          <input type="number" 
                 name="data_source[connection_config][connect_timeout]" 
                 value="<%= data_source.connection_config_value('connect_timeout') || 30 %>"
                 class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                 min="5"
                 max="300"
                 placeholder="30">
        </div>
        
        <div>
          <label class="block text-sm font-semibold text-slate-700 mb-3">
            <i class="fas fa-hourglass-half mr-2 text-primary-500" aria-hidden="true"></i>
            Read Timeout (seconds)
          </label>
          <input type="number" 
                 name="data_source[connection_config][read_timeout]" 
                 value="<%= data_source.connection_config_value('read_timeout') || 60 %>"
                 class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                 min="10"
                 max="600"
                 placeholder="60">
        </div>
      </div>
    </div>
  </details>

  <!-- Data Processing -->
  <details class="group/processing">
    <summary class="flex items-center justify-between p-4 bg-slate-100 rounded-xl cursor-pointer hover:bg-slate-200 transition-colors duration-200">
      <div class="flex items-center">
        <i class="fas fa-magic mr-3 text-slate-600" aria-hidden="true"></i>
        <span class="font-semibold text-slate-900">Data Processing</span>
      </div>
      <i class="fas fa-chevron-down text-slate-500 transition-transform duration-200 group-open/processing:rotate-180" aria-hidden="true"></i>
    </summary>
    
    <div class="mt-4 p-6 bg-white rounded-xl border border-slate-200 space-y-6">
      <!-- JSON Path -->
      <div>
        <label class="block text-sm font-semibold text-slate-700 mb-3">
          <i class="fas fa-route mr-2 text-primary-500" aria-hidden="true"></i>
          JSON Path (for nested data)
        </label>
        <input type="text" 
               name="data_source[connection_config][json_path]" 
               value="<%= data_source.connection_config_value('json_path') %>"
               class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
               placeholder="$.data.items or data.results"
               aria-describedby="json-path-help">
        <p id="json-path-help" class="mt-2 text-sm text-slate-600">
          JSONPath expression to extract data from nested JSON responses
        </p>
      </div>

      <!-- Pagination -->
      <div class="p-4 bg-amber-50 rounded-xl border border-amber-200">
        <h4 class="font-semibold text-amber-900 mb-2 flex items-center">
          <i class="fas fa-layer-group mr-2" aria-hidden="true"></i>
          Pagination Support
        </h4>
        <p class="text-sm text-amber-800 mb-3">
          Configure pagination to handle large datasets across multiple API calls.
        </p>
        <label class="flex items-center cursor-pointer">
          <input type="checkbox" 
                 name="data_source[connection_config][enable_pagination]" 
                 value="true"
                 <%= 'checked' if data_source.connection_config_value('enable_pagination') %>
                 class="w-4 h-4 text-primary-600 bg-white border-slate-300 rounded focus:ring-primary-500 focus:ring-2">
          <span class="ml-3 text-sm font-medium text-amber-900">Enable automatic pagination</span>
        </label>
      </div>
    </div>
  </details>
</div>

<script nonce="<%= content_security_policy_nonce %>">
  function toggleAuthFields(authType) {
    // Hide all auth fields
    const authFields = document.querySelectorAll('.auth-fields');
    authFields.forEach(field => {
      field.style.display = 'none';
    });
    
    // Show selected auth fields
    if (authType !== 'none') {
      const targetField = document.getElementById(`${authType.replace('_', '-')}-fields`);
      if (targetField) {
        targetField.style.display = 'block';
      }
    }
  }
  
  // Initialize auth fields on page load
  document.addEventListener('DOMContentLoaded', function() {
    const authSelect = document.querySelector('select[name*="auth_type"]');
    if (authSelect) {
      toggleAuthFields(authSelect.value);
    }
  });
</script>
