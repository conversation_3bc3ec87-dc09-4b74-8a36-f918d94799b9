<div class="space-y-8">
  <!-- S3 Bucket Configuration -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- Bucket Name -->
    <div class="group">
      <label class="block text-sm font-semibold text-slate-700 mb-3">
        <i class="fab fa-aws mr-2 text-primary-500" aria-hidden="true"></i>
        S3 Bucket Name <span class="text-red-500">*</span>
      </label>
      <input type="text" 
             name="data_source[connection_config][bucket_name]" 
             value="<%= data_source.connection_config_value('bucket_name') %>"
             class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
             placeholder="my-data-bucket"
             required
             aria-describedby="bucket-help">
      <p id="bucket-help" class="mt-2 text-sm text-slate-600 flex items-center">
        <i class="fas fa-info-circle mr-2 text-blue-500" aria-hidden="true"></i>
        The name of your S3 bucket containing the data files
      </p>
    </div>
    
    <!-- Region -->
    <div class="group">
      <label class="block text-sm font-semibold text-slate-700 mb-3">
        <i class="fas fa-globe mr-2 text-primary-500" aria-hidden="true"></i>
        AWS Region <span class="text-red-500">*</span>
      </label>
      <select name="data_source[connection_config][region]" 
              class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
              required>
        <option value="">Select a region</option>
        <option value="us-east-1" <%= 'selected' if data_source.connection_config_value('region') == 'us-east-1' %>>US East (N. Virginia)</option>
        <option value="us-east-2" <%= 'selected' if data_source.connection_config_value('region') == 'us-east-2' %>>US East (Ohio)</option>
        <option value="us-west-1" <%= 'selected' if data_source.connection_config_value('region') == 'us-west-1' %>>US West (N. California)</option>
        <option value="us-west-2" <%= 'selected' if data_source.connection_config_value('region') == 'us-west-2' %>>US West (Oregon)</option>
        <option value="eu-west-1" <%= 'selected' if data_source.connection_config_value('region') == 'eu-west-1' %>>Europe (Ireland)</option>
        <option value="eu-west-2" <%= 'selected' if data_source.connection_config_value('region') == 'eu-west-2' %>>Europe (London)</option>
        <option value="eu-central-1" <%= 'selected' if data_source.connection_config_value('region') == 'eu-central-1' %>>Europe (Frankfurt)</option>
        <option value="ap-southeast-1" <%= 'selected' if data_source.connection_config_value('region') == 'ap-southeast-1' %>>Asia Pacific (Singapore)</option>
        <option value="ap-southeast-2" <%= 'selected' if data_source.connection_config_value('region') == 'ap-southeast-2' %>>Asia Pacific (Sydney)</option>
        <option value="ap-northeast-1" <%= 'selected' if data_source.connection_config_value('region') == 'ap-northeast-1' %>>Asia Pacific (Tokyo)</option>
      </select>
    </div>
  </div>

  <!-- File Path Configuration -->
  <div class="group">
    <label class="block text-sm font-semibold text-slate-700 mb-3">
      <i class="fas fa-folder mr-2 text-primary-500" aria-hidden="true"></i>
      File Path/Prefix
    </label>
    <input type="text" 
           name="data_source[connection_config][file_path]" 
           value="<%= data_source.connection_config_value('file_path') %>"
           class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
           placeholder="data/exports/file.csv or data/exports/"
           aria-describedby="path-help">
    <p id="path-help" class="mt-2 text-sm text-slate-600">
      Specific file path or prefix to filter files. Leave empty to access all files in the bucket.
    </p>
  </div>

  <!-- AWS Credentials -->
  <div class="p-6 bg-slate-50 rounded-2xl border border-slate-200">
    <h3 class="text-lg font-semibold text-slate-900 mb-4 flex items-center">
      <i class="fas fa-key mr-3 text-amber-500" aria-hidden="true"></i>
      AWS Credentials
    </h3>
    
    <div class="space-y-6">
      <!-- Credential Type Selection -->
      <div class="group">
        <label class="block text-sm font-semibold text-slate-700 mb-3">
          <i class="fas fa-shield-alt mr-2 text-primary-500" aria-hidden="true"></i>
          Authentication Method
        </label>
        <select name="data_source[connection_config][auth_method]" 
                class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                onchange="toggleS3AuthFields(this.value)">
          <option value="access_key" <%= 'selected' if data_source.connection_config_value('auth_method') == 'access_key' || data_source.connection_config_value('auth_method').nil? %>>
            Access Key & Secret
          </option>
          <option value="iam_role" <%= 'selected' if data_source.connection_config_value('auth_method') == 'iam_role' %>>
            IAM Role (EC2/ECS)
          </option>
          <option value="assume_role" <%= 'selected' if data_source.connection_config_value('auth_method') == 'assume_role' %>>
            Assume Role
          </option>
        </select>
      </div>

      <!-- Access Key Fields -->
      <div id="access-key-fields" class="s3-auth-fields space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-semibold text-slate-700 mb-3">
              <i class="fas fa-user mr-2 text-primary-500" aria-hidden="true"></i>
              Access Key ID <span class="text-red-500">*</span>
            </label>
            <input type="text" 
                   name="data_source[connection_config][access_key_id]" 
                   value="<%= data_source.connection_config_value('access_key_id') %>"
                   class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                   placeholder="AKIAIOSFODNN7EXAMPLE">
          </div>
          <div>
            <label class="block text-sm font-semibold text-slate-700 mb-3">
              <i class="fas fa-lock mr-2 text-primary-500" aria-hidden="true"></i>
              Secret Access Key <span class="text-red-500">*</span>
            </label>
            <input type="password" 
                   name="data_source[connection_config][secret_access_key]" 
                   value="<%= data_source.connection_config_value('secret_access_key') %>"
                   class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                   placeholder="Your secret access key">
          </div>
        </div>
      </div>

      <!-- IAM Role Fields -->
      <div id="iam-role-fields" class="s3-auth-fields space-y-4 hidden">
        <div class="p-4 bg-blue-50 rounded-xl border border-blue-200">
          <div class="flex items-start">
            <i class="fas fa-info-circle text-blue-600 mt-1 mr-3" aria-hidden="true"></i>
            <div>
              <h4 class="font-semibold text-blue-900 mb-1">IAM Role Authentication</h4>
              <p class="text-sm text-blue-800">
                When using IAM roles, ensure your EC2 instance or ECS task has the appropriate IAM role attached 
                with S3 access permissions. No additional credentials are required.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Assume Role Fields -->
      <div id="assume-role-fields" class="s3-auth-fields space-y-4 hidden">
        <div>
          <label class="block text-sm font-semibold text-slate-700 mb-3">
            <i class="fas fa-user-tag mr-2 text-primary-500" aria-hidden="true"></i>
            Role ARN <span class="text-red-500">*</span>
          </label>
          <input type="text" 
                 name="data_source[connection_config][role_arn]" 
                 value="<%= data_source.connection_config_value('role_arn') %>"
                 class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                 placeholder="arn:aws:iam::123456789012:role/S3AccessRole">
        </div>
        <div>
          <label class="block text-sm font-semibold text-slate-700 mb-3">
            <i class="fas fa-id-badge mr-2 text-primary-500" aria-hidden="true"></i>
            External ID (Optional)
          </label>
          <input type="text" 
                 name="data_source[connection_config][external_id]" 
                 value="<%= data_source.connection_config_value('external_id') %>"
                 class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                 placeholder="Optional external ID for role assumption">
        </div>
      </div>

      <!-- Security Notice -->
      <div class="p-4 bg-emerald-50 rounded-xl border border-emerald-200">
        <div class="flex items-start">
          <i class="fas fa-shield-alt text-emerald-600 mt-1 mr-3" aria-hidden="true"></i>
          <div>
            <h4 class="font-semibold text-emerald-900 mb-1">Security Notice</h4>
            <p class="text-sm text-emerald-800">
              All AWS credentials are encrypted and stored securely. We recommend using IAM roles or 
              assume role authentication for enhanced security in production environments.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- File Format Configuration -->
  <div class="p-6 bg-slate-50 rounded-2xl border border-slate-200">
    <h3 class="text-lg font-semibold text-slate-900 mb-4 flex items-center">
      <i class="fas fa-file-alt mr-3 text-purple-500" aria-hidden="true"></i>
      File Format Settings
    </h3>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- File Type -->
      <div class="group">
        <label class="block text-sm font-semibold text-slate-700 mb-3">
          <i class="fas fa-file-code mr-2 text-primary-500" aria-hidden="true"></i>
          Expected File Type
        </label>
        <select name="data_source[connection_config][file_type]" 
                class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200">
          <option value="csv" <%= 'selected' if data_source.connection_config_value('file_type') == 'csv' || data_source.connection_config_value('file_type').nil? %>>
            CSV Files
          </option>
          <option value="json" <%= 'selected' if data_source.connection_config_value('file_type') == 'json' %>>
            JSON Files
          </option>
          <option value="parquet" <%= 'selected' if data_source.connection_config_value('file_type') == 'parquet' %>>
            Parquet Files
          </option>
          <option value="excel" <%= 'selected' if data_source.connection_config_value('file_type') == 'excel' %>>
            Excel Files
          </option>
        </select>
      </div>
      
      <!-- Compression -->
      <div class="group">
        <label class="block text-sm font-semibold text-slate-700 mb-3">
          <i class="fas fa-compress mr-2 text-primary-500" aria-hidden="true"></i>
          Compression
        </label>
        <select name="data_source[connection_config][compression]" 
                class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200">
          <option value="none" <%= 'selected' if data_source.connection_config_value('compression') == 'none' || data_source.connection_config_value('compression').nil? %>>
            None
          </option>
          <option value="gzip" <%= 'selected' if data_source.connection_config_value('compression') == 'gzip' %>>
            GZIP
          </option>
          <option value="bzip2" <%= 'selected' if data_source.connection_config_value('compression') == 'bzip2' %>>
            BZIP2
          </option>
          <option value="lz4" <%= 'selected' if data_source.connection_config_value('compression') == 'lz4' %>>
            LZ4
          </option>
        </select>
      </div>
    </div>
  </div>

  <!-- Advanced Options -->
  <details class="group/advanced">
    <summary class="flex items-center justify-between p-4 bg-slate-100 rounded-xl cursor-pointer hover:bg-slate-200 transition-colors duration-200">
      <div class="flex items-center">
        <i class="fas fa-cogs mr-3 text-slate-600" aria-hidden="true"></i>
        <span class="font-semibold text-slate-900">Advanced S3 Options</span>
      </div>
      <i class="fas fa-chevron-down text-slate-500 transition-transform duration-200 group-open/advanced:rotate-180" aria-hidden="true"></i>
    </summary>
    
    <div class="mt-4 p-6 bg-white rounded-xl border border-slate-200 space-y-6">
      <!-- File Pattern -->
      <div>
        <label class="block text-sm font-semibold text-slate-700 mb-3">
          <i class="fas fa-search mr-2 text-primary-500" aria-hidden="true"></i>
          File Pattern (Regex)
        </label>
        <input type="text" 
               name="data_source[connection_config][file_pattern]" 
               value="<%= data_source.connection_config_value('file_pattern') %>"
               class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
               placeholder=".*\.csv$ or data_\d{4}-\d{2}-\d{2}\.json"
               aria-describedby="pattern-help">
        <p id="pattern-help" class="mt-2 text-sm text-slate-600">
          Regular expression to match specific files. Leave empty to process all files.
        </p>
      </div>

      <!-- Processing Options -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <label class="flex items-center cursor-pointer group">
          <input type="checkbox" 
                 name="data_source[connection_config][recursive]" 
                 value="true"
                 <%= 'checked' if data_source.connection_config_value('recursive') %>
                 class="w-5 h-5 text-primary-600 bg-white border-slate-300 rounded focus:ring-primary-500 focus:ring-2">
          <div class="ml-4">
            <div class="font-semibold text-slate-900 group-hover:text-slate-700">Process subdirectories</div>
            <div class="text-sm text-slate-600">Recursively process files in subdirectories</div>
          </div>
        </label>
        
        <label class="flex items-center cursor-pointer group">
          <input type="checkbox" 
                 name="data_source[connection_config][delete_after_processing]" 
                 value="true"
                 <%= 'checked' if data_source.connection_config_value('delete_after_processing') %>
                 class="w-5 h-5 text-primary-600 bg-white border-slate-300 rounded focus:ring-primary-500 focus:ring-2">
          <div class="ml-4">
            <div class="font-semibold text-slate-900 group-hover:text-slate-700">Delete after processing</div>
            <div class="text-sm text-slate-600">Remove files from S3 after successful import</div>
          </div>
        </label>
      </div>
    </div>
  </details>

  <!-- Connection Preview -->
  <div class="p-6 bg-blue-50 rounded-2xl border border-blue-200">
    <h3 class="text-lg font-semibold text-blue-900 mb-4 flex items-center">
      <i class="fas fa-link mr-3 text-blue-600" aria-hidden="true"></i>
      S3 Connection Preview
    </h3>
    <div class="bg-slate-900 rounded-xl p-4 font-mono text-sm">
      <div class="text-green-400">
        s3://<span class="text-cyan-400">[bucket-name]</span>/<span class="text-yellow-400">[file-path]</span>
      </div>
      <div class="text-slate-400 mt-1">
        Region: <span class="text-purple-400">[region]</span> | Auth: <span class="text-orange-400">[auth-method]</span>
      </div>
    </div>
    <p class="mt-3 text-sm text-blue-800 flex items-center">
      <i class="fas fa-info-circle mr-2" aria-hidden="true"></i>
      This preview shows the S3 connection format. Actual credentials are never displayed.
    </p>
  </div>
</div>

<script nonce="<%= content_security_policy_nonce %>">
  function toggleS3AuthFields(authMethod) {
    // Hide all auth fields
    const authFields = document.querySelectorAll('.s3-auth-fields');
    authFields.forEach(field => {
      field.classList.add('hidden');
    });
    
    // Show selected auth fields
    const targetField = document.getElementById(`${authMethod.replace('_', '-')}-fields`);
    if (targetField) {
      targetField.classList.remove('hidden');
    }
  }
  
  // Initialize auth fields on page load
  document.addEventListener('DOMContentLoaded', function() {
    const authSelect = document.querySelector('select[name*="auth_method"]');
    if (authSelect) {
      toggleS3AuthFields(authSelect.value);
    }
  });
</script>
