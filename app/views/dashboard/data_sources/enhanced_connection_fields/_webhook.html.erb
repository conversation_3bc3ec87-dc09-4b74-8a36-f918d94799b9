<div class="space-y-8">
  <!-- Webhook URL Display -->
  <div class="p-6 bg-gradient-to-r from-primary-50 to-primary-50/50 border border-primary-200 rounded-2xl">
    <div class="flex items-start">
      <div class="flex-shrink-0">
        <div class="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
          <i class="fas fa-satellite-dish text-primary-600 text-lg" aria-hidden="true"></i>
        </div>
      </div>
      <div class="ml-4 flex-1">
        <h3 class="text-xl font-bold text-primary-900 mb-2">Your Webhook URL</h3>
        <p class="text-primary-700 mb-4">
          Use this URL to send data to your webhook endpoint. Copy and configure it in your external system.
        </p>
        
        <div class="bg-white rounded-xl p-4 border border-primary-200">
          <div class="flex items-center justify-between">
            <div class="flex-1 mr-4">
              <label class="block text-sm font-semibold text-primary-700 mb-2">Webhook Endpoint</label>
              <div class="bg-slate-900 rounded-lg p-3 font-mono text-sm text-green-400 break-all">
                <%= webhook_url_for_data_source(data_source) rescue "https://app.datareflow.com/webhooks/#{data_source.id || 'NEW'}/receive" %>
              </div>
            </div>
            <button type="button" 
                    class="flex-shrink-0 inline-flex items-center px-4 py-2 bg-primary-600 text-white text-sm font-medium rounded-lg hover:bg-primary-700 transition-colors duration-200"
                    onclick="copyWebhookUrl()"
                    aria-label="Copy webhook URL">
              <i class="fas fa-copy mr-2" aria-hidden="true"></i>
              Copy
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Webhook Configuration -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- HTTP Method -->
    <div class="group">
      <label class="block text-sm font-semibold text-slate-700 mb-3">
        <i class="fas fa-exchange-alt mr-2 text-primary-500" aria-hidden="true"></i>
        Accepted HTTP Methods
      </label>
      <div class="space-y-2">
        <label class="flex items-center cursor-pointer">
          <input type="checkbox" 
                 name="data_source[connection_config][allowed_methods][]" 
                 value="POST"
                 <%= 'checked' if data_source.connection_config_value('allowed_methods')&.include?('POST') || data_source.connection_config_value('allowed_methods').nil? %>
                 class="w-4 h-4 text-primary-600 bg-white border-slate-300 rounded focus:ring-primary-500 focus:ring-2">
          <span class="ml-3 text-sm font-medium text-slate-900">POST (recommended)</span>
        </label>
        <label class="flex items-center cursor-pointer">
          <input type="checkbox" 
                 name="data_source[connection_config][allowed_methods][]" 
                 value="PUT"
                 <%= 'checked' if data_source.connection_config_value('allowed_methods')&.include?('PUT') %>
                 class="w-4 h-4 text-primary-600 bg-white border-slate-300 rounded focus:ring-primary-500 focus:ring-2">
          <span class="ml-3 text-sm font-medium text-slate-900">PUT</span>
        </label>
        <label class="flex items-center cursor-pointer">
          <input type="checkbox" 
                 name="data_source[connection_config][allowed_methods][]" 
                 value="PATCH"
                 <%= 'checked' if data_source.connection_config_value('allowed_methods')&.include?('PATCH') %>
                 class="w-4 h-4 text-primary-600 bg-white border-slate-300 rounded focus:ring-primary-500 focus:ring-2">
          <span class="ml-3 text-sm font-medium text-slate-900">PATCH</span>
        </label>
      </div>
    </div>

    <!-- Content Type -->
    <div class="group">
      <label class="block text-sm font-semibold text-slate-700 mb-3">
        <i class="fas fa-file-code mr-2 text-primary-500" aria-hidden="true"></i>
        Expected Content Type
      </label>
      <select name="data_source[connection_config][content_type]" 
              class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200">
        <option value="application/json" <%= 'selected' if data_source.connection_config_value('content_type') == 'application/json' || data_source.connection_config_value('content_type').nil? %>>
          JSON (application/json)
        </option>
        <option value="application/x-www-form-urlencoded" <%= 'selected' if data_source.connection_config_value('content_type') == 'application/x-www-form-urlencoded' %>>
          Form Data (application/x-www-form-urlencoded)
        </option>
        <option value="text/plain" <%= 'selected' if data_source.connection_config_value('content_type') == 'text/plain' %>>
          Plain Text (text/plain)
        </option>
        <option value="text/csv" <%= 'selected' if data_source.connection_config_value('content_type') == 'text/csv' %>>
          CSV (text/csv)
        </option>
      </select>
    </div>
  </div>

  <!-- Security Configuration -->
  <div class="p-6 bg-slate-50 rounded-2xl border border-slate-200">
    <h3 class="text-lg font-semibold text-slate-900 mb-4 flex items-center">
      <i class="fas fa-shield-alt mr-3 text-emerald-500" aria-hidden="true"></i>
      Security & Authentication
    </h3>
    
    <div class="space-y-6">
      <!-- Authentication Type -->
      <div class="group">
        <label class="block text-sm font-semibold text-slate-700 mb-3">
          <i class="fas fa-key mr-2 text-primary-500" aria-hidden="true"></i>
          Authentication Method
        </label>
        <select name="data_source[connection_config][auth_type]" 
                class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                onchange="toggleWebhookAuthFields(this.value)">
          <option value="none" <%= 'selected' if data_source.connection_config_value('auth_type') == 'none' || data_source.connection_config_value('auth_type').nil? %>>
            None - Accept all requests
          </option>
          <option value="secret_token" <%= 'selected' if data_source.connection_config_value('auth_type') == 'secret_token' %>>
            Secret Token
          </option>
          <option value="hmac_signature" <%= 'selected' if data_source.connection_config_value('auth_type') == 'hmac_signature' %>>
            HMAC Signature (GitHub style)
          </option>
          <option value="basic_auth" <%= 'selected' if data_source.connection_config_value('auth_type') == 'basic_auth' %>>
            Basic Authentication
          </option>
        </select>
      </div>

      <!-- Secret Token Fields -->
      <div id="secret-token-fields" class="webhook-auth-fields space-y-4" style="display: none;">
        <div>
          <label class="block text-sm font-semibold text-slate-700 mb-3">
            <i class="fas fa-key mr-2 text-primary-500" aria-hidden="true"></i>
            Secret Token
          </label>
          <div class="flex space-x-2">
            <input type="password" 
                   name="data_source[connection_config][secret_token]" 
                   value="<%= data_source.connection_config_value('secret_token') %>"
                   class="flex-1 px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                   placeholder="Your secret token">
            <button type="button" 
                    class="px-4 py-3 bg-slate-600 text-white rounded-xl hover:bg-slate-700 transition-colors duration-200"
                    onclick="generateSecretToken()"
                    aria-label="Generate random secret token">
              <i class="fas fa-random" aria-hidden="true"></i>
            </button>
          </div>
          <p class="mt-2 text-sm text-slate-600">
            Include this token in the request header: <code class="bg-slate-100 px-2 py-1 rounded">X-Secret-Token</code>
          </p>
        </div>
      </div>

      <!-- HMAC Signature Fields -->
      <div id="hmac-signature-fields" class="webhook-auth-fields space-y-4" style="display: none;">
        <div>
          <label class="block text-sm font-semibold text-slate-700 mb-3">
            <i class="fas fa-shield-alt mr-2 text-primary-500" aria-hidden="true"></i>
            HMAC Secret
          </label>
          <div class="flex space-x-2">
            <input type="password" 
                   name="data_source[connection_config][hmac_secret]" 
                   value="<%= data_source.connection_config_value('hmac_secret') %>"
                   class="flex-1 px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                   placeholder="Your HMAC secret">
            <button type="button" 
                    class="px-4 py-3 bg-slate-600 text-white rounded-xl hover:bg-slate-700 transition-colors duration-200"
                    onclick="generateHmacSecret()"
                    aria-label="Generate random HMAC secret">
              <i class="fas fa-random" aria-hidden="true"></i>
            </button>
          </div>
          <p class="mt-2 text-sm text-slate-600">
            Include HMAC signature in header: <code class="bg-slate-100 px-2 py-1 rounded">X-Hub-Signature-256</code>
          </p>
        </div>
      </div>

      <!-- Basic Auth Fields -->
      <div id="basic-auth-fields" class="webhook-auth-fields space-y-4" style="display: none;">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-semibold text-slate-700 mb-3">
              <i class="fas fa-user mr-2 text-primary-500" aria-hidden="true"></i>
              Username
            </label>
            <input type="text" 
                   name="data_source[connection_config][basic_username]" 
                   value="<%= data_source.connection_config_value('basic_username') %>"
                   class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                   placeholder="webhook_user">
          </div>
          <div>
            <label class="block text-sm font-semibold text-slate-700 mb-3">
              <i class="fas fa-lock mr-2 text-primary-500" aria-hidden="true"></i>
              Password
            </label>
            <input type="password" 
                   name="data_source[connection_config][basic_password]" 
                   value="<%= data_source.connection_config_value('basic_password') %>"
                   class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                   placeholder="Your password">
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Data Processing -->
  <details class="group/processing">
    <summary class="flex items-center justify-between p-4 bg-slate-100 rounded-xl cursor-pointer hover:bg-slate-200 transition-colors duration-200">
      <div class="flex items-center">
        <i class="fas fa-magic mr-3 text-slate-600" aria-hidden="true"></i>
        <span class="font-semibold text-slate-900">Data Processing Options</span>
      </div>
      <i class="fas fa-chevron-down text-slate-500 transition-transform duration-200 group-open/processing:rotate-180" aria-hidden="true"></i>
    </summary>
    
    <div class="mt-4 p-6 bg-white rounded-xl border border-slate-200 space-y-6">
      <!-- JSON Path -->
      <div>
        <label class="block text-sm font-semibold text-slate-700 mb-3">
          <i class="fas fa-route mr-2 text-primary-500" aria-hidden="true"></i>
          JSON Path (for nested data)
        </label>
        <input type="text" 
               name="data_source[connection_config][json_path]" 
               value="<%= data_source.connection_config_value('json_path') %>"
               class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
               placeholder="$.data or $.payload.items"
               aria-describedby="json-path-help">
        <p id="json-path-help" class="mt-2 text-sm text-slate-600">
          JSONPath expression to extract data from nested webhook payloads
        </p>
      </div>

      <!-- Processing Options -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <label class="flex items-center cursor-pointer group">
          <input type="checkbox" 
                 name="data_source[connection_config][batch_processing]" 
                 value="true"
                 <%= 'checked' if data_source.connection_config_value('batch_processing') %>
                 class="w-5 h-5 text-primary-600 bg-white border-slate-300 rounded focus:ring-primary-500 focus:ring-2">
          <div class="ml-4">
            <div class="font-semibold text-slate-900 group-hover:text-slate-700">Batch processing</div>
            <div class="text-sm text-slate-600">Process multiple records in batches</div>
          </div>
        </label>
        
        <label class="flex items-center cursor-pointer group">
          <input type="checkbox" 
                 name="data_source[connection_config][validate_schema]" 
                 value="true"
                 <%= 'checked' if data_source.connection_config_value('validate_schema') %>
                 class="w-5 h-5 text-primary-600 bg-white border-slate-300 rounded focus:ring-primary-500 focus:ring-2">
          <div class="ml-4">
            <div class="font-semibold text-slate-900 group-hover:text-slate-700">Schema validation</div>
            <div class="text-sm text-slate-600">Validate incoming data against schema</div>
          </div>
        </label>
      </div>
    </div>
  </details>

  <!-- Testing Section -->
  <div class="p-6 bg-blue-50 rounded-2xl border border-blue-200">
    <h3 class="text-lg font-semibold text-blue-900 mb-4 flex items-center">
      <i class="fas fa-vial mr-3 text-blue-600" aria-hidden="true"></i>
      Test Your Webhook
    </h3>
    <p class="text-blue-800 mb-4">
      Send a test payload to verify your webhook configuration is working correctly.
    </p>
    
    <div class="space-y-4">
      <div>
        <label class="block text-sm font-semibold text-blue-700 mb-2">Test Payload (JSON)</label>
        <textarea rows="4" 
                  class="w-full px-4 py-3 bg-white border border-blue-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 font-mono text-sm"
                  placeholder='{"name": "Test Record", "email": "<EMAIL>", "created_at": "2024-01-01T00:00:00Z"}'
                  id="test-payload">{"name": "Test Record", "email": "<EMAIL>", "created_at": "2024-01-01T00:00:00Z"}</textarea>
      </div>
      
      <button type="button" 
              class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-xl shadow-sm hover:bg-blue-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              onclick="sendTestWebhook()"
              id="test-webhook-btn">
        <i class="fas fa-paper-plane mr-2" aria-hidden="true"></i>
        Send Test Webhook
      </button>
    </div>
  </div>
</div>

<script nonce="<%= content_security_policy_nonce %>">
  function toggleWebhookAuthFields(authType) {
    // Hide all auth fields
    const authFields = document.querySelectorAll('.webhook-auth-fields');
    authFields.forEach(field => {
      field.style.display = 'none';
    });
    
    // Show selected auth fields
    if (authType !== 'none') {
      const targetField = document.getElementById(`${authType.replace('_', '-')}-fields`);
      if (targetField) {
        targetField.style.display = 'block';
      }
    }
  }
  
  function generateSecretToken() {
    const token = generateRandomString(32);
    const input = document.querySelector('input[name*="secret_token"]');
    if (input) {
      input.value = token;
      input.type = 'text';
      setTimeout(() => { input.type = 'password'; }, 2000);
    }
  }
  
  function generateHmacSecret() {
    const secret = generateRandomString(64);
    const input = document.querySelector('input[name*="hmac_secret"]');
    if (input) {
      input.value = secret;
      input.type = 'text';
      setTimeout(() => { input.type = 'password'; }, 2000);
    }
  }
  
  function generateRandomString(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
  
  function copyWebhookUrl() {
    const urlElement = document.querySelector('.bg-slate-900 .text-green-400');
    if (urlElement) {
      navigator.clipboard.writeText(urlElement.textContent).then(() => {
        // Show success feedback
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check mr-2"></i>Copied!';
        button.classList.add('bg-emerald-600');
        button.classList.remove('bg-primary-600');
        
        setTimeout(() => {
          button.innerHTML = originalText;
          button.classList.remove('bg-emerald-600');
          button.classList.add('bg-primary-600');
        }, 2000);
      });
    }
  }
  
  function sendTestWebhook() {
    const payload = document.getElementById('test-payload').value;
    const button = document.getElementById('test-webhook-btn');
    
    // Show loading state
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';
    
    // Simulate webhook test (replace with actual implementation)
    setTimeout(() => {
      button.innerHTML = '<i class="fas fa-check mr-2"></i>Test Sent!';
      button.classList.add('bg-emerald-600');
      button.classList.remove('bg-blue-600');
      
      setTimeout(() => {
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-paper-plane mr-2"></i>Send Test Webhook';
        button.classList.remove('bg-emerald-600');
        button.classList.add('bg-blue-600');
      }, 3000);
    }, 2000);
  }
  
  // Initialize auth fields on page load
  document.addEventListener('DOMContentLoaded', function() {
    const authSelect = document.querySelector('select[name*="auth_type"]');
    if (authSelect) {
      toggleWebhookAuthFields(authSelect.value);
    }
  });
</script>
