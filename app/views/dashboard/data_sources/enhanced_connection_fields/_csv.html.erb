<div class="space-y-8" data-controller="enhanced-csv-form">
  <!-- File Source Selection -->
  <div class="group">
    <label class="block text-sm font-semibold text-slate-700 mb-4">
      <i class="fas fa-file-csv mr-2 text-primary-500" aria-hidden="true"></i>
      CSV Source
    </label>
    
    <!-- Enhanced Tab Navigation -->
    <div class="bg-slate-100 p-1 rounded-xl mb-6">
      <nav class="flex space-x-1" role="tablist" aria-label="CSV source options">
        <button type="button"
                class="csv-source-tab flex-1 py-3 px-4 text-sm font-semibold rounded-lg transition-all duration-200 bg-white text-primary-600 shadow-sm"
                data-tab="upload"
                data-enhanced-csv-form-target="tabButton"
                data-action="click->enhanced-csv-form#switchTab"
                role="tab"
                aria-selected="true"
                aria-controls="csv-upload-panel">
          <i class="fas fa-upload mr-2" aria-hidden="true"></i>
          Upload File
        </button>
        <button type="button"
                class="csv-source-tab flex-1 py-3 px-4 text-sm font-semibold rounded-lg transition-all duration-200 text-slate-600 hover:text-slate-800 hover:bg-slate-50"
                data-tab="url"
                data-enhanced-csv-form-target="tabButton"
                data-action="click->enhanced-csv-form#switchTab"
                role="tab"
                aria-selected="false"
                aria-controls="csv-url-panel">
          <i class="fas fa-link mr-2" aria-hidden="true"></i>
          Remote URL
        </button>
      </nav>
    </div>
    
    <!-- Upload Tab Content -->
    <div id="csv-upload-panel" 
         class="csv-tab-content" 
         data-enhanced-csv-form-target="tabContent"
         role="tabpanel"
         aria-labelledby="upload-tab">
      
      <!-- Existing File Display -->
      <% if data_source.persisted? && data_source.connection_config_value('original_filename').present? %>
        <div class="mb-6 p-6 bg-gradient-to-r from-emerald-50 to-emerald-50/50 border border-emerald-200 rounded-2xl">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-emerald-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-check-circle text-emerald-600 text-lg" aria-hidden="true"></i>
              </div>
            </div>
            <div class="ml-4 flex-1">
              <h4 class="font-semibold text-emerald-900 mb-2">Current File</h4>
              <div class="bg-white rounded-xl p-4 border border-emerald-200">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <i class="fas fa-file-csv text-emerald-600 text-xl mr-3" aria-hidden="true"></i>
                    <div>
                      <p class="font-medium text-emerald-900">
                        <%= data_source.connection_config_value('original_filename') %>
                      </p>
                      <div class="flex items-center space-x-4 text-sm text-emerald-700 mt-1">
                        <% if data_source.connection_config_value('file_size').present? %>
                          <span class="flex items-center">
                            <i class="fas fa-weight mr-1" aria-hidden="true"></i>
                            <%= number_to_human_size(data_source.connection_config_value('file_size')) %>
                          </span>
                        <% end %>
                        <% if data_source.connection_config_value('uploaded_at').present? %>
                          <span class="flex items-center">
                            <i class="fas fa-clock mr-1" aria-hidden="true"></i>
                            Uploaded <%= time_ago_in_words(data_source.connection_config_value('uploaded_at')) %> ago
                          </span>
                        <% end %>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <p class="text-sm text-emerald-700 mt-3 flex items-center">
                <i class="fas fa-info-circle mr-2" aria-hidden="true"></i>
                Upload a new file below to replace the existing one
              </p>
            </div>
          </div>
        </div>
      <% end %>
      
      <!-- Enhanced File Upload Area -->
      <div class="relative">
        <label for="csv_file" class="block cursor-pointer group">
          <div class="relative border-2 border-dashed border-slate-300 rounded-2xl p-8 text-center hover:border-primary-400 hover:bg-primary-50/50 transition-all duration-300 group-hover:scale-105">
            <!-- Upload Icon -->
            <div class="w-16 h-16 mx-auto mb-4 bg-slate-100 group-hover:bg-primary-100 rounded-2xl flex items-center justify-center transition-colors duration-200">
              <i class="fas fa-cloud-upload-alt text-2xl text-slate-500 group-hover:text-primary-600" aria-hidden="true"></i>
            </div>
            
            <!-- Upload Text -->
            <div class="space-y-2">
              <p class="text-lg font-semibold text-slate-900 group-hover:text-primary-900">
                <%= data_source.persisted? && data_source.connection_config_value('original_filename').present? ? 'Upload a new CSV file' : 'Upload your CSV file' %>
              </p>
              <p class="text-slate-600">
                Drag and drop your file here, or click to browse
              </p>
              <p class="text-sm text-slate-500">
                Supports CSV files up to 500MB
              </p>
            </div>
            
            <!-- Supported Formats -->
            <div class="mt-6 flex items-center justify-center space-x-4 text-xs text-slate-500">
              <span class="flex items-center">
                <i class="fas fa-file-csv mr-1" aria-hidden="true"></i>
                .csv
              </span>
              <span class="flex items-center">
                <i class="fas fa-file-alt mr-1" aria-hidden="true"></i>
                .txt
              </span>
              <span class="flex items-center">
                <i class="fas fa-compress mr-1" aria-hidden="true"></i>
                .gz
              </span>
            </div>
          </div>
          
          <input id="csv_file" 
                 name="data_source[csv_file]" 
                 type="file" 
                 class="sr-only" 
                 accept=".csv,.txt,.gz,text/csv,text/plain"
                 data-enhanced-csv-form-target="fileInput"
                 data-action="change->enhanced-csv-form#handleFileChange">
        </label>
        
        <!-- File Preview -->
        <div class="hidden mt-6 p-6 bg-white rounded-2xl border border-slate-200 shadow-sm" 
             data-enhanced-csv-form-target="filePreview">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-12 h-12 bg-emerald-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-file-csv text-emerald-600 text-lg" aria-hidden="true"></i>
              </div>
              <div class="ml-4">
                <p class="font-semibold text-slate-900" data-enhanced-csv-form-target="fileName"></p>
                <p class="text-sm text-slate-600" data-enhanced-csv-form-target="fileSize"></p>
              </div>
            </div>
            <button type="button" 
                    class="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg transition-colors duration-200"
                    data-action="click->enhanced-csv-form#clearFile"
                    aria-label="Remove selected file">
              <i class="fas fa-times" aria-hidden="true"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- URL Tab Content -->
    <div id="csv-url-panel" 
         class="csv-tab-content hidden" 
         data-enhanced-csv-form-target="tabContent"
         role="tabpanel"
         aria-labelledby="url-tab">
      
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-semibold text-slate-700 mb-3">
            <i class="fas fa-link mr-2 text-primary-500" aria-hidden="true"></i>
            File URL
          </label>
          <input type="url" 
                 name="data_source[connection_config][file_url]" 
                 value="<%= data_source.connection_config_value('file_url') %>"
                 class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                 placeholder="https://example.com/data.csv or s3://bucket/file.csv"
                 aria-describedby="url-help">
          <p id="url-help" class="mt-2 text-sm text-slate-600 flex items-center">
            <i class="fas fa-info-circle mr-2 text-blue-500" aria-hidden="true"></i>
            Supports HTTP/HTTPS, FTP, or S3 URLs
          </p>
        </div>
        
        <!-- URL Examples -->
        <div class="p-4 bg-blue-50 rounded-xl border border-blue-200">
          <h4 class="font-semibold text-blue-900 mb-2">Example URLs:</h4>
          <ul class="space-y-1 text-sm text-blue-800">
            <li class="flex items-center">
              <i class="fas fa-globe mr-2" aria-hidden="true"></i>
              https://example.com/data/sales.csv
            </li>
            <li class="flex items-center">
              <i class="fas fa-cloud mr-2" aria-hidden="true"></i>
              s3://my-bucket/exports/customers.csv
            </li>
            <li class="flex items-center">
              <i class="fas fa-server mr-2" aria-hidden="true"></i>
              ftp://ftp.example.com/data/inventory.csv
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <!-- CSV Configuration -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- Delimiter -->
    <div class="group">
      <label class="block text-sm font-semibold text-slate-700 mb-3">
        <i class="fas fa-columns mr-2 text-primary-500" aria-hidden="true"></i>
        Delimiter
      </label>
      <select name="data_source[connection_config][delimiter]" 
              class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200">
        <option value="">Auto-detect</option>
        <option value="," <%= 'selected' if data_source.connection_config_value('delimiter') == ',' %>>Comma (,)</option>
        <option value=";" <%= 'selected' if data_source.connection_config_value('delimiter') == ';' %>>Semicolon (;)</option>
        <option value="\t" <%= 'selected' if data_source.connection_config_value('delimiter') == "\t" %>>Tab</option>
        <option value="|" <%= 'selected' if data_source.connection_config_value('delimiter') == '|' %>>Pipe (|)</option>
      </select>
    </div>
    
    <!-- Encoding -->
    <div class="group">
      <label class="block text-sm font-semibold text-slate-700 mb-3">
        <i class="fas fa-font mr-2 text-primary-500" aria-hidden="true"></i>
        Encoding
      </label>
      <select name="data_source[connection_config][encoding]" 
              class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200">
        <option value="">Auto-detect</option>
        <option value="UTF-8" <%= 'selected' if data_source.connection_config_value('encoding') == 'UTF-8' %>>UTF-8</option>
        <option value="ISO-8859-1" <%= 'selected' if data_source.connection_config_value('encoding') == 'ISO-8859-1' %>>ISO-8859-1 (Latin-1)</option>
        <option value="Windows-1252" <%= 'selected' if data_source.connection_config_value('encoding') == 'Windows-1252' %>>Windows-1252</option>
        <option value="ASCII-8BIT" <%= 'selected' if data_source.connection_config_value('encoding') == 'ASCII-8BIT' %>>ASCII-8BIT</option>
      </select>
    </div>
  </div>

  <!-- Headers Option -->
  <div class="p-6 bg-slate-50 rounded-2xl border border-slate-200">
    <label class="flex items-center cursor-pointer group">
      <input type="hidden" name="data_source[connection_config][has_headers]" value="false">
      <input type="checkbox" 
             name="data_source[connection_config][has_headers]" 
             value="true"
             <%= 'checked' if data_source.connection_config_value('has_headers') != false %>
             class="w-5 h-5 text-primary-600 bg-white border-slate-300 rounded focus:ring-primary-500 focus:ring-2">
      <div class="ml-4">
        <div class="font-semibold text-slate-900 group-hover:text-slate-700">First row contains headers</div>
        <div class="text-sm text-slate-600">Enable this if your CSV file has column names in the first row</div>
      </div>
    </label>
  </div>

  <!-- Authentication Section -->
  <details class="group/auth">
    <summary class="flex items-center justify-between p-4 bg-slate-100 rounded-xl cursor-pointer hover:bg-slate-200 transition-colors duration-200">
      <div class="flex items-center">
        <i class="fas fa-shield-alt mr-3 text-slate-600" aria-hidden="true"></i>
        <span class="font-semibold text-slate-900">Authentication & Security</span>
        <span class="ml-2 text-sm text-slate-500">(for remote files)</span>
      </div>
      <i class="fas fa-chevron-down text-slate-500 transition-transform duration-200 group-open/auth:rotate-180" aria-hidden="true"></i>
    </summary>
    
    <div class="mt-4 p-6 bg-white rounded-xl border border-slate-200 space-y-6">
      <!-- Basic Auth -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-semibold text-slate-700 mb-3">
            <i class="fas fa-user mr-2 text-primary-500" aria-hidden="true"></i>
            Username
          </label>
          <input type="text" 
                 name="data_source[connection_config][username]" 
                 value="<%= data_source.connection_config_value('username') %>"
                 class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200" 
                 placeholder="Optional">
        </div>
        
        <div>
          <label class="block text-sm font-semibold text-slate-700 mb-3">
            <i class="fas fa-lock mr-2 text-primary-500" aria-hidden="true"></i>
            Password
          </label>
          <input type="password" 
                 name="data_source[connection_config][password]" 
                 value="<%= data_source.connection_config_value('password') %>"
                 class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200" 
                 placeholder="Optional">
        </div>
      </div>
      
      <!-- S3 Configuration -->
      <div>
        <label class="block text-sm font-semibold text-slate-700 mb-3">
          <i class="fab fa-aws mr-2 text-primary-500" aria-hidden="true"></i>
          S3 Configuration (JSON)
        </label>
        <textarea name="data_source[connection_config][s3_config]" 
                  rows="4" 
                  class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 font-mono text-sm" 
                  placeholder='{"region": "us-east-1", "access_key_id": "...", "secret_access_key": "..."}'><%= data_source.connection_config_value('s3_config') %></textarea>
        <p class="mt-2 text-sm text-slate-600">
          For S3 URLs, provide AWS credentials and configuration in JSON format
        </p>
      </div>
    </div>
  </details>

  <!-- Data Transformations -->
  <details class="group/transform">
    <summary class="flex items-center justify-between p-4 bg-slate-100 rounded-xl cursor-pointer hover:bg-slate-200 transition-colors duration-200">
      <div class="flex items-center">
        <i class="fas fa-magic mr-3 text-slate-600" aria-hidden="true"></i>
        <span class="font-semibold text-slate-900">Data Transformations</span>
        <span class="ml-2 text-sm text-slate-500">(optional)</span>
      </div>
      <i class="fas fa-chevron-down text-slate-500 transition-transform duration-200 group-open/transform:rotate-180" aria-hidden="true"></i>
    </summary>
    
    <div class="mt-4 p-6 bg-white rounded-xl border border-slate-200">
      <div class="mb-4">
        <label class="block text-sm font-semibold text-slate-700 mb-3">
          <i class="fas fa-code mr-2 text-primary-500" aria-hidden="true"></i>
          Transformation Rules (JSON)
        </label>
        <textarea name="data_source[connection_config][transformations]" 
                  rows="6" 
                  class="w-full px-4 py-3 bg-slate-900 text-green-400 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 font-mono text-sm" 
                  placeholder='{"field_name": [{"type": "trim"}, {"type": "uppercase"}]}'><%= data_source.connection_config_value('transformations')&.to_json %></textarea>
      </div>
      
      <!-- Transformation Examples -->
      <div class="p-4 bg-amber-50 rounded-xl border border-amber-200">
        <h4 class="font-semibold text-amber-900 mb-2">Example Transformations:</h4>
        <div class="space-y-2 text-sm text-amber-800 font-mono">
          <div>{"email": [{"type": "lowercase"}, {"type": "trim"}]}</div>
          <div>{"phone": [{"type": "format", "pattern": "(###) ###-####"}]}</div>
          <div>{"date": [{"type": "parse_date", "format": "MM/dd/yyyy"}]}</div>
        </div>
      </div>
    </div>
  </details>
</div>
