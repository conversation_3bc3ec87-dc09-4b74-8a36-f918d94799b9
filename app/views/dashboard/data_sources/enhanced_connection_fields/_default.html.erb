<div class="space-y-8">
  <!-- Coming Soon Message -->
  <div class="text-center py-16 bg-gradient-to-br from-slate-50 to-slate-100 rounded-2xl border-2 border-dashed border-slate-300">
    <div class="w-20 h-20 bg-slate-200 rounded-full flex items-center justify-center mx-auto mb-6">
      <i class="<%= data_source_icon(data_source.source_type) %> text-slate-500 text-2xl" aria-hidden="true"></i>
    </div>
    <h3 class="text-2xl font-bold text-slate-900 mb-3">
      <%= data_source.source_type_text %> Connector
    </h3>
    <p class="text-slate-600 mb-8 max-w-md mx-auto">
      The enhanced configuration interface for <%= data_source.source_type_text %> is currently under development. 
      We're working hard to bring you the best possible experience.
    </p>
    
    <!-- Feature Preview -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto mb-8">
      <div class="p-4 bg-white rounded-xl border border-slate-200">
        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
          <i class="fas fa-magic text-blue-600" aria-hidden="true"></i>
        </div>
        <h4 class="font-semibold text-slate-900 text-sm">Auto-Configuration</h4>
        <p class="text-xs text-slate-600 mt-1">Smart defaults and validation</p>
      </div>
      
      <div class="p-4 bg-white rounded-xl border border-slate-200">
        <div class="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center mx-auto mb-2">
          <i class="fas fa-shield-alt text-emerald-600" aria-hidden="true"></i>
        </div>
        <h4 class="font-semibold text-slate-900 text-sm">Secure Connection</h4>
        <p class="text-xs text-slate-600 mt-1">Enterprise-grade security</p>
      </div>
      
      <div class="p-4 bg-white rounded-xl border border-slate-200">
        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-2">
          <i class="fas fa-bolt text-purple-600" aria-hidden="true"></i>
        </div>
        <h4 class="font-semibold text-slate-900 text-sm">Real-time Sync</h4>
        <p class="text-xs text-slate-600 mt-1">Live data synchronization</p>
      </div>
    </div>
    
    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
      <a href="mailto:<EMAIL>?subject=Early Access: <%= data_source.source_type_text %> Connector" 
         class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white font-semibold rounded-xl shadow-lg hover:from-primary-600 hover:to-primary-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 hover:shadow-xl transform hover:scale-105">
        <i class="fas fa-envelope mr-2" aria-hidden="true"></i>
        Request Early Access
      </a>
      
      <a href="https://docs.datareflow.com/connectors/<%= data_source.source_type %>" 
         target="_blank"
         class="inline-flex items-center px-6 py-3 border border-slate-300 text-slate-700 bg-white font-semibold rounded-xl shadow-sm hover:bg-slate-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 hover:shadow-md">
        <i class="fas fa-book mr-2" aria-hidden="true"></i>
        View Documentation
      </a>
    </div>
  </div>

  <!-- Temporary Configuration -->
  <details class="group/temp">
    <summary class="flex items-center justify-between p-4 bg-amber-50 border border-amber-200 rounded-xl cursor-pointer hover:bg-amber-100 transition-colors duration-200">
      <div class="flex items-center">
        <i class="fas fa-tools mr-3 text-amber-600" aria-hidden="true"></i>
        <span class="font-semibold text-amber-900">Basic Configuration</span>
        <span class="ml-2 text-sm text-amber-700">(Temporary)</span>
      </div>
      <i class="fas fa-chevron-down text-amber-600 transition-transform duration-200 group-open/temp:rotate-180" aria-hidden="true"></i>
    </summary>
    
    <div class="mt-4 p-6 bg-white rounded-xl border border-amber-200 space-y-6">
      <div class="p-4 bg-amber-50 rounded-lg border border-amber-200">
        <div class="flex items-start">
          <i class="fas fa-info-circle text-amber-600 mt-1 mr-3" aria-hidden="true"></i>
          <div>
            <h4 class="font-semibold text-amber-900 mb-1">Basic Configuration Mode</h4>
            <p class="text-sm text-amber-800">
              This is a simplified configuration interface. The full enhanced interface with advanced features 
              will be available soon.
            </p>
          </div>
        </div>
      </div>

      <!-- Basic Connection String -->
      <div>
        <label class="block text-sm font-semibold text-slate-700 mb-3">
          <i class="fas fa-link mr-2 text-primary-500" aria-hidden="true"></i>
          Connection String
        </label>
        <textarea name="data_source[connection_config][connection_string]" 
                  rows="4" 
                  class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 font-mono text-sm" 
                  placeholder="Enter your connection string or configuration details..."
                  aria-describedby="connection-string-help"><%= data_source.connection_config_value('connection_string') %></textarea>
        <p id="connection-string-help" class="mt-2 text-sm text-slate-600">
          Provide the connection details specific to your <%= data_source.source_type_text %> setup.
        </p>
      </div>

      <!-- Basic Authentication -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-semibold text-slate-700 mb-3">
            <i class="fas fa-user mr-2 text-primary-500" aria-hidden="true"></i>
            Username/API Key
          </label>
          <input type="text" 
                 name="data_source[connection_config][username]" 
                 value="<%= data_source.connection_config_value('username') %>"
                 class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200" 
                 placeholder="Username or API key">
        </div>
        
        <div>
          <label class="block text-sm font-semibold text-slate-700 mb-3">
            <i class="fas fa-lock mr-2 text-primary-500" aria-hidden="true"></i>
            Password/Secret
          </label>
          <input type="password" 
                 name="data_source[connection_config][password]" 
                 value="<%= data_source.connection_config_value('password') %>"
                 class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200" 
                 placeholder="Password or secret key">
        </div>
      </div>

      <!-- Additional Configuration -->
      <div>
        <label class="block text-sm font-semibold text-slate-700 mb-3">
          <i class="fas fa-cog mr-2 text-primary-500" aria-hidden="true"></i>
          Additional Configuration (JSON)
        </label>
        <textarea name="data_source[connection_config][additional_config]" 
                  rows="6" 
                  class="w-full px-4 py-3 bg-slate-900 text-green-400 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 font-mono text-sm" 
                  placeholder='{"option1": "value1", "option2": "value2"}'><%= data_source.connection_config_value('additional_config')&.to_json %></textarea>
        <p class="mt-2 text-sm text-slate-600">
          Additional configuration options in JSON format. Refer to the documentation for available options.
        </p>
      </div>
    </div>
  </details>

  <!-- Help and Support -->
  <div class="p-6 bg-blue-50 rounded-2xl border border-blue-200">
    <h3 class="text-lg font-semibold text-blue-900 mb-4 flex items-center">
      <i class="fas fa-question-circle mr-3 text-blue-600" aria-hidden="true"></i>
      Need Help?
    </h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div class="bg-white rounded-xl p-4 border border-blue-200">
        <h4 class="font-semibold text-blue-900 mb-2 flex items-center">
          <i class="fas fa-book mr-2" aria-hidden="true"></i>
          Documentation
        </h4>
        <p class="text-sm text-blue-800 mb-3">
          Check our comprehensive guides for <%= data_source.source_type_text %> integration.
        </p>
        <a href="https://docs.datareflow.com/connectors/<%= data_source.source_type %>" 
           target="_blank"
           class="text-blue-600 hover:text-blue-700 text-sm font-medium">
          View Docs →
        </a>
      </div>
      
      <div class="bg-white rounded-xl p-4 border border-blue-200">
        <h4 class="font-semibold text-blue-900 mb-2 flex items-center">
          <i class="fas fa-headset mr-2" aria-hidden="true"></i>
          Support Team
        </h4>
        <p class="text-sm text-blue-800 mb-3">
          Our experts are here to help you get connected quickly.
        </p>
        <a href="mailto:<EMAIL>?subject=Help with <%= data_source.source_type_text %> Connection" 
           class="text-blue-600 hover:text-blue-700 text-sm font-medium">
          Contact Support →
        </a>
      </div>
    </div>
  </div>
</div>
