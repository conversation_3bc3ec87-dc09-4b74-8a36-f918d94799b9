<div class="space-y-8">
  <!-- Connection Details -->
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- Host -->
    <div class="group">
      <label class="block text-sm font-semibold text-slate-700 mb-3">
        <i class="fas fa-server mr-2 text-primary-500" aria-hidden="true"></i>
        Host <span class="text-red-500">*</span>
      </label>
      <input type="text" 
             name="data_source[connection_config][host]" 
             value="<%= data_source.connection_config_value('host') %>"
             class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
             placeholder="localhost or database.example.com"
             required
             aria-describedby="host-help">
      <p id="host-help" class="mt-2 text-sm text-slate-600 flex items-center">
        <i class="fas fa-info-circle mr-2 text-blue-500" aria-hidden="true"></i>
        The hostname or IP address of your PostgreSQL server
      </p>
    </div>
    
    <!-- Port -->
    <div class="group">
      <label class="block text-sm font-semibold text-slate-700 mb-3">
        <i class="fas fa-plug mr-2 text-primary-500" aria-hidden="true"></i>
        Port <span class="text-red-500">*</span>
      </label>
      <input type="number" 
             name="data_source[connection_config][port]" 
             value="<%= data_source.connection_config_value('port') || 5432 %>"
             class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
             placeholder="5432"
             min="1"
             max="65535"
             required
             aria-describedby="port-help">
      <p id="port-help" class="mt-2 text-sm text-slate-600 flex items-center">
        <i class="fas fa-info-circle mr-2 text-blue-500" aria-hidden="true"></i>
        Default PostgreSQL port is 5432
      </p>
    </div>
    
    <!-- Database -->
    <div class="group">
      <label class="block text-sm font-semibold text-slate-700 mb-3">
        <i class="fas fa-database mr-2 text-primary-500" aria-hidden="true"></i>
        Database <span class="text-red-500">*</span>
      </label>
      <input type="text" 
             name="data_source[connection_config][database]" 
             value="<%= data_source.connection_config_value('database') %>"
             class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
             placeholder="mydatabase"
             required
             aria-describedby="database-help">
      <p id="database-help" class="mt-2 text-sm text-slate-600 flex items-center">
        <i class="fas fa-info-circle mr-2 text-blue-500" aria-hidden="true"></i>
        The name of the PostgreSQL database to connect to
      </p>
    </div>
    
    <!-- Username -->
    <div class="group">
      <label class="block text-sm font-semibold text-slate-700 mb-3">
        <i class="fas fa-user mr-2 text-primary-500" aria-hidden="true"></i>
        Username <span class="text-red-500">*</span>
      </label>
      <input type="text" 
             name="data_source[connection_config][username]" 
             value="<%= data_source.connection_config_value('username') %>"
             class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
             placeholder="postgres"
             required
             aria-describedby="username-help">
      <p id="username-help" class="mt-2 text-sm text-slate-600 flex items-center">
        <i class="fas fa-info-circle mr-2 text-blue-500" aria-hidden="true"></i>
        Database user with appropriate permissions
      </p>
    </div>
  </div>

  <!-- Password -->
  <div class="group">
    <label class="block text-sm font-semibold text-slate-700 mb-3">
      <i class="fas fa-lock mr-2 text-primary-500" aria-hidden="true"></i>
      Password
    </label>
    <div class="relative">
      <input type="password" 
             name="data_source[connection_config][password]" 
             value="<%= data_source.connection_config_value('password') %>"
             class="w-full px-4 py-3 pr-12 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
             placeholder="••••••••"
             aria-describedby="password-help">
      <button type="button" 
              class="absolute inset-y-0 right-0 pr-4 flex items-center text-slate-400 hover:text-slate-600"
              onclick="togglePasswordVisibility(this)"
              aria-label="Toggle password visibility">
        <i class="fas fa-eye" aria-hidden="true"></i>
      </button>
    </div>
    <div class="mt-2 p-3 bg-emerald-50 rounded-lg border border-emerald-200">
      <p id="password-help" class="text-sm text-emerald-800 flex items-center">
        <i class="fas fa-shield-alt mr-2 text-emerald-600" aria-hidden="true"></i>
        Password is encrypted and stored securely using industry-standard encryption
      </p>
    </div>
  </div>

  <!-- SSL Configuration -->
  <div class="p-6 bg-slate-50 rounded-2xl border border-slate-200">
    <h3 class="text-lg font-semibold text-slate-900 mb-4 flex items-center">
      <i class="fas fa-shield-alt mr-3 text-emerald-500" aria-hidden="true"></i>
      SSL Configuration
    </h3>
    
    <div class="space-y-4">
      <div class="group">
        <label class="block text-sm font-semibold text-slate-700 mb-3">
          <i class="fas fa-certificate mr-2 text-primary-500" aria-hidden="true"></i>
          SSL Mode
        </label>
        <select name="data_source[connection_config][sslmode]" 
                class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                aria-describedby="ssl-help">
          <option value="disable" <%= 'selected' if data_source.connection_config_value('sslmode') == 'disable' %>>
            Disable - No SSL encryption
          </option>
          <option value="allow" <%= 'selected' if data_source.connection_config_value('sslmode') == 'allow' %>>
            Allow - SSL if available
          </option>
          <option value="prefer" <%= 'selected' if data_source.connection_config_value('sslmode') == 'prefer' || data_source.connection_config_value('sslmode').nil? %>>
            Prefer - SSL preferred (default)
          </option>
          <option value="require" <%= 'selected' if data_source.connection_config_value('sslmode') == 'require' %>>
            Require - SSL required
          </option>
          <option value="verify-ca" <%= 'selected' if data_source.connection_config_value('sslmode') == 'verify-ca' %>>
            Verify CA - SSL with CA verification
          </option>
          <option value="verify-full" <%= 'selected' if data_source.connection_config_value('sslmode') == 'verify-full' %>>
            Verify Full - SSL with full verification
          </option>
        </select>
        <p id="ssl-help" class="mt-2 text-sm text-slate-600">
          Choose the appropriate SSL mode based on your security requirements
        </p>
      </div>

      <!-- SSL Mode Descriptions -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        <div class="p-3 bg-white rounded-lg border border-slate-200">
          <h4 class="font-semibold text-slate-900 mb-2 flex items-center">
            <i class="fas fa-shield text-emerald-500 mr-2" aria-hidden="true"></i>
            Recommended
          </h4>
          <p class="text-slate-600">
            <strong>Prefer</strong> or <strong>Require</strong> for production environments to ensure encrypted connections.
          </p>
        </div>
        <div class="p-3 bg-amber-50 rounded-lg border border-amber-200">
          <h4 class="font-semibold text-amber-900 mb-2 flex items-center">
            <i class="fas fa-exclamation-triangle text-amber-500 mr-2" aria-hidden="true"></i>
            Security Note
          </h4>
          <p class="text-amber-800">
            <strong>Disable</strong> should only be used for local development or trusted networks.
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- Advanced Options -->
  <details class="group/advanced">
    <summary class="flex items-center justify-between p-4 bg-slate-100 rounded-xl cursor-pointer hover:bg-slate-200 transition-colors duration-200">
      <div class="flex items-center">
        <i class="fas fa-cogs mr-3 text-slate-600" aria-hidden="true"></i>
        <span class="font-semibold text-slate-900">Advanced Connection Options</span>
      </div>
      <i class="fas fa-chevron-down text-slate-500 transition-transform duration-200 group-open/advanced:rotate-180" aria-hidden="true"></i>
    </summary>
    
    <div class="mt-4 p-6 bg-white rounded-xl border border-slate-200 space-y-6">
      <!-- Connection Timeout -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-semibold text-slate-700 mb-3">
            <i class="fas fa-clock mr-2 text-primary-500" aria-hidden="true"></i>
            Connection Timeout (seconds)
          </label>
          <input type="number" 
                 name="data_source[connection_config][connect_timeout]" 
                 value="<%= data_source.connection_config_value('connect_timeout') || 30 %>"
                 class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                 min="5"
                 max="300"
                 placeholder="30">
        </div>
        
        <div>
          <label class="block text-sm font-semibold text-slate-700 mb-3">
            <i class="fas fa-hourglass-half mr-2 text-primary-500" aria-hidden="true"></i>
            Query Timeout (seconds)
          </label>
          <input type="number" 
                 name="data_source[connection_config][statement_timeout]" 
                 value="<%= data_source.connection_config_value('statement_timeout') || 300 %>"
                 class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                 min="30"
                 max="3600"
                 placeholder="300">
        </div>
      </div>

      <!-- Schema -->
      <div>
        <label class="block text-sm font-semibold text-slate-700 mb-3">
          <i class="fas fa-sitemap mr-2 text-primary-500" aria-hidden="true"></i>
          Default Schema
        </label>
        <input type="text" 
               name="data_source[connection_config][schema]" 
               value="<%= data_source.connection_config_value('schema') || 'public' %>"
               class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
               placeholder="public">
        <p class="mt-2 text-sm text-slate-600">
          Specify the default schema to use for queries (defaults to 'public')
        </p>
      </div>

      <!-- Connection Pool -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block text-sm font-semibold text-slate-700 mb-3">
            <i class="fas fa-layer-group mr-2 text-primary-500" aria-hidden="true"></i>
            Pool Size
          </label>
          <input type="number" 
                 name="data_source[connection_config][pool_size]" 
                 value="<%= data_source.connection_config_value('pool_size') || 5 %>"
                 class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                 min="1"
                 max="50"
                 placeholder="5">
        </div>
        
        <div>
          <label class="block text-sm font-semibold text-slate-700 mb-3">
            <i class="fas fa-stopwatch mr-2 text-primary-500" aria-hidden="true"></i>
            Pool Timeout (seconds)
          </label>
          <input type="number" 
                 name="data_source[connection_config][pool_timeout]" 
                 value="<%= data_source.connection_config_value('pool_timeout') || 5 %>"
                 class="w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"
                 min="1"
                 max="60"
                 placeholder="5">
        </div>
      </div>

      <!-- Additional Parameters -->
      <div>
        <label class="block text-sm font-semibold text-slate-700 mb-3">
          <i class="fas fa-code mr-2 text-primary-500" aria-hidden="true"></i>
          Additional Connection Parameters
        </label>
        <textarea name="data_source[connection_config][additional_params]" 
                  rows="3" 
                  class="w-full px-4 py-3 bg-slate-900 text-green-400 border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 font-mono text-sm" 
                  placeholder="application_name=DataReflow&#10;search_path=myschema,public"><%= data_source.connection_config_value('additional_params') %></textarea>
        <p class="mt-2 text-sm text-slate-600">
          Additional PostgreSQL connection parameters (one per line, format: key=value)
        </p>
      </div>
    </div>
  </details>

  <!-- Connection String Preview -->
  <div class="p-6 bg-blue-50 rounded-2xl border border-blue-200">
    <h3 class="text-lg font-semibold text-blue-900 mb-4 flex items-center">
      <i class="fas fa-link mr-3 text-blue-600" aria-hidden="true"></i>
      Connection Preview
    </h3>
    <div class="bg-slate-900 rounded-xl p-4 font-mono text-sm">
      <div class="text-green-400">
        postgresql://<span class="text-yellow-400">[username]</span>:<span class="text-red-400">[password]</span>@<span class="text-blue-400">[host]</span>:<span class="text-purple-400">[port]</span>/<span class="text-cyan-400">[database]</span>?sslmode=<span class="text-orange-400">[sslmode]</span>
      </div>
    </div>
    <p class="mt-3 text-sm text-blue-800 flex items-center">
      <i class="fas fa-info-circle mr-2" aria-hidden="true"></i>
      This preview shows the connection string format. Actual credentials are never displayed.
    </p>
  </div>
</div>

<script nonce="<%= content_security_policy_nonce %>">
  function togglePasswordVisibility(button) {
    const input = button.parentElement.querySelector('input');
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
      input.type = 'text';
      icon.classList.remove('fa-eye');
      icon.classList.add('fa-eye-slash');
      button.setAttribute('aria-label', 'Hide password');
    } else {
      input.type = 'password';
      icon.classList.remove('fa-eye-slash');
      icon.classList.add('fa-eye');
      button.setAttribute('aria-label', 'Show password');
    }
  }
</script>
