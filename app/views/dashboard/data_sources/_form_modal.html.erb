<%= form_with(model: [:dashboard, data_source], local: true, class: "space-y-6", html: { multipart: true }) do |form| %>
  <% if data_source.errors.any? %>
    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
      <h3 class="font-medium mb-2">Please fix the following errors:</h3>
      <ul class="list-disc list-inside text-sm">
        <% data_source.errors.full_messages.each do |message| %>
          <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <!-- Basic Information -->
  <div class="bg-white shadow rounded-lg p-6">
    <h2 class="text-lg font-medium text-neutral-900 mb-4">Basic Information</h2>
    
    <div class="space-y-4">
      <div>
        <%= form.label :name, class: "block text-sm font-medium text-neutral-700 mb-1" %>
        <%= form.text_field :name, class: "form-input w-full", placeholder: "e.g., Production Database" %>
        <p class="mt-1 text-sm text-neutral-500">A descriptive name for this data source</p>
      </div>

      <div>
        <%= form.label :description, class: "block text-sm font-medium text-neutral-700 mb-1" %>
        <%= form.text_area :description, rows: 3, class: "form-input w-full", placeholder: "Optional description..." %>
      </div>
    </div>
  </div>

  <!-- Connection Type -->
  <div class="bg-white shadow rounded-lg p-6">
    <h2 class="text-lg font-medium text-neutral-900 mb-4">Select Connection Type</h2>
    
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 mb-6">
      <% DataSource::SOURCE_TYPES.each do |type| %>
        <button type="button" 
                onclick="openConnectionModal('<%= type %>')"
                class="flex flex-col p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 
                       border-neutral-200 hover:border-primary-300 hover:shadow-sm
                       focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
          <div class="flex items-center mb-2">
            <i class="<%= data_source_icon(type) %> text-xl mr-3 text-neutral-400 group-hover:text-neutral-600"></i>
            <span class="text-sm font-medium text-neutral-900"><%= type.humanize %></span>
          </div>
          <span class="text-xs text-neutral-500">
            <%= case type
                when 'postgresql', 'mysql' then 'Database connector'
                when 'csv', 'excel', 'json', 'xml' then 'File import'
                when 'api', 'webhook' then 'API integration'
                when 's3', 'gcs', 'azure_blob' then 'Cloud storage'
                when 'salesforce', 'stripe', 'shopify', 'quickbooks' then 'SaaS connector'
                else 'Data source'
                end %>
          </span>
        </button>
      <% end %>
    </div>
    
    <!-- Hidden field to store selected type -->
    <%= form.hidden_field :source_type, id: 'selected_source_type' %>
    
    <!-- Display selected type -->
    <div id="selected-type-display" class="hidden p-4 bg-primary-50 border border-primary-200 rounded-lg">
      <p class="text-sm font-medium text-primary-900">
        Selected: <span id="selected-type-name"></span>
      </p>
    </div>
  </div>

  <!-- Form Actions -->
  <div class="flex justify-end space-x-3">
    <%= link_to "Cancel", dashboard_data_sources_path, class: "btn btn--secondary" %>
    <%= form.submit data_source.persisted? ? "Update Data Source" : "Create Data Source", class: "btn btn--primary" %>
  </div>
<% end %>

<!-- Connection Configuration Modal -->
<div id="connection-modal" class="fixed inset-0 z-50 hidden overflow-y-auto">
  <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
    <!-- Background overlay -->
    <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onclick="closeConnectionModal()"></div>

    <!-- Modal panel -->
    <div class="inline-block w-full max-w-2xl px-4 pt-5 pb-4 overflow-hidden text-left align-bottom transition-all transform bg-white rounded-lg shadow-xl sm:my-8 sm:align-middle sm:p-6">
      <div class="absolute top-0 right-0 pt-4 pr-4">
        <button type="button" onclick="closeConnectionModal()" class="text-gray-400 hover:text-gray-500 focus:outline-none">
          <span class="sr-only">Close</span>
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>

      <div class="sm:flex sm:items-start">
        <div class="flex-1">
          <h3 class="text-lg font-medium leading-6 text-gray-900 mb-2" id="modal-title">
            Configure Connection
          </h3>
          
          <div id="modal-content" class="mt-4">
            <!-- Dynamic content will be loaded here -->
          </div>
        </div>
      </div>

      <div class="mt-5 sm:mt-6 sm:flex sm:flex-row-reverse">
        <button type="button" onclick="saveConnectionConfig()" class="inline-flex justify-center w-full px-4 py-2 text-base font-medium text-white bg-primary-600 border border-transparent rounded-md shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
          Save Configuration
        </button>
        <button type="button" onclick="closeConnectionModal()" class="inline-flex justify-center w-full px-4 py-2 mt-3 text-base font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:w-auto sm:text-sm">
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>

<%= javascript_tag nonce: true do %>
  let currentSourceType = null;
  
  function openConnectionModal(sourceType) {
    currentSourceType = sourceType;
    const modal = document.getElementById('connection-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalContent = document.getElementById('modal-content');
    
    // Update modal title
    modalTitle.textContent = `Configure ${sourceType.charAt(0).toUpperCase() + sourceType.slice(1)} Connection`;
    
    // Show loading state
    modalContent.innerHTML = `
      <div class="flex items-center justify-center py-8">
        <div class="text-center">
          <i class="fas fa-spinner fa-spin text-2xl text-primary-500 mb-2"></i>
          <p class="text-neutral-500">Loading configuration options...</p>
        </div>
      </div>
    `;
    
    // Show modal
    modal.classList.remove('hidden');
    
    // Fetch the configuration fields
    fetch(`/dashboard/data_sources/connection_fields/${sourceType}`)
      .then(response => {
        if (!response.ok) {
          throw new Error('Configuration not available');
        }
        return response.text();
      })
      .then(html => {
        modalContent.innerHTML = html;
      })
      .catch(error => {
        console.error('Error loading configuration:', error);
        modalContent.innerHTML = `
          <div class="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-lg">
            <p class="font-medium">Coming Soon</p>
            <p class="text-sm mt-1">${sourceType.charAt(0).toUpperCase() + sourceType.slice(1)} connector is under development.</p>
          </div>
        `;
      });
  }
  
  function closeConnectionModal() {
    const modal = document.getElementById('connection-modal');
    modal.classList.add('hidden');
  }
  
  function saveConnectionConfig() {
    // Update the hidden field with selected type
    document.getElementById('selected_source_type').value = currentSourceType;
    
    // Update the display
    const display = document.getElementById('selected-type-display');
    const typeName = document.getElementById('selected-type-name');
    display.classList.remove('hidden');
    typeName.textContent = currentSourceType.charAt(0).toUpperCase() + currentSourceType.slice(1);
    
    // Get all input values from the modal
    const modalInputs = document.querySelectorAll('#modal-content input, #modal-content select, #modal-content textarea');
    
    // Here you would typically save the configuration to hidden fields or process it
    // For now, we'll just close the modal
    closeConnectionModal();
    
    // Show success message
    showNotification('Connection configuration saved. You can now save the data source.');
  }
  
  function showNotification(message) {
    // Create a simple notification
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
    notification.textContent = message;
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
      notification.remove();
    }, 3000);
  }
  
  // Handle escape key to close modal
  document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
      closeConnectionModal();
    }
  });
<% end %>