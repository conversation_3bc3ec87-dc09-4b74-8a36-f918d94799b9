<% content_for :title, "Sample Data - #{@data_source.name}" %>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Enhanced Header with Help -->
  <div class="mb-8">
    <!-- Breadcrumb Navigation -->
    <nav class="flex items-center text-sm mb-4" aria-label="Breadcrumb">
      <%= link_to "Data Sources", dashboard_data_sources_path,
          class: "text-neutral-500 hover:text-primary-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-sm",
          'aria-label': "Navigate to Data Sources list" %>
      <i class="fas fa-chevron-right mx-2 text-neutral-400 text-xs" aria-hidden="true"></i>
      <%= link_to @data_source.name, dashboard_data_source_path(@data_source),
          class: "text-neutral-500 hover:text-primary-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-sm",
          'aria-label': "Navigate to #{@data_source.name} details" %>
      <i class="fas fa-chevron-right mx-2 text-neutral-400 text-xs" aria-hidden="true"></i>
      <span class="text-neutral-900 font-medium">Sample Data</span>
    </nav>

    <!-- Page Header with Help -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div class="flex items-center gap-3">
        <div class="flex-shrink-0">
          <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
            <i class="fas fa-eye text-white text-lg" aria-hidden="true"></i>
          </div>
        </div>
        <div>
          <h1 class="text-2xl font-bold text-neutral-900">Sample Data</h1>
          <p class="text-sm text-neutral-600 mt-1">Preview of actual data from your source</p>
        </div>
      </div>

      <!-- Help Button -->
      <button type="button"
              class="inline-flex items-center px-3 py-2 border border-neutral-300 rounded-lg text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200"
              data-controller="tooltip"
              data-tooltip-content-value="Sample data shows real examples from your data source. This helps you understand the actual content and format of your data before setting up integrations or transformations."
              aria-label="Get help about sample data">
        <i class="fas fa-question-circle mr-2 text-neutral-500" aria-hidden="true"></i>
        What is Sample Data?
      </button>
    </div>
  </div>

  <% if @sample_data_result[:success] && @sample_data.present? %>
    <!-- Enhanced Sample Data Display -->
    <div class="bg-white shadow-sm border border-neutral-200 rounded-xl overflow-hidden" data-controller="view-toggle data-table">
      <!-- Header with Summary and Controls -->
      <div class="px-6 py-5 border-b border-neutral-200 bg-gradient-to-r from-neutral-50 to-white">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
              <i class="fas fa-table text-white text-sm" aria-hidden="true"></i>
            </div>
            <div>
              <h2 class="text-lg font-semibold text-neutral-900">
                Sample Data Preview
                <span class="inline-flex items-center ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                  <%= @sample_data.length %> rows
                </span>
                <span class="inline-flex items-center ml-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                  <%= @columns.length %> columns
                </span>
              </h2>
              <p class="text-sm text-neutral-600 mt-1">
                <% if @data_source.source_type == 'csv' && @sample_data_result[:metadata][:file_path].present? %>
                  From file: <span class="font-medium"><%= File.basename(@sample_data_result[:metadata][:file_path]) %></span>
                <% else %>
                  Real data from your <%= @data_source.source_type.upcase %> source
                <% end %>
              </p>
            </div>
          </div>

          <!-- Controls -->
          <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3">
            <!-- Search -->
            <div class="relative">
              <input type="text"
                     placeholder="Search data..."
                     class="w-full sm:w-64 pl-10 pr-4 py-2 border border-neutral-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                     data-data-table-target="searchInput"
                     data-action="input->data-table#search"
                     aria-label="Search sample data">
              <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 text-sm" aria-hidden="true"></i>
            </div>

            <!-- View Toggle -->
            <div class="flex items-center gap-2">
              <span class="text-sm text-neutral-600">View:</span>
              <div class="bg-neutral-100 rounded-lg p-1 flex" role="tablist" aria-label="Data view options">
                <button type="button"
                        class="px-3 py-1 text-sm font-medium rounded-md bg-white text-neutral-900 shadow-sm"
                        role="tab"
                        aria-selected="true"
                        aria-controls="table-view"
                        data-view-toggle-target="tableBtn"
                        data-action="click->view-toggle#showTable">
                  <i class="fas fa-table mr-1" aria-hidden="true"></i>
                  Table
                </button>
                <button type="button"
                        class="px-3 py-1 text-sm font-medium rounded-md text-neutral-600 hover:text-neutral-900"
                        role="tab"
                        aria-selected="false"
                        aria-controls="card-view"
                        data-view-toggle-target="cardBtn"
                        data-action="click->view-toggle#showCards">
                  <i class="fas fa-th-large mr-1" aria-hidden="true"></i>
                  Cards
                </button>
              </div>
            </div>

            <!-- Export Options -->
            <div class="relative" data-controller="dropdown">
              <button type="button"
                      class="inline-flex items-center px-3 py-2 border border-neutral-300 rounded-lg text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200"
                      data-action="click->dropdown#toggle"
                      aria-haspopup="true"
                      aria-expanded="false">
                <i class="fas fa-download mr-2 text-neutral-500" aria-hidden="true"></i>
                Export
                <i class="fas fa-chevron-down ml-1 text-neutral-400" aria-hidden="true"></i>
              </button>

              <div class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-neutral-200 z-10"
                   data-dropdown-target="menu">
                <div class="py-1">
                  <button type="button"
                          class="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100"
                          data-action="click->data-table#exportCSV">
                    <i class="fas fa-file-csv mr-2 text-neutral-500" aria-hidden="true"></i>
                    Export as CSV
                  </button>
                  <button type="button"
                          class="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100"
                          data-action="click->data-table#exportJSON">
                    <i class="fas fa-code mr-2 text-neutral-500" aria-hidden="true"></i>
                    Export as JSON
                  </button>
                  <button type="button"
                          class="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100"
                          onclick="window.print()">
                    <i class="fas fa-print mr-2 text-neutral-500" aria-hidden="true"></i>
                    Print Table
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Table View -->
      <div id="table-view" data-view-toggle-target="tableView" class="block">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-neutral-200" role="table" aria-label="Sample data table" data-data-table-target="table">
            <thead class="bg-neutral-50">
              <tr role="row">
                <% @columns.each_with_index do |column, index| %>
                  <th scope="col"
                      class="px-6 py-4 text-left text-xs font-semibold text-neutral-700 uppercase tracking-wider cursor-pointer hover:bg-neutral-100 transition-colors duration-150"
                      data-action="click->data-table#sortColumn"
                      data-column="<%= index %>"
                      data-column-name="<%= column %>"
                      tabindex="0"
                      role="columnheader"
                      aria-sort="none">
                    <div class="flex items-center gap-2">
                      <i class="fas fa-<%= case column.downcase
                        when /id|key/ then 'key'
                        when /name|title/ then 'tag'
                        when /email/ then 'envelope'
                        when /phone/ then 'phone'
                        when /date|time/ then 'calendar'
                        when /amount|price|cost/ then 'dollar-sign'
                        when /status/ then 'info-circle'
                        else 'columns'
                        end %> text-neutral-400 text-xs" aria-hidden="true"></i>
                      <span><%= column %></span>
                      <i class="fas fa-sort text-neutral-300 text-xs" aria-hidden="true" data-data-table-target="sortIcon<%= index %>"></i>
                    </div>
                  </th>
                <% end %>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-neutral-200" data-data-table-target="tableBody">
              <% @sample_data.each_with_index do |row, row_index| %>
                <tr role="row"
                    class="<%= row_index.even? ? 'bg-white' : 'bg-neutral-50' %> hover:bg-primary-50 transition-colors duration-150"
                    data-data-table-target="tableRow">
                  <% @columns.each do |column| %>
                    <td class="px-6 py-4 text-sm" role="gridcell">
                      <% value = row[column]&.to_s || '' %>
                      <% if value.present? %>
                        <div class="flex items-center gap-2">
                          <!-- Data Type Indicator -->
                          <%
                            # Enhanced date detection with multiple formats
                            data_type = case value
                            when /^\d+$/ then 'integer'
                            when /^\d*\.?\d+$/ then 'decimal'
                            when /^(true|false|1|0|yes|no)$/i then 'boolean'
                            when /^\d{4}-\d{2}-\d{2}/, /^\d{1,2}\/\d{1,2}\/\d{4}/, /^\d{1,2}-\d{1,2}-\d{4}/, /^\d{4}\/\d{2}\/\d{2}/ then 'date'
                            when /^\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}/, /^\d{1,2}\/\d{1,2}\/\d{4}\s\d{1,2}:\d{2}/ then 'datetime'
                            when /\S+@\S+\.\S+/ then 'email'
                            when /^\+?[\d\s\-\(\)]+$/ then 'phone'
                            else 'text'
                            end

                            # Format date values for display
                            display_value = value
                            formatted_date = nil
                            original_date = nil

                            if ['date', 'datetime'].include?(data_type)
                              begin
                                # Try to parse the date
                                parsed_date = Date.parse(value) rescue nil
                                if parsed_date.nil?
                                  parsed_datetime = DateTime.parse(value) rescue nil
                                  parsed_date = parsed_datetime&.to_date
                                end

                                if parsed_date
                                  original_date = value
                                  # Format for display: "Jan 15, 2024" for readability
                                  formatted_date = parsed_date.strftime("%b %d, %Y")
                                  display_value = formatted_date

                                  # For datetime, include time if present
                                  if data_type == 'datetime' && value.match(/\d{2}:\d{2}/)
                                    time_match = value.match(/(\d{1,2}:\d{2}(?::\d{2})?(?:\s?[AP]M)?)/i)
                                    if time_match
                                      display_value = "#{formatted_date} #{time_match[1]}"
                                    end
                                  end
                                end
                              rescue
                                # If parsing fails, keep original value
                                display_value = value
                              end
                            end
                          %>
                          <span class="inline-flex items-center w-2 h-2 rounded-full
                            <%= case data_type
                              when 'integer' then 'bg-blue-500'
                              when 'decimal' then 'bg-cyan-500'
                              when 'boolean' then 'bg-green-500'
                              when 'date', 'datetime' then 'bg-purple-500'
                              when 'email' then 'bg-orange-500'
                              when 'phone' then 'bg-pink-500'
                              else 'bg-gray-500'
                              end %>"
                            title="<%= data_type.capitalize %> data type"
                            aria-label="<%= data_type.capitalize %> data type"></span>

                          <!-- Formatted Value -->
                          <span class="text-neutral-900 whitespace-nowrap <%= 'font-mono' if ['integer', 'decimal', 'phone'].include?(data_type) %> <%= 'font-medium' if ['date', 'datetime'].include?(data_type) %>">
                            <% if ['date', 'datetime'].include?(data_type) && original_date %>
                              <!-- Date with tooltip showing original format -->
                              <span data-controller="tooltip"
                                    data-tooltip-content-value="Original: <%= original_date %>"
                                    class="cursor-help"
                                    aria-label="Date: <%= display_value %>, original format: <%= original_date %>">
                                <%= display_value %>
                              </span>
                            <% elsif display_value.length > 50 %>
                              <span data-controller="tooltip"
                                    data-tooltip-content-value="<%= value %>"
                                    class="cursor-help">
                                <%= truncate(display_value, length: 50) %>
                              </span>
                            <% else %>
                              <%= display_value %>
                            <% end %>
                          </span>

                          <!-- Special Formatting -->
                          <% if data_type == 'email' %>
                            <i class="fas fa-envelope text-orange-500 text-xs" aria-hidden="true"></i>
                          <% elsif data_type == 'phone' %>
                            <i class="fas fa-phone text-pink-500 text-xs" aria-hidden="true"></i>
                          <% elsif ['date', 'datetime'].include?(data_type) %>
                            <i class="fas fa-calendar text-purple-500 text-xs" aria-hidden="true" title="<%= data_type.capitalize %> field"></i>
                          <% elsif data_type == 'boolean' %>
                            <i class="fas fa-<%= value.downcase.in?(['true', '1', 'yes']) ? 'check' : 'times' %> text-green-500 text-xs" aria-hidden="true"></i>
                          <% end %>
                        </div>
                      <% else %>
                        <span class="text-neutral-400 italic">Empty</span>
                      <% end %>
                    </td>
                  <% end %>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>

        <!-- Table Footer with Statistics -->
        <div class="px-6 py-4 bg-neutral-50 border-t border-neutral-200">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div class="flex items-center gap-4 text-sm text-neutral-600">
              <span>
                <i class="fas fa-table mr-1" aria-hidden="true"></i>
                <%= @sample_data.length %> rows × <%= @columns.length %> columns
              </span>
              <span data-data-table-target="filteredCount" class="hidden">
                <i class="fas fa-filter mr-1" aria-hidden="true"></i>
                <span data-data-table-target="filteredCountText"></span> filtered
              </span>
            </div>

            <!-- Data Type Legend -->
            <div class="flex flex-wrap items-center gap-3 text-xs">
              <span class="text-neutral-600 font-medium">Data Types:</span>
              <div class="flex items-center gap-1">
                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                <span class="text-neutral-600">Numbers</span>
              </div>
              <div class="flex items-center gap-1">
                <span class="w-2 h-2 bg-gray-500 rounded-full"></span>
                <span class="text-neutral-600">Text</span>
              </div>
              <div class="flex items-center gap-1">
                <span class="w-2 h-2 bg-purple-500 rounded-full"></span>
                <span class="text-neutral-600">Dates</span>
              </div>
              <div class="flex items-center gap-1">
                <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                <span class="text-neutral-600">Boolean</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Card View (Mobile-Friendly) -->
      <div id="card-view" data-view-toggle-target="cardView" class="hidden">
        <div class="p-6 space-y-4" data-data-table-target="cardContainer">
          <% @sample_data.each_with_index do |row, row_index| %>
            <div class="bg-neutral-50 border border-neutral-200 rounded-lg p-4 hover:shadow-sm transition-shadow duration-150"
                 data-data-table-target="cardRow">
              <!-- Row Header -->
              <div class="flex items-center justify-between mb-3 pb-2 border-b border-neutral-200">
                <h3 class="font-semibold text-neutral-900 text-sm">
                  <i class="fas fa-list-ol mr-2 text-neutral-500" aria-hidden="true"></i>
                  Row <%= row_index + 1 %>
                </h3>
                <span class="text-xs text-neutral-500">
                  <%= @columns.length %> fields
                </span>
              </div>

              <!-- Row Data -->
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <% @columns.each do |column| %>
                  <% value = row[column]&.to_s || '' %>
                  <div class="bg-white border border-neutral-200 rounded p-3">
                    <!-- Field Header -->
                    <div class="flex items-center gap-2 mb-2">
                      <i class="fas fa-<%= case column.downcase
                        when /id|key/ then 'key'
                        when /name|title/ then 'tag'
                        when /email/ then 'envelope'
                        when /phone/ then 'phone'
                        when /date|time/ then 'calendar'
                        when /amount|price|cost/ then 'dollar-sign'
                        when /status/ then 'info-circle'
                        else 'columns'
                        end %> text-neutral-400 text-xs" aria-hidden="true"></i>
                      <h4 class="text-xs font-medium text-neutral-500 uppercase tracking-wider">
                        <%= column %>
                      </h4>
                    </div>

                    <!-- Field Value -->
                    <% if value.present? %>
                      <%
                        # Enhanced date detection with multiple formats (same as table view)
                        data_type = case value
                        when /^\d+$/ then 'integer'
                        when /^\d*\.?\d+$/ then 'decimal'
                        when /^(true|false|1|0|yes|no)$/i then 'boolean'
                        when /^\d{4}-\d{2}-\d{2}/, /^\d{1,2}\/\d{1,2}\/\d{4}/, /^\d{1,2}-\d{1,2}-\d{4}/, /^\d{4}\/\d{2}\/\d{2}/ then 'date'
                        when /^\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}/, /^\d{1,2}\/\d{1,2}\/\d{4}\s\d{1,2}:\d{2}/ then 'datetime'
                        when /\S+@\S+\.\S+/ then 'email'
                        when /^\+?[\d\s\-\(\)]+$/ then 'phone'
                        else 'text'
                        end

                        # Format date values for display (same logic as table view)
                        display_value = value
                        formatted_date = nil
                        original_date = nil

                        if ['date', 'datetime'].include?(data_type)
                          begin
                            # Try to parse the date
                            parsed_date = Date.parse(value) rescue nil
                            if parsed_date.nil?
                              parsed_datetime = DateTime.parse(value) rescue nil
                              parsed_date = parsed_datetime&.to_date
                            end

                            if parsed_date
                              original_date = value
                              # Format for display: "Jan 15, 2024" for readability
                              formatted_date = parsed_date.strftime("%b %d, %Y")
                              display_value = formatted_date

                              # For datetime, include time if present
                              if data_type == 'datetime' && value.match(/\d{2}:\d{2}/)
                                time_match = value.match(/(\d{1,2}:\d{2}(?::\d{2})?(?:\s?[AP]M)?)/i)
                                if time_match
                                  display_value = "#{formatted_date} #{time_match[1]}"
                                end
                              end
                            end
                          rescue
                            # If parsing fails, keep original value
                            display_value = value
                          end
                        end
                      %>
                      <div class="flex items-start gap-2">
                        <!-- Data Type Indicator -->
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border mt-0.5
                          <%= case data_type
                            when 'integer' then 'bg-blue-50 text-blue-700 border-blue-200'
                            when 'decimal' then 'bg-cyan-50 text-cyan-700 border-cyan-200'
                            when 'boolean' then 'bg-green-50 text-green-700 border-green-200'
                            when 'date', 'datetime' then 'bg-purple-50 text-purple-700 border-purple-200'
                            when 'email' then 'bg-orange-50 text-orange-700 border-orange-200'
                            when 'phone' then 'bg-pink-50 text-pink-700 border-pink-200'
                            else 'bg-gray-50 text-gray-700 border-gray-200'
                            end %>">
                          <%= data_type %>
                        </span>

                        <!-- Value -->
                        <div class="flex-1 min-w-0">
                          <% if ['date', 'datetime'].include?(data_type) && original_date %>
                            <!-- Date with tooltip showing original format and single-line display -->
                            <p class="text-sm text-neutral-900 font-medium whitespace-nowrap overflow-hidden text-ellipsis">
                              <span data-controller="tooltip"
                                    data-tooltip-content-value="Original: <%= original_date %>"
                                    class="cursor-help"
                                    aria-label="Date: <%= display_value %>, original format: <%= original_date %>">
                                <%= display_value %>
                              </span>
                            </p>
                          <% else %>
                            <p class="text-sm text-neutral-900 break-words <%= 'font-mono' if ['integer', 'decimal', 'phone'].include?(data_type) %>">
                              <% if display_value.length > 100 %>
                                <span data-controller="tooltip"
                                      data-tooltip-content-value="<%= value %>"
                                      class="cursor-help">
                                  <%= truncate(display_value, length: 100) %>
                                </span>
                              <% else %>
                                <%= display_value %>
                              <% end %>
                            </p>
                          <% end %>

                          <!-- Special Indicators -->
                          <% if data_type == 'email' %>
                            <div class="flex items-center gap-1 mt-1">
                              <i class="fas fa-envelope text-orange-500 text-xs" aria-hidden="true"></i>
                              <span class="text-xs text-orange-600">Email address</span>
                            </div>
                          <% elsif data_type == 'phone' %>
                            <div class="flex items-center gap-1 mt-1">
                              <i class="fas fa-phone text-pink-500 text-xs" aria-hidden="true"></i>
                              <span class="text-xs text-pink-600">Phone number</span>
                            </div>
                          <% elsif data_type == 'date' %>
                            <div class="flex items-center gap-1 mt-1">
                              <i class="fas fa-calendar text-purple-500 text-xs" aria-hidden="true"></i>
                              <span class="text-xs text-purple-600">Date value</span>
                            </div>
                          <% elsif data_type == 'datetime' %>
                            <div class="flex items-center gap-1 mt-1">
                              <i class="fas fa-clock text-purple-500 text-xs" aria-hidden="true"></i>
                              <span class="text-xs text-purple-600">Date & time value</span>
                            </div>
                          <% elsif data_type == 'boolean' %>
                            <div class="flex items-center gap-1 mt-1">
                              <i class="fas fa-<%= value.downcase.in?(['true', '1', 'yes']) ? 'check' : 'times' %> text-green-500 text-xs" aria-hidden="true"></i>
                              <span class="text-xs text-green-600">
                                <%= value.downcase.in?(['true', '1', 'yes']) ? 'True' : 'False' %> value
                              </span>
                            </div>
                          <% end %>
                        </div>
                      </div>
                    <% else %>
                      <div class="flex items-center gap-2">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-50 text-gray-700 border border-gray-200">
                          empty
                        </span>
                        <span class="text-sm text-neutral-400 italic">No value</span>
                      </div>
                    <% end %>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  <% elsif @sample_data.blank? %>
    <!-- Enhanced No Data State -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-8 text-center">
      <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <i class="fas fa-inbox text-yellow-600 text-2xl" aria-hidden="true"></i>
      </div>
      <h2 class="text-xl font-semibold text-yellow-900 mb-2">No Sample Data Available</h2>
      <p class="text-yellow-700 mb-4 max-w-md mx-auto">
        Your data source is connected but returned no sample data. This could mean the source is empty or there might be a configuration issue.
      </p>
      <div class="flex flex-col sm:flex-row gap-3 justify-center">
        <%= link_to dashboard_data_source_path(@data_source),
            class: "inline-flex items-center px-4 py-2 border border-yellow-300 rounded-lg text-sm font-medium text-yellow-700 bg-white hover:bg-yellow-50 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-colors duration-200" do %>
          <i class="fas fa-arrow-left mr-2" aria-hidden="true"></i>
          Back to Data Source
        <% end %>
        <button type="button"
                class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-colors duration-200"
                onclick="window.location.reload()">
          <i class="fas fa-redo mr-2" aria-hidden="true"></i>
          Refresh Data
        </button>
      </div>
    </div>
  <% else %>
    <!-- Enhanced Error State -->
    <div class="bg-red-50 border border-red-200 rounded-xl p-8 text-center">
      <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <i class="fas fa-exclamation-triangle text-red-600 text-2xl" aria-hidden="true"></i>
      </div>
      <h2 class="text-xl font-semibold text-red-900 mb-2">Unable to Load Sample Data</h2>
      <p class="text-red-700 mb-4 max-w-md mx-auto">
        <%= @sample_data_result[:error] || "We encountered an issue while fetching sample data from your source. This might be due to connectivity problems or data format issues." %>
      </p>
      <div class="flex flex-col sm:flex-row gap-3 justify-center">
        <%= link_to dashboard_data_source_path(@data_source),
            class: "inline-flex items-center px-4 py-2 border border-red-300 rounded-lg text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-200" do %>
          <i class="fas fa-arrow-left mr-2" aria-hidden="true"></i>
          Back to Data Source
        <% end %>
        <button type="button"
                class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-200"
                onclick="window.location.reload()">
          <i class="fas fa-redo mr-2" aria-hidden="true"></i>
          Try Again
        </button>
      </div>
    </div>
  <% end %>

  <!-- Enhanced Footer Navigation -->
  <div class="mt-8 pt-6 border-t border-neutral-200">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <!-- Back Navigation -->
      <div class="flex items-center gap-3">
        <%= link_to dashboard_data_source_path(@data_source),
            class: "inline-flex items-center px-4 py-2 border border-neutral-300 rounded-lg text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200" do %>
          <i class="fas fa-arrow-left mr-2" aria-hidden="true"></i>
          Back to Data Source
        <% end %>

        <%= link_to schema_dashboard_data_source_path(@data_source),
            class: "inline-flex items-center px-4 py-2 border border-primary-300 rounded-lg text-sm font-medium text-primary-700 bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200" do %>
          <i class="fas fa-table mr-2" aria-hidden="true"></i>
          View Schema
        <% end %>
      </div>

      <!-- Quick Actions -->
      <div class="flex items-center gap-2 text-sm text-neutral-600">
        <span>Quick actions:</span>
        <button type="button"
                class="text-primary-600 hover:text-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded"
                onclick="window.print()"
                aria-label="Print sample data">
          <i class="fas fa-print mr-1" aria-hidden="true"></i>
          Print
        </button>
        <span class="text-neutral-300">|</span>
        <button type="button"
                class="text-primary-600 hover:text-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded"
                onclick="window.location.reload()"
                aria-label="Refresh sample data">
          <i class="fas fa-redo mr-1" aria-hidden="true"></i>
          Refresh
        </button>
      </div>
    </div>

    <!-- Help Text -->
    <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <div class="flex items-start gap-3">
        <div class="flex-shrink-0">
          <i class="fas fa-lightbulb text-blue-600 mt-0.5" aria-hidden="true"></i>
        </div>
        <div class="text-sm">
          <h3 class="font-medium text-blue-900 mb-1">Understanding Your Sample Data</h3>
          <p class="text-blue-700 leading-relaxed">
            This preview shows actual data from your source with enhanced formatting for better readability.
            Date values are formatted consistently, and data types are color-coded for easy identification.
            Use the search function to find specific values, or export the data for further analysis.
          </p>
          <% if @sample_data.length >= 20 %>
            <p class="text-blue-700 mt-2">
              <i class="fas fa-info-circle mr-1" aria-hidden="true"></i>
              Showing first <%= @sample_data.length %> rows. Your full dataset may contain more records.
            </p>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>