<% content_for :title, "#{@data_source.name} - Data Sources" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50"
     data-controller="data-source-modal-trigger">
  <!-- Enhanced Header with Glassmorphism -->
  <div class="relative overflow-hidden bg-white/80 backdrop-blur-sm border-b border-slate-200/60 shadow-sm">
    <div class="absolute inset-0 bg-gradient-to-r from-primary-500/5 via-transparent to-primary-500/5"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <!-- Breadcrumb Navigation -->
      <nav class="flex items-center space-x-2 text-sm mb-4" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-2">
          <li>
            <%= link_to dashboard_data_sources_path,
                class: "inline-flex items-center text-slate-500 hover:text-primary-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-md px-2 py-1",
                'aria-label': 'Navigate to Data Sources' do %>
              <i class="fas fa-database mr-2 text-xs" aria-hidden="true"></i>
              Data Sources
            <% end %>
          </li>
          <li class="flex items-center">
            <i class="fas fa-chevron-right text-slate-400 mx-2 text-xs" aria-hidden="true"></i>
            <span class="text-slate-700 font-medium" aria-current="page"><%= @data_source.name %></span>
          </li>
        </ol>
      </nav>

      <!-- Header Content -->
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <div class="flex-1 min-w-0">
          <!-- Data Source Title with Enhanced Status -->
          <div class="flex flex-col sm:flex-row sm:items-center gap-4">
            <h1 class="text-3xl font-bold text-slate-900 tracking-tight">
              <%= @data_source.name %>
            </h1>

            <!-- Enhanced Status Badge with Tooltip -->
            <div class="relative group">
              <% case @data_source.status %>
              <% when 'active' %>
                <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-emerald-100 text-emerald-800 border border-emerald-200 shadow-sm">
                  <span class="w-2 h-2 bg-emerald-500 rounded-full mr-2 animate-pulse" aria-hidden="true"></span>
                  <i class="fas fa-check-circle mr-2" aria-hidden="true"></i>
                  Active
                </span>
              <% when 'syncing' %>
                <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-blue-100 text-blue-800 border border-blue-200 shadow-sm">
                  <span class="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse" aria-hidden="true"></span>
                  <i class="fas fa-sync-alt animate-spin mr-2" aria-hidden="true"></i>
                  Syncing
                </span>
              <% when 'error' %>
                <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-red-100 text-red-800 border border-red-200 shadow-sm">
                  <span class="w-2 h-2 bg-red-500 rounded-full mr-2" aria-hidden="true"></span>
                  <i class="fas fa-exclamation-triangle mr-2" aria-hidden="true"></i>
                  Error
                </span>
              <% when 'inactive' %>
                <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-slate-100 text-slate-700 border border-slate-200 shadow-sm">
                  <span class="w-2 h-2 bg-slate-400 rounded-full mr-2" aria-hidden="true"></span>
                  <i class="fas fa-pause-circle mr-2" aria-hidden="true"></i>
                  Inactive
                </span>
              <% end %>

              <!-- Status Tooltip -->
              <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 text-xs text-white bg-slate-900 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                <% case @data_source.status %>
                <% when 'active' %>
                  Data source is connected and operational
                <% when 'syncing' %>
                  Currently synchronizing data
                <% when 'error' %>
                  Connection or sync error detected
                <% when 'inactive' %>
                  Data source is not currently active
                <% end %>
                <div class="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-slate-900"></div>
              </div>
            </div>
          </div>

          <!-- Data Source Type and Description -->
          <div class="mt-3 flex flex-wrap items-center gap-4 text-sm text-slate-600">
            <div class="flex items-center">
              <i class="fas fa-tag mr-2 text-primary-500" aria-hidden="true"></i>
              <span class="font-medium"><%= @data_source.source_type_text %></span>
            </div>
            <div class="flex items-center">
              <i class="fas fa-clock mr-2 text-primary-500" aria-hidden="true"></i>
              <span>Created <%= @data_source.created_at.strftime("%B %d, %Y") %></span>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-3">
          <button type="button"
                  class="inline-flex items-center justify-center px-6 py-3 border border-slate-300 text-sm font-semibold text-slate-700 bg-white hover:bg-slate-50 rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 hover:shadow-md"
                  data-action="click->data-source-modal-trigger#openEditModal"
                  data-data-source-id="<%= @data_source.id %>"
                  aria-label="Edit data source settings">
            <i class="fas fa-edit mr-2" aria-hidden="true"></i>
            Edit Settings
          </button>

          <% if @data_source.can_sync? %>
            <%= button_to sync_dashboard_data_source_path(@data_source),
                method: :post,
                class: "inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-sm font-semibold text-white rounded-lg shadow-sm hover:from-primary-600 hover:to-primary-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 hover:shadow-md transform hover:scale-105",
                'aria-label': 'Start data synchronization now' do %>
              <i class="fas fa-sync-alt mr-2" aria-hidden="true"></i>
              Sync Now
            <% end %>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content Container -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Error Messages with Enhanced Design -->
    <% if @data_source.error_message.present? %>
      <div class="mb-8 bg-gradient-to-r from-red-50 to-red-50/50 border border-red-200 rounded-xl p-6 shadow-sm" role="alert" aria-live="polite">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
              <i class="fas fa-exclamation-triangle text-red-600" aria-hidden="true"></i>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <h3 class="text-lg font-semibold text-red-900 mb-2">Connection Error</h3>
            <p class="text-red-800 leading-relaxed"><%= @data_source.error_message %></p>
            <div class="mt-4">
              <%= link_to edit_dashboard_data_source_path(@data_source), 
                  class: "inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2",
                  'aria-label': 'Fix data source connection' do %>
                <i class="fas fa-tools mr-2" aria-hidden="true"></i>
                Fix Connection
              <% end %>
            </div>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Configuration Issues -->
    <% if @data_source.status == 'inactive' || (@data_source.source_type == 'csv' && @data_source.connection_config_value('file_path').blank?) %>
      <%= render 'activation_fix', data_source: @data_source %>
    <% end %>

    <!-- Enhanced Info Grid with Glassmorphism Cards -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
      <!-- Connection Details Card -->
      <div class="group relative bg-white/80 backdrop-blur-sm border border-slate-200/60 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
        <!-- Card Header -->
        <div class="flex items-center mb-6">
          <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center shadow-md group-hover:shadow-lg transition-shadow duration-300">
            <i class="fas fa-plug text-white text-lg" aria-hidden="true"></i>
          </div>
          <div class="ml-4">
            <h3 class="text-xl font-bold text-slate-900">Connection Details</h3>
            <p class="text-sm text-slate-600">Source configuration</p>
          </div>
        </div>

        <!-- Connection Information -->
        <dl class="space-y-4">
          <div class="flex justify-between items-center py-2 border-b border-slate-100">
            <dt class="text-sm font-semibold text-slate-600 flex items-center">
              <i class="fas fa-tag mr-2 text-primary-500" aria-hidden="true"></i>
              Type
            </dt>
            <dd class="text-sm font-medium text-slate-900 bg-slate-100 px-3 py-1 rounded-full">
              <%= @data_source.source_type_text %>
            </dd>
          </div>

          <div class="flex justify-between items-center py-2 border-b border-slate-100">
            <dt class="text-sm font-semibold text-slate-600 flex items-center">
              <i class="fas fa-clock mr-2 text-primary-500" aria-hidden="true"></i>
              Sync Frequency
            </dt>
            <dd class="text-sm font-medium text-slate-900">
              <%= @data_source.sync_frequency_text %>
            </dd>
          </div>

          <div class="flex justify-between items-center py-2 border-b border-slate-100">
            <dt class="text-sm font-semibold text-slate-600 flex items-center">
              <i class="fas fa-calendar mr-2 text-primary-500" aria-hidden="true"></i>
              Created
            </dt>
            <dd class="text-sm font-medium text-slate-900">
              <%= @data_source.created_at.strftime("%B %d, %Y") %>
            </dd>
          </div>

          <% if @data_source.source_type == 'csv' && @data_source.connection_config_value('original_filename').present? %>
            <div class="py-2">
              <dt class="text-sm font-semibold text-slate-600 flex items-center mb-2">
                <i class="fas fa-file-csv mr-2 text-primary-500" aria-hidden="true"></i>
                CSV File
              </dt>
              <dd class="text-sm text-slate-900 bg-slate-50 p-3 rounded-lg">
                <div class="font-medium"><%= @data_source.connection_config_value('original_filename') %></div>
                <% if @data_source.connection_config_value('file_size').present? %>
                  <div class="text-slate-600 text-xs mt-1">
                    Size: <%= number_to_human_size(@data_source.connection_config_value('file_size')) %>
                  </div>
                <% end %>
              </dd>
            </div>
          <% end %>
        </dl>

        <!-- Test Connection Button -->
        <div class="mt-6 pt-6 border-t border-slate-200">
          <%= button_to test_connection_dashboard_data_source_path(@data_source),
              method: :post,
              class: "w-full inline-flex items-center justify-center px-4 py-3 bg-gradient-to-r from-slate-600 to-slate-700 text-white text-sm font-semibold rounded-xl shadow-sm hover:from-slate-700 hover:to-slate-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-slate-500 focus:ring-offset-2 hover:shadow-md",
              'aria-label': 'Test connection to data source' do %>
            <i class="fas fa-plug mr-2" aria-hidden="true"></i>
            Test Connection
          <% end %>
        </div>
      </div>

      <!-- Sync Status Card -->
      <div class="group relative bg-white/80 backdrop-blur-sm border border-slate-200/60 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
        <!-- Card Header -->
        <div class="flex items-center mb-6">
          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-md group-hover:shadow-lg transition-shadow duration-300">
            <i class="fas fa-sync-alt text-white text-lg" aria-hidden="true"></i>
          </div>
          <div class="ml-4">
            <h3 class="text-xl font-bold text-slate-900">Sync Status</h3>
            <p class="text-sm text-slate-600">Data synchronization info</p>
          </div>
        </div>

        <!-- Sync Information -->
        <div class="space-y-6">
          <!-- Last Sync -->
          <div class="bg-slate-50 rounded-xl p-4">
            <dt class="text-sm font-semibold text-slate-600 flex items-center mb-2">
              <i class="fas fa-history mr-2 text-blue-500" aria-hidden="true"></i>
              Last Sync
            </dt>
            <dd class="text-slate-900">
              <% if @data_source.last_sync_at %>
                <div class="font-semibold text-lg"><%= time_ago_in_words(@data_source.last_sync_at) %> ago</div>
                <div class="text-sm text-slate-600 mt-1">
                  <%= @data_source.last_sync_at.strftime("%B %d, %Y at %I:%M %p") %>
                </div>
              <% else %>
                <div class="font-semibold text-lg text-slate-500">Never synced</div>
                <div class="text-sm text-slate-500 mt-1">No synchronization history</div>
              <% end %>
            </dd>
          </div>

          <!-- Total Records -->
          <div class="bg-emerald-50 rounded-xl p-4">
            <dt class="text-sm font-semibold text-slate-600 flex items-center mb-2">
              <i class="fas fa-database mr-2 text-emerald-500" aria-hidden="true"></i>
              Total Records
            </dt>
            <dd class="font-bold text-2xl text-emerald-700">
              <%= @data_source.formatted_row_count %>
            </dd>
          </div>

          <!-- Next Sync -->
          <div class="bg-amber-50 rounded-xl p-4">
            <dt class="text-sm font-semibold text-slate-600 flex items-center mb-2">
              <i class="fas fa-clock mr-2 text-amber-500" aria-hidden="true"></i>
              Next Sync
            </dt>
            <dd class="text-slate-900">
              <% if @data_source.sync_frequency == 'manual' %>
                <div class="font-semibold text-lg text-amber-700">Manual only</div>
                <div class="text-sm text-slate-600 mt-1">Sync when needed</div>
              <% elsif @data_source.active? %>
                <div class="font-semibold text-lg text-amber-700">
                  <%= @data_source.calculate_next_sync_time&.strftime("%b %d, %I:%M %p") || "Scheduled" %>
                </div>
                <div class="text-sm text-slate-600 mt-1">Automatic sync scheduled</div>
              <% else %>
                <div class="font-semibold text-lg text-slate-500">Not scheduled</div>
                <div class="text-sm text-slate-500 mt-1">Data source inactive</div>
              <% end %>
            </dd>
          </div>
        </div>
      </div>

      <!-- Quick Actions Card -->
      <div class="group relative bg-white/80 backdrop-blur-sm border border-slate-200/60 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
        <!-- Card Header -->
        <div class="flex items-center mb-6">
          <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-md group-hover:shadow-lg transition-shadow duration-300">
            <i class="fas fa-bolt text-white text-lg" aria-hidden="true"></i>
          </div>
          <div class="ml-4">
            <h3 class="text-xl font-bold text-slate-900">Quick Actions</h3>
            <p class="text-sm text-slate-600">Common operations</p>
          </div>
        </div>

        <!-- Action Links -->
        <div class="space-y-4">
          <%= link_to schema_dashboard_data_source_path(@data_source),
              class: "group/action flex items-center p-4 bg-slate-50 hover:bg-primary-50 rounded-xl transition-all duration-200 hover:shadow-md border border-transparent hover:border-primary-200",
              'aria-label': 'View data source schema structure' do %>
            <div class="w-10 h-10 bg-white group-hover/action:bg-primary-100 rounded-lg flex items-center justify-center shadow-sm transition-colors duration-200">
              <i class="fas fa-sitemap text-slate-600 group-hover/action:text-primary-600" aria-hidden="true"></i>
            </div>
            <div class="ml-4 flex-1">
              <div class="font-semibold text-slate-900 group-hover/action:text-primary-900">View Schema</div>
              <div class="text-sm text-slate-600 group-hover/action:text-primary-700">Explore data structure</div>
            </div>
            <i class="fas fa-chevron-right text-slate-400 group-hover/action:text-primary-500 transition-colors duration-200" aria-hidden="true"></i>
          <% end %>

          <%= link_to sample_data_dashboard_data_source_path(@data_source),
              class: "group/action flex items-center p-4 bg-slate-50 hover:bg-primary-50 rounded-xl transition-all duration-200 hover:shadow-md border border-transparent hover:border-primary-200",
              'aria-label': 'Preview sample data from source' do %>
            <div class="w-10 h-10 bg-white group-hover/action:bg-primary-100 rounded-lg flex items-center justify-center shadow-sm transition-colors duration-200">
              <i class="fas fa-table text-slate-600 group-hover/action:text-primary-600" aria-hidden="true"></i>
            </div>
            <div class="ml-4 flex-1">
              <div class="font-semibold text-slate-900 group-hover/action:text-primary-900">Preview Sample Data</div>
              <div class="text-sm text-slate-600 group-hover/action:text-primary-700">View data samples</div>
            </div>
            <i class="fas fa-chevron-right text-slate-400 group-hover/action:text-primary-500 transition-colors duration-200" aria-hidden="true"></i>
          <% end %>

          <%= link_to sync_history_dashboard_data_source_path(@data_source),
              class: "group/action flex items-center p-4 bg-slate-50 hover:bg-primary-50 rounded-xl transition-all duration-200 hover:shadow-md border border-transparent hover:border-primary-200",
              'aria-label': 'View synchronization history' do %>
            <div class="w-10 h-10 bg-white group-hover/action:bg-primary-100 rounded-lg flex items-center justify-center shadow-sm transition-colors duration-200">
              <i class="fas fa-history text-slate-600 group-hover/action:text-primary-600" aria-hidden="true"></i>
            </div>
            <div class="ml-4 flex-1">
              <div class="font-semibold text-slate-900 group-hover/action:text-primary-900">View Sync History</div>
              <div class="text-sm text-slate-600 group-hover/action:text-primary-700">Track sync activities</div>
            </div>
            <i class="fas fa-chevron-right text-slate-400 group-hover/action:text-primary-500 transition-colors duration-200" aria-hidden="true"></i>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Enhanced Tabbed Section -->
    <div class="bg-white/80 backdrop-blur-sm border border-slate-200/60 rounded-2xl shadow-xl overflow-hidden">
      <!-- Tab Navigation -->
      <div class="bg-gradient-to-r from-slate-50 to-slate-100/50 border-b border-slate-200/60">
        <nav class="flex" role="tablist" aria-label="Data source information tabs">
          <button type="button"
                  data-tab="overview"
                  class="tab-button relative flex-1 py-4 px-6 text-sm font-semibold text-primary-600 bg-white border-b-2 border-primary-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                  role="tab"
                  aria-selected="true"
                  aria-controls="overview-tab">
            <i class="fas fa-chart-line mr-2" aria-hidden="true"></i>
            Overview
          </button>
          <button type="button"
                  data-tab="pipelines"
                  class="tab-button relative flex-1 py-4 px-6 text-sm font-semibold text-slate-600 hover:text-slate-800 hover:bg-slate-50 border-b-2 border-transparent hover:border-slate-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                  role="tab"
                  aria-selected="false"
                  aria-controls="pipelines-tab">
            <i class="fas fa-project-diagram mr-2" aria-hidden="true"></i>
            Pipelines
          </button>
          <button type="button"
                  data-tab="settings"
                  class="tab-button relative flex-1 py-4 px-6 text-sm font-semibold text-slate-600 hover:text-slate-800 hover:bg-slate-50 border-b-2 border-transparent hover:border-slate-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                  role="tab"
                  aria-selected="false"
                  aria-controls="settings-tab">
            <i class="fas fa-cog mr-2" aria-hidden="true"></i>
            Settings
          </button>
        </nav>
      </div>

      <!-- Tab Content Container -->
      <div class="p-8">
        <!-- Overview Tab -->
        <div id="overview-tab" class="tab-content" role="tabpanel" aria-labelledby="overview-tab-button">
          <div class="flex items-center mb-8">
            <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center">
              <i class="fas fa-chart-line text-white" aria-hidden="true"></i>
            </div>
            <div class="ml-4">
              <h3 class="text-2xl font-bold text-slate-900">Recent Activity</h3>
              <p class="text-slate-600">Latest synchronization events</p>
            </div>
          </div>

          <% if @recent_syncs.any? %>
            <div class="bg-slate-50 rounded-2xl p-6">
              <div class="flow-root">
                <ul class="space-y-6">
                  <% @recent_syncs.each_with_index do |sync, index| %>
                    <li class="relative">
                      <% if index < @recent_syncs.length - 1 %>
                        <span class="absolute top-12 left-6 -ml-px h-full w-0.5 bg-slate-300" aria-hidden="true"></span>
                      <% end %>
                      <div class="relative flex items-start space-x-4">
                        <div class="flex-shrink-0">
                          <% case sync.status %>
                          <% when 'completed' %>
                            <div class="w-12 h-12 rounded-full bg-gradient-to-br from-emerald-500 to-emerald-600 flex items-center justify-center shadow-lg ring-4 ring-white">
                              <i class="fas fa-check text-white" aria-hidden="true"></i>
                            </div>
                          <% when 'failed' %>
                            <div class="w-12 h-12 rounded-full bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center shadow-lg ring-4 ring-white">
                              <i class="fas fa-times text-white" aria-hidden="true"></i>
                            </div>
                          <% when 'running' %>
                            <div class="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg ring-4 ring-white">
                              <i class="fas fa-sync fa-spin text-white" aria-hidden="true"></i>
                            </div>
                          <% else %>
                            <div class="w-12 h-12 rounded-full bg-gradient-to-br from-gray-500 to-gray-600 flex items-center justify-center shadow-lg ring-4 ring-white">
                              <i class="fas fa-clock text-white" aria-hidden="true"></i>
                            </div>
                          <% end %>
                        </div>
                        <div class="flex-1 bg-white rounded-xl p-4 shadow-sm border border-slate-200">
                          <div class="flex justify-between items-start">
                            <div>
                              <p class="font-semibold text-slate-900">
                                <% if sync.completed? %>
                                  Synced <%= number_with_delimiter(sync.records_imported || 0) %> records
                                <% elsif sync.failed? %>
                                  Sync failed
                                <% elsif sync.running? %>
                                  Sync in progress...
                                <% else %>
                                  Sync <%= sync.status %>
                                <% end %>
                              </p>
                              <p class="text-sm text-slate-600 mt-1">
                                <% if sync.completed? %>
                                  <%= sync.sync_type&.humanize || 'Full' %> sync completed
                                  <% if sync.duration_seconds %>
                                    in <%= sync.duration_seconds < 60 ? "#{sync.duration_seconds.round}s" : "#{(sync.duration_seconds / 60).round}m" %>
                                  <% end %>
                                <% elsif sync.failed? %>
                                  <%= sync.error_message || 'Synchronization failed' %>
                                <% elsif sync.running? %>
                                  Processing <%= number_with_delimiter(sync.records_processed || 0) %> records...
                                <% else %>
                                  <%= sync.status&.humanize %>
                                <% end %>
                              </p>
                            </div>
                            <div class="text-right">
                              <p class="text-sm font-medium text-slate-900">
                                <%= time_ago_in_words(sync.started_at || sync.created_at) %> ago
                              </p>
                              <p class="text-xs text-slate-500 mt-1">
                                <%= (sync.started_at || sync.created_at).strftime("%b %d, %I:%M %p") %>
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  <% end %>
                </ul>
              </div>
              
              <div class="mt-6 text-center">
                <%= link_to sync_history_dashboard_data_source_path(@data_source), 
                    class: "text-primary-600 hover:text-primary-700 font-medium text-sm inline-flex items-center" do %>
                  View Full History
                  <i class="fas fa-arrow-right ml-2" aria-hidden="true"></i>
                <% end %>
              </div>
            </div>
          <% else %>
            <div class="text-center py-12 bg-slate-50 rounded-2xl">
              <div class="w-16 h-16 bg-slate-200 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-sync-alt text-slate-500 text-xl" aria-hidden="true"></i>
              </div>
              <h4 class="text-lg font-semibold text-slate-900 mb-2">No sync activity yet</h4>
              <p class="text-slate-600 mb-6">Start importing data to see synchronization history</p>
              <% if @data_source.can_sync? %>
                <%= button_to sync_dashboard_data_source_path(@data_source),
                    method: :post,
                    class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white font-semibold rounded-xl shadow-sm hover:from-primary-600 hover:to-primary-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2" do %>
                  <i class="fas fa-sync-alt mr-2" aria-hidden="true"></i>
                  Start First Sync
                <% end %>
              <% end %>
            </div>
          <% end %>
        </div>

        <!-- Pipelines Tab -->
        <div id="pipelines-tab" class="tab-content hidden" role="tabpanel" aria-labelledby="pipelines-tab-button">
          <div class="flex items-center mb-8">
            <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
              <i class="fas fa-project-diagram text-white" aria-hidden="true"></i>
            </div>
            <div class="ml-4">
              <h3 class="text-2xl font-bold text-slate-900">Connected Pipelines</h3>
              <p class="text-slate-600">Transform and move data between sources</p>
            </div>
          </div>

          <!-- Pipeline Information -->
          <div class="bg-blue-50 rounded-2xl p-6 mb-8">
            <div class="flex items-start">
              <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <i class="fas fa-info-circle text-blue-600" aria-hidden="true"></i>
              </div>
              <div class="ml-4">
                <h4 class="font-semibold text-blue-900 mb-2">About Data Pipelines</h4>
                <p class="text-blue-800 leading-relaxed">
                  Pipelines enable you to create automated workflows that transform, validate, and route your data.
                  Connect this data source to pipelines to process data in real-time or on a schedule.
                </p>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div class="text-center py-16 bg-slate-50 rounded-2xl border-2 border-dashed border-slate-300">
            <div class="w-20 h-20 bg-slate-200 rounded-full flex items-center justify-center mx-auto mb-6">
              <i class="fas fa-project-diagram text-slate-500 text-2xl" aria-hidden="true"></i>
            </div>
            <h4 class="text-xl font-bold text-slate-900 mb-3">No pipelines connected</h4>
            <p class="text-slate-600 mb-8 max-w-md mx-auto">
              Create your first pipeline to start transforming and routing data from this source to other destinations.
            </p>
            <button type="button"
                    class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-500 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:from-purple-600 hover:to-purple-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 hover:shadow-xl transform hover:scale-105"
                    aria-label="Create a new data pipeline">
              <i class="fas fa-plus mr-3" aria-hidden="true"></i>
              Create Your First Pipeline
            </button>
          </div>
        </div>

        <!-- Settings Tab -->
        <div id="settings-tab" class="tab-content hidden" role="tabpanel" aria-labelledby="settings-tab-button">
          <div class="flex items-center mb-8">
            <div class="w-10 h-10 bg-gradient-to-br from-slate-500 to-slate-600 rounded-xl flex items-center justify-center">
              <i class="fas fa-cog text-white" aria-hidden="true"></i>
            </div>
            <div class="ml-4">
              <h3 class="text-2xl font-bold text-slate-900">Data Source Settings</h3>
              <p class="text-slate-600">Manage advanced configuration options</p>
            </div>
          </div>

          <div class="space-y-8">
            <!-- General Settings -->
            <div class="bg-slate-50 rounded-2xl p-6">
              <h4 class="text-lg font-semibold text-slate-900 mb-4 flex items-center">
                <i class="fas fa-sliders-h mr-3 text-slate-600" aria-hidden="true"></i>
                General Settings
              </h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-white rounded-xl p-4 border border-slate-200">
                  <label class="block text-sm font-semibold text-slate-700 mb-2">Sync Frequency</label>
                  <p class="text-slate-600 text-sm mb-3">How often data should be synchronized</p>
                  <div class="text-lg font-medium text-primary-600">
                    <%= @data_source.sync_frequency_text %>
                  </div>
                </div>
                <div class="bg-white rounded-xl p-4 border border-slate-200">
                  <label class="block text-sm font-semibold text-slate-700 mb-2">Data Retention</label>
                  <p class="text-slate-600 text-sm mb-3">How long to keep synchronized data</p>
                  <div class="text-lg font-medium text-slate-900">
                    Indefinite
                  </div>
                </div>
              </div>
            </div>

            <!-- Danger Zone -->
            <div class="bg-gradient-to-r from-red-50 to-red-50/50 border-2 border-red-200 rounded-2xl p-8">
              <div class="flex items-center mb-6">
                <div class="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center">
                  <i class="fas fa-exclamation-triangle text-red-600" aria-hidden="true"></i>
                </div>
                <div class="ml-4">
                  <h4 class="text-xl font-bold text-red-900">Danger Zone</h4>
                  <p class="text-red-700">These actions are irreversible. Please be certain before proceeding.</p>
                </div>
              </div>

              <div class="space-y-6">
                <!-- Clear Data Action -->
                <div class="bg-white rounded-xl p-6 border border-red-200">
                  <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div class="flex-1">
                      <h5 class="text-lg font-semibold text-slate-900 mb-2 flex items-center">
                        <i class="fas fa-trash-alt mr-3 text-red-500" aria-hidden="true"></i>
                        Clear All Data
                      </h5>
                      <p class="text-slate-600 leading-relaxed">
                        Remove all imported data from this source while keeping the connection configuration.
                        This will not affect the original data source.
                      </p>
                    </div>
                    <button type="button"
                            class="inline-flex items-center px-6 py-3 bg-red-600 text-white font-semibold rounded-xl shadow-sm hover:bg-red-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 hover:shadow-md"
                            onclick="if(confirm('Are you sure you want to clear all data? This action cannot be undone.')) { /* Add clear data logic */ }"
                            aria-label="Clear all imported data">
                      <i class="fas fa-broom mr-2" aria-hidden="true"></i>
                      Clear Data
                    </button>
                  </div>
                </div>

                <!-- Delete Source Action -->
                <div class="bg-white rounded-xl p-6 border border-red-300">
                  <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div class="flex-1">
                      <h5 class="text-lg font-semibold text-slate-900 mb-2 flex items-center">
                        <i class="fas fa-times-circle mr-3 text-red-600" aria-hidden="true"></i>
                        Delete Data Source
                      </h5>
                      <p class="text-slate-600 leading-relaxed">
                        Permanently remove this data source and all associated data, configurations, and history.
                        This action cannot be undone.
                      </p>
                    </div>
                    <%= button_to dashboard_data_source_path(@data_source),
                        method: :delete,
                        data: { confirm: "Are you absolutely sure? This will permanently delete the data source and all associated data. This action cannot be undone." },
                        class: "inline-flex items-center px-6 py-3 bg-red-700 text-white font-semibold rounded-xl shadow-sm hover:bg-red-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-red-600 focus:ring-offset-2 hover:shadow-md",
                        'aria-label': 'Permanently delete this data source' do %>
                      <i class="fas fa-trash mr-2" aria-hidden="true"></i>
                      Delete Source
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<%= javascript_tag nonce: true do %>
  document.addEventListener('DOMContentLoaded', function() {
    // Enhanced tab functionality with accessibility
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    // Initialize first tab as active
    if (tabButtons.length > 0) {
      setActiveTab(tabButtons[0]);
    }

    // Add click and keyboard handlers to tab buttons
    tabButtons.forEach((button, index) => {
      button.addEventListener('click', function(e) {
        e.preventDefault();
        setActiveTab(this);
      });

      // Keyboard navigation
      button.addEventListener('keydown', function(e) {
        let targetIndex = index;

        switch(e.key) {
          case 'ArrowLeft':
            e.preventDefault();
            targetIndex = index > 0 ? index - 1 : tabButtons.length - 1;
            break;
          case 'ArrowRight':
            e.preventDefault();
            targetIndex = index < tabButtons.length - 1 ? index + 1 : 0;
            break;
          case 'Home':
            e.preventDefault();
            targetIndex = 0;
            break;
          case 'End':
            e.preventDefault();
            targetIndex = tabButtons.length - 1;
            break;
          default:
            return;
        }

        tabButtons[targetIndex].focus();
        setActiveTab(tabButtons[targetIndex]);
      });
    });

    function setActiveTab(activeButton) {
      const targetTab = activeButton.getAttribute('data-tab');

      // Update button styles and ARIA attributes
      tabButtons.forEach(btn => {
        const isActive = btn === activeButton;

        // Remove all state classes
        btn.classList.remove(
          'border-primary-500', 'text-primary-600', 'bg-white',
          'border-transparent', 'text-slate-600', 'hover:text-slate-800', 'hover:bg-slate-50', 'hover:border-slate-300'
        );

        if (isActive) {
          btn.classList.add('border-primary-500', 'text-primary-600', 'bg-white');
          btn.setAttribute('aria-selected', 'true');
        } else {
          btn.classList.add('border-transparent', 'text-slate-600', 'hover:text-slate-800', 'hover:bg-slate-50', 'hover:border-slate-300');
          btn.setAttribute('aria-selected', 'false');
        }
      });

      // Show/hide tab content with smooth transition
      tabContents.forEach(content => {
        const isTargetContent = content.id === targetTab + '-tab';

        if (isTargetContent) {
          content.classList.remove('hidden');
          content.style.opacity = '0';
          content.style.transform = 'translateY(10px)';

          // Smooth fade in
          requestAnimationFrame(() => {
            content.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            content.style.opacity = '1';
            content.style.transform = 'translateY(0)';
          });
        } else {
          content.classList.add('hidden');
        }
      });
    }

    // Add smooth hover effects to action cards
    const actionCards = document.querySelectorAll('.group\\/action');
    actionCards.forEach(card => {
      card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px)';
      });

      card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
      });
    });

    // Add loading states for buttons
    const syncButton = document.querySelector('button[formaction*="sync"]');
    if (syncButton) {
      syncButton.addEventListener('click', function() {
        const icon = this.querySelector('i');
        if (icon) {
          icon.classList.add('animate-spin');
        }
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-sync-alt animate-spin mr-2" aria-hidden="true"></i>Syncing...';
      });
    }
  });
<% end %>