<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
  <div>
    <label class="block text-sm font-medium text-neutral-700 mb-1">Host</label>
    <input type="text" 
           name="data_source[connection_config][host]" 
           value="<%= data_source.connection_config_value('host') %>"
           class="form-input w-full" 
           placeholder="localhost or database.example.com"
           required>
  </div>
  
  <div>
    <label class="block text-sm font-medium text-neutral-700 mb-1">Port</label>
    <input type="number" 
           name="data_source[connection_config][port]" 
           value="<%= data_source.connection_config_value('port') || 5432 %>"
           class="form-input w-full" 
           placeholder="5432"
           required>
  </div>
  
  <div>
    <label class="block text-sm font-medium text-neutral-700 mb-1">Database</label>
    <input type="text" 
           name="data_source[connection_config][database]" 
           value="<%= data_source.connection_config_value('database') %>"
           class="form-input w-full" 
           placeholder="mydatabase"
           required>
  </div>
  
  <div>
    <label class="block text-sm font-medium text-neutral-700 mb-1">Username</label>
    <input type="text" 
           name="data_source[connection_config][username]" 
           value="<%= data_source.connection_config_value('username') %>"
           class="form-input w-full" 
           placeholder="postgres"
           required>
  </div>
  
  <div class="md:col-span-2">
    <label class="block text-sm font-medium text-neutral-700 mb-1">Password</label>
    <input type="password" 
           name="data_source[connection_config][password]" 
           value="<%= data_source.connection_config_value('password') %>"
           class="form-input w-full" 
           placeholder="••••••••">
    <p class="mt-1 text-sm text-neutral-500">Password is encrypted and stored securely</p>
  </div>
  
  <div class="md:col-span-2">
    <label class="block text-sm font-medium text-neutral-700 mb-1">SSL Mode</label>
    <select name="data_source[connection_config][sslmode]" class="form-input w-full">
      <option value="disable" <%= 'selected' if data_source.connection_config_value('sslmode') == 'disable' %>>Disable</option>
      <option value="allow" <%= 'selected' if data_source.connection_config_value('sslmode') == 'allow' %>>Allow</option>
      <option value="prefer" <%= 'selected' if data_source.connection_config_value('sslmode') == 'prefer' || data_source.connection_config_value('sslmode').nil? %>>Prefer (default)</option>
      <option value="require" <%= 'selected' if data_source.connection_config_value('sslmode') == 'require' %>>Require</option>
      <option value="verify-ca" <%= 'selected' if data_source.connection_config_value('sslmode') == 'verify-ca' %>>Verify CA</option>
      <option value="verify-full" <%= 'selected' if data_source.connection_config_value('sslmode') == 'verify-full' %>>Verify Full</option>
    </select>
  </div>
</div>