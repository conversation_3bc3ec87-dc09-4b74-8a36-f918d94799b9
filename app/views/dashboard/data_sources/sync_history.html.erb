<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Sync History</h1>
        <p class="mt-1 text-sm text-gray-600">
          <%= @data_source.name %> - View all synchronization attempts
        </p>
      </div>
      <div>
        <%= link_to dashboard_data_source_path(@data_source), class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" do %>
          <svg class="mr-2 -ml-1 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back to Data Source
        <% end %>
      </div>
    </div>
  </div>

  <div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <% if @sync_logs.any? %>
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Started At
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Duration
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Records
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Type
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Error
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <% @sync_logs.each do |sync_log| %>
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <%= sync_log.started_at.strftime("%Y-%m-%d %H:%M:%S") %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                  <%= case sync_log.status
                      when 'completed'
                        'bg-green-100 text-green-800'
                      when 'failed'
                        'bg-red-100 text-red-800'
                      when 'running'
                        'bg-blue-100 text-blue-800'
                      when 'cancelled'
                        'bg-gray-100 text-gray-800'
                      else
                        'bg-yellow-100 text-yellow-800'
                      end %>">
                  <%= sync_log.status.capitalize %>
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <% if sync_log.duration_seconds %>
                  <%= number_with_precision(sync_log.duration_seconds, precision: 2) %>s
                <% else %>
                  -
                <% end %>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div class="text-xs">
                  <div>Processed: <%= number_with_delimiter(sync_log.records_processed) %></div>
                  <div>Imported: <%= number_with_delimiter(sync_log.records_imported) %></div>
                  <% if sync_log.records_failed > 0 %>
                    <div class="text-red-600">Failed: <%= number_with_delimiter(sync_log.records_failed) %></div>
                  <% end %>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <%= sync_log.sync_type || 'Manual' %>
              </td>
              <td class="px-6 py-4 text-sm text-gray-500">
                <% if sync_log.error_message.present? %>
                  <div class="max-w-xs truncate" title="<%= sync_log.error_message %>">
                    <%= sync_log.error_message %>
                  </div>
                <% else %>
                  -
                <% end %>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>

      <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <%= paginate @sync_logs %>
      </div>
    <% else %>
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No sync history</h3>
        <p class="mt-1 text-sm text-gray-500">This data source has not been synchronized yet.</p>
        <div class="mt-6">
          <%= link_to "Sync Now", sync_dashboard_data_source_path(@data_source), method: :post, class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        </div>
      </div>
    <% end %>
  </div>
</div>