<% content_for :title, "Sync History - #{@data_source.name}" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Enhanced Header -->
    <div class="mb-8">
      <!-- Breadcrumb Navigation -->
      <nav class="flex items-center text-sm mb-4" aria-label="Breadcrumb">
        <div class="flex items-center gap-2">
          <i class="fas fa-home text-neutral-400" aria-hidden="true"></i>
          <%= link_to "Dashboard", dashboard_root_path,
              class: "text-neutral-500 hover:text-primary-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-sm" %>
          <i class="fas fa-chevron-right mx-2 text-neutral-400 text-xs" aria-hidden="true"></i>
          <%= link_to "Data Sources", dashboard_data_sources_path,
              class: "text-neutral-500 hover:text-primary-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-sm" %>
          <i class="fas fa-chevron-right mx-2 text-neutral-400 text-xs" aria-hidden="true"></i>
          <%= link_to @data_source.name, dashboard_data_source_path(@data_source),
              class: "text-neutral-500 hover:text-primary-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-sm" %>
          <i class="fas fa-chevron-right mx-2 text-neutral-400 text-xs" aria-hidden="true"></i>
          <span class="text-neutral-900 font-medium">Sync History</span>
        </div>
      </nav>

      <!-- Page Header -->
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div class="flex items-center gap-4">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center shadow-lg">
              <i class="fas fa-history text-white text-xl" aria-hidden="true"></i>
            </div>
          </div>
          <div>
            <h1 class="text-3xl font-bold text-neutral-900 tracking-tight">Sync History</h1>
            <p class="text-neutral-600 mt-1">
              Synchronization logs and performance metrics for
              <span class="font-medium text-neutral-900"><%= @data_source.name %></span>
            </p>
          </div>
        </div>

        <!-- Header Actions -->
        <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3">
          <!-- Sync Now Button -->
          <% if @data_source.can_sync? %>
            <%= button_to sync_dashboard_data_source_path(@data_source),
                method: :post,
                class: "inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-semibold text-white bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl",
                'aria-label': "Start a new sync for #{@data_source.name}" do %>
              <i class="fas fa-sync-alt mr-2 text-sm" aria-hidden="true"></i>
              Sync Now
            <% end %>
          <% end %>

          <!-- Back Button -->
          <%= link_to dashboard_data_source_path(@data_source),
              class: "inline-flex items-center px-4 py-2 border border-neutral-300 rounded-lg text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200 shadow-sm" do %>
            <i class="fas fa-arrow-left mr-2 text-sm" aria-hidden="true"></i>
            Back to Data Source
          <% end %>
        </div>
      </div>
    </div>

    <!-- Enhanced Statistics Cards -->
    <% if @sync_logs.any? %>
      <%
        total_syncs = @sync_logs.count
        completed_syncs = @sync_logs.select(&:completed?).count
        failed_syncs = @sync_logs.select(&:failed?).count
        running_syncs = @sync_logs.select(&:running?).count
        avg_duration = @sync_logs.completed.where.not(duration_seconds: nil).average(:duration_seconds)
        total_records = @sync_logs.sum(:records_imported)
      %>

      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Syncs -->
        <div class="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden hover:shadow-md transition-shadow duration-200">
          <div class="p-6">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center gap-3 mb-2">
                  <div class="w-10 h-10 bg-gradient-to-br from-slate-500 to-slate-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-sync-alt text-white text-lg" aria-hidden="true"></i>
                  </div>
                  <div>
                    <p class="text-sm font-medium text-neutral-600">Total Syncs</p>
                    <p class="text-2xl font-bold text-neutral-900"><%= total_syncs %></p>
                  </div>
                </div>
              </div>
              <button type="button"
                      class="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full"
                      data-controller="tooltip"
                      data-tooltip-content-value="Total number of synchronization attempts"
                      aria-label="Help: Total syncs information">
                <i class="fas fa-info-circle text-sm" aria-hidden="true"></i>
              </button>
            </div>
            <div class="mt-3 flex items-center text-sm">
              <span class="text-neutral-500">All sync attempts</span>
            </div>
          </div>
        </div>

        <!-- Success Rate -->
        <div class="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden hover:shadow-md transition-shadow duration-200">
          <div class="p-6">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center gap-3 mb-2">
                  <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check-circle text-white text-lg" aria-hidden="true"></i>
                  </div>
                  <div>
                    <p class="text-sm font-medium text-neutral-600">Success Rate</p>
                    <p class="text-2xl font-bold text-neutral-900">
                      <%= total_syncs > 0 ? ((completed_syncs.to_f / total_syncs * 100).round(1)) : 0 %>%
                    </p>
                  </div>
                </div>
              </div>
              <button type="button"
                      class="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full"
                      data-controller="tooltip"
                      data-tooltip-content-value="Percentage of successful synchronizations"
                      aria-label="Help: Success rate information">
                <i class="fas fa-info-circle text-sm" aria-hidden="true"></i>
              </button>
            </div>
            <div class="mt-3 flex items-center text-sm">
              <% if completed_syncs > 0 %>
                <span class="text-green-600 font-medium"><%= completed_syncs %> successful</span>
              <% else %>
                <span class="text-neutral-500">No completed syncs</span>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Average Duration -->
        <div class="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden hover:shadow-md transition-shadow duration-200">
          <div class="p-6">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center gap-3 mb-2">
                  <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-white text-lg" aria-hidden="true"></i>
                  </div>
                  <div>
                    <p class="text-sm font-medium text-neutral-600">Avg Duration</p>
                    <p class="text-2xl font-bold text-neutral-900">
                      <% if avg_duration %>
                        <% if avg_duration < 60 %>
                          <%= avg_duration.round %>s
                        <% elsif avg_duration < 3600 %>
                          <%= (avg_duration / 60).round %>m
                        <% else %>
                          <%= (avg_duration / 3600).round(1) %>h
                        <% end %>
                      <% else %>
                        -
                      <% end %>
                    </p>
                  </div>
                </div>
              </div>
              <button type="button"
                      class="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full"
                      data-controller="tooltip"
                      data-tooltip-content-value="Average time taken for completed synchronizations"
                      aria-label="Help: Average duration information">
                <i class="fas fa-info-circle text-sm" aria-hidden="true"></i>
              </button>
            </div>
            <div class="mt-3 flex items-center text-sm">
              <span class="text-neutral-500">Per sync operation</span>
            </div>
          </div>
        </div>

        <!-- Total Records -->
        <div class="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden hover:shadow-md transition-shadow duration-200">
          <div class="p-6">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center gap-3 mb-2">
                  <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <i class="fas fa-database text-white text-lg" aria-hidden="true"></i>
                  </div>
                  <div>
                    <p class="text-sm font-medium text-neutral-600">Records Synced</p>
                    <p class="text-2xl font-bold text-neutral-900"><%= number_with_delimiter(total_records) %></p>
                  </div>
                </div>
              </div>
              <button type="button"
                      class="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-full"
                      data-controller="tooltip"
                      data-tooltip-content-value="Total number of records successfully imported"
                      aria-label="Help: Total records information">
                <i class="fas fa-info-circle text-sm" aria-hidden="true"></i>
              </button>
            </div>
            <div class="mt-3 flex items-center text-sm">
              <span class="text-neutral-500">Across all syncs</span>
            </div>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Enhanced Sync Logs Table -->
    <div class="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden" data-controller="sync-history">
      <% if @sync_logs.any? %>
        <!-- Table Header -->
        <div class="px-6 py-5 border-b border-neutral-200 bg-gradient-to-r from-neutral-50 to-white">
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                <i class="fas fa-list text-white text-sm" aria-hidden="true"></i>
              </div>
              <div>
                <h2 class="text-lg font-semibold text-neutral-900">
                  Sync History
                  <span class="inline-flex items-center ml-2 px-2 py-1 bg-primary-100 text-primary-800 text-xs font-medium rounded-full">
                    <%= @sync_logs.count %> records
                  </span>
                </h2>
                <p class="text-sm text-neutral-600 mt-1">Detailed synchronization logs and performance data</p>
              </div>
            </div>

            <!-- Filter Controls -->
            <div class="flex items-center gap-2">
              <div class="relative" data-controller="dropdown">
                <button type="button"
                        class="inline-flex items-center px-3 py-2 border border-neutral-300 rounded-lg text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200"
                        data-action="click->dropdown#toggle"
                        aria-haspopup="true"
                        aria-expanded="false">
                  <i class="fas fa-filter mr-2 text-neutral-500 text-sm" aria-hidden="true"></i>
                  Filter
                  <i class="fas fa-chevron-down ml-2 text-neutral-400 text-xs" aria-hidden="true"></i>
                </button>

                <div class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-neutral-200 z-20"
                     data-dropdown-target="menu">
                  <div class="py-2">
                    <button type="button"
                            class="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100"
                            data-action="click->sync-history#filterByStatus"
                            data-status="all">
                      <i class="fas fa-list mr-2 text-neutral-500 text-sm" aria-hidden="true"></i>
                      All Syncs
                    </button>
                    <button type="button"
                            class="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100"
                            data-action="click->sync-history#filterByStatus"
                            data-status="completed">
                      <i class="fas fa-check-circle mr-2 text-green-500 text-sm" aria-hidden="true"></i>
                      Completed
                    </button>
                    <button type="button"
                            class="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100"
                            data-action="click->sync-history#filterByStatus"
                            data-status="failed">
                      <i class="fas fa-exclamation-triangle mr-2 text-red-500 text-sm" aria-hidden="true"></i>
                      Failed
                    </button>
                    <button type="button"
                            class="w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100 focus:outline-none focus:bg-neutral-100"
                            data-action="click->sync-history#filterByStatus"
                            data-status="running">
                      <i class="fas fa-sync-alt mr-2 text-blue-500 text-sm" aria-hidden="true"></i>
                      Running
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Table Content -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-neutral-200" role="table" aria-label="Sync history data">
            <thead class="bg-neutral-50">
              <tr role="row">
                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-neutral-700 uppercase tracking-wider">
                  <div class="flex items-center gap-2">
                    <i class="fas fa-calendar text-neutral-400 text-sm" aria-hidden="true"></i>
                    Started At
                  </div>
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-neutral-700 uppercase tracking-wider">
                  <div class="flex items-center gap-2">
                    <i class="fas fa-tag text-neutral-400 text-sm" aria-hidden="true"></i>
                    Type
                  </div>
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-neutral-700 uppercase tracking-wider">
                  <div class="flex items-center gap-2">
                    <i class="fas fa-info-circle text-neutral-400 text-sm" aria-hidden="true"></i>
                    Status
                  </div>
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-neutral-700 uppercase tracking-wider">
                  <div class="flex items-center gap-2">
                    <i class="fas fa-clock text-neutral-400 text-sm" aria-hidden="true"></i>
                    Duration
                  </div>
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-neutral-700 uppercase tracking-wider">
                  <div class="flex items-center gap-2">
                    <i class="fas fa-database text-neutral-400 text-sm" aria-hidden="true"></i>
                    Records
                  </div>
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-neutral-700 uppercase tracking-wider">
                  <div class="flex items-center gap-2">
                    <i class="fas fa-chart-line text-neutral-400 text-sm" aria-hidden="true"></i>
                    Success Rate
                  </div>
                </th>
                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-neutral-700 uppercase tracking-wider">
                  <div class="flex items-center gap-2">
                    <i class="fas fa-cog text-neutral-400 text-sm" aria-hidden="true"></i>
                    Actions
                  </div>
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-neutral-200" data-sync-history-target="tableBody">
              <% @sync_logs.each_with_index do |sync_log, index| %>
                <tr role="row"
                    class="<%= index.even? ? 'bg-white' : 'bg-neutral-50' %> hover:bg-neutral-100 transition-colors duration-150"
                    data-sync-history-target="syncRow"
                    data-status="<%= sync_log.status %>"
                    data-sync-id="<%= sync_log.id %>">

                  <!-- Started At -->
                  <td class="px-6 py-4 text-sm" role="gridcell">
                    <div class="flex flex-col">
                      <span class="font-medium text-neutral-900">
                        <%= sync_log.started_at.strftime("%b %d, %Y") %>
                      </span>
                      <span class="text-neutral-500 text-xs">
                        <%= sync_log.started_at.strftime("%l:%M %p") %>
                      </span>
                    </div>
                  </td>

                  <!-- Sync Type -->
                  <td class="px-6 py-4 text-sm" role="gridcell">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border
                      <%= case sync_log.sync_type || 'full'
                        when 'full' then 'bg-blue-50 text-blue-700 border-blue-200'
                        when 'incremental' then 'bg-green-50 text-green-700 border-green-200'
                        when 'refresh' then 'bg-purple-50 text-purple-700 border-purple-200'
                        else 'bg-gray-50 text-gray-700 border-gray-200'
                        end %>"
                      aria-label="Sync type: <%= sync_log.sync_type || 'full' %>">
                      <i class="fas fa-<%= case sync_log.sync_type || 'full'
                        when 'full' then 'database'
                        when 'incremental' then 'plus'
                        when 'refresh' then 'redo'
                        else 'sync'
                        end %> mr-1 text-xs" aria-hidden="true"></i>
                      <%= (sync_log.sync_type || 'full').humanize %>
                    </span>
                  </td>

                  <!-- Status -->
                  <td class="px-6 py-4 text-sm" role="gridcell">
                    <% case sync_log.status %>
                    <% when 'completed' %>
                      <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-50 text-green-700 border border-green-200"
                            aria-label="Status: Completed successfully">
                        <i class="fas fa-check-circle mr-1 text-xs" aria-hidden="true"></i>
                        Completed
                      </span>
                    <% when 'failed' %>
                      <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-50 text-red-700 border border-red-200"
                            aria-label="Status: Failed with errors">
                        <i class="fas fa-exclamation-triangle mr-1 text-xs" aria-hidden="true"></i>
                        Failed
                      </span>
                    <% when 'running' %>
                      <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-200"
                            aria-label="Status: Currently running">
                        <i class="fas fa-sync-alt animate-spin mr-1 text-xs" aria-hidden="true"></i>
                        Running
                      </span>
                    <% when 'cancelled' %>
                      <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-50 text-gray-700 border border-gray-200"
                            aria-label="Status: Cancelled">
                        <i class="fas fa-ban mr-1 text-xs" aria-hidden="true"></i>
                        Cancelled
                      </span>
                    <% else %>
                      <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-50 text-gray-700 border border-gray-200"
                            aria-label="Status: <%= sync_log.status %>">
                        <i class="fas fa-question-circle mr-1 text-xs" aria-hidden="true"></i>
                        <%= sync_log.status.humanize %>
                      </span>
                    <% end %>
                  </td>

                  <!-- Duration -->
                  <td class="px-6 py-4 text-sm" role="gridcell">
                    <% if sync_log.duration_seconds %>
                      <div class="flex items-center gap-2">
                        <i class="fas fa-stopwatch text-neutral-400 text-xs" aria-hidden="true"></i>
                        <span class="font-medium text-neutral-900">
                          <% if sync_log.duration_seconds < 60 %>
                            <%= sync_log.duration_seconds.round %>s
                          <% elsif sync_log.duration_seconds < 3600 %>
                            <%= (sync_log.duration_seconds / 60).round %>m
                          <% else %>
                            <%= (sync_log.duration_seconds / 3600).round(1) %>h
                          <% end %>
                        </span>
                      </div>
                    <% else %>
                      <span class="text-neutral-400 italic">-</span>
                    <% end %>
                  </td>

                  <!-- Records -->
                  <td class="px-6 py-4 text-sm" role="gridcell">
                    <div class="space-y-1">
                      <div class="flex items-center gap-2">
                        <i class="fas fa-check text-green-500 text-xs" aria-hidden="true"></i>
                        <span class="font-medium text-neutral-900">
                          <%= number_with_delimiter(sync_log.records_imported || 0) %>
                        </span>
                        <span class="text-neutral-500 text-xs">imported</span>
                      </div>
                      <% if sync_log.records_processed && sync_log.records_processed > 0 %>
                        <div class="text-xs text-neutral-500">
                          of <%= number_with_delimiter(sync_log.records_processed) %> processed
                          <% if sync_log.records_failed && sync_log.records_failed > 0 %>
                            <span class="text-red-600 font-medium">
                              (<%= number_with_delimiter(sync_log.records_failed) %> failed)
                            </span>
                          <% end %>
                        </div>
                      <% end %>
                    </div>
                  </td>

                  <!-- Success Rate -->
                  <td class="px-6 py-4 text-sm" role="gridcell">
                    <% if sync_log.records_processed && sync_log.records_processed > 0 %>
                      <div class="flex items-center gap-3">
                        <div class="flex-1">
                          <div class="w-full bg-neutral-200 rounded-full h-2">
                            <div class="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full transition-all duration-300"
                                 style="width: <%= sync_log.success_rate %>%"
                                 aria-label="Success rate: <%= sync_log.success_rate %>%"></div>
                          </div>
                        </div>
                        <span class="font-medium text-neutral-900 text-xs min-w-0">
                          <%= sync_log.success_rate %>%
                        </span>
                      </div>
                    <% else %>
                      <span class="text-neutral-400 italic">-</span>
                    <% end %>
                  </td>

                  <!-- Actions -->
                  <td class="px-6 py-4 text-sm" role="gridcell">
                    <button type="button"
                            class="inline-flex items-center px-3 py-1 border border-neutral-300 rounded-lg text-xs font-medium text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200"
                            data-action="click->sync-history#toggleDetails"
                            data-sync-id="<%= sync_log.id %>"
                            aria-expanded="false"
                            aria-controls="sync-details-<%= sync_log.id %>"
                            aria-label="Toggle details for sync started at <%= sync_log.started_at.strftime('%b %d, %Y %l:%M %p') %>">
                      <i class="fas fa-eye mr-1 text-xs" aria-hidden="true"></i>
                      Details
                    </button>
                  </td>
              </tr>

                <!-- Enhanced Expandable Details Row -->
                <tr id="sync-details-<%= sync_log.id %>"
                    class="hidden"
                    data-sync-history-target="detailsRow"
                    aria-labelledby="sync-row-<%= sync_log.id %>">
                  <td colspan="7" class="px-6 py-6 bg-gradient-to-r from-neutral-50 to-white border-t border-neutral-100">
                    <div class="max-w-6xl mx-auto">
                      <!-- Details Header -->
                      <div class="flex items-center gap-3 mb-6">
                        <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                          <i class="fas fa-info-circle text-white text-sm" aria-hidden="true"></i>
                        </div>
                        <div>
                          <h3 class="text-lg font-semibold text-neutral-900">Sync Details</h3>
                          <p class="text-sm text-neutral-600">
                            Detailed information for sync started at <%= sync_log.started_at.strftime("%B %d, %Y at %l:%M %p") %>
                          </p>
                        </div>
                      </div>

                      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Error Information -->
                        <% if sync_log.error_message %>
                          <div class="lg:col-span-2">
                            <div class="bg-red-50 border border-red-200 rounded-xl p-6">
                              <div class="flex items-start gap-3">
                                <div class="flex-shrink-0">
                                  <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-exclamation-triangle text-red-600 text-sm" aria-hidden="true"></i>
                                  </div>
                                </div>
                                <div class="flex-1">
                                  <h4 class="text-sm font-semibold text-red-900 mb-2">Error Information</h4>
                                  <p class="text-sm text-red-800 leading-relaxed mb-4"><%= sync_log.error_message %></p>

                                  <% if sync_log.error_details && sync_log.error_details['backtrace'] %>
                                    <details class="mt-4">
                                      <summary class="cursor-pointer text-sm font-medium text-red-700 hover:text-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 rounded">
                                        <i class="fas fa-code mr-2" aria-hidden="true"></i>
                                        View Technical Details
                                      </summary>
                                      <div class="mt-3 p-4 bg-red-100 rounded-lg">
                                        <pre class="text-xs text-red-900 overflow-x-auto whitespace-pre-wrap"><%= sync_log.error_details['backtrace'].join("\n") %></pre>
                                      </div>
                                    </details>
                                  <% end %>
                                </div>
                              </div>
                            </div>
                          </div>
                        <% end %>

                        <!-- Performance Metrics (if completed successfully) -->
                        <% if sync_log.completed? && !sync_log.error_message %>
                          <div class="bg-green-50 border border-green-200 rounded-xl p-6">
                            <div class="flex items-start gap-3">
                              <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                  <i class="fas fa-chart-line text-green-600 text-sm" aria-hidden="true"></i>
                                </div>
                              </div>
                              <div class="flex-1">
                                <h4 class="text-sm font-semibold text-green-900 mb-4">Performance Summary</h4>
                                <div class="grid grid-cols-2 gap-4">
                                  <div>
                                    <p class="text-xs font-medium text-green-700 uppercase tracking-wider">Processing Rate</p>
                                    <p class="text-lg font-bold text-green-900">
                                      <% if sync_log.duration_seconds && sync_log.duration_seconds > 0 %>
                                        <%= number_with_delimiter((sync_log.records_processed / sync_log.duration_seconds).round) %>/sec
                                      <% else %>
                                        -
                                      <% end %>
                                    </p>
                                  </div>
                                  <div>
                                    <p class="text-xs font-medium text-green-700 uppercase tracking-wider">Success Rate</p>
                                    <p class="text-lg font-bold text-green-900"><%= sync_log.success_rate %>%</p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        <% end %>

                        <!-- Additional Metadata -->
                        <% if sync_log.metadata && sync_log.metadata.any? %>
                          <div class="bg-blue-50 border border-blue-200 rounded-xl p-6">
                            <div class="flex items-start gap-3">
                              <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                  <i class="fas fa-info-circle text-blue-600 text-sm" aria-hidden="true"></i>
                                </div>
                              </div>
                              <div class="flex-1">
                                <h4 class="text-sm font-semibold text-blue-900 mb-4">Additional Information</h4>
                                <dl class="space-y-3">
                                  <% sync_log.metadata.each do |key, value| %>
                                    <div class="flex flex-col sm:flex-row sm:items-start gap-1 sm:gap-4">
                                      <dt class="text-xs font-medium text-blue-700 uppercase tracking-wider min-w-0 sm:w-32">
                                        <%= key.humanize %>:
                                      </dt>
                                      <dd class="text-sm text-blue-900 font-medium min-w-0 flex-1">
                                        <% if value.is_a?(Hash) || value.is_a?(Array) %>
                                          <details class="mt-1">
                                            <summary class="cursor-pointer text-blue-700 hover:text-blue-900">View Data</summary>
                                            <pre class="mt-2 text-xs bg-blue-100 p-3 rounded overflow-x-auto"><%= JSON.pretty_generate(value) %></pre>
                                          </details>
                                        <% else %>
                                          <%= value %>
                                        <% end %>
                                      </dd>
                                    </div>
                                  <% end %>
                                </dl>
                              </div>
                            </div>
                          </div>
                        <% end %>

                        <!-- Failed Rows Information -->
                        <% if sync_log.error_details && sync_log.error_details['failed_rows'] %>
                          <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
                            <div class="flex items-start gap-3">
                              <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                  <i class="fas fa-exclamation-triangle text-yellow-600 text-sm" aria-hidden="true"></i>
                                </div>
                              </div>
                              <div class="flex-1">
                                <h4 class="text-sm font-semibold text-yellow-900 mb-2">Failed Rows</h4>
                                <p class="text-sm text-yellow-800 mb-4">
                                  <%= number_with_delimiter(sync_log.error_details['failed_rows'].size) %> rows failed to import
                                </p>

                                <details class="mt-4">
                                  <summary class="cursor-pointer text-sm font-medium text-yellow-700 hover:text-yellow-900 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 rounded">
                                    <i class="fas fa-list mr-2" aria-hidden="true"></i>
                                    View Failed Rows (showing first 10)
                                  </summary>
                                  <div class="mt-3 space-y-2 max-h-64 overflow-y-auto">
                                    <% sync_log.error_details['failed_rows'].first(10).each do |failed_row| %>
                                      <div class="p-3 bg-yellow-100 rounded-lg border border-yellow-200">
                                        <div class="flex items-start gap-2">
                                          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-200 text-yellow-800">
                                            Row <%= failed_row['row_number'] %>
                                          </span>
                                          <span class="text-sm text-yellow-900 flex-1"><%= failed_row['error'] %></span>
                                        </div>
                                      </div>
                                    <% end %>
                                    <% if sync_log.error_details['failed_rows'].size > 10 %>
                                      <div class="text-center py-2">
                                        <span class="text-xs text-yellow-700 bg-yellow-100 px-3 py-1 rounded-full">
                                          ... and <%= number_with_delimiter(sync_log.error_details['failed_rows'].size - 10) %> more rows
                                        </span>
                                      </div>
                                    <% end %>
                                  </div>
                                </details>
                              </div>
                            </div>
                          </div>
                        <% end %>
                      </div>
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>

        <!-- Enhanced Pagination -->
        <% if @sync_logs.respond_to?(:current_page) %>
          <div class="bg-gradient-to-r from-neutral-50 to-white px-6 py-4 border-t border-neutral-200">
            <div class="flex items-center justify-between">
              <div class="text-sm text-neutral-600">
                Showing <%= @sync_logs.offset_value + 1 %> to <%= [@sync_logs.offset_value + @sync_logs.limit_value, @sync_logs.total_count].min %>
                of <%= @sync_logs.total_count %> sync records
              </div>
              <%= paginate @sync_logs, theme: 'twitter_bootstrap_4' %>
            </div>
          </div>
        <% end %>
      <% else %>
        <!-- Enhanced Empty State -->
        <div class="text-center py-16">
          <div class="w-20 h-20 bg-gradient-to-br from-neutral-100 to-neutral-200 rounded-full flex items-center justify-center mx-auto mb-6">
            <i class="fas fa-history text-3xl text-neutral-400" aria-hidden="true"></i>
          </div>
          <h3 class="text-xl font-semibold text-neutral-900 mb-2">No Sync History Yet</h3>
          <p class="text-neutral-600 mb-8 max-w-md mx-auto">
            This data source hasn't been synchronized yet. Start your first sync to begin tracking
            synchronization history and performance metrics.
          </p>

          <!-- Quick Actions -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <% if @data_source.can_sync? %>
              <%= button_to sync_dashboard_data_source_path(@data_source),
                  method: :post,
                  class: "inline-flex items-center px-6 py-3 border border-transparent rounded-lg text-sm font-semibold text-white bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200 shadow-lg hover:shadow-xl" do %>
                <i class="fas fa-sync-alt mr-2 text-sm" aria-hidden="true"></i>
                Start First Sync
              <% end %>
            <% else %>
              <div class="inline-flex items-center px-6 py-3 border border-neutral-300 rounded-lg text-sm font-medium text-neutral-500 bg-neutral-50 cursor-not-allowed">
                <i class="fas fa-exclamation-triangle mr-2 text-sm" aria-hidden="true"></i>
                Sync Not Available
              </div>
            <% end %>

            <%= link_to dashboard_data_source_path(@data_source),
                class: "inline-flex items-center px-6 py-3 border border-neutral-300 rounded-lg text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200" do %>
              <i class="fas fa-arrow-left mr-2 text-sm" aria-hidden="true"></i>
              Back to Data Source
            <% end %>
          </div>

          <!-- Help Information -->
          <div class="mt-12 max-w-2xl mx-auto">
            <div class="bg-blue-50 border border-blue-200 rounded-xl p-6">
              <div class="flex items-start gap-3">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-lightbulb text-blue-600 text-sm" aria-hidden="true"></i>
                  </div>
                </div>
                <div class="flex-1 text-left">
                  <h4 class="text-sm font-semibold text-blue-900 mb-2">About Sync History</h4>
                  <p class="text-sm text-blue-800 leading-relaxed">
                    Once you start syncing this data source, you'll see detailed logs including:
                  </p>
                  <ul class="mt-3 text-sm text-blue-800 space-y-1">
                    <li class="flex items-center gap-2">
                      <i class="fas fa-check text-blue-600 text-xs" aria-hidden="true"></i>
                      Sync duration and performance metrics
                    </li>
                    <li class="flex items-center gap-2">
                      <i class="fas fa-check text-blue-600 text-xs" aria-hidden="true"></i>
                      Number of records processed and imported
                    </li>
                    <li class="flex items-center gap-2">
                      <i class="fas fa-check text-blue-600 text-xs" aria-hidden="true"></i>
                      Error details and troubleshooting information
                    </li>
                    <li class="flex items-center gap-2">
                      <i class="fas fa-check text-blue-600 text-xs" aria-hidden="true"></i>
                      Success rates and data quality insights
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<script>
  function toggleDetails(id) {
    const row = document.getElementById(id);
    if (row) {
      row.classList.toggle('hidden');
    }
  }
</script>