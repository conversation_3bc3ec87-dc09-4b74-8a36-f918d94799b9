<%= form_with(model: [:dashboard, data_source],
              local: false,
              class: "h-full flex flex-col",
              html: { multipart: true },
              data: {
                controller: "enhanced-data-source-form",
                turbo: false,
                "enhanced-data-source-form-current-step-value": "1",
                "enhanced-data-source-form-total-steps-value": "4",
                action: "submit->enhanced-data-source-form#handleSubmit"
              }) do |form| %>

  <!-- Form Content Area -->
  <div class="flex-1 px-8 py-6 space-y-8">
    
    <!-- Error Messages -->
    <% if data_source.errors.any? %>
      <div class="bg-gradient-to-r from-red-50 to-red-50/50 border border-red-200 rounded-2xl p-6 shadow-sm" role="alert" aria-live="polite">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <div class="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center">
              <i class="fas fa-exclamation-triangle text-red-600" aria-hidden="true"></i>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <h3 class="text-lg font-semibold text-red-900 mb-2">Please fix the following errors:</h3>
            <ul class="space-y-1">
              <% data_source.errors.full_messages.each do |message| %>
                <li class="text-red-800 text-sm flex items-center">
                  <i class="fas fa-circle text-red-400 text-xs mr-2" aria-hidden="true"></i>
                  <%= message %>
                </li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Step 1: Basic Information -->
    <div class="form-step" data-step="1" data-enhanced-data-source-form-target="step">
      <div class="bg-white/80 backdrop-blur-sm border border-slate-200/60 rounded-2xl p-8 shadow-lg">
        <!-- Step Header -->
        <div class="flex items-center mb-8">
          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-md">
            <i class="fas fa-info-circle text-white text-lg" aria-hidden="true"></i>
          </div>
          <div class="ml-4">
            <h2 class="text-2xl font-bold text-slate-900">Basic Information</h2>
            <p class="text-slate-600">Configure the fundamental details of your data source</p>
          </div>
        </div>

        <!-- Form Fields -->
        <div class="space-y-6">
          <!-- Name Field -->
          <div class="group">
            <%= form.label :name, class: "block text-sm font-semibold text-slate-700 mb-3" do %>
              <i class="fas fa-tag mr-2 text-primary-500" aria-hidden="true"></i>
              Data Source Name
            <% end %>
            <%= form.text_field :name, 
                class: "w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 text-slate-900 placeholder-slate-400",
                placeholder: "e.g., Production Database, Customer CSV, Sales Data",
                'aria-describedby': 'name-help' %>
            <p id="name-help" class="mt-2 text-sm text-slate-600 flex items-center">
              <i class="fas fa-lightbulb mr-2 text-amber-500" aria-hidden="true"></i>
              Choose a descriptive name that helps identify this data source
            </p>
          </div>

          <!-- Description Field -->
          <div class="group">
            <%= form.label :description, class: "block text-sm font-semibold text-slate-700 mb-3" do %>
              <i class="fas fa-align-left mr-2 text-primary-500" aria-hidden="true"></i>
              Description <span class="text-slate-400 font-normal">(Optional)</span>
            <% end %>
            <%= form.text_area :description, 
                rows: 4,
                class: "w-full px-4 py-3 bg-white border border-slate-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 text-slate-900 placeholder-slate-400 resize-none",
                placeholder: "Provide additional context about this data source, its purpose, or any important notes...",
                'aria-describedby': 'description-help' %>
            <p id="description-help" class="mt-2 text-sm text-slate-600">
              Help your team understand what this data source contains and how it's used
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 2: Connection Type -->
    <div class="form-step hidden" data-step="2" data-enhanced-data-source-form-target="step">
      <div class="bg-white/80 backdrop-blur-sm border border-slate-200/60 rounded-2xl p-8 shadow-lg">
        <!-- Step Header -->
        <div class="flex items-center mb-8">
          <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-md">
            <i class="fas fa-plug text-white text-lg" aria-hidden="true"></i>
          </div>
          <div class="ml-4">
            <h2 class="text-2xl font-bold text-slate-900">Connection Type</h2>
            <p class="text-slate-600">Select how you want to connect to your data</p>
          </div>
        </div>

        <!-- Current Selection Display -->
        <div class="mb-8 p-6 bg-gradient-to-r from-primary-50 to-primary-50/50 rounded-2xl border border-primary-200" 
             data-enhanced-data-source-form-target="typeDescription">
          <div class="flex items-center">
            <div class="w-10 h-10 bg-primary-100 rounded-xl flex items-center justify-center">
              <i class="<%= data_source_icon(data_source.source_type) %> text-primary-600" aria-hidden="true"></i>
            </div>
            <div class="ml-4">
              <h3 class="font-semibold text-primary-900"><%= data_source.source_type_text %></h3>
              <p class="text-sm text-primary-700">Currently selected connection type</p>
            </div>
          </div>
        </div>

        <!-- Connection Type Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          <% DataSource::SOURCE_TYPES.each do |type| %>
            <label class="group relative cursor-pointer">
              <%= form.radio_button :source_type, type, 
                  class: "sr-only peer", 
                  data: { 
                    action: "change->enhanced-data-source-form#updateConnectionType",
                    "enhanced-data-source-form-target": "sourceTypeRadio"
                  } %>
              
              <div class="relative p-6 border-2 rounded-2xl transition-all duration-300 hover:shadow-lg
                          border-slate-200 hover:border-slate-300 hover:bg-slate-50
                          peer-checked:border-primary-500 peer-checked:bg-primary-50 peer-checked:shadow-lg">
                
                <!-- Icon and Title -->
                <div class="flex flex-col items-center text-center space-y-3">
                  <div class="w-12 h-12 bg-slate-100 group-hover:bg-slate-200 peer-checked:bg-primary-100 rounded-xl flex items-center justify-center transition-colors duration-200">
                    <i class="<%= data_source_icon(type) %> text-xl text-slate-500 group-hover:text-slate-600 peer-checked:text-primary-600" aria-hidden="true"></i>
                  </div>
                  
                  <div>
                    <h3 class="font-semibold text-slate-900 group-hover:text-slate-900 peer-checked:text-primary-900">
                      <%= type.humanize %>
                    </h3>
                    <p class="text-xs text-slate-500 mt-1">
                      <% case type %>
                      <% when 'postgresql', 'mysql' %>
                        Database connector
                      <% when 'csv', 'excel', 'json', 'xml' %>
                        File import
                      <% when 'api', 'webhook' %>
                        API integration
                      <% when 's3', 'gcs', 'azure_blob' %>
                        Cloud storage
                      <% when 'salesforce', 'stripe', 'shopify', 'quickbooks' %>
                        SaaS connector
                      <% else %>
                        Data source
                      <% end %>
                    </p>
                  </div>
                </div>
                
                <!-- Selection Indicator -->
                <div class="absolute top-3 right-3 opacity-0 peer-checked:opacity-100 transition-opacity duration-200">
                  <div class="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
                    <i class="fas fa-check text-white text-xs" aria-hidden="true"></i>
                  </div>
                </div>
              </div>
            </label>
          <% end %>
        </div>

        <!-- Features Display -->
        <div class="mt-8 p-6 bg-slate-50 rounded-2xl border border-slate-200" 
             data-enhanced-data-source-form-target="typeFeatures">
          <h4 class="font-semibold text-slate-900 mb-3 flex items-center">
            <i class="fas fa-star mr-2 text-amber-500" aria-hidden="true"></i>
            Features for Selected Type
          </h4>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-3" id="features-list">
            <!-- Dynamically populated -->
          </div>
        </div>
      </div>
    </div>

    <!-- Step 3: Connection Settings -->
    <div class="form-step hidden" data-step="3" data-enhanced-data-source-form-target="step">
      <div class="bg-white/80 backdrop-blur-sm border border-slate-200/60 rounded-2xl p-8 shadow-lg">
        <!-- Step Header -->
        <div class="flex items-center mb-8">
          <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-md">
            <i class="fas fa-cog text-white text-lg" aria-hidden="true"></i>
          </div>
          <div class="ml-4">
            <h2 class="text-2xl font-bold text-slate-900">Connection Settings</h2>
            <p class="text-slate-600">Configure the specific connection parameters</p>
          </div>
        </div>

        <!-- Dynamic Connection Fields -->
        <div id="connection-settings" data-enhanced-data-source-form-target="connectionFields">
          <% if data_source.persisted? && data_source.source_type.present? %>
            <%= render partial: "dashboard/data_sources/enhanced_connection_fields/#{data_source.source_type}", locals: { form: form, data_source: data_source } %>
          <% else %>
            <div class="text-center py-12">
              <div class="w-16 h-16 bg-slate-200 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-arrow-left text-slate-500 text-xl" aria-hidden="true"></i>
              </div>
              <p class="text-slate-600">Please select a connection type in the previous step to configure settings.</p>
            </div>
          <% end %>
        </div>

        <!-- Connection Test -->
        <div class="mt-8 p-6 bg-blue-50 rounded-2xl border border-blue-200">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                <i class="fas fa-plug text-blue-600" aria-hidden="true"></i>
              </div>
              <div class="ml-4">
                <h4 class="font-semibold text-blue-900">Test Connection</h4>
                <p class="text-sm text-blue-700">Verify your connection settings before saving</p>
              </div>
            </div>
            <button type="button" 
                    class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-semibold rounded-xl shadow-sm hover:bg-blue-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    data-action="click->enhanced-data-source-form#testConnection"
                    data-enhanced-data-source-form-target="testButton">
              <i class="fas fa-play mr-2" aria-hidden="true"></i>
              Test Connection
            </button>
          </div>
          
          <!-- Test Results -->
          <div class="mt-4 hidden" data-enhanced-data-source-form-target="testResults">
            <!-- Dynamically populated -->
          </div>
        </div>
      </div>
    </div>

    <!-- Step 4: Sync Settings -->
    <div class="form-step hidden" data-step="4" data-enhanced-data-source-form-target="step">
      <div class="bg-white/80 backdrop-blur-sm border border-slate-200/60 rounded-2xl p-8 shadow-lg">
        <!-- Step Header -->
        <div class="flex items-center mb-8">
          <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center shadow-md">
            <i class="fas fa-sync-alt text-white text-lg" aria-hidden="true"></i>
          </div>
          <div class="ml-4">
            <h2 class="text-2xl font-bold text-slate-900">Sync Settings</h2>
            <p class="text-slate-600">Configure how and when data should be synchronized</p>
          </div>
        </div>

        <div class="space-y-8">
          <!-- Sync Frequency -->
          <div class="group">
            <%= form.label :sync_frequency, class: "block text-sm font-semibold text-slate-700 mb-4" do %>
              <i class="fas fa-clock mr-2 text-primary-500" aria-hidden="true"></i>
              Sync Frequency
            <% end %>
            
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <% [
                ['manual', 'Manual Only', 'Sync when needed', 'fas fa-hand-paper'],
                ['realtime', 'Real-time', 'Instant updates', 'fas fa-bolt'],
                ['every_5_minutes', 'Every 5 minutes', 'High frequency', 'fas fa-tachometer-alt'],
                ['every_15_minutes', 'Every 15 minutes', 'Regular updates', 'fas fa-clock'],
                ['hourly', 'Hourly', 'Standard sync', 'fas fa-hourglass-half'],
                ['daily', 'Daily', 'Once per day', 'fas fa-calendar-day']
              ].each do |value, label, description, icon| %>
                <label class="group/freq relative cursor-pointer">
                  <%= form.radio_button :sync_frequency, value, 
                      class: "sr-only peer",
                      data: { action: "change->enhanced-data-source-form#updateSyncRecommendation" } %>
                  
                  <div class="p-4 border-2 rounded-xl transition-all duration-200 hover:shadow-md
                              border-slate-200 hover:border-slate-300 hover:bg-slate-50
                              peer-checked:border-primary-500 peer-checked:bg-primary-50">
                    <div class="flex items-center space-x-3">
                      <div class="w-8 h-8 bg-slate-100 group-hover/freq:bg-slate-200 peer-checked:bg-primary-100 rounded-lg flex items-center justify-center">
                        <i class="<%= icon %> text-slate-500 group-hover/freq:text-slate-600 peer-checked:text-primary-600" aria-hidden="true"></i>
                      </div>
                      <div class="flex-1">
                        <div class="font-semibold text-slate-900"><%= label %></div>
                        <div class="text-xs text-slate-500"><%= description %></div>
                      </div>
                    </div>
                    
                    <!-- Selection Indicator -->
                    <div class="absolute top-2 right-2 opacity-0 peer-checked:opacity-100 transition-opacity">
                      <div class="w-5 h-5 bg-primary-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-check text-white text-xs" aria-hidden="true"></i>
                      </div>
                    </div>
                  </div>
                </label>
              <% end %>
            </div>
            
            <!-- Sync Recommendation -->
            <div class="mt-4 p-4 bg-amber-50 rounded-xl border border-amber-200" 
                 data-enhanced-data-source-form-target="syncRecommendation">
              <div class="flex items-start">
                <i class="fas fa-lightbulb text-amber-500 mt-1 mr-3" aria-hidden="true"></i>
                <div>
                  <h4 class="font-semibold text-amber-900 mb-1">Recommendation</h4>
                  <p class="text-sm text-amber-800">Choose a frequency based on how often your data changes and your performance requirements.</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Active Toggle -->
          <div class="p-6 bg-slate-50 rounded-2xl border border-slate-200">
            <label class="flex items-center cursor-pointer group">
              <%= form.check_box :active, 
                  class: "w-5 h-5 text-primary-600 bg-white border-slate-300 rounded focus:ring-primary-500 focus:ring-2 transition-colors duration-200" %>
              <div class="ml-4">
                <div class="font-semibold text-slate-900 group-hover:text-slate-700">Enable automatic syncing</div>
                <div class="text-sm text-slate-600">When enabled, data will sync automatically based on the frequency above</div>
              </div>
            </label>
          </div>

          <!-- Advanced Options -->
          <details class="group/details">
            <summary class="flex items-center justify-between p-4 bg-slate-100 rounded-xl cursor-pointer hover:bg-slate-200 transition-colors duration-200">
              <div class="flex items-center">
                <i class="fas fa-cogs mr-3 text-slate-600" aria-hidden="true"></i>
                <span class="font-semibold text-slate-900">Advanced Sync Options</span>
              </div>
              <i class="fas fa-chevron-down text-slate-500 transition-transform duration-200 group-open/details:rotate-180" aria-hidden="true"></i>
            </summary>
            
            <div class="mt-4 p-6 bg-white rounded-xl border border-slate-200 space-y-4">
              <label class="flex items-center cursor-pointer group">
                <%= form.check_box :incremental_sync, 
                    class: "w-5 h-5 text-primary-600 bg-white border-slate-300 rounded focus:ring-primary-500 focus:ring-2" %>
                <div class="ml-4">
                  <div class="font-medium text-slate-900">Enable incremental sync</div>
                  <div class="text-sm text-slate-600">Only sync new or changed records to improve performance</div>
                </div>
              </label>
              
              <label class="flex items-center cursor-pointer group">
                <%= form.check_box :deduplicate, 
                    class: "w-5 h-5 text-primary-600 bg-white border-slate-300 rounded focus:ring-primary-500 focus:ring-2" %>
                <div class="ml-4">
                  <div class="font-medium text-slate-900">Remove duplicate records</div>
                  <div class="text-sm text-slate-600">Automatically detect and remove duplicate entries during sync</div>
                </div>
              </label>
            </div>
          </details>
        </div>
      </div>
    </div>
  </div>

  <!-- Sticky Footer with Actions -->
  <div class="bg-white/95 backdrop-blur-sm border-t border-slate-200/60 px-8 py-6">
    <div class="flex items-center justify-between">
      <!-- Step Navigation -->
      <div class="flex items-center space-x-4">
        <button type="button" 
                class="inline-flex items-center px-6 py-3 border border-slate-300 text-slate-700 bg-white rounded-xl shadow-sm hover:bg-slate-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                data-action="click->enhanced-data-source-form#previousStep"
                data-enhanced-data-source-form-target="prevButton"
                disabled>
          <i class="fas fa-chevron-left mr-2" aria-hidden="true"></i>
          Previous
        </button>
        
        <button type="button" 
                class="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-xl shadow-sm hover:bg-primary-700 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                data-action="click->enhanced-data-source-form#nextStep"
                data-enhanced-data-source-form-target="nextButton">
          Next
          <i class="fas fa-chevron-right ml-2" aria-hidden="true"></i>
        </button>
      </div>
      
      <!-- Action Buttons -->
      <div class="flex items-center space-x-4">
        <%= link_to dashboard_data_source_path(data_source), 
            class: "inline-flex items-center px-6 py-3 border border-slate-300 text-slate-700 bg-white rounded-xl shadow-sm hover:bg-slate-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2" do %>
          <i class="fas fa-times mr-2" aria-hidden="true"></i>
          Cancel
        <% end %>
        
        <%= form.submit data_source.persisted? ? "Update Data Source" : "Create Data Source", 
            class: "inline-flex items-center px-8 py-3 bg-gradient-to-r from-emerald-600 to-emerald-700 text-white font-semibold rounded-xl shadow-lg hover:from-emerald-700 hover:to-emerald-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 hover:shadow-xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed",
            data: { "enhanced-data-source-form-target": "submitButton" } do %>
          <i class="fas fa-save mr-2" aria-hidden="true"></i>
          <%= data_source.persisted? ? "Update Data Source" : "Create Data Source" %>
        <% end %>
      </div>
    </div>
  </div>
<% end %>
