<% content_for :title, "Help Center - Data Reflow Support & Resources" %>
<% content_for :description, "Find answers, tutorials, and support resources for Data Reflow. Access our knowledge base, user guides, and get help with your data analytics platform." %>
<% content_for :body_class, "help-center-page" %>

<!-- Font Awesome Icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">

<div data-controller="landing-page">
<!-- Hero Section -->
<section class="pt-24 lg:pt-32 pb-16 lg:pb-24 bg-gradient-to-br from-slate-900 via-slate-800 to-primary-900 relative overflow-hidden">
  <!-- Background Pattern -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0); background-size: 20px 20px;"></div>
  </div>
  
  <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative">
    <div class="max-w-4xl mx-auto text-center text-white">
      <!-- Badge -->
      <div class="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-8">
        <i class="fas fa-graduation-cap text-primary-400 text-sm"></i>
        <span class="text-sm font-medium">Self-Service Support</span>
      </div>

      <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight mb-6">
        Data Reflow
        <span class="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-primary-400">
          Help Center
        </span>
      </h1>

      <p class="text-xl text-gray-300 mb-8 leading-relaxed max-w-3xl mx-auto">
        Find answers, tutorials, and resources to get the most out of your Data Reflow platform. 
        From getting started guides to advanced features, we've got you covered.
      </p>

      <!-- Search Bar -->
      <div class="max-w-2xl mx-auto">
        <div class="relative">
          <input type="text" 
                 placeholder="Search for help articles, tutorials, or features..."
                 class="w-full px-6 py-4 pl-14 text-lg bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-transparent transition-all duration-200"
                 data-controller="search"
                 data-action="input->search#filter">
          <i class="fas fa-search absolute left-5 top-1/2 transform -translate-y-1/2 text-gray-300 text-lg"></i>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Quick Help Categories -->
<section class="py-16 lg:py-24 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16" data-controller="animate" data-animate-animation-value="fadeInUp">
      <h2 class="text-3xl lg:text-4xl font-bold text-neutral-800 mb-6">
        Popular Help Topics
      </h2>
      <p class="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
        Quick access to the most commonly requested help topics and resources.
      </p>
    </div>

    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
      <!-- Getting Started -->
      <div class="bg-gradient-to-br from-primary-50 to-primary-100 rounded-xl p-8 hover:shadow-lg transition-all duration-300 transform hover:scale-105" 
           data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="100">
        <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center mb-6">
          <i class="fas fa-rocket text-white text-2xl"></i>
        </div>
        <h3 class="text-xl font-bold text-neutral-800 mb-4">Getting Started</h3>
        <p class="text-neutral-600 mb-6 leading-relaxed">
          New to Data Reflow? Learn the basics and get up and running quickly with our step-by-step guides.
        </p>
        <a href="#getting-started" class="text-primary-600 hover:text-primary-700 font-semibold inline-flex items-center space-x-2">
          <span>View Guides</span>
          <i class="fas fa-arrow-right text-sm"></i>
        </a>
      </div>

      <!-- Data Sources -->
      <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-8 hover:shadow-lg transition-all duration-300 transform hover:scale-105" 
           data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="200">
        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-6">
          <i class="fas fa-database text-white text-2xl"></i>
        </div>
        <h3 class="text-xl font-bold text-neutral-800 mb-4">Data Sources</h3>
        <p class="text-neutral-600 mb-6 leading-relaxed">
          Connect your data sources, configure integrations, and troubleshoot connection issues.
        </p>
        <a href="#data-sources" class="text-blue-600 hover:text-blue-700 font-semibold inline-flex items-center space-x-2">
          <span>Learn More</span>
          <i class="fas fa-arrow-right text-sm"></i>
        </a>
      </div>

      <!-- Analytics & Reports -->
      <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-8 hover:shadow-lg transition-all duration-300 transform hover:scale-105" 
           data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="300">
        <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-6">
          <i class="fas fa-chart-line text-white text-2xl"></i>
        </div>
        <h3 class="text-xl font-bold text-neutral-800 mb-4">Analytics & Reports</h3>
        <p class="text-neutral-600 mb-6 leading-relaxed">
          Create dashboards, generate reports, and understand your analytics data.
        </p>
        <a href="#analytics" class="text-green-600 hover:text-green-700 font-semibold inline-flex items-center space-x-2">
          <span>Explore</span>
          <i class="fas fa-arrow-right text-sm"></i>
        </a>
      </div>

      <!-- Account & Billing -->
      <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-8 hover:shadow-lg transition-all duration-300 transform hover:scale-105" 
           data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="400">
        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-6">
          <i class="fas fa-user-cog text-white text-2xl"></i>
        </div>
        <h3 class="text-xl font-bold text-neutral-800 mb-4">Account & Billing</h3>
        <p class="text-neutral-600 mb-6 leading-relaxed">
          Manage your account settings, billing information, and subscription plans.
        </p>
        <a href="#account" class="text-purple-600 hover:text-purple-700 font-semibold inline-flex items-center space-x-2">
          <span>Manage</span>
          <i class="fas fa-arrow-right text-sm"></i>
        </a>
      </div>

      <!-- API & Integrations -->
      <div class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-8 hover:shadow-lg transition-all duration-300 transform hover:scale-105" 
           data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="500">
        <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-6">
          <i class="fas fa-code text-white text-2xl"></i>
        </div>
        <h3 class="text-xl font-bold text-neutral-800 mb-4">API & Integrations</h3>
        <p class="text-neutral-600 mb-6 leading-relaxed">
          Developer resources, API documentation, and third-party integrations.
        </p>
        <a href="#api" class="text-orange-600 hover:text-orange-700 font-semibold inline-flex items-center space-x-2">
          <span>Documentation</span>
          <i class="fas fa-arrow-right text-sm"></i>
        </a>
      </div>

      <!-- Troubleshooting -->
      <div class="bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-8 hover:shadow-lg transition-all duration-300 transform hover:scale-105" 
           data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="600">
        <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center mb-6">
          <i class="fas fa-tools text-white text-2xl"></i>
        </div>
        <h3 class="text-xl font-bold text-neutral-800 mb-4">Troubleshooting</h3>
        <p class="text-neutral-600 mb-6 leading-relaxed">
          Common issues, error messages, and step-by-step troubleshooting guides.
        </p>
        <a href="#troubleshooting" class="text-red-600 hover:text-red-700 font-semibold inline-flex items-center space-x-2">
          <span>Get Help</span>
          <i class="fas fa-arrow-right text-sm"></i>
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Knowledge Base Articles -->
<section class="py-16 lg:py-24 bg-neutral-bg">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16" data-controller="animate" data-animate-animation-value="fadeInUp">
      <h2 class="text-3xl lg:text-4xl font-bold text-neutral-800 mb-6">
        Knowledge Base
      </h2>
      <p class="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
        Comprehensive articles and guides to help you master Data Reflow.
      </p>
    </div>

    <div class="max-w-6xl mx-auto">
      <div class="grid lg:grid-cols-3 gap-8">
        <!-- Featured Articles -->
        <div class="lg:col-span-2">
          <h3 class="text-2xl font-bold text-neutral-800 mb-8">Featured Articles</h3>
          <div class="space-y-6">
            <!-- Article 1 -->
            <article class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
              <div class="flex items-start space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center flex-shrink-0">
                  <i class="fas fa-play text-white"></i>
                </div>
                <div class="flex-1">
                  <h4 class="text-lg font-semibold text-neutral-800 mb-2">
                    <a href="#" class="hover:text-primary-600 transition-colors">
                      Getting Started with Data Reflow: Complete Setup Guide
                    </a>
                  </h4>
                  <p class="text-neutral-600 mb-3 leading-relaxed">
                    Learn how to set up your Data Reflow account, connect your first data source, and create your initial dashboard in under 30 minutes.
                  </p>
                  <div class="flex items-center space-x-4 text-sm text-neutral-500">
                    <span><i class="fas fa-clock mr-1"></i>15 min read</span>
                    <span><i class="fas fa-eye mr-1"></i>2.3k views</span>
                    <span class="text-green-600"><i class="fas fa-check-circle mr-1"></i>Beginner</span>
                  </div>
                </div>
              </div>
            </article>

            <!-- Article 2 -->
            <article class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
              <div class="flex items-start space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                  <i class="fas fa-database text-white"></i>
                </div>
                <div class="flex-1">
                  <h4 class="text-lg font-semibold text-neutral-800 mb-2">
                    <a href="#" class="hover:text-primary-600 transition-colors">
                      Connecting Multiple Data Sources: Best Practices
                    </a>
                  </h4>
                  <p class="text-neutral-600 mb-3 leading-relaxed">
                    Master the art of connecting and managing multiple data sources including databases, APIs, and cloud platforms.
                  </p>
                  <div class="flex items-center space-x-4 text-sm text-neutral-500">
                    <span><i class="fas fa-clock mr-1"></i>12 min read</span>
                    <span><i class="fas fa-eye mr-1"></i>1.8k views</span>
                    <span class="text-yellow-600"><i class="fas fa-star mr-1"></i>Intermediate</span>
                  </div>
                </div>
              </div>
            </article>

            <!-- Article 3 -->
            <article class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
              <div class="flex items-start space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center flex-shrink-0">
                  <i class="fas fa-chart-bar text-white"></i>
                </div>
                <div class="flex-1">
                  <h4 class="text-lg font-semibold text-neutral-800 mb-2">
                    <a href="#" class="hover:text-primary-600 transition-colors">
                      Creating Custom Dashboards and Reports
                    </a>
                  </h4>
                  <p class="text-neutral-600 mb-3 leading-relaxed">
                    Build powerful, interactive dashboards that tell your data story and drive business decisions.
                  </p>
                  <div class="flex items-center space-x-4 text-sm text-neutral-500">
                    <span><i class="fas fa-clock mr-1"></i>20 min read</span>
                    <span><i class="fas fa-eye mr-1"></i>3.1k views</span>
                    <span class="text-yellow-600"><i class="fas fa-star mr-1"></i>Intermediate</span>
                  </div>
                </div>
              </div>
            </article>

            <!-- Article 4 -->
            <article class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200">
              <div class="flex items-start space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                  <i class="fas fa-brain text-white"></i>
                </div>
                <div class="flex-1">
                  <h4 class="text-lg font-semibold text-neutral-800 mb-2">
                    <a href="#" class="hover:text-primary-600 transition-colors">
                      AI-Powered Analytics: Understanding Predictions
                    </a>
                  </h4>
                  <p class="text-neutral-600 mb-3 leading-relaxed">
                    Leverage machine learning insights and predictive analytics to forecast trends and identify opportunities.
                  </p>
                  <div class="flex items-center space-x-4 text-sm text-neutral-500">
                    <span><i class="fas fa-clock mr-1"></i>18 min read</span>
                    <span><i class="fas fa-eye mr-1"></i>1.5k views</span>
                    <span class="text-red-600"><i class="fas fa-graduation-cap mr-1"></i>Advanced</span>
                  </div>
                </div>
              </div>
            </article>
          </div>

          <!-- View All Articles Button -->
          <div class="text-center mt-12">
            <a href="#" class="bg-primary-600 text-white px-8 py-3 rounded-lg hover:bg-primary-700 transition-all duration-200 font-semibold inline-flex items-center space-x-2">
              <span>View All Articles</span>
              <i class="fas fa-arrow-right text-sm"></i>
            </a>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-8">
          <!-- Quick Links -->
          <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h3 class="text-lg font-bold text-neutral-800 mb-4">Quick Links</h3>
            <ul class="space-y-3">
              <li>
                <a href="#" class="text-neutral-600 hover:text-primary-600 transition-colors flex items-center space-x-2">
                  <i class="fas fa-video text-sm"></i>
                  <span>Video Tutorials</span>
                </a>
              </li>
              <li>
                <a href="#" class="text-neutral-600 hover:text-primary-600 transition-colors flex items-center space-x-2">
                  <i class="fas fa-download text-sm"></i>
                  <span>User Manual (PDF)</span>
                </a>
              </li>
              <li>
                <a href="#" class="text-neutral-600 hover:text-primary-600 transition-colors flex items-center space-x-2">
                  <i class="fas fa-code text-sm"></i>
                  <span>API Documentation</span>
                </a>
              </li>
              <li>
                <a href="#" class="text-neutral-600 hover:text-primary-600 transition-colors flex items-center space-x-2">
                  <i class="fas fa-comments text-sm"></i>
                  <span>Community Forum</span>
                </a>
              </li>
              <li>
                <a href="#" class="text-neutral-600 hover:text-primary-600 transition-colors flex items-center space-x-2">
                  <i class="fas fa-calendar text-sm"></i>
                  <span>Webinar Schedule</span>
                </a>
              </li>
            </ul>
          </div>

          <!-- Popular Downloads -->
          <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
            <h3 class="text-lg font-bold text-neutral-800 mb-4">Popular Downloads</h3>
            <div class="space-y-4">
              <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                  <i class="fas fa-file-pdf text-red-600"></i>
                </div>
                <div class="flex-1">
                  <h4 class="font-medium text-neutral-800 text-sm">Quick Start Guide</h4>
                  <p class="text-xs text-neutral-500">2.3 MB PDF</p>
                </div>
                <a href="#" class="text-primary-600 hover:text-primary-700">
                  <i class="fas fa-download"></i>
                </a>
              </div>

              <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <i class="fas fa-file-excel text-blue-600"></i>
                </div>
                <div class="flex-1">
                  <h4 class="font-medium text-neutral-800 text-sm">Sample Data Templates</h4>
                  <p class="text-xs text-neutral-500">1.8 MB XLSX</p>
                </div>
                <a href="#" class="text-primary-600 hover:text-primary-700">
                  <i class="fas fa-download"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- User Guides & Tutorials -->
<section class="py-16 lg:py-24 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16" data-controller="animate" data-animate-animation-value="fadeInUp">
      <h2 class="text-3xl lg:text-4xl font-bold text-neutral-800 mb-6">
        User Guides & Tutorials
      </h2>
      <p class="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
        Step-by-step tutorials and comprehensive guides for every skill level.
      </p>
    </div>

    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
      <!-- Video Tutorials -->
      <div class="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300"
           data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="100">
        <div class="aspect-video bg-gradient-to-br from-primary-500 to-primary-600 flex items-center justify-center">
          <div class="text-center text-white">
            <i class="fas fa-play-circle text-6xl mb-4"></i>
            <p class="text-lg font-semibold">Video Tutorials</p>
          </div>
        </div>
        <div class="p-6">
          <h3 class="text-xl font-bold text-neutral-800 mb-3">Interactive Video Guides</h3>
          <p class="text-neutral-600 mb-4 leading-relaxed">
            Watch step-by-step video tutorials covering everything from basic setup to advanced analytics.
          </p>
          <a href="#" class="text-primary-600 hover:text-primary-700 font-semibold inline-flex items-center space-x-2">
            <span>Watch Now</span>
            <i class="fas fa-external-link-alt text-sm"></i>
          </a>
        </div>
      </div>

      <!-- Interactive Demos -->
      <div class="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300"
           data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="200">
        <div class="aspect-video bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
          <div class="text-center text-white">
            <i class="fas fa-mouse-pointer text-6xl mb-4"></i>
            <p class="text-lg font-semibold">Interactive Demos</p>
          </div>
        </div>
        <div class="p-6">
          <h3 class="text-xl font-bold text-neutral-800 mb-3">Hands-On Practice</h3>
          <p class="text-neutral-600 mb-4 leading-relaxed">
            Try Data Reflow features in our interactive sandbox environment with sample data.
          </p>
          <a href="#" class="text-blue-600 hover:text-blue-700 font-semibold inline-flex items-center space-x-2">
            <span>Try Demo</span>
            <i class="fas fa-external-link-alt text-sm"></i>
          </a>
        </div>
      </div>

      <!-- Webinars -->
      <div class="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300"
           data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="300">
        <div class="aspect-video bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center">
          <div class="text-center text-white">
            <i class="fas fa-users text-6xl mb-4"></i>
            <p class="text-lg font-semibold">Live Webinars</p>
          </div>
        </div>
        <div class="p-6">
          <h3 class="text-xl font-bold text-neutral-800 mb-3">Expert-Led Sessions</h3>
          <p class="text-neutral-600 mb-4 leading-relaxed">
            Join live training sessions and Q&A with our data analytics experts.
          </p>
          <a href="#" class="text-green-600 hover:text-green-700 font-semibold inline-flex items-center space-x-2">
            <span>View Schedule</span>
            <i class="fas fa-external-link-alt text-sm"></i>
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Contact Support -->
<section class="py-16 lg:py-24 bg-neutral-bg">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-16" data-controller="animate" data-animate-animation-value="fadeInUp">
      <h2 class="text-3xl lg:text-4xl font-bold text-neutral-800 mb-6">
        Still Need Help?
      </h2>
      <p class="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
        Our support team is here to help you succeed with Data Reflow.
      </p>
    </div>

    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
      <!-- Live Chat -->
      <div class="text-center" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="100">
        <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-comments text-white text-2xl"></i>
        </div>
        <h3 class="text-lg font-bold text-neutral-800 mb-2">Live Chat</h3>
        <p class="text-neutral-600 mb-4">Instant help available</p>
        <button class="text-primary-600 hover:text-primary-700 font-medium"
                data-action="click->landing#openChat">
          Start Chat
        </button>
      </div>

      <!-- Email Support -->
      <div class="text-center" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="200">
        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-envelope text-white text-2xl"></i>
        </div>
        <h3 class="text-lg font-bold text-neutral-800 mb-2">Email Support</h3>
        <p class="text-neutral-600 mb-4">Response within 4 hours</p>
        <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-700 font-medium">
          Send Email
        </a>
      </div>

      <!-- Phone Support -->
      <div class="text-center" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="300">
        <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-phone text-white text-2xl"></i>
        </div>
        <h3 class="text-lg font-bold text-neutral-800 mb-2">Phone Support</h3>
        <p class="text-neutral-600 mb-4">Mon-Fri, 9AM-6PM EST</p>
        <a href="tel:******-DATA-FLOW" class="text-green-600 hover:text-green-700 font-medium">
          Call Now
        </a>
      </div>

      <!-- Schedule Call -->
      <div class="text-center" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="400">
        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-6">
          <i class="fas fa-calendar-alt text-white text-2xl"></i>
        </div>
        <h3 class="text-lg font-bold text-neutral-800 mb-2">Schedule Call</h3>
        <p class="text-neutral-600 mb-4">Book a consultation</p>
        <a href="#" class="text-purple-600 hover:text-purple-700 font-medium">
          Book Now
        </a>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-16 lg:py-24 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="max-w-4xl mx-auto">
      <div class="text-center mb-16" data-controller="animate" data-animate-animation-value="fadeInUp">
        <h2 class="text-3xl lg:text-4xl font-bold text-neutral-800 mb-6">
          Frequently Asked Questions
        </h2>
        <p class="text-xl text-neutral-600 leading-relaxed">
          Quick answers to common questions about using Data Reflow.
        </p>
      </div>

      <div class="space-y-6" data-controller="faq" data-faq-accordion-value="true">
        <!-- FAQ Item 1 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100">
          <button class="w-full px-6 py-4 text-left flex items-center justify-between focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-xl"
                  data-action="click->faq#toggle keydown->faq#keydown"
                  data-faq-target="trigger"
                  type="button"
                  aria-expanded="false">
            <span class="text-lg font-semibold text-neutral-800">How do I connect my first data source?</span>
            <i class="fas fa-chevron-down text-neutral-400 transition-transform duration-200" data-faq-target="icon"></i>
          </button>
          <div class="px-6 pb-4" data-faq-target="content">
            <p class="text-neutral-600 leading-relaxed">
              Navigate to the Data Sources section in your dashboard, click "Add New Source," select your data type (database, API, file),
              and follow the connection wizard. Most sources can be connected in under 5 minutes.
            </p>
          </div>
        </div>

        <!-- FAQ Item 2 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100">
          <button class="w-full px-6 py-4 text-left flex items-center justify-between focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-xl"
                  data-action="click->faq#toggle keydown->faq#keydown"
                  data-faq-target="trigger"
                  type="button"
                  aria-expanded="false">
            <span class="text-lg font-semibold text-neutral-800">Can I customize my dashboard layout?</span>
            <i class="fas fa-chevron-down text-neutral-400 transition-transform duration-200" data-faq-target="icon"></i>
          </button>
          <div class="px-6 pb-4" data-faq-target="content">
            <p class="text-neutral-600 leading-relaxed">
              Yes! You can drag and drop widgets, resize charts, add custom visualizations, and create multiple dashboard views.
              Use the dashboard editor to customize layouts for different teams or use cases.
            </p>
          </div>
        </div>

        <!-- FAQ Item 3 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100">
          <button class="w-full px-6 py-4 text-left flex items-center justify-between focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-xl"
                  data-action="click->faq#toggle keydown->faq#keydown"
                  data-faq-target="trigger"
                  type="button"
                  aria-expanded="false">
            <span class="text-lg font-semibold text-neutral-800">How do I share reports with my team?</span>
            <i class="fas fa-chevron-down text-neutral-400 transition-transform duration-200" data-faq-target="icon"></i>
          </button>
          <div class="px-6 pb-4" data-faq-target="content">
            <p class="text-neutral-600 leading-relaxed">
              Click the "Share" button on any report or dashboard. You can generate public links, schedule email reports,
              or invite team members with specific permissions. Reports can be exported as PDF, Excel, or embedded in other applications.
            </p>
          </div>
        </div>

        <!-- FAQ Item 4 -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100">
          <button class="w-full px-6 py-4 text-left flex items-center justify-between focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-xl"
                  data-action="click->faq#toggle keydown->faq#keydown"
                  data-faq-target="trigger"
                  type="button"
                  aria-expanded="false">
            <span class="text-lg font-semibold text-neutral-800">What if I need help with data modeling?</span>
            <i class="fas fa-chevron-down text-neutral-400 transition-transform duration-200" data-faq-target="icon"></i>
          </button>
          <div class="px-6 pb-4" data-faq-target="content">
            <p class="text-neutral-600 leading-relaxed">
              Our Customer Success team offers free data modeling consultations for all paid plans.
              You can also access our data modeling templates, join our weekly office hours, or book a 1-on-1 session.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Final CTA Section -->
<section class="py-16 lg:py-24 bg-gradient-to-br from-slate-900 via-slate-800 to-primary-900 relative overflow-hidden">
  <!-- Background Pattern -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0); background-size: 20px 20px;"></div>
  </div>

  <div class="container mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
    <div data-controller="animate" data-animate-animation-value="fadeInUp">
      <h2 class="text-3xl lg:text-4xl font-bold text-white mb-6">
        Ready to Get Started?
      </h2>
      <p class="text-xl text-primary-100 mb-8 max-w-3xl mx-auto leading-relaxed">
        Have questions? Our team is here to help you succeed with Data Reflow.
        Get personalized support and start transforming your data today.
      </p>

      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="/contact"
           class="bg-primary-600 text-white px-8 py-4 rounded-lg hover:bg-primary-700 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 inline-flex items-center justify-center space-x-2">
          <i class="fas fa-headset text-sm"></i>
          <span>Contact Support</span>
        </a>
        <a href="#demo"
           class="border-2 border-white text-white px-8 py-4 rounded-lg hover:bg-white hover:text-neutral-800 transition-all duration-200 font-semibold inline-flex items-center justify-center space-x-2"
           data-action="click->landing#openDemoModal">
          <i class="fas fa-play text-sm"></i>
          <span>Watch Demo</span>
        </a>
      </div>
    </div>
  </div>
</section>
</div>
