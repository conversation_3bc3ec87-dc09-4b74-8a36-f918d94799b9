<% content_for :title, "Data Reflow - Enterprise Analytics Platform for SMEs" %>
<% content_for :description, "Transform raw data into actionable insights with our enterprise-grade analytics platform. Reduce costs by 30%, increase efficiency by 40%, and achieve ROI within 90 days." %>
<% content_for :body_class, "landing-page" %>

<!-- Font Awesome Icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">

<div data-controller="landing-page">
<!-- Hero Section -->
<section class="pt-24 lg:pt-32 pb-16 lg:pb-24 bg-gradient-to-br from-slate-900 via-slate-800 to-primary-900 relative overflow-hidden">
  <!-- Background Pattern -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0); background-size: 20px 20px;"></div>
  </div>

  <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative">
    <div class="grid lg:grid-cols-2 gap-12 items-center">
      <!-- Hero Content -->
      <div class="text-white" data-controller="animate" data-animate-animation-value="fadeInUp">
        <!-- Trust Indicators -->
        <div class="flex items-center space-x-6 mb-6">
          <div class="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-3 py-1">
            <i class="fas fa-shield-check text-green-400 text-sm"></i>
            <span class="text-sm font-medium">SOC 2 Compliant</span>
          </div>
          <div class="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-3 py-1">
            <i class="fas fa-lock text-blue-400 text-sm"></i>
            <span class="text-sm font-medium">GDPR Ready</span>
          </div>
          <div class="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-3 py-1">
            <i class="fas fa-clock text-yellow-400 text-sm"></i>
            <span class="text-sm font-medium">99.9% Uptime</span>
          </div>
        </div>

        <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight mb-6">
          Transform Raw Data into
          <span class="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-primary-400">
            Actionable Insights
          </span>
        </h1>

        <p class="text-xl text-gray-300 mb-8 leading-relaxed max-w-2xl">
          Enterprise-grade data analytics platform designed for SMEs.
          Reduce costs by 30%, increase efficiency by 40%, and achieve ROI within 90 days.
        </p>

        <!-- Key Benefits -->
        <div class="grid sm:grid-cols-3 gap-4 mb-8">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center">
              <i class="fas fa-chart-line text-green-400 text-sm"></i>
            </div>
            <span class="text-sm font-medium">700+ Hours Saved Annually</span>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <i class="fas fa-brain text-blue-400 text-sm"></i>
            </div>
            <span class="text-sm font-medium">89% AI Accuracy</span>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center">
              <i class="fas fa-rocket text-purple-400 text-sm"></i>
            </div>
            <span class="text-sm font-medium">90-Day ROI</span>
          </div>
        </div>

        <!-- CTA Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 mb-8">
          <%= link_to new_user_registration_path,
              class: "bg-primary-600 text-white px-8 py-4 rounded-lg hover:bg-primary-700 transition-all duration-200 font-semibold text-lg shadow-xl hover:shadow-2xl transform hover:scale-105 flex items-center justify-center space-x-2" do %>
            <span>Start Free Trial</span>
            <i class="fas fa-arrow-right text-sm"></i>
          <% end %>
          <a href="#demo"
             class="bg-white/10 backdrop-blur-sm text-white border-2 border-white/20 px-8 py-4 rounded-lg hover:bg-white/20 transition-all duration-200 font-semibold text-lg flex items-center justify-center space-x-2"
             data-action="click->landing#openDemoModal">
            <i class="fas fa-play text-sm"></i>
            <span>Watch Demo</span>
          </a>
        </div>

        <!-- Social Proof -->
        <p class="text-sm text-gray-400">
          Trusted by 2,500+ businesses worldwide • No credit card required • 14-day free trial
        </p>
      </div>

      <!-- Hero Visual - Dashboard Mockup -->
      <div class="relative" data-controller="animate" data-animate-animation-value="fadeInRight" data-animate-delay-value="300">
        <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 shadow-2xl border border-white/20">
          <!-- Dashboard Header -->
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center space-x-3">
              <div class="w-3 h-3 bg-red-400 rounded-full"></div>
              <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
              <div class="w-3 h-3 bg-green-400 rounded-full"></div>
            </div>
            <div class="text-white/60 text-sm font-medium">Data Reflow Dashboard</div>
          </div>

          <!-- Dashboard Content -->
          <div class="space-y-4">
            <!-- Metrics Row -->
            <div class="grid grid-cols-3 gap-4">
              <div class="bg-white/5 rounded-lg p-3">
                <div class="text-white/60 text-xs mb-1">Revenue</div>
                <div class="text-white font-bold text-lg" data-controller="counter" data-counter-target-value="847000" data-counter-prefix-value="$">$0</div>
                <div class="text-green-400 text-xs">↗ 23.5%</div>
              </div>
              <div class="bg-white/5 rounded-lg p-3">
                <div class="text-white/60 text-xs mb-1">Efficiency</div>
                <div class="text-white font-bold text-lg" data-controller="counter" data-counter-target-value="94" data-counter-suffix-value="%">0%</div>
                <div class="text-green-400 text-xs">↗ 12.3%</div>
              </div>
              <div class="bg-white/5 rounded-lg p-3">
                <div class="text-white/60 text-xs mb-1">Cost Savings</div>
                <div class="text-white font-bold text-lg" data-controller="counter" data-counter-target-value="156000" data-counter-prefix-value="$">$0</div>
                <div class="text-green-400 text-xs">↗ 31.2%</div>
              </div>
            </div>

            <!-- Chart Placeholder -->
            <div class="bg-white/5 rounded-lg p-4 h-32 flex items-end justify-between space-x-2">
              <div class="bg-primary-500 rounded-t w-4 h-16"></div>
              <div class="bg-primary-500 rounded-t w-4 h-20"></div>
              <div class="bg-primary-500 rounded-t w-4 h-12"></div>
              <div class="bg-primary-500 rounded-t w-4 h-24"></div>
              <div class="bg-primary-500 rounded-t w-4 h-28"></div>
              <div class="bg-primary-500 rounded-t w-4 h-18"></div>
              <div class="bg-primary-500 rounded-t w-4 h-22"></div>
              <div class="bg-primary-500 rounded-t w-4 h-26"></div>
            </div>
          </div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute -top-4 -right-4 bg-green-500 text-white p-3 rounded-lg shadow-lg animate-bounce">
          <i class="fas fa-check text-sm"></i>
        </div>
        <div class="absolute -bottom-4 -left-4 bg-blue-500 text-white p-3 rounded-lg shadow-lg" style="animation: float 3s ease-in-out infinite;">
          <i class="fas fa-chart-bar text-sm"></i>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Social Proof Bar -->
<section class="py-12 bg-white border-b border-gray-200">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-8" data-controller="animate" data-animate-animation-value="fadeInUp">
      <p class="text-sm font-medium text-neutral-600 mb-6">Trusted by industry leaders worldwide</p>

      <!-- Customer Logos -->
      <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center opacity-60">
        <div class="flex items-center justify-center h-12">
          <div class="bg-gray-200 rounded-lg px-6 py-3 text-gray-600 font-semibold text-sm">TechCorp</div>
        </div>
        <div class="flex items-center justify-center h-12">
          <div class="bg-gray-200 rounded-lg px-6 py-3 text-gray-600 font-semibold text-sm">DataFlow Inc</div>
        </div>
        <div class="flex items-center justify-center h-12">
          <div class="bg-gray-200 rounded-lg px-6 py-3 text-gray-600 font-semibold text-sm">Analytics Pro</div>
        </div>
        <div class="flex items-center justify-center h-12">
          <div class="bg-gray-200 rounded-lg px-6 py-3 text-gray-600 font-semibold text-sm">SmartData</div>
        </div>
        <div class="flex items-center justify-center h-12">
          <div class="bg-gray-200 rounded-lg px-6 py-3 text-gray-600 font-semibold text-sm">InsightCo</div>
        </div>
        <div class="flex items-center justify-center h-12">
          <div class="bg-gray-200 rounded-lg px-6 py-3 text-gray-600 font-semibold text-sm">MetricMax</div>
        </div>
      </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="200">
      <div>
        <div class="text-3xl lg:text-4xl font-bold text-primary-600 mb-2" data-controller="counter" data-counter-target-value="2500" data-counter-suffix-value="+">0+</div>
        <div class="text-sm font-medium text-neutral-600">Active Customers</div>
      </div>
      <div>
        <div class="text-3xl lg:text-4xl font-bold text-primary-600 mb-2" data-controller="counter" data-counter-target-value="99.9" data-counter-suffix-value="%" data-counter-decimals-value="1">0%</div>
        <div class="text-sm font-medium text-neutral-600">Uptime SLA</div>
      </div>
      <div>
        <div class="text-3xl lg:text-4xl font-bold text-primary-600 mb-2" data-controller="counter" data-counter-target-value="50" data-counter-suffix-value="M+">0M+</div>
        <div class="text-sm font-medium text-neutral-600">Data Points Processed</div>
      </div>
      <div>
        <div class="text-3xl lg:text-4xl font-bold text-primary-600 mb-2" data-controller="counter" data-counter-target-value="90" data-counter-suffix-value=" days">0 days</div>
        <div class="text-sm font-medium text-neutral-600">Average ROI Timeline</div>
      </div>
    </div>
  </div>
</section>

<!-- Features Section -->
<section id="features" class="py-16 lg:py-24 bg-neutral-bg">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section Header -->
    <div class="text-center mb-16" data-controller="animate" data-animate-animation-value="fadeInUp">
      <h2 class="text-3xl lg:text-4xl font-bold text-neutral-800 mb-6">
        Everything You Need to Transform Your Data
      </h2>
      <p class="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
        Our comprehensive platform provides all the tools and capabilities your business needs to turn raw data into competitive advantages.
      </p>
    </div>

    <!-- Features Grid -->
    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
      <% @features.each_with_index do |feature, index| %>
        <div class="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 group"
             data-controller="animate"
             data-animate-animation-value="fadeInUp"
             data-animate-delay-value="<%= index * 100 %>">

          <!-- Feature Icon -->
          <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
            <% case feature[:icon] %>
            <% when "pipeline" %>
              <i class="fas fa-project-diagram text-white text-xl"></i>
            <% when "ai-brain" %>
              <i class="fas fa-brain text-white text-xl"></i>
            <% when "templates" %>
              <i class="fas fa-layer-group text-white text-xl"></i>
            <% when "security" %>
              <i class="fas fa-shield-alt text-white text-xl"></i>
            <% when "api" %>
              <i class="fas fa-code text-white text-xl"></i>
            <% when "support" %>
              <i class="fas fa-headset text-white text-xl"></i>
            <% when "scalability" %>
              <i class="fas fa-expand-arrows-alt text-white text-xl"></i>
            <% when "integration" %>
              <i class="fas fa-plug text-white text-xl"></i>
            <% else %>
              <i class="fas fa-chart-line text-white text-xl"></i>
            <% end %>
          </div>

          <!-- Feature Content -->
          <h3 class="text-xl font-bold text-neutral-800 mb-4 group-hover:text-primary-600 transition-colors duration-300">
            <%= feature[:title] %>
          </h3>

          <p class="text-neutral-600 mb-4 leading-relaxed">
            <%= feature[:description] %>
          </p>

          <!-- Benefit Badge -->
          <div class="bg-primary-50 text-primary-700 px-4 py-2 rounded-lg text-sm font-medium inline-flex items-center space-x-2">
            <i class="fas fa-check-circle text-primary-600"></i>
            <span><%= feature[:benefit] %></span>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Additional Features Row -->
    <div class="grid md:grid-cols-2 gap-8 mt-12">
      <!-- Real-time Monitoring -->
      <div class="bg-white rounded-xl p-8 shadow-lg border border-gray-100" data-controller="animate" data-animate-animation-value="fadeInLeft">
        <div class="flex items-start space-x-6">
          <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center flex-shrink-0">
            <i class="fas fa-chart-line text-white text-xl"></i>
          </div>
          <div>
            <h3 class="text-xl font-bold text-neutral-800 mb-3">Real-time Monitoring & Alerts</h3>
            <p class="text-neutral-600 mb-4">Monitor your data pipelines 24/7 with intelligent alerting and automated issue resolution.</p>
            <ul class="space-y-2 text-sm text-neutral-600">
              <li class="flex items-center space-x-2">
                <i class="fas fa-check text-green-500 text-xs"></i>
                <span>Instant anomaly detection</span>
              </li>
              <li class="flex items-center space-x-2">
                <i class="fas fa-check text-green-500 text-xs"></i>
                <span>Custom alert thresholds</span>
              </li>
              <li class="flex items-center space-x-2">
                <i class="fas fa-check text-green-500 text-xs"></i>
                <span>Multi-channel notifications</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Collaboration Tools -->
      <div class="bg-white rounded-xl p-8 shadow-lg border border-gray-100" data-controller="animate" data-animate-animation-value="fadeInRight">
        <div class="flex items-start space-x-6">
          <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0">
            <i class="fas fa-users text-white text-xl"></i>
          </div>
          <div>
            <h3 class="text-xl font-bold text-neutral-800 mb-3">Team Collaboration & Governance</h3>
            <p class="text-neutral-600 mb-4">Enable seamless collaboration with role-based access controls and audit trails.</p>
            <ul class="space-y-2 text-sm text-neutral-600">
              <li class="flex items-center space-x-2">
                <i class="fas fa-check text-green-500 text-xs"></i>
                <span>Role-based permissions</span>
              </li>
              <li class="flex items-center space-x-2">
                <i class="fas fa-check text-green-500 text-xs"></i>
                <span>Complete audit trails</span>
              </li>
              <li class="flex items-center space-x-2">
                <i class="fas fa-check text-green-500 text-xs"></i>
                <span>Shared workspaces</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Industry Solutions Section -->
<section id="solutions" class="py-16 lg:py-24 bg-gradient-to-br from-neutral-50 to-white relative overflow-hidden">
  <!-- Background Pattern -->
  <div class="absolute inset-0 opacity-5">
    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 2px 2px, rgba(33, 128, 141, 0.3) 1px, transparent 0); background-size: 40px 40px;"></div>
  </div>

  <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative">
    <!-- Section Header -->
    <div class="text-center mb-20" data-controller="animate" data-animate-animation-value="fadeInUp">
      <div class="inline-flex items-center space-x-2 bg-primary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
        <i class="fas fa-industry text-primary-600"></i>
        <span>Industry Expertise</span>
      </div>
      <h2 class="text-3xl lg:text-5xl font-bold text-neutral-800 mb-6">
        Tailored Solutions for
        <span class="text-transparent bg-clip-text bg-gradient-to-r from-primary-600 to-primary-700">
          Every Industry
        </span>
      </h2>
      <p class="text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed">
        Pre-configured templates, industry-specific workflows, and compliance-ready solutions designed to address your sector's unique data challenges and regulatory requirements.
      </p>
    </div>

    <!-- Featured Industry Showcase -->
    <div class="mb-20" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="200">
      <div class="bg-white rounded-2xl shadow-2xl border border-gray-100 overflow-hidden">
        <div class="grid lg:grid-cols-2 gap-0">
          <!-- Content Side -->
          <div class="p-8 lg:p-12">
            <div class="flex items-center space-x-3 mb-6">
              <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center shadow-lg">
                <i class="fas fa-shopping-cart text-white text-2xl"></i>
              </div>
              <div>
                <h3 class="text-2xl font-bold text-neutral-800">Retail & E-commerce</h3>
                <p class="text-primary-600 font-medium">Most Popular Solution</p>
              </div>
            </div>

            <p class="text-lg text-neutral-600 mb-8 leading-relaxed">
              Transform your retail operations with AI-powered insights that drive customer engagement, optimize inventory, and boost profitability across all channels.
            </p>

            <!-- Key Metrics -->
            <div class="grid grid-cols-3 gap-6 mb-8">
              <div class="text-center">
                <div class="text-3xl font-bold text-primary-600 mb-2">340%</div>
                <div class="text-sm text-neutral-600">Average ROI</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold text-primary-600 mb-2">25%</div>
                <div class="text-sm text-neutral-600">Sales Increase</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold text-primary-600 mb-2">15%</div>
                <div class="text-sm text-neutral-600">Cost Reduction</div>
              </div>
            </div>

            <!-- Use Cases -->
            <div class="space-y-3 mb-8">
              <div class="flex items-center space-x-3">
                <div class="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                  <i class="fas fa-check text-primary-600 text-xs"></i>
                </div>
                <span class="text-neutral-700">Customer journey mapping & personalization</span>
              </div>
              <div class="flex items-center space-x-3">
                <div class="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                  <i class="fas fa-check text-primary-600 text-xs"></i>
                </div>
                <span class="text-neutral-700">Dynamic pricing & inventory optimization</span>
              </div>
              <div class="flex items-center space-x-3">
                <div class="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                  <i class="fas fa-check text-primary-600 text-xs"></i>
                </div>
                <span class="text-neutral-700">Demand forecasting & supply chain analytics</span>
              </div>
            </div>

            <!-- Customer Logo -->
            <div class="flex items-center space-x-4 mb-8 p-4 bg-gray-50 rounded-lg">
              <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                <span class="text-gray-600 font-bold text-sm">TC</span>
              </div>
              <div>
                <div class="font-semibold text-neutral-800">Thompson Retail Group</div>
                <div class="text-sm text-neutral-600">"20% sales increase in 6 months"</div>
              </div>
            </div>

            <!-- CTAs -->
            <div class="flex flex-col sm:flex-row gap-4">
              <a href="#demo"
                 class="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-all duration-200 font-semibold inline-flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105"
                 data-action="click->landing#openDemoModal"
                 data-industry="retail">
                <i class="fas fa-calendar text-sm"></i>
                <span>Schedule Retail Demo</span>
              </a>
              <button class="border-2 border-primary-600 text-primary-600 px-6 py-3 rounded-lg hover:bg-primary-50 transition-all duration-200 font-semibold inline-flex items-center justify-center space-x-2"
                      data-controller="roi-calculator"
                      data-action="click->roi-calculator#toggle"
                      data-industry="retail">
                <i class="fas fa-calculator text-sm"></i>
                <span>Calculate ROI</span>
              </button>
            </div>
          </div>

          <!-- Visual Side -->
          <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-8 lg:p-12 flex items-center justify-center">
            <div class="w-full max-w-md">
              <!-- Dashboard Mockup -->
              <div class="bg-white rounded-xl shadow-lg p-6 border border-blue-200">
                <div class="flex items-center justify-between mb-4">
                  <h4 class="font-semibold text-neutral-800">Retail Analytics</h4>
                  <div class="flex space-x-1">
                    <div class="w-2 h-2 bg-red-400 rounded-full"></div>
                    <div class="w-2 h-2 bg-yellow-400 rounded-full"></div>
                    <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                  </div>
                </div>

                <!-- Chart -->
                <div class="h-32 bg-gradient-to-r from-blue-100 to-blue-200 rounded-lg mb-4 flex items-end justify-between p-4">
                  <div class="bg-blue-500 rounded-t w-6 h-16"></div>
                  <div class="bg-blue-500 rounded-t w-6 h-20"></div>
                  <div class="bg-blue-500 rounded-t w-6 h-12"></div>
                  <div class="bg-blue-500 rounded-t w-6 h-24"></div>
                  <div class="bg-blue-500 rounded-t w-6 h-28"></div>
                  <div class="bg-blue-500 rounded-t w-6 h-18"></div>
                </div>

                <!-- Metrics -->
                <div class="grid grid-cols-2 gap-4 text-center">
                  <div class="bg-blue-50 rounded-lg p-3">
                    <div class="text-lg font-bold text-blue-600">£2.4M</div>
                    <div class="text-xs text-neutral-600">Revenue</div>
                  </div>
                  <div class="bg-green-50 rounded-lg p-3">
                    <div class="text-lg font-bold text-green-600">↗ 23%</div>
                    <div class="text-xs text-neutral-600">Growth</div>
                  </div>
                </div>
              </div>

              <!-- Floating Elements -->
              <div class="absolute top-4 right-4 bg-green-500 text-white p-2 rounded-lg shadow-lg animate-bounce">
                <i class="fas fa-arrow-up text-sm"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Industry Solutions Grid -->
    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
      <!-- Manufacturing -->
      <div class="group bg-white rounded-xl p-8 shadow-lg border border-gray-100 hover:shadow-2xl hover:border-primary-200 transition-all duration-300 transform hover:-translate-y-2"
           data-controller="animate"
           data-animate-animation-value="fadeInUp"
           data-animate-delay-value="100"
           role="article"
           aria-labelledby="manufacturing-title">

        <div class="flex items-center space-x-4 mb-6">
          <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <i class="fas fa-industry text-white text-2xl"></i>
          </div>
          <div>
            <h3 id="manufacturing-title" class="text-xl font-bold text-neutral-800 group-hover:text-primary-600 transition-colors duration-300">Manufacturing</h3>
            <div class="text-sm text-orange-600 font-medium">280% Avg ROI</div>
          </div>
        </div>

        <p class="text-neutral-600 mb-6 leading-relaxed">
          Optimize production efficiency, reduce downtime, and improve quality with IoT-powered predictive analytics and real-time monitoring.
        </p>

        <!-- Key Features -->
        <div class="space-y-3 mb-6">
          <div class="flex items-center space-x-3">
            <i class="fas fa-cog text-orange-500 text-sm"></i>
            <span class="text-sm text-neutral-700">Predictive maintenance alerts</span>
          </div>
          <div class="flex items-center space-x-3">
            <i class="fas fa-chart-line text-orange-500 text-sm"></i>
            <span class="text-sm text-neutral-700">Quality control analytics</span>
          </div>
          <div class="flex items-center space-x-3">
            <i class="fas fa-truck text-orange-500 text-sm"></i>
            <span class="text-sm text-neutral-700">Supply chain optimization</span>
          </div>
        </div>

        <!-- Success Metric -->
        <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
              <span class="text-orange-600 font-bold text-xs">PM</span>
            </div>
            <div>
              <div class="text-sm font-semibold text-neutral-800">Precision Manufacturing</div>
              <div class="text-xs text-neutral-600">30% downtime reduction</div>
            </div>
          </div>
        </div>

        <!-- CTAs -->
        <div class="space-y-3">
          <a href="#demo"
             class="w-full bg-orange-600 text-white px-4 py-3 rounded-lg hover:bg-orange-700 transition-all duration-200 font-semibold text-center inline-flex items-center justify-center space-x-2"
             data-action="click->landing#openDemoModal"
             data-industry="manufacturing">
            <i class="fas fa-calendar text-sm"></i>
            <span>Schedule Demo</span>
          </a>
          <button class="w-full border border-orange-600 text-orange-600 px-4 py-2 rounded-lg hover:bg-orange-50 transition-all duration-200 font-medium text-sm"
                  data-controller="expandable"
                  data-action="click->expandable#toggle">
            <span>View Use Cases</span>
            <i class="fas fa-chevron-down ml-2 text-xs"></i>
          </button>
        </div>
      </div>

      <!-- Healthcare -->
      <div class="group bg-white rounded-xl p-8 shadow-lg border border-gray-100 hover:shadow-2xl hover:border-primary-200 transition-all duration-300 transform hover:-translate-y-2"
           data-controller="animate"
           data-animate-animation-value="fadeInUp"
           data-animate-delay-value="200"
           role="article"
           aria-labelledby="healthcare-title">

        <div class="flex items-center space-x-4 mb-6">
          <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <i class="fas fa-heartbeat text-white text-2xl"></i>
          </div>
          <div>
            <h3 id="healthcare-title" class="text-xl font-bold text-neutral-800 group-hover:text-primary-600 transition-colors duration-300">Healthcare</h3>
            <div class="text-sm text-green-600 font-medium">420% Avg ROI</div>
          </div>
        </div>

        <p class="text-neutral-600 mb-6 leading-relaxed">
          Improve patient outcomes, optimize resource allocation, and ensure regulatory compliance with healthcare-specific analytics and reporting.
        </p>

        <!-- Key Features -->
        <div class="space-y-3 mb-6">
          <div class="flex items-center space-x-3">
            <i class="fas fa-user-md text-green-500 text-sm"></i>
            <span class="text-sm text-neutral-700">Patient outcome analytics</span>
          </div>
          <div class="flex items-center space-x-3">
            <i class="fas fa-shield-alt text-green-500 text-sm"></i>
            <span class="text-sm text-neutral-700">HIPAA compliance tools</span>
          </div>
          <div class="flex items-center space-x-3">
            <i class="fas fa-hospital text-green-500 text-sm"></i>
            <span class="text-sm text-neutral-700">Resource optimization</span>
          </div>
        </div>

        <!-- Success Metric -->
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <span class="text-green-600 font-bold text-xs">CH</span>
            </div>
            <div>
              <div class="text-sm font-semibold text-neutral-800">City Health Partners</div>
              <div class="text-xs text-neutral-600">35% satisfaction improvement</div>
            </div>
          </div>
        </div>

        <!-- CTAs -->
        <div class="space-y-3">
          <a href="#demo"
             class="w-full bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 transition-all duration-200 font-semibold text-center inline-flex items-center justify-center space-x-2"
             data-action="click->landing#openDemoModal"
             data-industry="healthcare">
            <i class="fas fa-calendar text-sm"></i>
            <span>Schedule Demo</span>
          </a>
          <button class="w-full border border-green-600 text-green-600 px-4 py-2 rounded-lg hover:bg-green-50 transition-all duration-200 font-medium text-sm"
                  data-controller="expandable"
                  data-action="click->expandable#toggle">
            <span>View Use Cases</span>
            <i class="fas fa-chevron-down ml-2 text-xs"></i>
          </button>
        </div>
      </div>

      <!-- Financial Services -->
      <div class="group bg-white rounded-xl p-8 shadow-lg border border-gray-100 hover:shadow-2xl hover:border-primary-200 transition-all duration-300 transform hover:-translate-y-2"
           data-controller="animate"
           data-animate-animation-value="fadeInUp"
           data-animate-delay-value="300"
           role="article"
           aria-labelledby="financial-title">

        <div class="flex items-center space-x-4 mb-6">
          <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <i class="fas fa-university text-white text-2xl"></i>
          </div>
          <div>
            <h3 id="financial-title" class="text-xl font-bold text-neutral-800 group-hover:text-primary-600 transition-colors duration-300">Financial Services</h3>
            <div class="text-sm text-purple-600 font-medium">380% Avg ROI</div>
          </div>
        </div>

        <p class="text-neutral-600 mb-6 leading-relaxed">
          Enhance risk management, detect fraud, and improve customer experience with advanced financial analytics and regulatory reporting.
        </p>

        <!-- Key Features -->
        <div class="space-y-3 mb-6">
          <div class="flex items-center space-x-3">
            <i class="fas fa-shield-check text-purple-500 text-sm"></i>
            <span class="text-sm text-neutral-700">Fraud detection & prevention</span>
          </div>
          <div class="flex items-center space-x-3">
            <i class="fas fa-chart-pie text-purple-500 text-sm"></i>
            <span class="text-sm text-neutral-700">Risk assessment analytics</span>
          </div>
          <div class="flex items-center space-x-3">
            <i class="fas fa-file-contract text-purple-500 text-sm"></i>
            <span class="text-sm text-neutral-700">Regulatory compliance</span>
          </div>
        </div>

        <!-- Success Metric -->
        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <span class="text-purple-600 font-bold text-xs">FS</span>
            </div>
            <div>
              <div class="text-sm font-semibold text-neutral-800">FinSecure Bank</div>
              <div class="text-xs text-neutral-600">60% fraud reduction</div>
            </div>
          </div>
        </div>

        <!-- CTAs -->
        <div class="space-y-3">
          <a href="#demo"
             class="w-full bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700 transition-all duration-200 font-semibold text-center inline-flex items-center justify-center space-x-2"
             data-action="click->landing#openDemoModal"
             data-industry="financial">
            <i class="fas fa-calendar text-sm"></i>
            <span>Schedule Demo</span>
          </a>
          <button class="w-full border border-purple-600 text-purple-600 px-4 py-2 rounded-lg hover:bg-purple-50 transition-all duration-200 font-medium text-sm"
                  data-controller="expandable"
                  data-action="click->expandable#toggle">
            <span>View Use Cases</span>
            <i class="fas fa-chevron-down ml-2 text-xs"></i>
          </button>
        </div>
      </div>

      <!-- Logistics & Transportation -->
      <div class="group bg-white rounded-xl p-8 shadow-lg border border-gray-100 hover:shadow-2xl hover:border-primary-200 transition-all duration-300 transform hover:-translate-y-2"
           data-controller="animate"
           data-animate-animation-value="fadeInUp"
           data-animate-delay-value="400"
           role="article"
           aria-labelledby="logistics-title">

        <div class="flex items-center space-x-4 mb-6">
          <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <i class="fas fa-shipping-fast text-white text-2xl"></i>
          </div>
          <div>
            <h3 id="logistics-title" class="text-xl font-bold text-neutral-800 group-hover:text-primary-600 transition-colors duration-300">Logistics & Transportation</h3>
            <div class="text-sm text-indigo-600 font-medium">320% Avg ROI</div>
          </div>
        </div>

        <p class="text-neutral-600 mb-6 leading-relaxed">
          Optimize routes, reduce fuel costs, and improve delivery times with real-time tracking and predictive logistics analytics.
        </p>

        <!-- Key Features -->
        <div class="space-y-3 mb-6">
          <div class="flex items-center space-x-3">
            <i class="fas fa-route text-indigo-500 text-sm"></i>
            <span class="text-sm text-neutral-700">Route optimization</span>
          </div>
          <div class="flex items-center space-x-3">
            <i class="fas fa-gas-pump text-indigo-500 text-sm"></i>
            <span class="text-sm text-neutral-700">Fuel cost reduction</span>
          </div>
          <div class="flex items-center space-x-3">
            <i class="fas fa-clock text-indigo-500 text-sm"></i>
            <span class="text-sm text-neutral-700">Delivery time prediction</span>
          </div>
        </div>

        <!-- Success Metric -->
        <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4 mb-6">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
              <span class="text-indigo-600 font-bold text-xs">GL</span>
            </div>
            <div>
              <div class="text-sm font-semibold text-neutral-800">Global Logistics Co</div>
              <div class="text-xs text-neutral-600">25% fuel cost savings</div>
            </div>
          </div>
        </div>

        <!-- CTAs -->
        <div class="space-y-3">
          <a href="#demo"
             class="w-full bg-indigo-600 text-white px-4 py-3 rounded-lg hover:bg-indigo-700 transition-all duration-200 font-semibold text-center inline-flex items-center justify-center space-x-2"
             data-action="click->landing#openDemoModal"
             data-industry="logistics">
            <i class="fas fa-calendar text-sm"></i>
            <span>Schedule Demo</span>
          </a>
          <button class="w-full border border-indigo-600 text-indigo-600 px-4 py-2 rounded-lg hover:bg-indigo-50 transition-all duration-200 font-medium text-sm"
                  data-controller="expandable"
                  data-action="click->expandable#toggle">
            <span>View Use Cases</span>
            <i class="fas fa-chevron-down ml-2 text-xs"></i>
          </button>
        </div>
      </div>

      <!-- Energy & Utilities -->
      <div class="group bg-white rounded-xl p-8 shadow-lg border border-gray-100 hover:shadow-2xl hover:border-primary-200 transition-all duration-300 transform hover:-translate-y-2"
           data-controller="animate"
           data-animate-animation-value="fadeInUp"
           data-animate-delay-value="500"
           role="article"
           aria-labelledby="energy-title">

        <div class="flex items-center space-x-4 mb-6">
          <div class="w-16 h-16 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <i class="fas fa-bolt text-white text-2xl"></i>
          </div>
          <div>
            <h3 id="energy-title" class="text-xl font-bold text-neutral-800 group-hover:text-primary-600 transition-colors duration-300">Energy & Utilities</h3>
            <div class="text-sm text-yellow-600 font-medium">290% Avg ROI</div>
          </div>
        </div>

        <p class="text-neutral-600 mb-6 leading-relaxed">
          Monitor grid performance, predict equipment failures, and optimize energy distribution with smart grid analytics and IoT integration.
        </p>

        <!-- Key Features -->
        <div class="space-y-3 mb-6">
          <div class="flex items-center space-x-3">
            <i class="fas fa-chart-area text-yellow-500 text-sm"></i>
            <span class="text-sm text-neutral-700">Grid performance monitoring</span>
          </div>
          <div class="flex items-center space-x-3">
            <i class="fas fa-exclamation-triangle text-yellow-500 text-sm"></i>
            <span class="text-sm text-neutral-700">Predictive maintenance</span>
          </div>
          <div class="flex items-center space-x-3">
            <i class="fas fa-leaf text-yellow-500 text-sm"></i>
            <span class="text-sm text-neutral-700">Energy efficiency optimization</span>
          </div>
        </div>

        <!-- Success Metric -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
              <span class="text-yellow-600 font-bold text-xs">PE</span>
            </div>
            <div>
              <div class="text-sm font-semibold text-neutral-800">PowerGrid Energy</div>
              <div class="text-xs text-neutral-600">40% efficiency improvement</div>
            </div>
          </div>
        </div>

        <!-- CTAs -->
        <div class="space-y-3">
          <a href="#demo"
             class="w-full bg-yellow-600 text-white px-4 py-3 rounded-lg hover:bg-yellow-700 transition-all duration-200 font-semibold text-center inline-flex items-center justify-center space-x-2"
             data-action="click->landing#openDemoModal"
             data-industry="energy">
            <i class="fas fa-calendar text-sm"></i>
            <span>Schedule Demo</span>
          </a>
          <button class="w-full border border-yellow-600 text-yellow-600 px-4 py-2 rounded-lg hover:bg-yellow-50 transition-all duration-200 font-medium text-sm"
                  data-controller="expandable"
                  data-action="click->expandable#toggle">
            <span>View Use Cases</span>
            <i class="fas fa-chevron-down ml-2 text-xs"></i>
          </button>
        </div>
      </div>

      <!-- Real Estate -->
      <div class="group bg-white rounded-xl p-8 shadow-lg border border-gray-100 hover:shadow-2xl hover:border-primary-200 transition-all duration-300 transform hover:-translate-y-2"
           data-controller="animate"
           data-animate-animation-value="fadeInUp"
           data-animate-delay-value="600"
           role="article"
           aria-labelledby="realestate-title">

        <div class="flex items-center space-x-4 mb-6">
          <div class="w-16 h-16 bg-gradient-to-br from-teal-500 to-teal-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
            <i class="fas fa-building text-white text-2xl"></i>
          </div>
          <div>
            <h3 id="realestate-title" class="text-xl font-bold text-neutral-800 group-hover:text-primary-600 transition-colors duration-300">Real Estate</h3>
            <div class="text-sm text-teal-600 font-medium">260% Avg ROI</div>
          </div>
        </div>

        <p class="text-neutral-600 mb-6 leading-relaxed">
          Optimize property valuations, predict market trends, and improve tenant satisfaction with comprehensive real estate analytics.
        </p>

        <!-- Key Features -->
        <div class="space-y-3 mb-6">
          <div class="flex items-center space-x-3">
            <i class="fas fa-home text-teal-500 text-sm"></i>
            <span class="text-sm text-neutral-700">Property valuation models</span>
          </div>
          <div class="flex items-center space-x-3">
            <i class="fas fa-trending-up text-teal-500 text-sm"></i>
            <span class="text-sm text-neutral-700">Market trend analysis</span>
          </div>
          <div class="flex items-center space-x-3">
            <i class="fas fa-users text-teal-500 text-sm"></i>
            <span class="text-sm text-neutral-700">Tenant analytics</span>
          </div>
        </div>

        <!-- Success Metric -->
        <div class="bg-teal-50 border border-teal-200 rounded-lg p-4 mb-6">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center">
              <span class="text-teal-600 font-bold text-xs">RP</span>
            </div>
            <div>
              <div class="text-sm font-semibold text-neutral-800">Realty Partners</div>
              <div class="text-xs text-neutral-600">18% portfolio growth</div>
            </div>
          </div>
        </div>

        <!-- CTAs -->
        <div class="space-y-3">
          <a href="#demo"
             class="w-full bg-teal-600 text-white px-4 py-3 rounded-lg hover:bg-teal-700 transition-all duration-200 font-semibold text-center inline-flex items-center justify-center space-x-2"
             data-action="click->landing#openDemoModal"
             data-industry="realestate">
            <i class="fas fa-calendar text-sm"></i>
            <span>Schedule Demo</span>
          </a>
          <button class="w-full border border-teal-600 text-teal-600 px-4 py-2 rounded-lg hover:bg-teal-50 transition-all duration-200 font-medium text-sm"
                  data-controller="expandable"
                  data-action="click->expandable#toggle">
            <span>View Use Cases</span>
            <i class="fas fa-chevron-down ml-2 text-xs"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- ROI Calculator Section -->
    <div class="bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 lg:p-12 mb-16" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="700">
      <div class="grid lg:grid-cols-2 gap-8 items-center">
        <div class="text-white">
          <h3 class="text-2xl lg:text-3xl font-bold mb-4">Calculate Your Industry ROI</h3>
          <p class="text-primary-100 mb-6 leading-relaxed">
            See how Data Reflow can impact your bottom line with our industry-specific ROI calculator. Get personalized estimates based on your business size and sector.
          </p>
          <div class="flex flex-col sm:flex-row gap-4">
            <button class="bg-white text-primary-600 px-6 py-3 rounded-lg hover:bg-gray-100 transition-all duration-200 font-semibold inline-flex items-center justify-center space-x-2"
                    data-controller="roi-calculator"
                    data-action="click->roi-calculator#open">
              <i class="fas fa-calculator text-sm"></i>
              <span>Calculate ROI</span>
            </button>
            <%= link_to new_user_registration_path,
                class: "border-2 border-white text-white px-6 py-3 rounded-lg hover:bg-white hover:text-primary-600 transition-all duration-200 font-semibold inline-flex items-center justify-center space-x-2" do %>
              <i class="fas fa-rocket text-sm"></i>
              <span>Start Free Trial</span>
            <% end %>
          </div>
        </div>
        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
          <div class="grid grid-cols-2 gap-4 text-center text-white">
            <div>
              <div class="text-2xl font-bold mb-1">90 Days</div>
              <div class="text-sm text-primary-100">Average ROI Timeline</div>
            </div>
            <div>
              <div class="text-2xl font-bold mb-1">340%</div>
              <div class="text-sm text-primary-100">Average ROI</div>
            </div>
            <div>
              <div class="text-2xl font-bold mb-1">£180K</div>
              <div class="text-sm text-primary-100">Annual Savings</div>
            </div>
            <div>
              <div class="text-2xl font-bold mb-1">700+</div>
              <div class="text-sm text-primary-100">Hours Saved</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Custom Solutions CTA -->
    <div class="text-center" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="800">
      <div class="bg-white rounded-xl p-8 shadow-lg border border-gray-100 max-w-4xl mx-auto">
        <h3 class="text-2xl font-bold text-neutral-800 mb-4">Don't See Your Industry?</h3>
        <p class="text-neutral-600 mb-8 leading-relaxed">
          We create custom solutions for any business vertical. Our team of industry experts will work with you to design a tailored analytics platform that addresses your specific challenges and compliance requirements.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="#demo"
             class="bg-primary-600 text-white px-8 py-3 rounded-lg hover:bg-primary-700 transition-all duration-200 font-semibold inline-flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105"
             data-action="click->landing#openDemoModal"
             data-industry="custom">
            <i class="fas fa-comments text-sm"></i>
            <span>Discuss Custom Solution</span>
          </a>
          <a href="#"
             class="border-2 border-primary-600 text-primary-600 px-8 py-3 rounded-lg hover:bg-primary-50 transition-all duration-200 font-semibold inline-flex items-center justify-center space-x-2">
            <i class="fas fa-file-alt text-sm"></i>
            <span>View Case Studies</span>
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Testimonials Section -->
<section id="testimonials" class="py-16 lg:py-24 bg-gradient-to-br from-primary-50 to-primary-100">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section Header -->
    <div class="text-center mb-16" data-controller="animate" data-animate-animation-value="fadeInUp">
      <h2 class="text-3xl lg:text-4xl font-bold text-neutral-800 mb-6">
        Success Stories from Our Customers
      </h2>
      <p class="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
        See how businesses like yours have transformed their operations and achieved remarkable results with Data Reflow.
      </p>
    </div>

    <!-- Testimonials Grid -->
    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
      <% @testimonials.each_with_index do |testimonial, index| %>
        <div class="bg-white rounded-xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
             data-controller="animate"
             data-animate-animation-value="fadeInUp"
             data-animate-delay-value="<%= index * 100 %>">

          <!-- Quote -->
          <div class="mb-6">
            <i class="fas fa-quote-left text-primary-600 text-2xl mb-4"></i>
            <p class="text-neutral-700 leading-relaxed italic">
              "<%= testimonial[:quote] %>"
            </p>
          </div>

          <!-- Customer Info -->
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-bold">
              <%= testimonial[:author][:name]&.split(' ')&.map(&:first)&.join('') || 'U' %>
            </div>
            <div>
              <div class="font-semibold text-neutral-800"><%= testimonial[:author][:name] || 'Unknown User' %></div>
              <div class="text-sm text-neutral-600"><%= testimonial[:author][:title] %></div>
              <div class="text-sm font-medium text-primary-600"><%= testimonial[:author][:company] %></div>
            </div>
          </div>

          <!-- Results -->
          <% if testimonial[:metrics] %>
            <div class="mt-6 pt-6 border-t border-gray-200">
              <div class="grid grid-cols-2 gap-4 text-center">
                <% testimonial[:metrics].each do |metric| %>
                  <div>
                    <div class="text-sm font-bold text-primary-600"><%= metric %></div>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>

    <!-- Additional Social Proof -->
    <div class="text-center mt-12" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="400">
      <div class="bg-white rounded-xl p-8 shadow-lg border border-gray-100 max-w-4xl mx-auto">
        <div class="grid md:grid-cols-3 gap-8 items-center">
          <div class="text-center">
            <div class="text-3xl font-bold text-primary-600 mb-2">4.9/5</div>
            <div class="text-sm text-neutral-600">Customer Satisfaction</div>
            <div class="flex justify-center mt-2">
              <% 5.times do %>
                <i class="fas fa-star text-yellow-400 text-sm"></i>
              <% end %>
            </div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-primary-600 mb-2">98%</div>
            <div class="text-sm text-neutral-600">Customer Retention</div>
            <div class="text-xs text-neutral-500 mt-1">Year-over-year</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-primary-600 mb-2">24/7</div>
            <div class="text-sm text-neutral-600">Expert Support</div>
            <div class="text-xs text-neutral-500 mt-1">Global coverage</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Pricing Section -->
<section id="pricing" class="py-16 lg:py-24 bg-white">
  <div class="container mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section Header -->
    <div class="text-center mb-16" data-controller="animate" data-animate-animation-value="fadeInUp">
      <h2 class="text-3xl lg:text-4xl font-bold text-neutral-800 mb-6">
        Simple, Transparent Pricing
      </h2>
      <p class="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed mb-8">
        Choose the plan that fits your business needs. All plans include our core features with 14-day free trial and no setup fees.
      </p>

      <!-- Billing Toggle -->
      <div class="flex items-center justify-center space-x-4 mb-8">
        <span class="text-neutral-600 font-medium">Monthly</span>
        <button class="relative inline-flex h-6 w-11 items-center rounded-full bg-primary-600 transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                data-controller="toggle" data-toggle-target="switch" data-action="click->toggle#toggle">
          <span class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" data-toggle-target="indicator"></span>
        </button>
        <span class="text-neutral-600 font-medium">
          Annual
          <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-semibold ml-2">Save 20%</span>
        </span>
      </div>
    </div>

    <!-- Pricing Cards -->
    <div class="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
      <% @pricing_plans.each_with_index do |plan, index| %>
        <div class="<%= plan[:highlighted] ? 'bg-gradient-to-br from-primary-600 to-primary-700 text-white' : 'bg-white border border-gray-200' %> rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 relative <%= plan[:highlighted] ? 'scale-105 lg:scale-110' : '' %>"
             data-controller="animate"
             data-animate-animation-value="fadeInUp"
             data-animate-delay-value="<%= index * 100 %>">

          <!-- Popular Badge -->
          <% if plan[:highlighted] %>
            <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <div class="bg-yellow-400 text-yellow-900 px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                <i class="fas fa-star mr-1"></i>
                <%= plan[:badge] || 'Most Popular' %>
              </div>
            </div>
          <% end %>

          <!-- Plan Header -->
          <div class="text-center mb-8">
            <h3 class="text-2xl font-bold mb-2 <%= plan[:highlighted] ? 'text-white' : 'text-neutral-800' %>">
              <%= plan[:name] %>
            </h3>
            <p class="<%= plan[:highlighted] ? 'text-primary-100' : 'text-neutral-600' %> mb-6">
              <%= plan[:target] || plan[:description] %>
            </p>

            <!-- Price -->
            <div class="mb-6">
              <div class="flex items-baseline justify-center space-x-2">
                <span class="text-4xl font-bold <%= plan[:highlighted] ? 'text-white' : 'text-neutral-800' %>">
                  <%= plan[:currency] || '$' %><%= plan[:price] %>
                </span>
                <span class="<%= plan[:highlighted] ? 'text-primary-100' : 'text-neutral-600' %> font-medium">
                  /<%= plan[:period]&.split(' ')&.last || 'month' %>
                </span>
              </div>
              <% if plan[:annual_price] %>
                <div class="text-sm <%= plan[:highlighted] ? 'text-primary-100' : 'text-neutral-500' %> mt-1">
                  $<%= plan[:annual_price] %>/month billed annually
                </div>
              <% end %>
            </div>
          </div>

          <!-- Features List -->
          <div class="space-y-4 mb-8">
            <% plan[:features].each do |feature| %>
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0 w-5 h-5 <%= plan[:highlighted] ? 'bg-white/20' : 'bg-primary-100' %> rounded-full flex items-center justify-center mt-0.5">
                  <i class="fas fa-check text-xs <%= plan[:highlighted] ? 'text-white' : 'text-primary-600' %>"></i>
                </div>
                <span class="<%= plan[:highlighted] ? 'text-primary-50' : 'text-neutral-600' %> text-sm leading-relaxed">
                  <%= feature %>
                </span>
              </div>
            <% end %>
          </div>

          <!-- CTA Button -->
          <div class="text-center">
            <%= link_to new_user_registration_path,
                class: "w-full inline-flex items-center justify-center px-6 py-3 rounded-lg font-semibold transition-all duration-200 #{plan[:highlighted] ? 'bg-white text-primary-600 hover:bg-gray-100' : 'bg-primary-600 text-white hover:bg-primary-700'} shadow-md hover:shadow-lg transform hover:scale-105",
                data: { plan: plan[:name] } do %>
              <%= plan[:cta_text] || 'Start Free Trial' %>
            <% end %>
            <p class="text-xs <%= plan[:highlighted] ? 'text-primary-100' : 'text-neutral-500' %> mt-3">
              14-day free trial • No credit card required
            </p>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Enterprise Section -->
    <div class="mt-16 text-center" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="400">
      <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-8 border border-gray-200 max-w-4xl mx-auto">
        <div class="grid md:grid-cols-2 gap-8 items-center">
          <div class="text-left">
            <h3 class="text-2xl font-bold text-neutral-800 mb-4">Enterprise Solutions</h3>
            <p class="text-neutral-600 mb-6">
              Need custom features, dedicated support, or on-premise deployment? Our enterprise team will create a solution tailored to your specific requirements.
            </p>
            <ul class="space-y-2 text-sm text-neutral-600">
              <li class="flex items-center space-x-2">
                <i class="fas fa-check text-primary-600 text-xs"></i>
                <span>Custom integrations and workflows</span>
              </li>
              <li class="flex items-center space-x-2">
                <i class="fas fa-check text-primary-600 text-xs"></i>
                <span>Dedicated customer success manager</span>
              </li>
              <li class="flex items-center space-x-2">
                <i class="fas fa-check text-primary-600 text-xs"></i>
                <span>On-premise or private cloud deployment</span>
              </li>
              <li class="flex items-center space-x-2">
                <i class="fas fa-check text-primary-600 text-xs"></i>
                <span>SLA guarantees and priority support</span>
              </li>
            </ul>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-neutral-800 mb-4">Custom Pricing</div>
            <p class="text-neutral-600 mb-6">Based on your specific requirements</p>
            <a href="#demo"
               class="bg-primary-600 text-white px-8 py-3 rounded-lg hover:bg-primary-700 transition-all duration-200 font-semibold inline-flex items-center space-x-2"
               data-action="click->landing#openDemoModal">
              <span>Contact Sales</span>
              <i class="fas fa-arrow-right text-sm"></i>
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- FAQ Section -->
    <div class="mt-16" data-controller="animate" data-animate-animation-value="fadeInUp" data-animate-delay-value="500">
      <h3 class="text-2xl font-bold text-neutral-800 text-center mb-8">Frequently Asked Questions</h3>
      <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
        <div class="space-y-6">
          <div>
            <h4 class="font-semibold text-neutral-800 mb-2">Can I change plans anytime?</h4>
            <p class="text-neutral-600 text-sm">Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.</p>
          </div>
          <div>
            <h4 class="font-semibold text-neutral-800 mb-2">What payment methods do you accept?</h4>
            <p class="text-neutral-600 text-sm">We accept all major credit cards, PayPal, and bank transfers for annual plans.</p>
          </div>
          <div>
            <h4 class="font-semibold text-neutral-800 mb-2">Is there a setup fee?</h4>
            <p class="text-neutral-600 text-sm">No setup fees for any plan. We'll help you get started with free onboarding support.</p>
          </div>
        </div>
        <div class="space-y-6">
          <div>
            <h4 class="font-semibold text-neutral-800 mb-2">What happens after the trial?</h4>
            <p class="text-neutral-600 text-sm">Your trial automatically converts to the plan you selected. Cancel anytime during the trial with no charges.</p>
          </div>
          <div>
            <h4 class="font-semibold text-neutral-800 mb-2">Do you offer refunds?</h4>
            <p class="text-neutral-600 text-sm">Yes, we offer a 30-day money-back guarantee on all paid plans.</p>
          </div>
          <div>
            <h4 class="font-semibold text-neutral-800 mb-2">Is my data secure?</h4>
            <p class="text-neutral-600 text-sm">Absolutely. We're SOC 2 compliant with enterprise-grade security and encryption.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Final CTA Section -->
<section class="py-16 lg:py-24 bg-gradient-to-r from-primary-600 to-primary-700 relative overflow-hidden">
  <!-- Background Pattern -->
  <div class="absolute inset-0 opacity-10">
    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0); background-size: 20px 20px;"></div>
  </div>

  <div class="container mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
    <div data-controller="animate" data-animate-animation-value="fadeInUp">
      <h2 class="text-3xl lg:text-4xl font-bold text-white mb-6">
        Ready to Transform Your Data Strategy?
      </h2>
      <p class="text-xl text-primary-100 mb-8 max-w-3xl mx-auto leading-relaxed">
        Join thousands of businesses that have unlocked their data's potential with Data Reflow.
        Start your free trial today and see results within the first week.
      </p>

      <!-- CTA Buttons -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
        <%= link_to new_user_registration_path,
            class: "bg-white text-primary-600 px-8 py-4 rounded-lg hover:bg-gray-100 transition-all duration-200 font-semibold text-lg shadow-xl hover:shadow-2xl transform hover:scale-105 inline-flex items-center justify-center space-x-2" do %>
          <span>Start Free Trial</span>
          <i class="fas fa-arrow-right text-sm"></i>
        <% end %>
        <a href="#demo"
           class="bg-primary-800 text-white border-2 border-primary-500 px-8 py-4 rounded-lg hover:bg-primary-900 transition-all duration-200 font-semibold text-lg inline-flex items-center justify-center space-x-2"
           data-action="click->landing#openDemoModal">
          <i class="fas fa-calendar text-sm"></i>
          <span>Schedule Demo</span>
        </a>
      </div>

      <!-- Trust Indicators -->
      <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8 text-primary-100">
        <div class="flex items-center space-x-2">
          <i class="fas fa-check-circle text-green-400"></i>
          <span class="text-sm">14-day free trial</span>
        </div>
        <div class="flex items-center space-x-2">
          <i class="fas fa-credit-card text-blue-400"></i>
          <span class="text-sm">No credit card required</span>
        </div>
        <div class="flex items-center space-x-2">
          <i class="fas fa-headset text-purple-400"></i>
          <span class="text-sm">24/7 expert support</span>
        </div>
        <div class="flex items-center space-x-2">
          <i class="fas fa-shield-alt text-yellow-400"></i>
          <span class="text-sm">Enterprise security</span>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Modals -->
<!-- Trial Modal -->
<div id="trial-modal" class="fixed inset-0 z-50 hidden" data-controller="modal" data-modal-target="container">
  <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" data-action="click->modal#close"></div>
  <div class="fixed inset-0 flex items-center justify-center p-4">
    <div class="bg-white rounded-xl shadow-2xl max-w-md w-full max-h-screen overflow-y-auto">
      <div class="p-6">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-2xl font-bold text-neutral-800">Start Your Free Trial</h3>
          <button class="text-gray-400 hover:text-gray-600 transition-colors duration-200" data-action="click->modal#close">
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>
        <div data-modal-target="content">
          <!-- Content will be loaded dynamically -->
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Demo Modal -->
<div id="demo-modal" class="fixed inset-0 z-50 hidden" data-controller="modal" data-modal-target="container">
  <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" data-action="click->modal#close"></div>
  <div class="fixed inset-0 flex items-center justify-center p-4">
    <div class="bg-white rounded-xl shadow-2xl max-w-md w-full max-h-screen overflow-y-auto">
      <div class="p-6">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-2xl font-bold text-neutral-800">Schedule a Demo</h3>
          <button class="text-gray-400 hover:text-gray-600 transition-colors duration-200" data-action="click->modal#close">
            <i class="fas fa-times text-xl"></i>
          </button>
        </div>
        <div data-modal-target="content">
          <!-- Content will be loaded dynamically -->
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Back to Top Button -->
<button id="back-to-top" data-landing-page-target="backToTop"
        class="fixed bottom-8 right-8 bg-primary-600 text-white p-3 rounded-full shadow-lg hover:bg-primary-700 transition-all duration-200 transform hover:scale-110 z-40 hidden"
        aria-label="Back to top">
  <i class="fas fa-arrow-up text-lg"></i>
</button>

<!-- Custom Styles for Animations -->
<style>
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  /* Enhanced Navigation Styles */
  .nav-link {
    position: relative;
    overflow: hidden;
  }

  .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(33, 128, 141, 0.1), transparent);
    transition: left 0.5s;
  }

  .nav-link:hover::before {
    left: 100%;
  }

  .mobile-nav-link {
    position: relative;
    overflow: hidden;
  }

  .mobile-nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #21808D, #2DD4BF);
    transition: width 0.3s ease-in-out;
  }

  .mobile-nav-link:hover::after,
  .mobile-nav-link.active::after {
    width: 100%;
  }

  /* Navigation underline animation */
  .nav-link div {
    transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Mobile menu animation */
  #mobile-menu {
    transform: translateY(-10px);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  #mobile-menu:not(.hidden) {
    transform: translateY(0);
    opacity: 1;
  }

  /* Hamburger menu animation */
  [data-navigation-target="menuLine"] {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
  }

  /* Header backdrop blur enhancement */
  header {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }

  /* Active section highlighting */
  .nav-link.active {
    background: linear-gradient(135deg, rgba(33, 128, 141, 0.1), rgba(45, 212, 191, 0.1));
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Loading states */
  .loading {
    opacity: 0.6;
    pointer-events: none;
  }

  /* Focus styles for accessibility */
  .focus\:ring-primary:focus {
    --tw-ring-color: rgb(33 128 141 / 0.5);
  }

  /* Custom gradient backgrounds */
  .bg-gradient-mesh {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  /* Hover effects */
  .hover-lift:hover {
    transform: translateY(-2px);
  }

  /* Custom shadows */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(33, 128, 141, 0.3);
  }

  /* Responsive text sizing */
  @media (max-width: 640px) {
    .text-responsive-xl {
      font-size: 1.5rem;
      line-height: 2rem;
    }
  }

  /* Animation delays for staggered effects */
  .animate-delay-100 { animation-delay: 100ms; }
  .animate-delay-200 { animation-delay: 200ms; }
  .animate-delay-300 { animation-delay: 300ms; }
  .animate-delay-400 { animation-delay: 400ms; }
  .animate-delay-500 { animation-delay: 500ms; }
</style>

</div>
<!-- End of landing page controller -->

