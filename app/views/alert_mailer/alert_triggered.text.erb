ALERT TRIGGERED: <%= @alert.title %>
=====================================

Severity: <%= @alert.severity.upcase %>
Alert Type: <%= @alert.alert_type.humanize %>
<% if @data_source %>Data Source: <%= @data_source.name %> (<%= @data_source.source_type_text %>)<% end %>
Triggered At: <%= @alert.triggered_at.strftime('%B %d, %Y at %I:%M %p %Z') %>
Rule: <%= @monitoring_rule.name %>

MESSAGE
-------
<%= @alert.message %>

<% if @alert.details.present? %>
ADDITIONAL DETAILS
------------------
<%= JSON.pretty_generate(@alert.details) %>
<% end %>

View this alert in your dashboard:
<%= dashboard_alert_url(@alert) %>

--
This alert was generated by the monitoring rule "<%= @monitoring_rule.name %>" 
for <%= @organization.name %>.