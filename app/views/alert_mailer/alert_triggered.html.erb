<!DOCTYPE html>
<html>
  <head>
    <meta content='text/html; charset=UTF-8' http-equiv='Content-Type' />
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
      }
      .alert-header {
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        color: white;
      }
      .alert-header.critical {
        background-color: #dc2626;
      }
      .alert-header.warning {
        background-color: #f59e0b;
      }
      .alert-header.info {
        background-color: #3b82f6;
      }
      .alert-details {
        background-color: #f3f4f6;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
      }
      .detail-row {
        display: flex;
        padding: 8px 0;
        border-bottom: 1px solid #e5e7eb;
      }
      .detail-label {
        font-weight: 600;
        width: 150px;
      }
      .action-button {
        display: inline-block;
        padding: 10px 20px;
        background-color: #3b82f6;
        color: white;
        text-decoration: none;
        border-radius: 6px;
        margin-top: 20px;
      }
    </style>
  </head>

  <body>
    <div class="alert-header <%= @alert.severity %>">
      <h1 style="margin: 0;">Alert Triggered</h1>
      <p style="margin: 10px 0 0 0; font-size: 18px;"><%= @alert.title %></p>
    </div>

    <div class="alert-details">
      <div class="detail-row">
        <span class="detail-label">Severity:</span>
        <span><%= @alert.severity.capitalize %></span>
      </div>
      
      <div class="detail-row">
        <span class="detail-label">Alert Type:</span>
        <span><%= @alert.alert_type.humanize %></span>
      </div>
      
      <% if @data_source %>
        <div class="detail-row">
          <span class="detail-label">Data Source:</span>
          <span><%= @data_source.name %> (<%= @data_source.source_type_text %>)</span>
        </div>
      <% end %>
      
      <div class="detail-row">
        <span class="detail-label">Triggered At:</span>
        <span><%= @alert.triggered_at.strftime('%B %d, %Y at %I:%M %p %Z') %></span>
      </div>
      
      <div class="detail-row">
        <span class="detail-label">Rule:</span>
        <span><%= @monitoring_rule.name %></span>
      </div>
    </div>

    <div style="background-color: white; padding: 20px; border: 1px solid #e5e7eb; border-radius: 8px;">
      <h3 style="margin-top: 0;">Alert Message</h3>
      <p><%= @alert.message %></p>
      
      <% if @alert.details.present? %>
        <h4>Additional Details</h4>
        <pre style="background-color: #f3f4f6; padding: 10px; border-radius: 4px; overflow-x: auto;"><%= JSON.pretty_generate(@alert.details) %></pre>
      <% end %>
    </div>

    <div style="text-align: center; margin-top: 30px;">
      <%= link_to 'View Alert in Dashboard', dashboard_alert_url(@alert), class: 'action-button' %>
    </div>

    <p style="margin-top: 40px; font-size: 14px; color: #6b7280;">
      This alert was generated by the monitoring rule "<%= @monitoring_rule.name %>" 
      for <%= @organization.name %>.
    </p>
  </body>
</html>