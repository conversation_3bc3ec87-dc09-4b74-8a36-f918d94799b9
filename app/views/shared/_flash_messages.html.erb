<% if flash.any? %>
  <div id="flash-messages-container" class="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full pointer-events-none">
    <% flash.each do |type, message| %>
      <% next if message.blank? %>
      
      <% 
        # Map Rails flash types to our design system
        flash_config = case type.to_s
        when 'notice'
          {
            type: 'info',
            icon: 'fas fa-info-circle',
            bg_class: 'bg-blue-50 border-blue-200',
            text_class: 'text-blue-800',
            icon_class: 'text-blue-400',
            button_class: 'text-blue-400 hover:text-blue-600',
            progress_class: 'bg-blue-500'
          }
        when 'success'
          {
            type: 'success',
            icon: 'fas fa-check-circle',
            bg_class: 'bg-green-50 border-green-200',
            text_class: 'text-green-800',
            icon_class: 'text-green-400',
            button_class: 'text-green-400 hover:text-green-600',
            progress_class: 'bg-green-500'
          }
        when 'alert', 'error'
          {
            type: 'error',
            icon: 'fas fa-exclamation-circle',
            bg_class: 'bg-red-50 border-red-200',
            text_class: 'text-red-800',
            icon_class: 'text-red-400',
            button_class: 'text-red-400 hover:text-red-600',
            progress_class: 'bg-red-500'
          }
        when 'warning'
          {
            type: 'warning',
            icon: 'fas fa-exclamation-triangle',
            bg_class: 'bg-yellow-50 border-yellow-200',
            text_class: 'text-yellow-800',
            icon_class: 'text-yellow-400',
            button_class: 'text-yellow-400 hover:text-yellow-600',
            progress_class: 'bg-yellow-500'
          }
        else
          {
            type: 'info',
            icon: 'fas fa-info-circle',
            bg_class: 'bg-gray-50 border-gray-200',
            text_class: 'text-gray-800',
            icon_class: 'text-gray-400',
            button_class: 'text-gray-400 hover:text-gray-600',
            progress_class: 'bg-gray-500'
          }
        end
      %>

      <div class="flash-message pointer-events-auto transform transition-all duration-300 ease-in-out opacity-0 translate-x-full"
           data-controller="flash-message"
           data-flash-message-type-value="<%= flash_config[:type] %>"
           data-flash-message-auto-dismiss-value="<%= flash_config[:type] != 'error' %>"
           data-flash-message-timeout-value="<%= flash_config[:type] == 'success' ? 4000 : 6000 %>"
           data-action="mouseenter->flash-message#mouseEnter mouseleave->flash-message#mouseLeave focus->flash-message#focus blur->flash-message#blur"
           data-flash-message-target="message"
           role="alert"
           aria-live="polite"
           aria-atomic="true">
        
        <!-- Main message container -->
        <div class="relative overflow-hidden rounded-md border shadow-sm <%= flash_config[:bg_class] %> max-w-xs w-full">
          <!-- Progress bar for auto-dismiss -->
          <% unless flash_config[:type] == 'error' %>
            <div class="absolute top-0 left-0 h-0.5 w-full bg-gray-200">
              <div class="flash-progress h-full <%= flash_config[:progress_class] %> transform scale-x-0 origin-left"
                   data-flash-message-target="progressBar"></div>
            </div>
          <% end %>

          <!-- Message content -->
          <div class="p-3 pt-4">
            <div class="flex items-start">
              <!-- Icon -->
              <div class="flex-shrink-0">
                <i class="<%= flash_config[:icon] %> <%= flash_config[:icon_class] %> text-sm" aria-hidden="true"></i>
              </div>

              <!-- Message text -->
              <div class="ml-2 w-0 flex-1">
                <div class="text-xs font-medium <%= flash_config[:text_class] %>">
                  <%= sanitize message %>
                </div>
              </div>

              <!-- Close button -->
              <div class="ml-2 flex-shrink-0 flex">
                <button type="button"
                        class="inline-flex rounded-sm <%= flash_config[:button_class] %> focus:outline-none focus:ring-1 focus:ring-primary-500 focus:ring-offset-1 transition-colors duration-200 p-0.5"
                        data-action="click->flash-message#manualDismiss"
                        aria-label="Dismiss notification">
                  <span class="sr-only">Close</span>
                  <i class="fas fa-times text-xs" aria-hidden="true"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
<% end %>
