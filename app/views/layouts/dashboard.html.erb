<!DOCTYPE html>
<html lang="en" class="h-full bg-gray-50">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title><%= @page_title ? "#{@page_title} - Data Reflow" : "Dashboard - Data Reflow" %></title>
    <meta name="description" content="<%= content_for?(:description) ? yield(:description) : 'Data Reflow Dashboard - Transform your data into actionable insights' %>">
    
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Stylesheets -->
    <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>
  </head>

  <body class="h-full">
    <div class="min-h-full">
      <!-- Navigation -->
      <nav class="bg-white shadow-sm border-b border-gray-200" data-controller="dashboard-navigation">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div class="flex h-16 justify-between">
            <div class="flex">
              <!-- Logo -->
              <div class="flex flex-shrink-0 items-center">
                <%= link_to dashboard_root_path, class: "flex items-center space-x-3 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg p-1" do %>
                  <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center shadow-md hover:shadow-lg transition-all duration-300">
                    <i class="fas fa-stream text-white text-sm" aria-hidden="true"></i>
                  </div>
                  <span class="text-xl font-bold text-neutral-800 hover:text-primary-600 transition-colors duration-300">Data Reflow</span>
                <% end %>
              </div>

              <!-- Desktop Navigation Links -->
              <div class="hidden md:-my-px md:ml-6 md:flex md:space-x-8">
                <%= link_to dashboard_root_path,
                    class: "#{current_page?(dashboard_root_path) ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'} inline-flex items-center border-b-2 px-1 pt-1 text-sm font-medium transition-colors duration-200" do %>
                  <i class="fas fa-chart-line mr-2" aria-hidden="true"></i>
                  Overview
                <% end %>

                <%= link_to dashboard_data_sources_path,
                    class: "#{current_page?(dashboard_data_sources_path) ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'} inline-flex items-center border-b-2 px-1 pt-1 text-sm font-medium transition-colors duration-200" do %>
                  <i class="fas fa-database mr-2" aria-hidden="true"></i>
                  Data Sources
                <% end %>

                <%= link_to dashboard_pipelines_path,
                    class: "#{controller_name == 'pipelines' ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'} inline-flex items-center border-b-2 px-1 pt-1 text-sm font-medium transition-colors duration-200" do %>
                  <i class="fas fa-stream mr-2" aria-hidden="true"></i>
                  Pipelines
                <% end %>

                <a href="#" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center border-b-2 px-1 pt-1 text-sm font-medium transition-colors duration-200">
                  <i class="fas fa-chart-bar mr-2" aria-hidden="true"></i>
                  Analytics
                </a>

                <a href="#" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center border-b-2 px-1 pt-1 text-sm font-medium transition-colors duration-200">
                  <i class="fas fa-file-alt mr-2" aria-hidden="true"></i>
                  Reports
                </a>

                <%= link_to dashboard_monitoring_index_path,
                    class: "#{controller_name == 'monitoring' ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'} inline-flex items-center border-b-2 px-1 pt-1 text-sm font-medium transition-colors duration-200" do %>
                  <i class="fas fa-tachometer-alt mr-2" aria-hidden="true"></i>
                  Monitoring
                <% end %>
              </div>
            </div>

            <!-- Desktop User Menu -->
            <div class="hidden md:ml-6 md:flex md:items-center">
              <!-- Notifications -->
              <div class="relative">
                <button type="button"
                        class="relative rounded-full bg-white p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors duration-200"
                        data-action="click->dashboard-navigation#toggleNotificationMenu"
                        data-dashboard-navigation-target="notificationButton"
                        aria-expanded="false"
                        aria-haspopup="true">
                  <span class="absolute -inset-1.5"></span>
                  <span class="sr-only">View notifications</span>
                  <i class="fas fa-bell h-5 w-5" aria-hidden="true"></i>
                  <!-- Notification badge -->
                  <span class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
                </button>

                <!-- Notification dropdown -->
                <div class="hidden absolute right-0 z-10 mt-2 w-80 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                     data-dashboard-navigation-target="notificationMenu"
                     role="menu"
                     aria-orientation="vertical"
                     aria-labelledby="notification-menu-button"
                     tabindex="-1">
                  <!-- Notification header -->
                  <div class="px-4 py-3 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                      <h3 class="text-sm font-medium text-gray-900">Notifications</h3>
                      <button class="text-xs text-primary-600 hover:text-primary-700 font-medium">Mark all as read</button>
                    </div>
                  </div>

                  <!-- Notification items -->
                  <div class="max-h-64 overflow-y-auto">
                    <a href="#" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 border-l-4 border-blue-500" role="menuitem" tabindex="-1">
                      <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                          <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-sync-alt text-blue-600 text-xs" aria-hidden="true"></i>
                          </div>
                        </div>
                        <div class="flex-1 min-w-0">
                          <p class="font-medium text-gray-900">Data sync completed</p>
                          <p class="text-gray-500 text-xs">Sales data from Shopify has been synchronized</p>
                          <p class="text-gray-400 text-xs mt-1">2 minutes ago</p>
                        </div>
                      </div>
                    </a>

                    <a href="#" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 border-l-4 border-green-500" role="menuitem" tabindex="-1">
                      <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                          <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-file-alt text-green-600 text-xs" aria-hidden="true"></i>
                          </div>
                        </div>
                        <div class="flex-1 min-w-0">
                          <p class="font-medium text-gray-900">Report generated</p>
                          <p class="text-gray-500 text-xs">Monthly revenue report is ready for review</p>
                          <p class="text-gray-400 text-xs mt-1">1 hour ago</p>
                        </div>
                      </div>
                    </a>

                    <a href="#" class="block px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 border-l-4 border-yellow-500" role="menuitem" tabindex="-1">
                      <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                          <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-exclamation-triangle text-yellow-600 text-xs" aria-hidden="true"></i>
                          </div>
                        </div>
                        <div class="flex-1 min-w-0">
                          <p class="font-medium text-gray-900">Data quality alert</p>
                          <p class="text-gray-500 text-xs">Some records may need attention</p>
                          <p class="text-gray-400 text-xs mt-1">3 hours ago</p>
                        </div>
                      </div>
                    </a>
                  </div>

                  <!-- View all notifications -->
                  <div class="border-t border-gray-200">
                    <a href="#" class="block px-4 py-3 text-sm text-center text-primary-600 hover:text-primary-700 font-medium">
                      View all notifications
                    </a>
                  </div>
                </div>
              </div>

              <!-- Profile dropdown -->
              <div class="relative ml-3">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <div class="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
                      <span class="text-sm font-medium text-white">
                        <%= current_user&.first_name&.first || current_user&.email&.first&.upcase %>
                      </span>
                    </div>
                  </div>
                  <div class="hidden lg:block">
                    <div class="text-sm font-medium text-gray-700"><%= current_user&.full_name || current_user&.email %></div>
                    <div class="text-xs text-gray-500"><%= current_organization&.name %></div>
                  </div>

                  <!-- Dropdown menu button -->
                  <div class="relative">
                    <button type="button"
                            class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 p-1 hover:bg-gray-50 transition-colors duration-200"
                            data-action="click->dashboard-navigation#toggleUserMenu"
                            data-dashboard-navigation-target="userMenuButton"
                            aria-expanded="false"
                            aria-haspopup="true">
                      <i class="fas fa-chevron-down text-gray-400" aria-hidden="true"></i>
                    </button>

                    <!-- Dropdown menu -->
                    <div class="hidden absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                         data-dashboard-navigation-target="userMenu"
                         role="menu"
                         aria-orientation="vertical"
                         aria-labelledby="user-menu-button"
                         tabindex="-1">
                      <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem" tabindex="-1">
                        <i class="fas fa-user mr-2" aria-hidden="true"></i>
                        Your Profile
                      </a>
                      <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem" tabindex="-1">
                        <i class="fas fa-cog mr-2" aria-hidden="true"></i>
                        Settings
                      </a>
                      <%= link_to destroy_user_session_path, data: { "turbo-method": :delete }, class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100", role: "menuitem", tabindex: "-1" do %>
                        <i class="fas fa-sign-out-alt mr-2" aria-hidden="true"></i>
                        Sign out
                      <% end %>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Mobile menu button -->
            <div class="md:hidden flex items-center">
              <button type="button"
                      class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 transition-colors duration-200"
                      data-action="click->dashboard-navigation#toggleMobileMenu"
                      data-dashboard-navigation-target="mobileMenuButton"
                      aria-controls="mobile-menu"
                      aria-expanded="false">
                <span class="sr-only">Open main menu</span>
                <div class="w-6 h-6 flex flex-col justify-center items-center">
                  <span class="block w-5 h-0.5 bg-gray-600 transition-all duration-200 transform" data-dashboard-navigation-target="menuLine"></span>
                  <span class="block w-5 h-0.5 bg-gray-600 mt-1 transition-all duration-200 transform" data-dashboard-navigation-target="menuLine"></span>
                  <span class="block w-5 h-0.5 bg-gray-600 mt-1 transition-all duration-200 transform" data-dashboard-navigation-target="menuLine"></span>
                </div>
              </button>
            </div>
          </div>
        </div>

        <!-- Mobile menu -->
        <div class="md:hidden hidden" data-dashboard-navigation-target="mobileMenu" id="mobile-menu">
          <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200">
            <%= link_to dashboard_root_path,
                class: "#{current_page?(dashboard_root_path) ? 'bg-primary-50 border-primary-500 text-primary-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800'} block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-colors duration-200" do %>
              <i class="fas fa-chart-line mr-3" aria-hidden="true"></i>
              Overview
            <% end %>

            <%= link_to dashboard_data_sources_path,
                class: "#{current_page?(dashboard_data_sources_path) ? 'bg-primary-50 border-primary-500 text-primary-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800'} block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-colors duration-200" do %>
              <i class="fas fa-database mr-3" aria-hidden="true"></i>
              Data Sources
            <% end %>

            <%= link_to dashboard_pipelines_path,
                class: "#{controller_name == 'pipelines' ? 'bg-primary-50 border-primary-500 text-primary-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800'} block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-colors duration-200" do %>
              <i class="fas fa-stream mr-3" aria-hidden="true"></i>
              Pipelines
            <% end %>

            <a href="#" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-colors duration-200">
              <i class="fas fa-chart-bar mr-3" aria-hidden="true"></i>
              Analytics
            </a>

            <a href="#" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-colors duration-200">
              <i class="fas fa-file-alt mr-3" aria-hidden="true"></i>
              Reports
            </a>

            <%= link_to dashboard_monitoring_index_path,
                class: "#{controller_name == 'monitoring' ? 'bg-primary-50 border-primary-500 text-primary-700' : 'border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800'} block pl-3 pr-4 py-2 border-l-4 text-base font-medium transition-colors duration-200" do %>
              <i class="fas fa-tachometer-alt mr-3" aria-hidden="true"></i>
              Monitoring
            <% end %>
          </div>

          <!-- Mobile user menu -->
          <div class="pt-4 pb-3 border-t border-gray-200 bg-gray-50">
            <div class="flex items-center px-5">
              <div class="flex-shrink-0">
                <div class="h-10 w-10 rounded-full bg-primary-600 flex items-center justify-center">
                  <span class="text-sm font-medium text-white">
                    <%= current_user&.first_name&.first || current_user&.email&.first&.upcase %>
                  </span>
                </div>
              </div>
              <div class="ml-3">
                <div class="text-base font-medium text-gray-800"><%= current_user&.full_name || current_user&.email %></div>
                <div class="text-sm font-medium text-gray-500"><%= current_organization&.name %></div>
              </div>
              <button type="button" class="ml-auto flex-shrink-0 bg-gray-50 p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <span class="sr-only">View notifications</span>
                <i class="fas fa-bell h-6 w-6" aria-hidden="true"></i>
              </button>
            </div>
            <div class="mt-3 px-2 space-y-1">
              <a href="#" class="block px-3 py-2 rounded-md text-base font-medium text-gray-400 hover:text-gray-800 hover:bg-gray-100 transition-colors duration-200">
                <i class="fas fa-user mr-3" aria-hidden="true"></i>
                Your Profile
              </a>
              <a href="#" class="block px-3 py-2 rounded-md text-base font-medium text-gray-400 hover:text-gray-800 hover:bg-gray-100 transition-colors duration-200">
                <i class="fas fa-cog mr-3" aria-hidden="true"></i>
                Settings
              </a>
              <%= link_to destroy_user_session_path, data: { "turbo-method": :delete }, class: "block px-3 py-2 rounded-md text-base font-medium text-gray-400 hover:text-gray-800 hover:bg-gray-100 transition-colors duration-200" do %>
                <i class="fas fa-sign-out-alt mr-3" aria-hidden="true"></i>
                Sign out
              <% end %>
            </div>
          </div>
        </div>
      </nav>

      <!-- Page header -->
      <% if @page_title || @breadcrumbs %>
        <header class="bg-white shadow-sm">
          <div class="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
            <div class="md:flex md:items-center md:justify-between">
              <div class="min-w-0 flex-1">
                <% if @breadcrumbs %>
                  <nav class="flex mb-2" aria-label="Breadcrumb">
                    <ol role="list" class="flex items-center space-x-4">
                      <% @breadcrumbs.each_with_index do |crumb, index| %>
                        <li>
                          <div class="flex items-center">
                            <% if index > 0 %>
                              <i class="fas fa-chevron-right flex-shrink-0 h-4 w-4 text-gray-400 mr-4" aria-hidden="true"></i>
                            <% end %>
                            <% if crumb[:path] %>
                              <%= link_to crumb[:name], crumb[:path], class: "text-sm font-medium text-gray-500 hover:text-gray-700" %>
                            <% else %>
                              <span class="text-sm font-medium text-gray-900"><%= crumb[:name] %></span>
                            <% end %>
                          </div>
                        </li>
                      <% end %>
                    </ol>
                  </nav>
                <% end %>
                
                <% if @page_title %>
                  <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    <%= @page_title %>
                  </h1>
                <% end %>
              </div>
            </div>
          </div>
        </header>
      <% end %>

      <!-- Main content -->
      <main>
        <div class="mx-auto max-w-7xl py-6 sm:px-6 lg:px-8">
          <!-- Flash Messages -->
          <%= render 'shared/dashboard_flash_messages' %>
          
          <!-- Page content -->
          <%= yield %>
        </div>
      </main>
    </div>
  </body>
</html>
