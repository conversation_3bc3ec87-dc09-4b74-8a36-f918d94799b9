<% if resource.errors.any? %>
  <div id="error_explanation"
       class="rounded-lg bg-red-50 border border-red-200 p-4 mb-6"
       data-turbo-cache="false">
    <div class="flex">
      <div class="flex-shrink-0">
        <i class="fas fa-exclamation-circle text-red-400" aria-hidden="true"></i>
      </div>
      <div class="ml-3">
        <h3 class="text-sm font-medium text-red-800">
          <%= I18n.t("errors.messages.not_saved",
                     count: resource.errors.count,
                     resource: resource.class.model_name.human.downcase)
           %>
        </h3>
        <div class="mt-2 text-sm text-red-700">
          <ul class="list-disc space-y-1 pl-5">
            <% resource.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      </div>
    </div>
  </div>
<% end %>
