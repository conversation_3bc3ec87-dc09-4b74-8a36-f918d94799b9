<% content_for :title, "Dashboard - Data Reflow" %>

<div class="min-h-screen bg-neutral-bg">
  <!-- Navigation -->
  <nav class="bg-white shadow-sm border-b border-neutral-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <!-- Logo -->
          <div class="flex-shrink-0 flex items-center">
            <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg mr-3"></div>
            <h1 class="text-xl font-semibold text-neutral-text">Data Reflow</h1>
          </div>
          
          <!-- Navigation Items -->
          <nav class="hidden md:ml-10 md:flex space-x-8">
            <a href="#" class="text-primary-600 border-b-2 border-primary-600 px-1 pt-1 text-sm font-medium">
              Dashboard
            </a>
            <a href="#" class="text-neutral-600 hover:text-neutral-900 px-1 pt-1 text-sm font-medium">
              Data Sources
            </a>
            <a href="#" class="text-neutral-600 hover:text-neutral-900 px-1 pt-1 text-sm font-medium">
              Pipelines
            </a>
            <a href="#" class="text-neutral-600 hover:text-neutral-900 px-1 pt-1 text-sm font-medium">
              Analytics
            </a>
          </nav>
        </div>
        
        <!-- User Menu -->
        <div class="flex items-center space-x-4">
          <!-- Notifications -->
          <button class="p-2 rounded-lg text-neutral-600 hover:bg-neutral-100 hover:text-neutral-900 transition-colors">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
            </svg>
          </button>
          
          <!-- User dropdown -->
          <div class="relative" data-controller="dropdown">
            <button class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2" data-dropdown-target="button">
              <div class="w-8 h-8 bg-gradient-to-br from-primary-400 to-primary-600 rounded-full flex items-center justify-center text-white font-medium mr-2">
                <%= current_user.initials %>
              </div>
              <span class="text-neutral-700 font-medium mr-1"><%= current_user.full_name %></span>
              <svg class="w-4 h-4 text-neutral-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            
            <div class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1 z-10" data-dropdown-target="menu">
              <a href="#" class="block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100">Settings</a>
              <a href="#" class="block px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100">Billing</a>
              <hr class="my-1 border-neutral-200">
              <%= button_to "Sign out", destroy_user_session_path, method: :delete, class: "block w-full text-left px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-100" %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="px-4 sm:px-0 mb-8">
      <h2 class="text-2xl font-bold text-neutral-text">Welcome back, <%= current_user.first_name %>!</h2>
      <p class="mt-1 text-sm text-neutral-600">Here's what's happening with your data today.</p>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 px-4 sm:px-0 mb-8">
      <!-- Stat Card 1 -->
      <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-neutral-200">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-neutral-600 truncate">Data Sources</dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-neutral-900">0</div>
                  <div class="ml-2 flex items-baseline text-sm font-semibold text-green-600">
                    <span class="sr-only">Active</span>
                    Active
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Stat Card 2 -->
      <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-neutral-200">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-semantic-info-light rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-semantic-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-neutral-600 truncate">Active Pipelines</dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-neutral-900">0</div>
                  <div class="ml-2 flex items-baseline text-sm font-semibold text-neutral-600">
                    / 10 allowed
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Stat Card 3 -->
      <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-neutral-200">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-semantic-warning-light rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-semantic-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-neutral-600 truncate">Data Usage</dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-neutral-900"><%= @organization.usage_percentage %>%</div>
                  <div class="ml-2 flex items-baseline text-sm text-neutral-600">
                    <span><%= number_with_delimiter(@organization.current_month_rows) %> rows</span>
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Stat Card 4 -->
      <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-neutral-200">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-12 h-12 bg-semantic-success-light rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-semantic-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-neutral-600 truncate">Plan</dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-neutral-900"><%= @organization.plan.capitalize %></div>
                  <div class="ml-2 flex items-baseline text-sm font-semibold text-primary-600">
                    <a href="#" class="hover:text-primary-700">Upgrade</a>
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="px-4 sm:px-0 mb-8">
      <h3 class="text-lg font-semibold text-neutral-text mb-4">Quick Actions</h3>
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <!-- Action Card 1 -->
        <div class="relative bg-white p-6 rounded-lg shadow-sm border border-neutral-200 hover:shadow-md transition-shadow">
          <div>
            <span class="rounded-lg inline-flex p-3 bg-primary-100 text-primary-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
            </span>
          </div>
          <div class="mt-4">
            <h4 class="text-lg font-medium text-neutral-900">Connect Data Source</h4>
            <p class="mt-2 text-sm text-neutral-600">
              Add a new data source from our library of 200+ connectors.
            </p>
          </div>
          <div class="mt-4">
            <%= link_to dashboard_data_sources_path, class: "text-sm font-medium text-primary-600 hover:text-primary-700" do %>
              Get started <span aria-hidden="true">&rarr;</span>
            <% end %>
          </div>
        </div>

        <!-- Action Card 2 -->
        <div class="relative bg-white p-6 rounded-lg shadow-sm border border-neutral-200 hover:shadow-md transition-shadow">
          <div>
            <span class="rounded-lg inline-flex p-3 bg-semantic-info-light text-semantic-info">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </span>
          </div>
          <div class="mt-4">
            <h4 class="text-lg font-medium text-neutral-900">Create Pipeline</h4>
            <p class="mt-2 text-sm text-neutral-600">
              Build a data pipeline with our visual drag-and-drop editor.
            </p>
          </div>
          <div class="mt-4">
            <a href="#" class="text-sm font-medium text-primary-600 hover:text-primary-700">
              Create pipeline <span aria-hidden="true">&rarr;</span>
            </a>
          </div>
        </div>

        <!-- Action Card 3 -->
        <div class="relative bg-white p-6 rounded-lg shadow-sm border border-neutral-200 hover:shadow-md transition-shadow">
          <div>
            <span class="rounded-lg inline-flex p-3 bg-semantic-success-light text-semantic-success">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </span>
          </div>
          <div class="mt-4">
            <h4 class="text-lg font-medium text-neutral-900">View Reports</h4>
            <p class="mt-2 text-sm text-neutral-600">
              Access pre-built reports and analytics dashboards.
            </p>
          </div>
          <div class="mt-4">
            <a href="#" class="text-sm font-medium text-primary-600 hover:text-primary-700">
              View reports <span aria-hidden="true">&rarr;</span>
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div class="px-4 sm:px-0">
      <div class="bg-white shadow-sm rounded-lg border-2 border-dashed border-neutral-300">
        <div class="text-center py-12 px-4">
          <svg class="mx-auto h-12 w-12 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-neutral-900">No data sources connected</h3>
          <p class="mt-1 text-sm text-neutral-600">Get started by connecting your first data source.</p>
          <div class="mt-6">
            <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
              <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              Connect Data Source
            </button>
          </div>
        </div>
      </div>
    </div>
  </main>
</div>