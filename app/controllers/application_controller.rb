# frozen_string_literal: true

class ApplicationController < ActionController::Base
  protect_from_forgery with: :exception
  
  before_action :configure_permitted_parameters, if: :devise_controller?
  
  # Multi-tenancy with acts_as_tenant
  set_current_tenant_through_filter
  before_action :set_tenant_for_authenticated_user
  
  # Authentication (when needed in child controllers)
  # before_action :authenticate_user!
  
  # Handle Pundit errors
  rescue_from Pundit::NotAuthorizedError, with: :user_not_authorized
  
  # Handle 404 errors
  rescue_from ActiveRecord::RecordNotFound, with: :not_found
  
  # Set locale
  around_action :switch_locale
  
  private
  
  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:sign_up, keys: [:first_name, :last_name])
    devise_parameter_sanitizer.permit(:account_update, keys: [:first_name, :last_name])
  end
  
  def set_tenant_for_authenticated_user
    if user_signed_in?
      ActsAsTenant.current_tenant = current_user.organization
    end
  end
  
  def user_not_authorized
    flash[:alert] = "You are not authorized to perform this action."
    redirect_back(fallback_location: root_path)
  end
  
  def not_found
    render file: "#{Rails.root}/public/404.html", status: 404, layout: false
  end
  
  def switch_locale(&action)
    locale = extract_locale_from_accept_language_header || I18n.default_locale
    I18n.with_locale(locale, &action)
  end
  
  def extract_locale_from_accept_language_header
    request.env['HTTP_ACCEPT_LANGUAGE']&.scan(/^[a-z]{2}/)&.first
  end
  
  # Helper method to check if user is admin
  def admin_user?
    current_user&.admin?
  end
  helper_method :admin_user?
end
