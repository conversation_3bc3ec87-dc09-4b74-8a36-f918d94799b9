# frozen_string_literal: true

class Users::SessionsController < Devise::SessionsController
  # before_action :configure_sign_in_params, only: [:create]
  skip_before_action :set_tenant_for_authenticated_user, only: [:new, :create]

  # GET /resource/sign_in
  def new
    super
  end

  # POST /resource/sign_in
  def create
    super do |resource|
      if resource.persisted?
        flash[:success] = "Welcome back, #{resource.first_name || resource.email}! You have successfully signed in."
      end
    end
  end

  # DELETE /resource/sign_out
  def destroy
    super
  end

  protected

  # If you have extra params to permit, append them to the sanitizer.
  # def configure_sign_in_params
  #   devise_parameter_sanitizer.permit(:sign_in, keys: [:attribute])
  # end
  
  # Override the path after sign in
  def after_sign_in_path_for(resource)
    dashboard_root_path
  end
  
  # Override the path after sign out
  def after_sign_out_path_for(resource_or_scope)
    root_path
  end
end
