# frozen_string_literal: true

class Users::RegistrationsController < Devise::RegistrationsController
  before_action :configure_sign_up_params, only: [:create]
  skip_before_action :set_tenant_for_authenticated_user, only: [:new, :create]

  # GET /resource/sign_up
  def new
    @organization = Organization.new
    super
  end

  # POST /resource
  def create
    @organization = Organization.new(organization_params)
    
    if @organization.save
      # Set the organization context before creating user
      ActsAsTenant.with_tenant(@organization) do
        build_resource(sign_up_params.merge(organization: @organization, role: 'admin'))
        resource.save
        yield resource if block_given?
        
        if resource.persisted?
          if resource.active_for_authentication?
            flash[:success] = "Welcome to Data Reflow, #{resource.first_name || resource.email}! Your account has been created successfully. Start exploring your analytics dashboard."
            sign_up(resource_name, resource)
            respond_with resource, location: after_sign_up_path_for(resource)
          else
            flash[:warning] = "Your account has been created but needs to be activated. Please check your email for activation instructions."
            expire_data_after_sign_in!
            respond_with resource, location: after_inactive_sign_up_path_for(resource)
          end
        else
          @organization.destroy
          flash[:error] = "There was an issue creating your account. Please check the form and try again."
          clean_up_passwords resource
          set_minimum_password_length
          respond_with resource
        end
      end
    else
      flash[:error] = "There was an issue with your organization information. Please check the form and try again."
      build_resource(sign_up_params)
      clean_up_passwords resource
      set_minimum_password_length
      render :new
    end
  end

  # GET /resource/edit
  # def edit
  #   super
  # end

  # PUT /resource
  # def update
  #   super
  # end

  # DELETE /resource
  # def destroy
  #   super
  # end

  # GET /resource/cancel
  # Forces the session data which is usually expired after sign
  # in to be expired now. This is useful if the user wants to
  # cancel oauth signing in/up in the middle of the process,
  # removing all OAuth session data.
  # def cancel
  #   super
  # end

  protected

  # If you have extra params to permit, append them to the sanitizer.
  def configure_sign_up_params
    devise_parameter_sanitizer.permit(:sign_up, keys: [:first_name, :last_name])
  end
  
  private
  
  def organization_params
    params.require(:organization).permit(:name, :subdomain)
  end

  # If you have extra params to permit, append them to the sanitizer.
  # def configure_account_update_params
  #   devise_parameter_sanitizer.permit(:account_update, keys: [:attribute])
  # end

  # The path used after sign up.
  def after_sign_up_path_for(resource)
    dashboard_root_path
  end

  # The path used after sign up for inactive accounts.
  # def after_inactive_sign_up_path_for(resource)
  #   super(resource)
  # end
end
