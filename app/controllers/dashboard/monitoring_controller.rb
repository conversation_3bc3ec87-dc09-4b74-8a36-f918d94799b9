module Dashboard
  class MonitoringController < ApplicationController
    before_action :authenticate_user!
    before_action :set_organization

    def index
      @monitoring_rules = @organization.monitoring_rules.includes(:pipeline)
      @active_alerts = @organization.alerts.active.includes(:monitoring_rule, :pipeline)
      @recent_metrics = @organization.performance_metrics
                                     .includes(:pipeline)
                                     .order(created_at: :desc)
                                     .limit(100)
      
      # Calculate aggregate metrics
      @aggregate_metrics = calculate_aggregate_metrics
      
      # Get pipeline status overview
      @pipeline_statuses = @organization.pipelines.group(:status).count
      
      # Get recent alert history
      @alert_history = @organization.alerts
                                    .includes(:monitoring_rule, :pipeline)
                                    .order(created_at: :desc)
                                    .limit(20)
    end

    def metrics
      pipeline = @organization.pipelines.find(params[:pipeline_id]) if params[:pipeline_id]
      
      metrics = @organization.performance_metrics
      metrics = metrics.where(pipeline: pipeline) if pipeline
      metrics = metrics.where('created_at >= ?', params[:from]) if params[:from]
      metrics = metrics.where('created_at <= ?', params[:to]) if params[:to]
      
      render json: {
        metrics: metrics.order(created_at: :desc).limit(1000).map do |metric|
          {
            id: metric.id,
            pipeline_id: metric.pipeline_id,
            pipeline_name: metric.pipeline&.name,
            metric_type: metric.metric_type,
            value: metric.value,
            unit: metric.unit,
            metadata: metric.metadata,
            created_at: metric.created_at
          }
        end
      }
    end

    def alerts
      alerts = @organization.alerts.includes(:monitoring_rule, :pipeline)
      alerts = alerts.where(status: params[:status]) if params[:status]
      alerts = alerts.where(severity: params[:severity]) if params[:severity]
      
      render json: {
        alerts: alerts.order(created_at: :desc).map do |alert|
          {
            id: alert.id,
            rule: alert.monitoring_rule&.name,
            pipeline: alert.pipeline&.name,
            severity: alert.severity,
            status: alert.status,
            message: alert.message,
            triggered_at: alert.triggered_at,
            resolved_at: alert.resolved_at
          }
        end
      }
    end

    def acknowledge_alert
      alert = @organization.alerts.find(params[:id])
      alert.acknowledge!(current_user)
      
      ActionCable.server.broadcast(
        "monitoring_#{@organization.id}",
        {
          type: 'alert_acknowledged',
          alert: alert.as_json(include: [:monitoring_rule, :pipeline])
        }
      )
      
      redirect_to dashboard_monitoring_index_path, notice: 'Alert acknowledged'
    end

    def resolve_alert
      alert = @organization.alerts.find(params[:id])
      alert.resolve!(current_user)
      
      ActionCable.server.broadcast(
        "monitoring_#{@organization.id}",
        {
          type: 'alert_resolved',
          alert: alert.as_json(include: [:monitoring_rule, :pipeline])
        }
      )
      
      redirect_to dashboard_monitoring_index_path, notice: 'Alert resolved'
    end

    private

    def set_organization
      @organization = current_user.organization
    end

    def calculate_aggregate_metrics
      last_24h = 24.hours.ago
      
      {
        avg_execution_time: @organization.performance_metrics
                                         .where(metric_type: 'execution_time')
                                         .where('created_at >= ?', last_24h)
                                         .average(:value)&.round(2),
        total_records_processed: @organization.performance_metrics
                                             .where(metric_type: 'records_processed')
                                             .where('created_at >= ?', last_24h)
                                             .sum(:value),
        avg_cpu_usage: @organization.performance_metrics
                                    .where(metric_type: 'cpu_usage')
                                    .where('created_at >= ?', last_24h)
                                    .average(:value)&.round(2),
        avg_memory_usage: @organization.performance_metrics
                                       .where(metric_type: 'memory_usage')
                                       .where('created_at >= ?', last_24h)
                                       .average(:value)&.round(2),
        total_errors: @organization.performance_metrics
                                   .where(metric_type: 'error_count')
                                   .where('created_at >= ?', last_24h)
                                   .sum(:value)
      }
    end
  end
end