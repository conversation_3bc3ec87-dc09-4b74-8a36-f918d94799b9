# frozen_string_literal: true

class Dashboard::BaseController < ApplicationController
  before_action :authenticate_user!
  before_action :ensure_organization_context
  
  layout 'dashboard'
  
  protected
  
  def ensure_organization_context
    unless current_user&.organization
      redirect_to new_user_registration_path, alert: 'Please complete your organization setup.'
    end
  end
  
  def current_organization
    @current_organization ||= current_user&.organization
  end
  helper_method :current_organization
  
  def authorize_admin!
    unless current_user&.admin?
      redirect_to dashboard_root_path, alert: 'Access denied. Admin privileges required.'
    end
  end
  
  def set_page_title(title)
    @page_title = title
  end
  
  def breadcrumb_add(name, path = nil)
    @breadcrumbs ||= []
    @breadcrumbs << { name: name, path: path }
  end
end
