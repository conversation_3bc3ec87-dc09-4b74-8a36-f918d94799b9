require 'fileutils'

class Dashboard::DataSourcesController < Dashboard::BaseController
  before_action :set_data_source, only: [:show, :edit, :update, :destroy, :test_connection, :sync, :schema, :sample_data, :fix_file_path, :sync_history]
  
  def index
    @data_sources = current_organization.data_sources.order(created_at: :desc)
    @stats = {
      total: @data_sources.count,
      active: @data_sources.active.count,
      with_errors: @data_sources.with_errors.count,
      syncing: @data_sources.where(status: 'syncing').count
    }
  end
  
  def test
    # Test page for debugging Stimulus controller
  end
  
  def stimulus_test
    # Test page for debugging Stimulus
  end
  
  def simple_test
    # Simple test without Stimulus
  end
  
  def new_modal
    @data_source = current_organization.data_sources.build
  end
  
  def debug
    @data_source = current_organization.data_sources.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    redirect_to dashboard_data_sources_path, alert: I18n.t('data_source.messages.source_not_found')
  end
  
  def show
    @recent_syncs = @data_source.sync_logs.recent.limit(DataSourceConfig.ui['recent_syncs_limit'])
  end
  
  def new
    @data_source = current_organization.data_sources.build
    @source_types = DataSource::SOURCE_TYPES.map do |type|
      [DataSource.new(source_type: type).source_type_text, type]
    end.sort_by(&:first)
  end
  
  def create
    # Extract csv_file before building the data source
    csv_file = params[:data_source][:csv_file]
    
    # Build data source without csv_file parameter
    @data_source = current_organization.data_sources.build(data_source_params.except(:csv_file))
    
    if @data_source.save
      # Handle CSV file upload AFTER saving if present
      if @data_source.source_type == 'csv' && csv_file.present?
        handle_csv_file_upload(@data_source, csv_file)
        # Save again to persist the file path
        @data_source.save!
      end
      
      # Test connection if requested
      if params[:test_connection] == 'true'
        result = @data_source.test_connection
        if result[:success]
          flash[:notice] = "Data source created and connection successful!"
        else
          flash[:alert] = "Data source created but connection failed: #{result[:error]}"
        end
      else
        flash[:notice] = "Data source created successfully!"
      end
      
      redirect_to dashboard_data_source_path(@data_source)
    else
      @source_types = DataSource::SOURCE_TYPES.map do |type|
        [DataSource.new(source_type: type).source_type_text, type]
      end.sort_by(&:first)
      render :new, status: :unprocessable_entity
    end
  end
  
  def edit
    @source_types = DataSource::SOURCE_TYPES.map do |type|
      [DataSource.new(source_type: type).source_type_text, type]
    end.sort_by(&:first)

    # Handle AJAX requests for modal
    respond_to do |format|
      format.html # Regular page request
      format.js   # AJAX request - render modal content
    end
  end
  
  def update
    # Extract csv_file before updating
    csv_file = params[:data_source][:csv_file]

    if @data_source.update(data_source_params.except(:csv_file))
      # Handle CSV file upload AFTER updating if present
      if @data_source.source_type == 'csv' && csv_file.present?
        handle_csv_file_upload(@data_source, csv_file)
        # Save again to persist the file path
        @data_source.save!
      end

      respond_to do |format|
        format.html do
          flash[:notice] = "Data source updated successfully!"
          redirect_to dashboard_data_source_path(@data_source)
        end
        format.json do
          render json: {
            success: true,
            message: "Data source updated successfully!",
            redirect_url: dashboard_data_source_path(@data_source)
          }
        end
      end
    else
      @source_types = DataSource::SOURCE_TYPES.map do |type|
        [DataSource.new(source_type: type).source_type_text, type]
      end.sort_by(&:first)

      respond_to do |format|
        format.html { render :edit, status: :unprocessable_entity }
        format.json do
          render json: {
            success: false,
            errors: @data_source.errors.full_messages,
            html: render_to_string(partial: 'enhanced_form', locals: { data_source: @data_source })
          }, status: :unprocessable_entity
        end
      end
    end
  end
  
  def destroy
    @data_source.destroy
    flash[:notice] = "Data source deleted successfully!"
    redirect_to dashboard_data_sources_path
  end
  
  # Connection fields partial loader
  def connection_fields
    source_type = params[:source_type]

    if source_type.present? && DataSource::SOURCE_TYPES.include?(source_type)
      @data_source = current_organization.data_sources.build(source_type: source_type)

      begin
        render partial: "dashboard/data_sources/connection_fields/#{source_type}",
               locals: { data_source: @data_source, form: nil }
      rescue ActionView::MissingTemplate
        render plain: "<div class='bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-lg'>
                        <p class='font-medium'>Coming Soon</p>
                        <p class='text-sm mt-1'>Connection settings for #{source_type.humanize} are not yet implemented.</p>
                      </div>",
               status: :ok
      end
    else
      render plain: "Invalid source type", status: :bad_request
    end
  end

  # Enhanced connection fields partial loader for modal interface
  def enhanced_connection_fields
    source_type = params[:source_type]

    if source_type.present? && DataSource::SOURCE_TYPES.include?(source_type)
      @data_source = current_organization.data_sources.build(source_type: source_type)

      begin
        render partial: "dashboard/data_sources/enhanced_connection_fields/#{source_type}",
               locals: { data_source: @data_source, form: nil }
      rescue ActionView::MissingTemplate
        # Fall back to default template for unsupported types
        render partial: "dashboard/data_sources/enhanced_connection_fields/default",
               locals: { data_source: @data_source, form: nil }
      end
    else
      render plain: "Invalid source type", status: :bad_request
    end
  end
  
  def test_connection
    Rails.logger.info "Test connection called for DataSource #{@data_source.id}"

    result = @data_source.test_connection

    respond_to do |format|
      format.html do
        if result[:success]
          flash[:notice] = result[:message] || "Connection successful!"
        else
          flash[:alert] = "Connection failed: #{result[:error]}"
        end
        redirect_to dashboard_data_source_path(@data_source)
      end

      format.json do
        render json: {
          success: result[:success],
          message: result[:success] ? (result[:message] || "Connection successful!") : nil,
          error: result[:success] ? nil : result[:error]
        }
      end
    end
  end

  # Test connection with form parameters (for modal interface)
  def test_connection_with_params
    Rails.logger.info "Test connection with params called"

    # Create a temporary data source with the provided parameters
    temp_data_source = current_organization.data_sources.build(data_source_params)

    begin
      result = temp_data_source.test_connection

      render json: {
        success: result[:success],
        message: result[:success] ? (result[:message] || "Connection successful!") : nil,
        error: result[:success] ? nil : result[:error]
      }
    rescue => e
      Rails.logger.error "Test connection error: #{e.message}"
      render json: {
        success: false,
        error: "Connection test failed: #{e.message}"
      }
    end
  end
  
  def sync
    result = @data_source.sync!
    
    if result[:success]
      flash[:notice] = result[:message]
    else
      flash[:alert] = "Sync failed: #{result[:error]}"
    end
    
    redirect_to dashboard_data_source_path(@data_source)
  end
  
  def schema
    Rails.logger.info "=== SCHEMA ACTION START ==="
    Rails.logger.info "DataSource ID: #{@data_source.id}"
    Rails.logger.info "DataSource connection_config: #{@data_source.connection_config}"
    Rails.logger.info "DataSource connection_settings: #{@data_source.connection_settings.inspect}"
    
    @schema_info = @data_source.fetch_schema
    
    if @schema_info[:success]
      @schema = @schema_info[:schema]
    else
      Rails.logger.error "Schema fetch failed: #{@schema_info[:error]}"
      flash[:alert] = "Failed to fetch schema: #{@schema_info[:error]}"
      redirect_to dashboard_data_source_path(@data_source)
    end
  end
  
  def sample_data
    @sample_data_result = @data_source.fetch_sample_data(limit: DataSourceConfig.ui['sample_data_limit'])
    
    if @sample_data_result[:success]
      @sample_data = @sample_data_result[:data]
      @columns = @sample_data_result[:metadata][:columns]
    else
      flash[:alert] = "Failed to fetch sample data: #{@sample_data_result[:error]}"
      redirect_to dashboard_data_source_path(@data_source)
    end
  end
  
  def fix_file_path
    # This action helps fix CSV data sources that have missing file paths
    if @data_source.source_type == 'csv'
      # Check if there's a file in the expected location
      expected_dir = Rails.root.join('storage', 'uploads', 'csv', current_organization.id.to_s)
      
      if Dir.exist?(expected_dir)
        files = Dir.glob(File.join(expected_dir, "*#{@data_source.connection_config_value('original_filename')}"))
        
        if files.any?
          # Use the most recent file
          file_path = files.max_by { |f| File.mtime(f) }
          
          # Update the connection config
          config = @data_source.connection_settings
          config['file_path'] = File.realpath(file_path)
          
          # Set default delimiter if empty
          config['delimiter'] = ',' if config['delimiter'].blank?
          config['encoding'] = 'UTF-8' if config['encoding'].blank?
          
          @data_source.update_connection_settings(config)
          
          flash[:notice] = "File path has been fixed. Please test the connection."
        else
          flash[:alert] = "Could not find the uploaded file. Please re-upload."
        end
      else
        flash[:alert] = "Upload directory does not exist. Please re-upload the file."
      end
    else
      flash[:alert] = "This action is only for CSV data sources."
    end
    
    redirect_to dashboard_data_source_path(@data_source)
  end
  
  def sync_history
    @sync_logs = @data_source.sync_logs.recent.page(params[:page]).per(DataSourceConfig.ui['pagination_per_page'])
  end
  
  private
  
  def set_data_source
    @data_source = current_organization.data_sources.find(params[:id])
  end
  
  def data_source_params
    params.require(:data_source).permit(
      :name,
      :description,
      :source_type,
      :sync_frequency,
      :active,
      :csv_file,
      connection_config: [
        :file_path, :file_url, :delimiter, :encoding, :has_headers,
        :username, :password, :s3_config, :transformations,
        :host, :port, :database, :sslmode
      ]
    ).tap do |p|
      # Handle nested connection_config parameters
      if p[:connection_config].present?
        # Parse JSON fields if they're strings
        [:s3_config, :transformations].each do |field|
          if p[:connection_config][field].is_a?(String) && p[:connection_config][field].present?
            begin
              p[:connection_config][field] = JSON.parse(p[:connection_config][field])
            rescue JSON::ParserError
              # Keep as string if not valid JSON
            end
          end
        end
        
        # Convert connection_config hash to JSON string for storage
        p[:connection_config] = p[:connection_config].to_json
      end
    end
  end
  
  def current_organization
    current_user.organization
  end
  
  def handle_csv_file_upload(data_source, uploaded_file)
    # Create uploads directory if it doesn't exist
    uploads_dir = Rails.root.join(DataSourceConfig.uploads['base_path'], 'csv', current_organization.id.to_s)
    FileUtils.mkdir_p(uploads_dir)
    
    # Generate unique filename
    timestamp = Time.current.strftime('%Y%m%d%H%M%S')
    filename = "#{timestamp}_#{uploaded_file.original_filename}"
    file_path = uploads_dir.join(filename)
    
    # Save the file
    File.open(file_path, 'wb') do |file|
      file.write(uploaded_file.read)
    end
    
    # Get existing config and merge with file upload data
    existing_config = data_source.connection_settings
    
    # Use the real path to handle case sensitivity
    real_path = file_path.to_s
    if File.exist?(real_path)
      real_path = File.realpath(real_path)
    end
    
    # Update with file path and metadata
    file_config = {
      'file_path' => real_path,
      'original_filename' => uploaded_file.original_filename,
      'file_size' => uploaded_file.size,
      'uploaded_at' => Time.current.to_s
    }
    
    # Merge configs, preserving existing settings
    final_config = existing_config.merge(file_config)
    
    # Ensure delimiter and encoding have defaults
    final_config['delimiter'] ||= DataSourceConstants::DEFAULTS[:delimiter]
    final_config['encoding'] ||= DataSourceConstants::DEFAULTS[:encoding]
    
    # Update the data source
    data_source.connection_config = final_config.to_json
    
    Rails.logger.info "CSV upload successful - File config: #{file_config.inspect}"
    Rails.logger.info "Final connection_config: #{final_config.inspect}"
  rescue => e
    Rails.logger.error "Failed to handle CSV upload: #{e.message}"
    data_source.errors.add(:base, "Failed to upload CSV file: #{e.message}")
  end
end