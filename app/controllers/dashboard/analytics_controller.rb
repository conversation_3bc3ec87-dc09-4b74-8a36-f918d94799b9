class Dashboard::AnalyticsController < Dashboard::BaseController
  include ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>
  
  def index
    @date_range = params[:date_range] || '30_days'
    @date_from, @date_to = parse_date_range(@date_range)
    
    # Core metrics
    @metrics = {
      total_syncs: calculate_total_syncs,
      success_rate: calculate_success_rate,
      total_data_volume: calculate_data_volume,
      avg_sync_duration: calculate_avg_sync_duration,
      active_sources: count_active_sources,
      error_rate: calculate_error_rate
    }
    
    # Time series data for charts
    @sync_timeline_data = fetch_sync_timeline_data
    @source_performance_data = fetch_source_performance_data
    @data_volume_trend = fetch_data_volume_trend
    @error_distribution = fetch_error_distribution
    
    # Top performers and issues
    @top_sources = fetch_top_performing_sources
    @problematic_sources = fetch_problematic_sources
    @recent_errors = fetch_recent_errors
    
    # Real-time stats
    @current_syncs = fetch_current_syncs
    @sync_queue_size = fetch_sync_queue_size
    
    respond_to do |format|
      format.html
      format.json { render json: analytics_json_response }
    end
  end
  
  def sync_performance
    @source_id = params[:source_id]
    @date_range = params[:date_range] || '7_days'
    @date_from, @date_to = parse_date_range(@date_range)
    
    if @source_id.present?
      @data_source = current_organization.data_sources.find(@source_id)
      @performance_data = fetch_source_detailed_performance(@data_source)
    else
      @performance_data = fetch_overall_sync_performance
    end
    
    render json: @performance_data
  end
  
  def data_insights
    @insights = {
      growth_rate: calculate_data_growth_rate,
      peak_sync_times: identify_peak_sync_times,
      source_reliability: calculate_source_reliability_scores,
      recommendations: generate_recommendations
    }
    
    render json: @insights
  end
  
  def export
    @format = params[:format_type] || 'csv'
    @date_range = params[:date_range] || '30_days'
    
    case @format
    when 'csv'
      send_data generate_csv_report, filename: "analytics_report_#{Date.current}.csv", type: 'text/csv'
    when 'pdf'
      send_data generate_pdf_report, filename: "analytics_report_#{Date.current}.pdf", type: 'application/pdf'
    when 'json'
      send_data analytics_json_response.to_json, filename: "analytics_report_#{Date.current}.json", type: 'application/json'
    end
  end
  
  private
  
  def parse_date_range(range)
    case range
    when '24_hours'
      [24.hours.ago, Time.current]
    when '7_days'
      [7.days.ago, Time.current]
    when '30_days'
      [30.days.ago, Time.current]
    when '90_days'
      [90.days.ago, Time.current]
    when 'custom'
      from = params[:date_from].present? ? Date.parse(params[:date_from]).beginning_of_day : 30.days.ago
      to = params[:date_to].present? ? Date.parse(params[:date_to]).end_of_day : Time.current
      [from, to]
    else
      [30.days.ago, Time.current]
    end
  end
  
  def calculate_total_syncs
    Rails.cache.fetch(analytics_cache_key('total_syncs'), expires_in: 5.minutes) do
      current_organization.sync_logs
        .where(started_at: @date_from..@date_to)
        .count
    end
  end
  
  def calculate_success_rate
    Rails.cache.fetch(analytics_cache_key('success_rate'), expires_in: 5.minutes) do
      total = current_organization.sync_logs
        .where(started_at: @date_from..@date_to)
        .count
      
      return 0 if total.zero?
      
      successful = current_organization.sync_logs
        .where(started_at: @date_from..@date_to, status: 'completed')
        .count
      
      ((successful.to_f / total) * 100).round(1)
    end
  end
  
  def calculate_data_volume
    total_bytes = current_organization.data_sources
      .joins(:sync_logs)
      .where(sync_logs: { started_at: @date_from..@date_to })
      .sum('sync_logs.records_imported * COALESCE(data_sources.avg_record_size_bytes, 1000)')
    
    number_to_human_size(total_bytes)
  end
  
  def calculate_avg_sync_duration
    Rails.cache.fetch(analytics_cache_key('avg_sync_duration'), expires_in: 5.minutes) do
      avg_seconds = current_organization.sync_logs
        .where(started_at: @date_from..@date_to)
        .where.not(duration_seconds: nil)
        .average(:duration_seconds) || 0
      
      avg_seconds.round(1)
    end
  end
  
  def count_active_sources
    current_organization.data_sources.active.count
  end
  
  def calculate_error_rate
    total = current_organization.sync_logs
      .where(started_at: @date_from..@date_to)
      .count
    
    return 0 if total.zero?
    
    errors = current_organization.sync_logs
      .where(started_at: @date_from..@date_to, status: 'failed')
      .count
    
    ((errors.to_f / total) * 100).round(1)
  end
  
  def fetch_sync_timeline_data
    Rails.cache.fetch(analytics_cache_key('sync_timeline'), expires_in: 5.minutes) do
      current_organization.sync_logs
        .where(started_at: @date_from..@date_to)
        .group_by_period(:hour, :started_at, n: (@date_to - @date_from) > 7.days ? 24 : 1)
        .group(:status)
        .count
        .transform_keys { |k| { time: k[0], status: k[1] } }
    end
  end
  
  def fetch_source_performance_data
    current_organization.data_sources
      .joins(:sync_logs)
      .where(sync_logs: { started_at: @date_from..@date_to })
      .group('data_sources.name')
      .average('sync_logs.duration_seconds')
      .sort_by { |_, v| -v }
      .first(10)
      .to_h
  end
  
  def fetch_data_volume_trend
    current_organization.sync_logs
      .where(started_at: @date_from..@date_to)
      .group_by_period(:day, :started_at)
      .sum(:records_imported)
  end
  
  def fetch_error_distribution
    current_organization.sync_logs
      .joins(:data_source)
      .where(started_at: @date_from..@date_to, status: 'failed')
      .group('data_sources.source_type')
      .count
  end
  
  def fetch_top_performing_sources
    current_organization.data_sources
      .joins(:sync_logs)
      .where(sync_logs: { started_at: @date_from..@date_to, status: 'completed' })
      .group('data_sources.id', 'data_sources.name')
      .order('COUNT(sync_logs.id) DESC')
      .limit(5)
      .pluck('data_sources.name', 'COUNT(sync_logs.id)', 'AVG(sync_logs.duration_seconds)')
      .map { |name, count, avg_duration| 
        { 
          name: name, 
          sync_count: count, 
          avg_duration: avg_duration&.round(1) || 0 
        } 
      }
  end
  
  def fetch_problematic_sources
    current_organization.data_sources
      .joins(:sync_logs)
      .where(sync_logs: { started_at: @date_from..@date_to, status: 'failed' })
      .group('data_sources.id', 'data_sources.name')
      .order('COUNT(sync_logs.id) DESC')
      .limit(5)
      .pluck('data_sources.name', 'COUNT(sync_logs.id)')
      .map { |name, error_count| { name: name, error_count: error_count } }
  end
  
  def fetch_recent_errors
    current_organization.sync_logs
      .includes(:data_source)
      .where(status: 'failed')
      .where(started_at: @date_from..@date_to)
      .order(started_at: :desc)
      .limit(10)
      .map { |log| 
        {
          source_name: log.data_source.name,
          error_message: log.error_message,
          occurred_at: log.started_at,
          source_type: log.data_source.source_type
        }
      }
  end
  
  def fetch_current_syncs
    current_organization.data_sources.syncing.count
  end
  
  def fetch_sync_queue_size
    # This would connect to your job queue system
    0 # Placeholder
  end
  
  def fetch_source_detailed_performance(data_source)
    {
      hourly_syncs: data_source.sync_logs
        .where(started_at: @date_from..@date_to)
        .group_by_period(:hour, :started_at)
        .count,
      
      success_trend: data_source.sync_logs
        .where(started_at: @date_from..@date_to)
        .group_by_period(:day, :started_at)
        .group(:status)
        .count,
      
      duration_trend: data_source.sync_logs
        .where(started_at: @date_from..@date_to)
        .group_by_period(:day, :started_at)
        .average(:duration_seconds),
      
      row_count_trend: data_source.sync_logs
        .where(started_at: @date_from..@date_to)
        .group_by_period(:day, :started_at)
        .sum(:records_imported)
    }
  end
  
  def fetch_overall_sync_performance
    {
      by_source_type: current_organization.sync_logs
        .joins(:data_source)
        .where(started_at: @date_from..@date_to)
        .group('data_sources.source_type')
        .average('sync_logs.duration_seconds'),
      
      by_hour_of_day: current_organization.sync_logs
        .where(started_at: @date_from..@date_to)
        .group_by_hour_of_day(:started_at)
        .count,
      
      by_day_of_week: current_organization.sync_logs
        .where(started_at: @date_from..@date_to)
        .group_by_day_of_week(:started_at)
        .count
    }
  end
  
  def calculate_data_growth_rate
    current_period = current_organization.sync_logs
      .where(started_at: @date_from..@date_to)
      .sum(:records_imported)
    
    previous_period_start = @date_from - (@date_to - @date_from)
    previous_period = current_organization.sync_logs
      .where(started_at: previous_period_start..@date_from)
      .sum(:records_imported)
    
    return 0 if previous_period.zero?
    
    ((current_period - previous_period).to_f / previous_period * 100).round(1)
  end
  
  def identify_peak_sync_times
    current_organization.sync_logs
      .where(started_at: @date_from..@date_to)
      .group_by_hour_of_day(:started_at)
      .count
      .sort_by { |_, count| -count }
      .first(3)
      .map { |hour, count| { hour: hour, count: count } }
  end
  
  def calculate_source_reliability_scores
    current_organization.data_sources.map do |source|
      total_syncs = source.sync_logs.where(started_at: @date_from..@date_to).count
      next if total_syncs.zero?
      
      successful_syncs = source.sync_logs.where(started_at: @date_from..@date_to, status: 'completed').count
      reliability = (successful_syncs.to_f / total_syncs * 100).round(1)
      
      {
        source_name: source.name,
        reliability_score: reliability,
        total_syncs: total_syncs
      }
    end.compact.sort_by { |s| -s[:reliability_score] }
  end
  
  def generate_recommendations
    recommendations = []
    
    # Check for high error rates
    if @metrics[:error_rate] > 10
      recommendations << {
        type: 'error_rate',
        severity: 'high',
        message: 'High error rate detected. Review failing data sources and connection configurations.',
        action: 'Review error logs'
      }
    end
    
    # Check for slow syncs
    if @metrics[:avg_sync_duration] > 300 # 5 minutes
      recommendations << {
        type: 'performance',
        severity: 'medium',
        message: 'Average sync duration is high. Consider optimizing queries or increasing sync frequency.',
        action: 'Optimize performance'
      }
    end
    
    # Check for inactive sources
    inactive_count = current_organization.data_sources.inactive.count
    if inactive_count > 0
      recommendations << {
        type: 'inactive_sources',
        severity: 'low',
        message: "#{inactive_count} inactive data sources detected. Consider removing or reactivating them.",
        action: 'Review inactive sources'
      }
    end
    
    recommendations
  end
  
  def analytics_json_response
    {
      metrics: @metrics,
      charts: {
        sync_timeline: @sync_timeline_data,
        source_performance: @source_performance_data,
        data_volume_trend: @data_volume_trend,
        error_distribution: @error_distribution
      },
      insights: {
        top_sources: @top_sources,
        problematic_sources: @problematic_sources,
        recent_errors: @recent_errors
      },
      real_time: {
        current_syncs: @current_syncs,
        queue_size: @sync_queue_size
      }
    }
  end
  
  def generate_csv_report
    require 'csv'
    
    CSV.generate(headers: true) do |csv|
      csv << ['Analytics Report', "Generated: #{Time.current}", "Period: #{@date_from.to_date} to #{@date_to.to_date}"]
      csv << []
      
      csv << ['Key Metrics']
      @metrics.each do |key, value|
        csv << [key.to_s.humanize, value]
      end
      csv << []
      
      csv << ['Top Performing Sources']
      csv << ['Source Name', 'Sync Count', 'Avg Duration (s)']
      @top_sources.each do |source|
        csv << [source[:name], source[:sync_count], source[:avg_duration]]
      end
      csv << []
      
      csv << ['Problematic Sources']
      csv << ['Source Name', 'Error Count']
      @problematic_sources.each do |source|
        csv << [source[:name], source[:error_count]]
      end
    end
  end
  
  def generate_pdf_report
    # This would use a PDF generation library like Prawn or WickedPDF
    # For now, returning a placeholder
    "PDF Report Generation - To be implemented"
  end
  
  def analytics_cache_key(metric)
    "analytics/#{current_organization.id}/#{@date_range}/#{metric}/#{@date_from.to_i}-#{@date_to.to_i}"
  end
  
  def clear_analytics_cache
    Rails.cache.delete_matched("analytics/#{current_organization.id}/*")
  end
end