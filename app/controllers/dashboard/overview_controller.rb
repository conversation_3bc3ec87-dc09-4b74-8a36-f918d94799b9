# frozen_string_literal: true

class Dashboard::OverviewController < Dashboard::BaseController
  def index
    set_page_title('Dashboard Overview')
    breadcrumb_add('Dashboard')

    # Demo flash messages for testing
    handle_demo_flash if params[:demo_flash]

    # Load dashboard data
    load_dashboard_metrics
    load_recent_activity
    load_quick_stats
  end
  
  private

  def handle_demo_flash
    case params[:demo_flash]
    when 'success'
      flash[:success] = "Data synchronization completed successfully! Your latest sales data from Shopify has been imported."
    when 'error'
      flash[:error] = "Failed to connect to your data source. Please check your API credentials and try again."
    when 'warning'
      flash[:warning] = "Your data sync is taking longer than usual. This may be due to high server load."
    when 'info'
      flash[:notice] = "New features are available! Check out our enhanced reporting dashboard."
    when 'multiple'
      flash[:success] = "Data sync completed successfully!"
      flash[:warning] = "Some records were skipped due to validation errors."
      flash[:notice] = "View the sync report for detailed information."
    end
    redirect_to dashboard_root_path
  end

  def load_dashboard_metrics
    # Placeholder for dashboard metrics
    # In a real application, these would come from your analytics service
    @metrics = {
      total_revenue: calculate_total_revenue,
      active_customers: count_active_customers,
      data_processed: calculate_data_processed,
      conversion_rate: calculate_conversion_rate
    }
  end
  
  def load_recent_activity
    # Placeholder for recent activity
    # This would typically come from an activity log or audit trail
    @recent_activities = [
      {
        type: 'data_sync',
        description: 'Sales data synchronized from Shopify',
        timestamp: 2.hours.ago,
        status: 'completed'
      },
      {
        type: 'report_generated',
        description: 'Monthly revenue report generated',
        timestamp: 4.hours.ago,
        status: 'completed'
      },
      {
        type: 'pipeline_run',
        description: 'Customer segmentation pipeline executed',
        timestamp: 6.hours.ago,
        status: 'completed'
      },
      {
        type: 'alert',
        description: 'Data quality threshold exceeded',
        timestamp: 8.hours.ago,
        status: 'warning'
      }
    ]
  end
  
  def load_quick_stats
    # Placeholder for quick stats
    @quick_stats = {
      data_sources_connected: 5,
      active_pipelines: 12,
      reports_this_month: 28,
      team_members: current_organization&.users&.count || 1
    }
  end
  
  # Placeholder calculation methods
  # In a real application, these would query your actual data
  
  def calculate_total_revenue
    # Placeholder calculation
    rand(100_000..500_000)
  end
  
  def count_active_customers
    # Placeholder calculation
    rand(500..2000)
  end
  
  def calculate_data_processed
    # Placeholder calculation - in GB
    rand(50..200)
  end
  
  def calculate_conversion_rate
    # Placeholder calculation - percentage
    rand(15..35)
  end
end
