module Dashboard
  class PipelinesController < ApplicationController
    before_action :authenticate_user!
    before_action :set_organization
    before_action :set_pipeline, only: [:show, :edit, :update, :destroy, :run, :schedule, :pause, :logs]

    def index
      @pipelines = @organization.pipelines.includes(:data_source).order(created_at: :desc)
      @page_title = "Pipelines"
      @stats = {
        total: @pipelines.count,
        active: @pipelines.where(status: 'active').count,
        running: @pipelines.where(status: 'running').count,
        scheduled: @pipelines.scheduled.count
      }
    end

    def show
      @page_title = @pipeline.name
      @recent_runs = @pipeline.pipeline_runs.recent.limit(5)
      @next_run_time = @pipeline.next_scheduled_run_time if @pipeline.scheduled?
    end

    def new
      @pipeline = @organization.pipelines.build
      @page_title = "New Pipeline"
      @data_sources = @organization.data_sources.active
    end

    def create
      @pipeline = @organization.pipelines.build(pipeline_params)
      
      if @pipeline.save
        redirect_to dashboard_pipeline_path(@pipeline), notice: 'Pipeline was successfully created.'
      else
        @data_sources = @organization.data_sources.active
        render :new
      end
    end

    def edit
      @page_title = "Edit #{@pipeline.name}"
      @data_sources = @organization.data_sources.active
    end

    def update
      if @pipeline.update(pipeline_params)
        redirect_to dashboard_pipeline_path(@pipeline), notice: 'Pipeline was successfully updated.'
      else
        @data_sources = @organization.data_sources.active
        render :edit
      end
    end

    def destroy
      @pipeline.destroy
      redirect_to dashboard_pipelines_path, notice: 'Pipeline was successfully deleted.'
    end

    def run
      result = @pipeline.run!
      
      if result[:success]
        redirect_to dashboard_pipeline_path(@pipeline), notice: 'Pipeline run started successfully.'
      else
        redirect_to dashboard_pipeline_path(@pipeline), alert: result[:error]
      end
    end

    def schedule
      if @pipeline.update(schedule: params[:pipeline][:schedule], status: 'scheduled')
        redirect_to dashboard_pipeline_path(@pipeline), notice: 'Pipeline schedule updated successfully.'
      else
        redirect_to dashboard_pipeline_path(@pipeline), alert: 'Failed to update pipeline schedule.'
      end
    end

    def pause
      if @pipeline.update(status: 'paused')
        redirect_to dashboard_pipeline_path(@pipeline), notice: 'Pipeline paused successfully.'
      else
        redirect_to dashboard_pipeline_path(@pipeline), alert: 'Failed to pause pipeline.'
      end
    end

    def logs
      @runs = @pipeline.pipeline_runs.includes(:step_executions)
      
      # Filter by status if provided
      @runs = @runs.where(status: params[:status]) if params[:status].present?
      
      # Filter by date range
      if params[:date_range].present?
        days = params[:date_range].to_i
        @runs = @runs.where('started_at >= ?', days.days.ago)
      end
      
      @runs = @runs.order(started_at: :desc).page(params[:page]).per(20)
    end

    private

    def set_organization
      @organization = current_user.organization
    end

    def set_pipeline
      @pipeline = @organization.pipelines.find(params[:id])
    end

    def pipeline_params
      params.require(:pipeline).permit(:name, :description, :data_source_id, :schedule, :status, :configuration, :enabled)
    end
  end
end