# frozen_string_literal: true

class LandingController < ApplicationController
  # Landing pages are public - no authentication required
  
  # GET /
  def index
    # Redirect authenticated users to dashboard
    if user_signed_in?
      redirect_to dashboard_root_path
      return
    end
    
    # Testimonials data
    @testimonials = testimonials_data
    
    # Features data
    @features = features_data
    
    # Pricing plans
    @pricing_plans = pricing_plans_data
    
    # Metrics
    @company_metrics = company_metrics_data
  end
  
  # GET /features
  def features
    @features = detailed_features_data
  end
  
  # GET /pricing
  def pricing
    @pricing_plans = pricing_plans_data
  end
  
  # GET /about
  def about
  end
  
  # GET /contact
  def contact
  end

  def help_center
  end
  
  # POST /request-demo
  def request_demo
    # TODO: Implement demo request functionality
    render json: { 
      success: true, 
      message: "Demo functionality coming soon! Please email <NAME_EMAIL>" 
    }
  end
  
  # POST /start-trial
  def start_trial
    # TODO: Implement trial registration
    # For now, redirect to sign up
    render json: { 
      success: true, 
      message: "Please sign up for a free trial",
      redirect_url: new_user_registration_path
    }
  end
  
  private
  
  def demo_params
    params.require(:demo_request).permit(:email, :name, :phone, :preferred_time, :company_name, :message)
  end
  
  def trial_params
    params.require(:trial_registration).permit(:email, :company_name, :company_size)
  end
  
  def testimonials_data
    [
      {
        quote: "Data Reflow helped us achieve 30% downtime reduction and £180,000 annual savings through predictive maintenance insights.",
        metrics: ["30% downtime reduction", "£180K savings"],
        author: {
          name: "Sarah Mitchell",
          title: "Operations Director",
          company: "Precision Manufacturing Ltd",
          avatar: "testimonials/sarah-mitchell.jpg"
        }
      },
      {
        quote: "Our inventory costs dropped 15% while sales increased 20% using Data Reflow's demand forecasting.",
        metrics: ["20% sales increase", "15% cost reduction"],
        author: {
          name: "Marcus Thompson",
          title: "CEO",
          company: "Thompson Retail Group",
          avatar: "testimonials/marcus-thompson.jpg"
        }
      },
      {
        quote: "Patient satisfaction improved 35% and operational efficiency increased 25% since implementing Data Reflow.",
        metrics: ["35% satisfaction improvement", "25% efficiency increase"],
        author: {
          name: "Dr. Emily Chen",
          title: "Practice Manager",
          company: "City Health Partners",
          avatar: "testimonials/emily-chen.jpg"
        }
      }
    ]
  end
  
  def features_data
    [
      {
        title: "Visual ETL Pipeline Builder",
        description: "Drag-and-drop interface with 200+ pre-built connectors",
        benefit: "Reduce integration time by 700+ hours annually",
        icon: "pipeline",
        image: "features/etl-builder.jpg"
      },
      {
        title: "AI-Powered Predictive Analytics",
        description: "Machine learning insights with 89% confidence scoring",
        benefit: "Enable proactive decision-making and risk mitigation",
        icon: "ai-brain",
        image: "features/ai-analytics.jpg"
      },
      {
        title: "Industry-Specific Templates",
        description: "Pre-configured solutions for retail, manufacturing, healthcare",
        benefit: "Achieve ROI within 90 days of implementation",
        icon: "templates",
        image: "features/templates.jpg"
      },
      {
        title: "Real-Time Collaboration",
        description: "Team workspaces with role-based access controls",
        benefit: "Improve cross-departmental alignment by 40%",
        icon: "support",
        image: "features/collaboration.jpg"
      },
      {
        title: "Enterprise Security",
        description: "SOC 2 compliant with end-to-end encryption",
        benefit: "Meet compliance requirements with zero effort",
        icon: "security",
        image: "features/security.jpg"
      },
      {
        title: "REST API & Integrations",
        description: "Comprehensive API with 200+ pre-built connectors",
        benefit: "Connect any data source in minutes, not months",
        icon: "api",
        image: "features/api.jpg"
      },
      {
        title: "Auto-Scaling Infrastructure",
        description: "Cloud-native architecture that scales with your business",
        benefit: "Handle data growth without performance degradation",
        icon: "scalability",
        image: "features/scalability.jpg"
      },
      {
        title: "Smart Data Connectors",
        description: "Intelligent connectors that adapt to schema changes",
        benefit: "Reduce maintenance overhead by 80%",
        icon: "integration",
        image: "features/integration.jpg"
      }
    ]
  end
  
  def pricing_plans_data
    [
      {
        name: "Starter",
        price: 99,
        currency: "$",
        period: "per month",
        target: "Small businesses",
        features: [
          "3 data sources",
          "Basic dashboards",
          "Email support",
          "Monthly reports"
        ],
        highlighted: false,
        cta_text: "Start Free Trial",
        cta_style: "outline"
      },
      {
        name: "Growth",
        price: 299,
        currency: "$",
        period: "per month",
        target: "Growing SMEs",
        features: [
          "10 data sources",
          "AI predictions",
          "Priority support",
          "Real-time alerts"
        ],
        highlighted: true,
        badge: "Most Popular",
        cta_text: "Start Free Trial",
        cta_style: "primary"
      },
      {
        name: "Enterprise",
        price: 699,
        currency: "$",
        period: "per month",
        target: "Established companies",
        features: [
          "Unlimited sources",
          "White-label",
          "Dedicated manager",
          "Custom integrations"
        ],
        highlighted: false,
        cta_text: "Contact Sales",
        cta_style: "outline"
      }
    ]
  end
  
  def company_metrics_data
    {
      customers: "2,500+",
      connectors: "200+",
      uptime: "99.9%",
      roi_days: 90,
      hours_saved: "700+",
      efficiency_improvement: "40%",
      prediction_accuracy: "96%"
    }
  end
  
  def detailed_features_data
    features_data + [
      {
        title: "Advanced Security",
        description: "Enterprise-grade encryption and compliance",
        benefit: "SOC 2, GDPR, and HIPAA compliant",
        icon: "security",
        details: [
          "256-bit AES encryption at rest",
          "TLS 1.3 encryption in transit",
          "Multi-factor authentication",
          "Role-based access control",
          "Audit logs and compliance reports"
        ]
      },
      {
        title: "Custom Integrations",
        description: "Connect any data source with our flexible API",
        benefit: "Integrate with your existing tech stack",
        icon: "api",
        details: [
          "RESTful API with comprehensive documentation",
          "Webhook support for real-time events",
          "Custom connector development",
          "SDK for popular languages",
          "GraphQL endpoint (coming soon)"
        ]
      }
    ]
  end
end
