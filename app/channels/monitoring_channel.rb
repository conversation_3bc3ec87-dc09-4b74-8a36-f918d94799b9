class MonitoringChannel < ApplicationCable::Channel
  def subscribed
    if current_user && current_user.organization
      stream_from "monitoring_#{current_user.organization_id}"
    else
      reject
    end
  end

  def unsubscribed
    stop_all_streams
  end
  
  def acknowledge_alert(data)
    alert = current_user.organization.alerts.find(data['alert_id'])
    
    if alert.acknowledge!(current_user, notes: data['notes'])
      ActionCable.server.broadcast(
        "monitoring_#{current_user.organization_id}",
        {
          type: 'alert_acknowledged',
          alert: {
            id: alert.id,
            acknowledged_by: current_user.full_name,
            acknowledged_at: alert.acknowledged_at
          }
        }
      )
    end
  end
  
  def resolve_alert(data)
    alert = current_user.organization.alerts.find(data['alert_id'])
    
    if alert.resolve!(current_user, notes: data['notes'])
      ActionCable.server.broadcast(
        "monitoring_#{current_user.organization_id}",
        {
          type: 'alert_resolved',
          alert: {
            id: alert.id,
            resolved_by: current_user.full_name,
            resolved_at: alert.resolved_at
          }
        }
      )
    end
  end
  
  def request_metrics_update(data)
    # Broadcast current metrics
    metrics = gather_current_metrics(data['metric_types'])
    
    transmit({
      type: 'metrics_update',
      metrics: metrics
    })
  end
  
  private
  
  def gather_current_metrics(types = [])
    organization = current_user.organization
    metrics = {}
    
    types = ['system', 'syncs', 'alerts'] if types.blank?
    
    if types.include?('system')
      metrics[:system] = {
        active_syncs: organization.data_sources.syncing.count,
        queue_depth: 0, # Would connect to job queue
        last_updated: Time.current
      }
    end
    
    if types.include?('syncs')
      recent_syncs = organization.sync_logs.recent.limit(10)
      metrics[:recent_syncs] = recent_syncs.map do |sync|
        {
          id: sync.id,
          data_source_name: sync.data_source.name,
          status: sync.status,
          started_at: sync.started_at,
          duration: sync.duration_seconds,
          records_processed: sync.records_processed
        }
      end
    end
    
    if types.include?('alerts')
      metrics[:alert_counts] = {
        critical: organization.alerts.unresolved.critical.count,
        warning: organization.alerts.unresolved.by_severity('warning').count,
        info: organization.alerts.unresolved.by_severity('info').count
      }
    end
    
    metrics
  end
end