class AnalyticsChannel < ApplicationCable::Channel
  def subscribed
    if current_user && current_user.organization
      stream_for current_user.organization
    else
      reject
    end
  end

  def unsubscribed
    # Any cleanup needed when channel is unsubscribed
  end
  
  # Broadcast analytics updates to all connected users in the organization
  def self.broadcast_update(organization, data)
    broadcast_to(organization, {
      type: 'analytics_update',
      data: data,
      timestamp: Time.current
    })
  end
  
  # Broadcast specific metric updates
  def self.broadcast_metric_update(organization, metric_name, value)
    broadcast_to(organization, {
      type: 'metric_update',
      metric: metric_name,
      value: value,
      timestamp: Time.current
    })
  end
  
  # Broadcast sync status updates
  def self.broadcast_sync_update(organization, source_id, status)
    broadcast_to(organization, {
      type: 'sync_update',
      source_id: source_id,
      status: status,
      timestamp: Time.current
    })
  end
end