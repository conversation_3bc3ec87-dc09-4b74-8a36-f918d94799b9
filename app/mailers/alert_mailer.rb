class AlertMailer < ApplicationMailer
  def alert_triggered(alert, recipients)
    @alert = alert
    @organization = alert.organization
    @data_source = alert.data_source
    @monitoring_rule = alert.monitoring_rule
    
    subject = "[#{@alert.severity.upcase}] #{@alert.title}"
    
    mail(
      to: recipients,
      subject: subject,
      template_path: 'alert_mailer',
      template_name: 'alert_triggered'
    )
  end
  
  def alert_resolved(alert, recipients)
    @alert = alert
    @organization = alert.organization
    @data_source = alert.data_source
    @monitoring_rule = alert.monitoring_rule
    @resolved_by = alert.resolved_by
    
    subject = "[RESOLVED] #{@alert.title}"
    
    mail(
      to: recipients,
      subject: subject,
      template_path: 'alert_mailer',
      template_name: 'alert_resolved'
    )
  end
  
  def daily_alert_summary(organization, recipients)
    @organization = organization
    @date = Date.current
    
    # Get alerts from the last 24 hours
    @new_alerts = @organization.alerts
      .where(triggered_at: 24.hours.ago..Time.current)
      .includes(:data_source, :monitoring_rule)
      .order(severity: :desc, triggered_at: :desc)
    
    @active_alerts = @organization.alerts
      .active
      .includes(:data_source, :monitoring_rule)
      .order(severity: :desc, triggered_at: :desc)
    
    @resolved_alerts = @organization.alerts
      .resolved
      .where(resolved_at: 24.hours.ago..Time.current)
      .includes(:data_source, :monitoring_rule, :resolved_by)
      .order(resolved_at: :desc)
    
    return if @new_alerts.empty? && @active_alerts.empty? && @resolved_alerts.empty?
    
    mail(
      to: recipients,
      subject: "Daily Alert Summary - #{@organization.name}",
      template_path: 'alert_mailer',
      template_name: 'daily_summary'
    )
  end
end