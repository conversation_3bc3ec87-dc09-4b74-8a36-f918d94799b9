require 'csv'
require 'open-uri'
require 'net/http'

module Connectors
  class CsvConnector < BaseConnector
    SUPPORTED_ENCODINGS = %w[UTF-8 ISO-8859-1 Windows-1252 ASCII-8BIT].freeze
    
    def max_file_size
      @max_file_size ||= DataSourceConfig.csv['max_file_size'].to_i
    end
    
    def chunk_size
      @chunk_size ||= DataSourceConfig.csv['chunk_size'].to_i
    end
    
    def sample_read_size
      @sample_read_size ||= DataSourceConfig.csv['sample_read_size'].to_i
    end
    
    def batch_size
      @batch_size ||= DataSourceConfig.csv['batch_size'].to_i
    end
    
    def test_connection
      Rails.logger.info "CSV test_connection - connection_settings: #{connection_settings.inspect}"
      validate_required_settings(:file_path)
      
      file_info = analyze_file
      
      {
        success: true,
        message: "Successfully connected to CSV file",
        details: {
          file_size: file_info[:size],
          encoding: file_info[:encoding],
          delimiter: file_info[:delimiter],
          headers: file_info[:headers],
          row_count_estimate: file_info[:row_count_estimate]
        }
      }
    rescue => e
      handle_error(e)
    end
    
    def fetch_schema
      Rails.logger.info "CSV fetch_schema - connection_settings: #{connection_settings.inspect}"
      Rails.logger.info "CSV fetch_schema - file_path: #{connection_settings['file_path']}"
      validate_required_settings(:file_path)
      
      headers, sample_data = read_file_headers_and_sample
      
      schema = headers.map do |header|
        column_data = sample_data.map { |row| row[header] }.compact
        
        {
          name: header,
          type: infer_column_type(column_data),
          nullable: column_data.size < sample_data.size,
          sample_values: column_data.first(5)
        }
      end
      
      {
        success: true,
        schema: {
          columns: schema,
          row_count_estimate: estimate_row_count,
          file_info: analyze_file
        },
        metadata: {
          columns: headers,
          row_count: schema.length
        }
      }
    rescue => e
      handle_error(e)
    end
    
    def fetch_sample_data(limit: 10)
      validate_required_settings(:file_path)
      
      rows = []
      headers = nil
      
      begin
        file_path = get_file_path
        options = csv_options
        
        Rails.logger.info "fetch_sample_data - Reading CSV file: #{file_path}"
        
        row_count = 0
        CSV.foreach(file_path, **options) do |row|
          if headers.nil?
            headers = row.headers
          end
          
          if row_count < limit
            rows << row.to_h
          else
            break
          end
          
          row_count += 1
        end
      rescue => e
        Rails.logger.error "Error in fetch_sample_data: #{e.message}"
        Rails.logger.error e.backtrace.first(5).join("\n")
        return handle_error(e)
      end
      
      {
        success: true,
        data: rows,
        metadata: {
          columns: headers,
          total_rows: rows.size,
          file_path: connection_settings['file_path']
        }
      }
    end
    
    def sync
      validate_required_settings(:file_path)
      
      # Create sync log
      file_size = begin
        File.size(get_file_path)
      rescue
        nil
      end
      
      sync_log = data_source.sync_logs.create!(
        organization: data_source.organization,
        sync_type: 'full',
        status: 'running',
        started_at: Time.current,
        metadata: {
          file_path: connection_settings['file_path'],
          file_size: file_size
        }
      )
      
      begin
        # Get external_id field from settings or auto-detect
        external_id_field = connection_settings['external_id_field'] || detect_external_id_field
        
        # Process file in chunks
        process_csv_file(sync_log, external_id_field)
        
        # Mark sync as completed
        sync_log.mark_as_completed!
        
        # Update data source metadata
        data_source.update!(
          last_sync_at: Time.current,
          row_count: data_source.imported_records.count,
          metadata: data_source.metadata.merge(
            last_sync_status: 'success',
            last_sync_duration: sync_log.duration_seconds
          )
        )
        
        {
          success: true,
          message: "Successfully imported #{sync_log.records_imported} records",
          sync_log_id: sync_log.id,
          row_count: sync_log.records_imported,
          duration: sync_log.duration_seconds,
          metadata: {
            records_processed: sync_log.records_processed,
            records_imported: sync_log.records_imported,
            records_updated: sync_log.metadata['records_updated'] || 0,
            records_skipped: sync_log.records_skipped,
            records_failed: sync_log.records_failed
          }
        }
      rescue => e
        Rails.logger.error "CSV sync failed: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
        
        sync_log.mark_as_failed!(e.message, {
          error_class: e.class.name,
          backtrace: e.backtrace.first(10)
        })
        
        data_source.update!(
          status: 'error',
          error_message: e.message,
          metadata: data_source.metadata.merge(
            last_sync_status: 'failed',
            last_sync_error: e.message
          )
        )
        
        handle_error(e)
      end
    end
    
    private
    
    def analyze_file
      file_path = get_file_path
      
      if remote_file?
        analyze_remote_file(file_path)
      else
        analyze_local_file(file_path)
      end
    end
    
    def get_file_path
      # Use presence to handle empty strings
      path = connection_settings['file_url'].presence || connection_settings['file_path'].presence
      Rails.logger.info "CSV Connector - get_file_path: #{path}"
      Rails.logger.info "Connection settings: #{connection_settings.inspect}"
      path
    end
    
    def analyze_remote_file(url)
      response = URI.open(url, read_timeout: 30)
      
      {
        size: response.size || 0,
        encoding: detect_encoding(response),
        delimiter: detect_delimiter(response),
        headers: detect_headers(response),
        row_count_estimate: estimate_row_count_from_sample(response)
      }
    end
    
    def analyze_local_file(path)
      # Ensure we have a valid file path
      raise I18n.t('data_source.errors.file_path_empty') if path.blank?
      
      # Don't use File.absolute_path if path is already absolute
      absolute_path = path.start_with?('/') ? path : File.absolute_path(path)
      
      Rails.logger.info "Analyzing CSV file at: #{absolute_path}"
      Rails.logger.info "File exists? #{File.exist?(absolute_path)}"
      Rails.logger.info "Is directory? #{File.directory?(absolute_path)}" if File.exist?(absolute_path)
      
      raise I18n.t('data_source.errors.file_not_found', path: absolute_path) unless File.exist?(absolute_path)
      raise I18n.t('data_source.errors.is_directory', path: absolute_path) if File.directory?(absolute_path)
      file_size = File.size(absolute_path)
      if file_size > max_file_size
        size_mb = (file_size / 1.megabyte).round
        max_mb = (max_file_size / 1.megabyte).round
        raise I18n.t('data_source.errors.file_too_large', size: size_mb, max: max_mb)
      end
      
      # Read sample with binary encoding first
      sample = File.read(absolute_path, sample_read_size, mode: 'rb')
      detected_encoding = detect_encoding(sample)
      
      # Re-read with detected encoding
      sample_encoded = sample.force_encoding(detected_encoding)
      
      {
        size: File.size(absolute_path),
        encoding: detected_encoding,
        delimiter: detect_delimiter(sample_encoded),
        headers: detect_headers(sample_encoded),
        row_count_estimate: estimate_row_count
      }
    end
    
    def detect_encoding(sample)
      # Ensure sample is in binary format
      sample = sample.force_encoding('BINARY') if sample.encoding != Encoding::ASCII_8BIT
      
      # Try to detect encoding from BOM or content
      return 'UTF-8' if sample.start_with?("\xEF\xBB\xBF".force_encoding('BINARY'))
      return 'UTF-16LE' if sample.start_with?("\xFF\xFE".force_encoding('BINARY'))
      return 'UTF-16BE' if sample.start_with?("\xFE\xFF".force_encoding('BINARY'))
      
      # Try different encodings
      SUPPORTED_ENCODINGS.each do |encoding|
        begin
          test_sample = sample.dup.force_encoding(encoding)
          if test_sample.valid_encoding?
            # Additional check: try to split into lines
            test_sample.lines.first
            return encoding
          end
        rescue
          next
        end
      end
      
      'UTF-8' # Default fallback
    end
    
    def detect_delimiter(sample)
      # Ensure sample is properly encoded as UTF-8 for delimiter detection
      if sample.encoding == Encoding::ASCII_8BIT
        sample = sample.force_encoding('UTF-8')
      end
      
      lines = sample.lines.first(10)
      
      # Return comma if no lines found
      return ',' if lines.empty?
      
      delimiters = {
        ',' => 0,
        ';' => 0,
        "\t" => 0,
        '|' => 0
      }
      
      lines.each do |line|
        delimiters.each_key do |delimiter|
          delimiters[delimiter] += line.count(delimiter)
        end
      end
      
      # Find the delimiter with the highest count
      best_delimiter = delimiters.max_by { |_, count| count }
      
      # Return comma if no delimiter found or count is 0
      best_delimiter && best_delimiter[1] > 0 ? best_delimiter[0] : ','
    end
    
    def detect_headers(sample)
      delimiter = connection_settings['delimiter'] || detect_delimiter(sample)
      
      # Ensure delimiter is never empty
      delimiter = ',' if delimiter.blank?
      
      Rails.logger.info "detect_headers - delimiter: #{delimiter.inspect}"
      
      first_line = sample.lines.first
      return [] if first_line.blank?
      
      CSV.parse_line(first_line, col_sep: delimiter)
    end
    
    def read_file_headers_and_sample
      headers = []
      sample_data = []
      
      Rails.logger.info "read_file_headers_and_sample - starting"
      
      begin
        file_path = get_file_path
        options = csv_options
        
        Rails.logger.info "Reading CSV file directly: #{file_path}"
        
        row_count = 0
        CSV.foreach(file_path, **options) do |row|
          if headers.empty?
            headers = row.headers
            Rails.logger.info "Headers: #{headers.inspect}"
          end
          
          if row_count < DataSourceConfig.csv['row_estimate_lines']
            sample_data << row.to_h
          else
            break
          end
          
          row_count += 1
        end
      rescue => e
        Rails.logger.error "Error in read_file_headers_and_sample: #{e.message}"
        Rails.logger.error "Error class: #{e.class}"
        Rails.logger.error e.backtrace.first(5).join("\n")
        raise
      end
      
      Rails.logger.info "read_file_headers_and_sample - found #{headers.length} headers and #{sample_data.length} sample rows"
      
      [headers, sample_data]
    end
    
    def read_csv(&block)
      options = csv_options
      
      Rails.logger.info "read_csv - options: #{options.inspect}"
      Rails.logger.info "read_csv - remote_file?: #{remote_file?}"
      
      if remote_file?
        read_remote_csv(options, &block)
      else
        read_local_csv(options, &block)
      end
    end
    
    def read_remote_csv(options)
      file_path = get_file_path
      
      URI.open(file_path, read_timeout: 300) do |file|
        CSV.parse(file, **options) do |row|
          yield row  # CSV.parse already returns CSV::Row objects when headers: true
        end
      end
    end
    
    def read_local_csv(options)
      file_path = get_file_path
      
      # Don't use File.absolute_path if path is already absolute or empty
      if file_path.blank?
        raise I18n.t('data_source.errors.file_path_empty')
      end
      
      absolute_path = file_path.start_with?('/') ? file_path : File.absolute_path(file_path)
      
      Rails.logger.info "Reading CSV from: #{absolute_path}"
      Rails.logger.info "File exists? #{File.exist?(absolute_path)}"
      Rails.logger.info "Is directory? #{File.directory?(absolute_path)}" if File.exist?(absolute_path)
      
      unless File.exist?(absolute_path)
        raise I18n.t('data_source.errors.csv_not_found', path: absolute_path)
      end
      
      if File.directory?(absolute_path)
        raise I18n.t('data_source.errors.is_directory', path: absolute_path)
      end
      
      CSV.foreach(absolute_path, **options) do |row|
        yield row
      end
    end
    
    def csv_options
      Rails.logger.info "csv_options - connection_settings: #{connection_settings.inspect}"
      
      # Since we already have delimiter and encoding set, use them directly
      delimiter = connection_settings['delimiter'].presence
      encoding = connection_settings['encoding'].presence
      
      # Only try to detect if they're not already set
      delimiter ||= begin
        detect_delimiter_from_file
      rescue => e
        Rails.logger.warn "Failed to detect delimiter: #{e.message}, using comma"
        ','
      end
      
      encoding ||= begin
        detect_encoding_from_file
      rescue => e
        Rails.logger.warn "Failed to detect encoding: #{e.message}, using UTF-8"
        'UTF-8'
      end
      
      # Handle encoding conversion for CSV
      csv_encoding = if encoding == 'ASCII-8BIT'
        'BINARY:UTF-8'  # Convert from binary to UTF-8
      else
        "#{encoding}:UTF-8"  # Convert from source encoding to UTF-8
      end
      
      # Ensure delimiter is never empty
      final_delimiter = delimiter.presence || ','
      
      options = {
        headers: connection_settings['has_headers'] != false,
        col_sep: final_delimiter,
        encoding: csv_encoding,
        skip_blanks: true,
        liberal_parsing: true
      }
      
      Rails.logger.info "csv_options - final options: #{options.inspect}"
      options
    end
    
    def detect_delimiter_from_file
      file_path = get_file_path
      
      if file_path.blank?
        Rails.logger.warn "File path is empty, defaulting to comma delimiter"
        return ','
      end
      
      sample = if remote_file?
        URI.open(file_path).read(10_000)
      else
        absolute_path = file_path.start_with?('/') ? file_path : File.absolute_path(file_path)
        
        unless File.exist?(absolute_path) && !File.directory?(absolute_path)
          Rails.logger.warn "Invalid file path for delimiter detection: #{absolute_path}, defaulting to comma"
          return ','
        end
        
        # Read as binary first, then convert
        File.read(absolute_path, sample_read_size, mode: 'rb')
      end
      
      detect_delimiter(sample)
    end
    
    def detect_encoding_from_file
      file_path = get_file_path
      
      if file_path.blank?
        Rails.logger.warn "File path is empty, defaulting to UTF-8 encoding"
        return 'UTF-8'
      end
      
      sample = if remote_file?
        URI.open(file_path).read(10_000)
      else
        absolute_path = file_path.start_with?('/') ? file_path : File.absolute_path(file_path)
        
        unless File.exist?(absolute_path) && !File.directory?(absolute_path)
          Rails.logger.warn "Invalid file path for encoding detection: #{absolute_path}, defaulting to UTF-8"
          return 'UTF-8'
        end
        
        # Read as binary for encoding detection
        File.read(absolute_path, sample_read_size, mode: 'rb')
      end
      
      detect_encoding(sample)
    end
    
    def remote_file?
      file_path_or_url = connection_settings['file_url'] || connection_settings['file_path']
      file_path_or_url&.start_with?('http://', 'https://', 'ftp://', 's3://')
    end
    
    def estimate_row_count
      if remote_file?
        estimate_row_count_from_remote
      else
        estimate_row_count_from_local
      end
    end
    
    def estimate_row_count_from_local
      file_path = get_file_path
      
      return 0 if file_path.blank?
      
      absolute_path = file_path.start_with?('/') ? file_path : File.absolute_path(file_path)
      
      unless File.exist?(absolute_path) && !File.directory?(absolute_path)
        Rails.logger.warn "Invalid file path for row count estimation: #{absolute_path}"
        return 0
      end
      
      file_size = File.size(absolute_path)
      
      # Read first N lines to get average row size
      sample_size = 0
      line_count = 0
      
      File.foreach(absolute_path).with_index do |line, index|
        break if index >= DataSourceConfig.csv['row_estimate_lines']
        sample_size += line.bytesize
        line_count += 1
      end
      
      return 0 if line_count == 0
      
      avg_row_size = sample_size.to_f / line_count
      (file_size / avg_row_size).to_i
    end
    
    def estimate_row_count_from_remote
      # For remote files, we can't easily estimate without downloading
      # Return nil to indicate unknown
      nil
    end
    
    def estimate_row_count_from_sample(sample)
      lines = sample.lines
      return 0 if lines.empty?
      
      # Rough estimate based on sample
      avg_line_size = sample.bytesize.to_f / lines.size
      total_size = sample.respond_to?(:size) ? sample.size : sample.bytesize
      
      (total_size / avg_line_size).to_i
    end
    
    def infer_column_type(values)
      return 'string' if values.empty?
      
      # Check if all values match a specific type
      if values.all? { |v| v.nil? || v.to_s.match?(/^\d+$/) }
        'integer'
      elsif values.all? { |v| v.nil? || v.to_s.match?(/^\d*\.?\d+$/) }
        'decimal'
      elsif values.all? { |v| v.nil? || v.to_s.match?(/^(true|false|1|0|yes|no)$/i) }
        'boolean'
      elsif values.all? { |v| v.nil? || valid_date?(v) }
        'date'
      elsif values.all? { |v| v.nil? || valid_datetime?(v) }
        'datetime'
      else
        'string'
      end
    end
    
    def valid_date?(value)
      Date.parse(value.to_s)
      true
    rescue
      false
    end
    
    def valid_datetime?(value)
      DateTime.parse(value.to_s)
      true
    rescue
      false
    end
    
    def process_row(row)
      # Apply transformations if configured
      transformed = row.to_h
      
      if connection_settings['transformations'].present?
        apply_transformations(transformed)
      end
      
      # Add metadata
      transformed['_imported_at'] = Time.current
      transformed['_source_file'] = get_file_path
      transformed['_row_number'] = row.lineno if row.respond_to?(:lineno)
      
      transformed
    end
    
    def apply_transformations(row)
      transformations = connection_settings['transformations']
      
      transformations.each do |field, rules|
        next unless row.key?(field)
        
        value = row[field]
        
        rules.each do |rule|
          case rule['type']
          when 'trim'
            value = value&.strip
          when 'lowercase'
            value = value&.downcase
          when 'uppercase'
            value = value&.upcase
          when 'replace'
            value = value&.gsub(rule['from'], rule['to'])
          when 'date_format'
            value = format_date(value, rule['format'])
          when 'number_format'
            value = format_number(value, rule['decimals'])
          end
        end
        
        row[field] = value
      end
    end
    
    def format_date(value, format)
      return nil if value.nil? || value.to_s.empty?
      
      date = Date.parse(value.to_s)
      date.strftime(format)
    rescue
      value
    end
    
    def format_number(value, decimals)
      return nil if value.nil? || value.to_s.empty?
      
      number = value.to_f
      decimals ? number.round(decimals) : number.to_i
    rescue
      value
    end
    
    def create_staging_table
      # Implementation depends on target database
      # This is a placeholder - actual implementation would create
      # a temporary table in the target database
      "staging_#{data_source.id}_#{Time.current.to_i}"
    end
    
    def insert_batch(table, batch)
      # Implementation depends on target database
      # This would bulk insert the batch into the staging table
      {
        inserted: batch.size,
        errors: []
      }
    end
    
    def finalize_sync(staging_table)
      # Move data from staging to final table
      # Implementation depends on target database
    end
    
    def cleanup_staging_table(staging_table)
      # Drop the staging table
      # Implementation depends on target database
    end
    
    def generate_warnings(errors)
      warnings = []
      
      if errors.any? { |e| e.include?('encoding') }
        warnings << I18n.t('data_source.warnings.encoding_issues')
      end
      
      if errors.any? { |e| e.include?('parse') }
        warnings << I18n.t('data_source.warnings.parse_issues')
      end
      
      if errors.size >= DataSourceConfig.csv['error_limit']
        warnings << I18n.t('data_source.warnings.error_limit_reached')
      end
      
      warnings
    end
    
    def handle_error(error)
      case error
      when Errno::ENOENT
        { success: false, error: "File not found: #{connection_settings['file_path']}" }
      when URI::Error, Net::HTTPError, SocketError
        { success: false, error: "Failed to download file: #{error.message}" }
      when CSV::MalformedCSVError
        { success: false, error: "Invalid CSV format: #{error.message}" }
      when Encoding::UndefinedConversionError, Encoding::InvalidByteSequenceError
        { success: false, error: "Encoding error: #{error.message}. Try specifying a different encoding." }
      else
        { success: false, error: error.message }
      end
    end
    
    def process_csv_file(sync_log, external_id_field)
      file_path = get_file_path
      options = csv_options
      
      Rails.logger.info "Processing CSV file: #{file_path}"
      
      batch = []
      batch_size = self.batch_size # Process records in configured batch size
      row_number = 0
      
      records_updated = 0
      records_created = 0
      
      CSV.foreach(file_path, **options) do |row|
        row_number += 1
        
        begin
          record_data = row.to_h
          external_id = external_id_field ? record_data[external_id_field] : nil
          
          # Add row metadata
          record_data['_row_number'] = row_number
          record_data['_imported_at'] = Time.current.iso8601
          
          batch << {
            record_data: record_data,
            external_id: external_id,
            row_number: row_number
          }
          
          # Process batch when it reaches the size limit
          if batch.size >= batch_size
            result = process_batch(sync_log, batch)
            records_created += result[:created]
            records_updated += result[:updated]
            batch.clear
            
            # Log progress
            Rails.logger.info "Processed #{row_number} rows..."
          end
          
          sync_log.increment_processed!
        rescue => e
          Rails.logger.error "Error processing row #{row_number}: #{e.message}"
          sync_log.increment_failed!
          
          # Store error details
          sync_log.error_details['failed_rows'] ||= []
          sync_log.error_details['failed_rows'] << {
            row_number: row_number,
            error: e.message,
            data: row.to_h
          }
          sync_log.save!
        end
      end
      
      # Process remaining batch
      if batch.any?
        result = process_batch(sync_log, batch)
        records_created += result[:created]
        records_updated += result[:updated]
      end
      
      # Store final counts
      sync_log.add_metadata('records_created', records_created)
      sync_log.add_metadata('records_updated', records_updated)
      
      Rails.logger.info "CSV processing completed. Created: #{records_created}, Updated: #{records_updated}"
    end
    
    def process_batch(sync_log, batch)
      created = 0
      updated = 0
      skipped = 0
      
      ActiveRecord::Base.transaction do
        batch.each do |item|
          result = ImportedRecord.upsert_record(
            data_source,
            item[:record_data],
            item[:external_id],
            sync_log
          )
          
          case result
          when :created
            created += 1
            sync_log.increment_imported!
          when :updated
            updated += 1
            sync_log.increment_imported!
          when :skipped
            skipped += 1
            sync_log.increment_skipped!
          end
        end
      end
      
      { created: created, updated: updated, skipped: skipped }
    end
    
    def detect_external_id_field
      # Try to detect common ID field names
      headers, _ = read_file_headers_and_sample
      
      id_patterns = ['id', 'ID', 'Id', '_id', 'uuid', 'UUID', 'guid', 'GUID', 'key', 'KEY']
      
      headers.find { |header| id_patterns.include?(header) } || headers.first
    end
  end
end