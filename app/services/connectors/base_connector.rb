module Connectors
  class BaseConnector
    attr_reader :data_source, :connection_settings
    
    def initialize(data_source)
      @data_source = data_source
      @connection_settings = data_source.connection_settings
    end
    
    # Test if the connection is valid
    def test_connection
      raise NotImplementedError, "#{self.class.name} must implement test_connection"
    end
    
    # Sync data from the source
    def sync
      raise NotImplementedError, "#{self.class.name} must implement sync"
    end
    
    # Fetch the schema/structure of the data source
    def fetch_schema
      raise NotImplementedError, "#{self.class.name} must implement fetch_schema"
    end
    
    # Fetch sample data for preview
    def fetch_sample_data(limit: 10)
      raise NotImplementedError, "#{self.class.name} must implement fetch_sample_data"
    end
    
    # Execute a query (for database sources)
    def execute_query(query)
      raise NotImplementedError, "#{self.class.name} must implement execute_query"
    end
    
    protected
    
    # Common error handling
    def handle_connection_error(error)
      Rails.logger.error "Connection error for DataSource #{data_source.id}: #{error.message}"
      Rails.logger.error error.backtrace.join("\n")
      
      {
        success: false,
        error: error.message,
        error_details: {
          class: error.class.name,
          message: error.message,
          timestamp: Time.current
        }
      }
    end
    
    # Log sync activity
    def log_sync_activity(action, details = {})
      Rails.logger.info "DataSource #{data_source.id} - #{action}: #{details.to_json}"
    end
    
    # Validate required connection settings
    def validate_required_settings(*required_keys)
      # Convert both to strings for comparison
      existing_keys = connection_settings.keys.map(&:to_s)
      required = required_keys.map(&:to_s)
      missing_keys = required - existing_keys
      
      if missing_keys.any?
        Rails.logger.error "Connection settings keys: #{existing_keys.inspect}"
        Rails.logger.error "Required keys: #{required.inspect}"
        Rails.logger.error "Missing keys: #{missing_keys.inspect}"
        raise ArgumentError, "Missing required connection settings: #{missing_keys.join(', ')}"
      end
      
      # Also check if the required values are not empty
      required_keys.each do |key|
        value = connection_settings[key.to_s]
        if value.blank?
          Rails.logger.error "Empty value for required key: #{key}"
          Rails.logger.error "Connection settings: #{connection_settings.inspect}"
          raise ArgumentError, "Empty value for required connection setting: #{key}"
        end
      end
    end
    
    # Sanitize sensitive data for logging
    def sanitized_settings
      settings = connection_settings.dup
      
      # Remove sensitive fields
      %w[password api_key secret_key access_token refresh_token].each do |key|
        settings[key] = '[REDACTED]' if settings[key].present?
      end
      
      settings
    end
  end
end