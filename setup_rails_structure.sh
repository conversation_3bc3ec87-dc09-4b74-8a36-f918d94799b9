#!/bin/bash

# Create Rails directory structure
mkdir -p app/assets/javascripts
mkdir -p app/assets/stylesheets
mkdir -p app/assets/images
mkdir -p app/channels
mkdir -p app/controllers/concerns
mkdir -p app/helpers
mkdir -p app/javascript/controllers
mkdir -p app/javascript/packs
mkdir -p app/jobs
mkdir -p app/mailers
mkdir -p app/models/concerns
mkdir -p app/views/layouts
mkdir -p app/views/landing
mkdir -p app/views/shared
mkdir -p bin
mkdir -p config/environments
mkdir -p config/initializers
mkdir -p config/locales
mkdir -p db/migrate
mkdir -p lib/assets
mkdir -p lib/tasks
mkdir -p log
mkdir -p public
mkdir -p spec/controllers
mkdir -p spec/models
mkdir -p spec/helpers
mkdir -p spec/views
mkdir -p spec/support
mkdir -p tmp/cache
mkdir -p tmp/pids
mkdir -p tmp/sessions
mkdir -p tmp/sockets
mkdir -p vendor/assets/javascripts
mkdir -p vendor/assets/stylesheets

echo "Rails directory structure created!"
