# DataReflow

A powerful data integration and analytics platform designed for Small and Medium Enterprises (SMEs). DataReflow provides a no-code solution for businesses to connect, transform, and analyze data from multiple sources without requiring technical expertise or enterprise-level budgets.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Architecture](#architecture)
- [Monitoring System](#monitoring-system)
- [Setup Instructions](#setup-instructions)
- [Development](#development)
- [Testing](#testing)
- [Deployment](#deployment)
- [Contributing](#contributing)
- [License](#license)

## Overview

DataReflow empowers SMEs to:
- Connect and sync data from multiple sources (databases, APIs, files)
- Build ETL pipelines without coding
- Monitor data quality and system performance
- Get real-time alerts on issues
- Make data-driven decisions with integrated analytics

### Target Audience
- Companies with 5-500 employees
- Organizations with multiple disconnected data systems
- Teams with limited technical resources
- Businesses seeking enterprise-level data capabilities at SME prices

## Features

### 📊 Data Source Management
- **Multi-Source Connectivity**: PostgreSQL, MySQL, CSV, JSON, APIs, S3, webhooks
- **Automated Syncing**: Configure sync schedules from every 5 minutes to monthly
- **Schema Discovery**: Automatic detection of tables, columns, and data types
- **Sample Preview**: View sample data before full sync
- **Connection Testing**: Real-time validation of credentials and permissions

### 🔄 ETL Pipeline System
- **Visual Pipeline Builder**: Drag-and-drop interface for data transformations
- **Flexible Scheduling**: Run pipelines on demand or on schedule
- **Step Execution**: Track individual transformation steps
- **Error Handling**: Graceful failure management with detailed logs
- **Pipeline Templates**: Pre-built templates for common workflows

### 🚨 Performance Monitoring & Alerting

#### Real-Time Monitoring
- **Performance Metrics**: Track sync duration, throughput, CPU, memory usage
- **Custom Alert Rules**: Define conditions for different alert types:
  - Sync failures (consecutive failures, failure rates)
  - Performance degradation (duration, resource usage)
  - Data quality issues (error rates, missing data)
  - Uptime monitoring (source availability)
- **Multi-Channel Notifications**: 
  - Email alerts with rich HTML formatting
  - Slack integration with color-coded messages
  - Webhook support for custom integrations
- **Alert Management**: Acknowledge, resolve, and track alert history

#### Monitoring Dashboard
- 24-hour aggregate metrics overview
- Real-time Chart.js visualizations
- Active alert management interface
- Pipeline status monitoring
- Historical trend analysis

### 👥 Multi-Tenancy & Security
- **Organization Isolation**: Complete data separation between organizations
- **Role-Based Access**: Admin, analyst, and viewer roles
- **Encrypted Storage**: Secure credential and configuration storage
- **SSL/TLS Enforcement**: Encrypted data transmission
- **Content Security Policy**: XSS and injection protection

### 📈 Analytics & Reporting
- **Revenue Analytics**: Track business metrics
- **Customer Insights**: Understand user behavior
- **Data Processing Metrics**: Monitor platform usage
- **Custom Reports**: Build tailored reports
- **Export Capabilities**: Download data in multiple formats

### 💼 Pricing Plans
- **Starter**: Up to 500K rows/month
- **Professional**: Up to 5M rows/month
- **Enterprise**: Unlimited with priority support

## Architecture

### Technology Stack

#### Backend
- **Framework**: Rails 8.0.2 with Ruby 3.4.3
- **Database**: PostgreSQL with multi-database architecture
  - Primary: Application data
  - Cache: Solid Cache for performance
  - Queue: Solid Queue for background jobs
  - Cable: Solid Cable for WebSockets
- **Background Jobs**: Solid Queue (database-backed)
- **WebSockets**: ActionCable for real-time updates

#### Frontend
- **UI Framework**: Hotwire (Turbo + Stimulus)
- **Styling**: Tailwind CSS
- **JavaScript**: Import Maps (no bundler)
- **Charts**: Chart.js for data visualization
- **Icons**: Heroicons

#### Infrastructure
- **Container**: Docker with multi-stage builds
- **Asset Serving**: Thruster for optimized delivery
- **Deployment**: Kamal for container orchestration
- **Monitoring**: Built-in performance tracking

### High-Level Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│   Web Browser   │────▶│   Rails App     │────▶│   PostgreSQL    │
│  (Hotwire/JS)   │◀────│  (Puma Server)  │◀────│   (Primary DB)  │
└─────────────────┘     └─────────────────┘     └─────────────────┘
         │                       │                         │
         │                       │                         │
         ▼                       ▼                         ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  ActionCable    │     │  Solid Queue    │     │  Solid Cache    │
│  (WebSockets)   │     │ (Background Jobs)│     │   (Caching)     │
└─────────────────┘     └─────────────────┘     └─────────────────┘
         │                       │
         │                       │
         ▼                       ▼
┌─────────────────┐     ┌─────────────────┐
│ Monitoring      │     │ Data Sources    │
│ Dashboard       │     │ (APIs/DBs/Files)│
└─────────────────┘     └─────────────────┘
```

### Key Design Patterns
- **Service Objects**: Business logic encapsulation
- **Connector Pattern**: Pluggable data source adapters
- **Observer Pattern**: Real-time alert broadcasting
- **Strategy Pattern**: Different monitoring rule types
- **Job Pattern**: Asynchronous processing for heavy operations

## Monitoring System

### 🏗️ Core Architecture

The monitoring system follows a **reactive monitoring pattern** with real-time capabilities:

```
Data Sources → Sync Operations → Monitoring Rules → Alerts → Notifications
                      ↓                    ↓           ↓
              Performance Metrics    Rule Evaluation  Multi-Channel
                                                      Delivery
```

### 🔍 Key Components

**1. Alert System Core**
- **Alerts** track issues with severity levels (critical/high/medium/low)
- **MonitoringRules** define when to trigger alerts:
  - Sync failures (consecutive failures, failure rates)
  - Performance issues (duration thresholds, resource usage)
  - Data quality (error rates, missing data)
  - Uptime monitoring (source availability)
- Auto-acknowledgment and resolution workflows
- Cooldown periods prevent alert spam

**2. Performance Tracking**
- **PerformanceMetrics** store time-series data:
  - Sync duration and throughput
  - CPU and memory usage
  - Error rates and record counts
- Automatic anomaly detection using z-scores
- Period-based aggregation for trend analysis

**3. Pipeline Monitoring**
- **PipelineRuns** track execution history
- **StepExecutions** monitor individual step performance
- Integration with alert system for failure notifications

### ⚡ Alert Triggering Flow

**Sync-Based Monitoring:**
```ruby
SyncLog completes → MonitoringCheckJob runs → Evaluates all rules → Creates alerts
```

**Scheduled Uptime Checks:**
```ruby
UptimeMonitoringJob (periodic) → Checks last sync times → Alerts on missing syncs
```

### 📡 Real-Time Updates

The system uses **ActionCable** for live updates:
- Organization-specific monitoring channels
- Real-time alert broadcasts
- Live metric updates on dashboards
- WebSocket-based acknowledge/resolve actions

### 📬 Multi-Channel Notifications

**AlertNotificationJob** delivers alerts through:
- **Email**: HTML-formatted alerts with context and action buttons
- **Slack**: Color-coded messages with severity indicators
- **Webhooks**: JSON payloads to custom endpoints with retry logic

### 🎯 Monitoring Rule Types

**Sync Failure Rules**
- Track consecutive failures (e.g., alert after 3 failures)
- Calculate failure rates over time windows
- Configurable thresholds per organization

**Performance Rules**
- Monitor execution duration against baselines
- Track resource usage (CPU/memory) limits
- Ensure minimum throughput requirements

**Data Quality Rules**
- Monitor error rates with percentage thresholds
- Detect "no data" conditions
- Track record-level processing failures

**Uptime Rules**
- Alert when sources haven't synced within expected timeframe
- Auto-resolve when syncs resume
- Configurable hour-based thresholds

## Setup Instructions

### Prerequisites

- Ruby 3.4.3
- PostgreSQL 14+
- Redis 6+ (for ActionCable in production)
- Node.js 18+ (for JavaScript runtime)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/datareflow.git
   cd datareflow
   ```

2. **Install dependencies**
   ```bash
   bundle install
   ```

3. **Setup environment variables**
   ```bash
   cp .env.example .env
   ```
   Edit `.env` with your configuration:
   - Database credentials
   - Redis URL (for production)
   - Mail server settings
   - Secret keys

4. **Setup databases**
   ```bash
   rails db:create
   rails db:migrate
   rails db:seed # Creates sample organizations and data
   ```

5. **Install JavaScript dependencies**
   ```bash
   rails importmap:install
   rails turbo:install
   rails stimulus:install
   ```

6. **Setup Tailwind CSS**
   ```bash
   rails tailwindcss:install
   ```

7. **Start the application**
   ```bash
   bin/dev # Starts Rails server, Tailwind watcher, and Solid Queue
   ```

8. **Access the application**
   - Development: http://localhost:3000
   - Default admin: <EMAIL> / password123

### Configuration

#### Database Configuration
The application uses multiple databases:
- **primary**: Main application data
- **cache**: Solid Cache storage
- **queue**: Solid Queue job storage
- **cable**: ActionCable connection storage

Configure in `config/database.yml`

#### Background Jobs
Solid Queue processes background jobs for:
- Data source syncing
- Alert notifications
- Performance metric collection
- Uptime monitoring

Configure in `config/solid_queue.yml`

#### Email Configuration
Configure mail settings in `config/environments/production.rb`:
```ruby
config.action_mailer.smtp_settings = {
  address: ENV['SMTP_ADDRESS'],
  port: ENV['SMTP_PORT'],
  user_name: ENV['SMTP_USERNAME'],
  password: ENV['SMTP_PASSWORD'],
  authentication: :plain,
  enable_starttls_auto: true
}
```

## Development

### Running the Development Server
```bash
bin/dev
```

This starts:
- Rails server on port 3000
- Tailwind CSS watcher
- Solid Queue worker

### Code Style
```bash
rubocop -a # Auto-fix style issues
```

### Console Access
```bash
rails console
```

### Database Tasks
```bash
rails db:migrate        # Run pending migrations
rails db:rollback      # Rollback last migration
rails db:reset         # Drop, create, migrate, and seed
```

### Creating Connectors
To add a new data source connector:
1. Create a class in `app/services/connectors/`
2. Implement required methods: `test_connection`, `discover_schema`, `sync_data`
3. Register in `DataSource::CONNECTION_TYPES`

## Testing

### Running Tests
```bash
# Run all tests
rspec

# Run specific test file
rspec spec/models/alert_spec.rb

# Run with coverage
COVERAGE=true rspec
```

### Test Structure
- Models: `spec/models/`
- Controllers: `spec/controllers/`
- Services: `spec/services/`
- Features: `spec/features/`
- Background Jobs: `spec/jobs/`

### Testing Guidelines
- Use factories for test data
- Test happy paths and edge cases
- Mock external API calls
- Test background job behavior

## Deployment

### Docker Deployment

1. **Build the image**
   ```bash
   docker build -t datareflow .
   ```

2. **Run with Docker Compose**
   ```bash
   docker-compose up -d
   ```

### Kamal Deployment

1. **Configure Kamal**
   Edit `config/deploy.yml` with your servers

2. **Setup servers**
   ```bash
   kamal setup
   ```

3. **Deploy**
   ```bash
   kamal deploy
   ```

### Production Considerations

- Enable SSL/TLS certificates
- Configure production database backups
- Setup monitoring and logging
- Configure error tracking (e.g., Sentry)
- Set up CDN for assets
- Configure rate limiting
- Enable CORS if needed for API access

### Environment Variables
Required for production:
- `DATABASE_URL`
- `REDIS_URL`
- `SECRET_KEY_BASE`
- `RAILS_MASTER_KEY`
- `SMTP_*` (mail configuration)

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Write tests for new features
- Follow Ruby style guide
- Update documentation
- Add database migrations carefully
- Consider performance impact

## License

This project is proprietary software. All rights reserved.

---

For more information, visit [datareflow.com](https://datareflow.com) <NAME_EMAIL>