// chart.js@4.4.1 downloaded from https://ga.jspm.io/npm:chart.js@4.4.1/dist/chart.js

import{r as t,c as e,a as s,e as i,i as n,d as o,b as a,f as r,s as l,g as c,v as h,u as d,l as u,h as g,j as f,_ as p,k as m,m as x,n as b,H as _,P as y,t as v,o as k,p as M,q as S,w,x as D,y as C,z as P,A,B as L,C as T,D as O,E,F as I,G as R,I as F,J as B,K as z,L as V,M as W,N,O as H,Q as j,R as $,S as U,U as Y,V as X,W as K,X as G,Y as q,Z as J,$ as Z,a0 as Q,a1 as tt,a2 as et,a3 as st,a4 as it,a5 as nt,a6 as ot,a7 as at,a8 as rt,a9 as lt,aa as ct,ab as ht,ac as dt,ad as ut,ae as gt,af as ft,ag as pt,ah as mt,ai as xt,aj as bt,ak as _t,al as yt,am as vt,an as kt,ao as Mt,ap as St,aq as wt,ar as Dt,as as Ct,at as Pt,au as At,av as Lt,aw as Tt,ax as Ot,ay as Et,az as It,aA as Rt,aB as Ft,aC as Bt,aD as zt,aE as Vt,aF as Wt,aG as Nt,aH as Ht,aI as jt,aJ as $t,aK as Ut,aL as Yt,T as Xt,aM as Kt,aN as Gt,aO as qt,aP as Jt}from"../_/6Mg_Thua.js";import"@kurkle/color";class Animator{constructor(){this._request=null;this._charts=new Map;this._running=false;this._lastDate=void 0}_notify(t,e,s,i){const n=e.listeners[i];const o=e.duration;n.forEach((i=>i({chart:t,initial:e.initial,numSteps:o,currentStep:Math.min(s-e.start,o)})))}_refresh(){if(!this._request){this._running=true;this._request=t.call(window,(()=>{this._update();this._request=null;this._running&&this._refresh()}))}}_update(t=Date.now()){let e=0;this._charts.forEach(((s,i)=>{if(!s.running||!s.items.length)return;const n=s.items;let o=n.length-1;let a=false;let r;for(;o>=0;--o){r=n[o];if(r._active){r._total>s.duration&&(s.duration=r._total);r.tick(t);a=true}else{n[o]=n[n.length-1];n.pop()}}if(a){i.draw();this._notify(i,s,t,"progress")}if(!n.length){s.running=false;this._notify(i,s,t,"complete");s.initial=false}e+=n.length}));this._lastDate=t;e===0&&(this._running=false)}_getAnims(t){const e=this._charts;let s=e.get(t);if(!s){s={running:false,initial:true,items:[],listeners:{complete:[],progress:[]}};e.set(t,s)}return s}listen(t,e,s){this._getAnims(t).listeners[e].push(s)}add(t,e){e&&e.length&&this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);if(e){e.running=true;e.start=Date.now();e.duration=e.items.reduce(((t,e)=>Math.max(t,e._duration)),0);this._refresh()}}running(t){if(!this._running)return false;const e=this._charts.get(t);return!!(e&&e.running&&e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const s=e.items;let i=s.length-1;for(;i>=0;--i)s[i].cancel();e.items=[];this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var Zt=new Animator;const Qt="transparent";const te={boolean(t,e,s){return s>.5?e:t},color(t,s,i){const n=e(t||Qt);const o=n.valid&&e(s||Qt);return o&&o.valid?o.mix(n,i).hexString():s},number(t,e,s){return t+(e-t)*s}};class Animation{constructor(t,e,n,o){const a=e[n];o=s([t.to,o,a,t.from]);const r=s([t.from,a,o]);this._active=true;this._fn=t.fn||te[t.type||typeof r];this._easing=i[t.easing]||i.linear;this._start=Math.floor(Date.now()+(t.delay||0));this._duration=this._total=Math.floor(t.duration);this._loop=!!t.loop;this._target=e;this._prop=n;this._from=r;this._to=o;this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(false);const n=this._target[this._prop];const o=i-this._start;const a=this._duration-o;this._start=i;this._duration=Math.floor(Math.max(a,t.duration));this._total+=o;this._loop=!!t.loop;this._to=s([t.to,e,n,t.from]);this._from=s([t.from,n,e])}}cancel(){if(this._active){this.tick(Date.now());this._active=false;this._notify(false)}}tick(t){const e=t-this._start;const s=this._duration;const i=this._prop;const n=this._from;const o=this._loop;const a=this._to;let r;this._active=n!==a&&(o||e<s);if(this._active)if(e<0)this._target[i]=n;else{r=e/s%2;r=o&&r>1?2-r:r;r=this._easing(Math.min(1,Math.max(0,r)));this._target[i]=this._fn(n,a,r)}else{this._target[i]=a;this._notify(true)}}wait(){const t=this._promises||(this._promises=[]);return new Promise(((e,s)=>{t.push({res:e,rej:s})}))}_notify(t){const e=t?"res":"rej";const s=this._promises||[];for(let t=0;t<s.length;t++)s[t][e]()}}class Animations{constructor(t,e){this._chart=t;this._properties=new Map;this.configure(e)}configure(t){if(!n(t))return;const e=Object.keys(o.animation);const s=this._properties;Object.getOwnPropertyNames(t).forEach((i=>{const o=t[i];if(!n(o))return;const r={};for(const t of e)r[t]=o[t];(a(o.properties)&&o.properties||[i]).forEach((t=>{t!==i&&s.has(t)||s.set(t,r)}))}))}_animateOptions(t,e){const s=e.options;const i=resolveTargetOptions(t,s);if(!i)return[];const n=this._createAnimations(i,s);s.$shared&&awaitAll(t.options.$animations,s).then((()=>{t.options=s}),(()=>{}));return n}_createAnimations(t,e){const s=this._properties;const i=[];const n=t.$animations||(t.$animations={});const o=Object.keys(e);const a=Date.now();let r;for(r=o.length-1;r>=0;--r){const l=o[r];if(l.charAt(0)==="$")continue;if(l==="options"){i.push(...this._animateOptions(t,e));continue}const c=e[l];let h=n[l];const d=s.get(l);if(h){if(d&&h.active()){h.update(d,c,a);continue}h.cancel()}if(d&&d.duration){n[l]=h=new Animation(d,t,l,c);i.push(h)}else t[l]=c}return i}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}const s=this._createAnimations(t,e);if(s.length){Zt.add(this._chart,s);return true}}}function awaitAll(t,e){const s=[];const i=Object.keys(e);for(let e=0;e<i.length;e++){const n=t[i[e]];n&&n.active()&&s.push(n.wait())}return Promise.all(s)}function resolveTargetOptions(t,e){if(!e)return;let s=t.options;if(s){s.$shared&&(t.options=s=Object.assign({},s,{$shared:false,$animations:{}}));return s}t.options=e}function scaleClip(t,e){const s=t&&t.options||{};const i=s.reverse;const n=s.min===void 0?e:0;const o=s.max===void 0?e:0;return{start:i?o:n,end:i?n:o}}function defaultClip(t,e,s){if(s===false)return false;const i=scaleClip(t,s);const n=scaleClip(e,s);return{top:n.end,right:i.end,bottom:n.start,left:i.start}}function toClip(t){let e,s,i,o;if(n(t)){e=t.top;s=t.right;i=t.bottom;o=t.left}else e=s=i=o=t;return{top:e,right:s,bottom:i,left:o,disabled:t===false}}function getSortedDatasetIndices(t,e){const s=[];const i=t._getSortedDatasetMetas(e);let n,o;for(n=0,o=i.length;n<o;++n)s.push(i[n].index);return s}function applyStack(t,e,s,i={}){const n=t.keys;const o=i.mode==="single";let a,c,h,d;if(e!==null){for(a=0,c=n.length;a<c;++a){h=+n[a];if(h===s){if(i.all)continue;break}d=t.values[h];r(d)&&(o||e===0||l(e)===l(d))&&(e+=d)}return e}}function convertObjectDataToArray(t){const e=Object.keys(t);const s=new Array(e.length);let i,n,o;for(i=0,n=e.length;i<n;++i){o=e[i];s[i]={x:o,y:t[o]}}return s}function isStacked(t,e){const s=t&&t.options.stacked;return s||s===void 0&&e.stack!==void 0}function getStackKey(t,e,s){return`${t.id}.${e.id}.${s.stack||s.type}`}function getUserBounds(t){const{min:e,max:s,minDefined:i,maxDefined:n}=t.getUserBounds();return{min:i?e:Number.NEGATIVE_INFINITY,max:n?s:Number.POSITIVE_INFINITY}}function getOrCreateStack(t,e,s){const i=t[e]||(t[e]={});return i[s]||(i[s]={})}function getLastIndexInStack(t,e,s,i){for(const n of e.getMatchingVisibleMetas(i).reverse()){const e=t[n.index];if(s&&e>0||!s&&e<0)return n.index}return null}function updateStacks(t,e){const{chart:s,_cachedMeta:i}=t;const n=s._stacks||(s._stacks={});const{iScale:o,vScale:a,index:r}=i;const l=o.axis;const c=a.axis;const h=getStackKey(o,a,i);const d=e.length;let u;for(let t=0;t<d;++t){const s=e[t];const{[l]:o,[c]:d}=s;const g=s._stacks||(s._stacks={});u=g[c]=getOrCreateStack(n,h,o);u[r]=d;u._top=getLastIndexInStack(u,a,true,i.type);u._bottom=getLastIndexInStack(u,a,false,i.type);const f=u._visualValues||(u._visualValues={});f[r]=d}}function getFirstScaleId(t,e){const s=t.scales;return Object.keys(s).filter((t=>s[t].axis===e)).shift()}function createDatasetContext(t,e){return c(t,{active:false,dataset:void 0,datasetIndex:e,index:e,mode:"default",type:"dataset"})}function createDataContext(t,e,s){return c(t,{active:false,dataIndex:e,parsed:void 0,raw:void 0,element:s,index:e,mode:"default",type:"data"})}function clearStacks(t,e){const s=t.controller.index;const i=t.vScale&&t.vScale.axis;if(i){e=e||t._parsed;for(const t of e){const e=t._stacks;if(!e||e[i]===void 0||e[i][s]===void 0)return;delete e[i][s];e[i]._visualValues!==void 0&&e[i]._visualValues[s]!==void 0&&delete e[i]._visualValues[s]}}}const isDirectUpdateMode=t=>t==="reset"||t==="none";const cloneIfNotShared=(t,e)=>e?t:Object.assign({},t);const createStack=(t,e,s)=>t&&!e.hidden&&e._stacked&&{keys:getSortedDatasetIndices(s,true),values:null};class DatasetController{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,e){this.chart=t;this._ctx=t.ctx;this.index=e;this._cachedDataOpts={};this._cachedMeta=this.getMeta();this._type=this._cachedMeta.type;this.options=void 0;this._parsing=false;this._data=void 0;this._objectData=void 0;this._sharedOptions=void 0;this._drawStart=void 0;this._drawCount=void 0;this.enableOptionSharing=false;this.supportsDecimation=false;this.$context=void 0;this._syncList=[];this.datasetElementType=new.target.datasetElementType;this.dataElementType=new.target.dataElementType;this.initialize()}initialize(){const t=this._cachedMeta;this.configure();this.linkScales();t._stacked=isStacked(t.vScale,t);this.addElements();this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&clearStacks(this._cachedMeta);this.index=t}linkScales(){const t=this.chart;const e=this._cachedMeta;const s=this.getDataset();const chooseId=(t,e,s,i)=>t==="x"?e:t==="r"?i:s;const i=e.xAxisID=h(s.xAxisID,getFirstScaleId(t,"x"));const n=e.yAxisID=h(s.yAxisID,getFirstScaleId(t,"y"));const o=e.rAxisID=h(s.rAxisID,getFirstScaleId(t,"r"));const a=e.indexAxis;const r=e.iAxisID=chooseId(a,i,n,o);const l=e.vAxisID=chooseId(a,n,i,o);e.xScale=this.getScaleForId(i);e.yScale=this.getScaleForId(n);e.rScale=this.getScaleForId(o);e.iScale=this.getScaleForId(r);e.vScale=this.getScaleForId(l)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&d(this._data,this);t._stacked&&clearStacks(t)}_dataCheck(){const t=this.getDataset();const e=t.data||(t.data=[]);const s=this._data;if(n(e))this._data=convertObjectDataToArray(e);else if(s!==e){if(s){d(s,this);const t=this._cachedMeta;clearStacks(t);t._parsed=[]}e&&Object.isExtensible(e)&&u(e,this);this._syncList=[];this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck();this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta;const s=this.getDataset();let i=false;this._dataCheck();const n=e._stacked;e._stacked=isStacked(e.vScale,e);if(e.stack!==s.stack){i=true;clearStacks(e);e.stack=s.stack}this._resyncElements(t);(i||n!==e._stacked)&&updateStacks(this,e._parsed)}configure(){const t=this.chart.config;const e=t.datasetScopeKeys(this._type);const s=t.getOptionScopes(this.getDataset(),e,true);this.options=t.createResolver(s,this.getContext());this._parsing=this.options.parsing;this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:s,_data:i}=this;const{iScale:o,_stacked:r}=s;const l=o.axis;let c=t===0&&e===i.length||s._sorted;let h=t>0&&s._parsed[t-1];let d,u,g;if(this._parsing===false){s._parsed=i;s._sorted=true;g=i}else{g=a(i[t])?this.parseArrayData(s,i,t,e):n(i[t])?this.parseObjectData(s,i,t,e):this.parsePrimitiveData(s,i,t,e);const isNotInOrderComparedToPrev=()=>u[l]===null||h&&u[l]<h[l];for(d=0;d<e;++d){s._parsed[d+t]=u=g[d];if(c){isNotInOrderComparedToPrev()&&(c=false);h=u}}s._sorted=c}r&&updateStacks(this,g)}parsePrimitiveData(t,e,s,i){const{iScale:n,vScale:o}=t;const a=n.axis;const r=o.axis;const l=n.getLabels();const c=n===o;const h=new Array(i);let d,u,g;for(d=0,u=i;d<u;++d){g=d+s;h[d]={[a]:c||n.parse(l[g],g),[r]:o.parse(e[g],g)}}return h}parseArrayData(t,e,s,i){const{xScale:n,yScale:o}=t;const a=new Array(i);let r,l,c,h;for(r=0,l=i;r<l;++r){c=r+s;h=e[c];a[r]={x:n.parse(h[0],c),y:o.parse(h[1],c)}}return a}parseObjectData(t,e,s,i){const{xScale:n,yScale:o}=t;const{xAxisKey:a="x",yAxisKey:r="y"}=this._parsing;const l=new Array(i);let c,h,d,u;for(c=0,h=i;c<h;++c){d=c+s;u=e[d];l[c]={x:n.parse(g(u,a),d),y:o.parse(g(u,r),d)}}return l}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,s){const i=this.chart;const n=this._cachedMeta;const o=e[t.axis];const a={keys:getSortedDatasetIndices(i,true),values:e._stacks[t.axis]._visualValues};return applyStack(a,o,n.index,{mode:s})}updateRangeFromParsed(t,e,s,i){const n=s[e.axis];let o=n===null?NaN:n;const a=i&&s._stacks[e.axis];if(i&&a){i.values=a;o=applyStack(i,n,this._cachedMeta.index)}t.min=Math.min(t.min,o);t.max=Math.max(t.max,o)}getMinMax(t,e){const s=this._cachedMeta;const i=s._parsed;const n=s._sorted&&t===s.iScale;const o=i.length;const a=this._getOtherScale(t);const l=createStack(e,s,this.chart);const c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};const{min:h,max:d}=getUserBounds(a);let u,g;function _skip(){g=i[u];const e=g[a.axis];return!r(g[t.axis])||h>e||d<e}for(u=0;u<o;++u)if(!_skip()){this.updateRangeFromParsed(c,t,g,l);if(n)break}if(n)for(u=o-1;u>=0;--u)if(!_skip()){this.updateRangeFromParsed(c,t,g,l);break}return c}getAllParsedValues(t){const e=this._cachedMeta._parsed;const s=[];let i,n,o;for(i=0,n=e.length;i<n;++i){o=e[i][t.axis];r(o)&&s.push(o)}return s}getMaxOverflow(){return false}getLabelAndValue(t){const e=this._cachedMeta;const s=e.iScale;const i=e.vScale;const n=this.getParsed(t);return{label:s?""+s.getLabelForValue(n[s.axis]):"",value:i?""+i.getLabelForValue(n[i.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default");e._clip=toClip(h(this.options.clip,defaultClip(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx;const e=this.chart;const s=this._cachedMeta;const i=s.data||[];const n=e.chartArea;const o=[];const a=this._drawStart||0;const r=this._drawCount||i.length-a;const l=this.options.drawActiveElementsOnTop;let c;s.dataset&&s.dataset.draw(t,n,a,r);for(c=a;c<a+r;++c){const e=i[c];e.hidden||(e.active&&l?o.push(e):e.draw(t,n))}for(c=0;c<o.length;++c)o[c].draw(t,n)}getStyle(t,e){const s=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(t||0,s)}getContext(t,e,s){const i=this.getDataset();let n;if(t>=0&&t<this._cachedMeta.data.length){const e=this._cachedMeta.data[t];n=e.$context||(e.$context=createDataContext(this.getContext(),t,e));n.parsed=this.getParsed(t);n.raw=i.data[t];n.index=n.dataIndex=t}else{n=this.$context||(this.$context=createDatasetContext(this.chart.getContext(),this.index));n.dataset=i;n.index=n.datasetIndex=this.index}n.active=!!e;n.mode=s;return n}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",s){const i=e==="active";const n=this._cachedDataOpts;const a=t+"-"+e;const r=n[a];const l=this.enableOptionSharing&&f(s);if(r)return cloneIfNotShared(r,l);const c=this.chart.config;const h=c.datasetElementScopeKeys(this._type,t);const d=i?[`${t}Hover`,"hover",t,""]:[t,""];const u=c.getOptionScopes(this.getDataset(),h);const g=Object.keys(o.elements[t]);const context=()=>this.getContext(s,i,e);const p=c.resolveNamedOptions(u,g,context,d);if(p.$shared){p.$shared=l;n[a]=Object.freeze(cloneIfNotShared(p,l))}return p}_resolveAnimations(t,e,s){const i=this.chart;const n=this._cachedDataOpts;const o=`animation-${e}`;const a=n[o];if(a)return a;let r;if(i.options.animation!==false){const i=this.chart.config;const n=i.datasetAnimationScopeKeys(this._type,e);const o=i.getOptionScopes(this.getDataset(),n);r=i.createResolver(o,this.getContext(t,s,e))}const l=new Animations(i,r&&r.animations);r&&r._cacheable&&(n[o]=Object.freeze(l));return l}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||isDirectUpdateMode(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const s=this.resolveDataElementOptions(t,e);const i=this._sharedOptions;const n=this.getSharedOptions(s);const o=this.includeOptions(e,n)||n!==i;this.updateSharedOptions(n,e,s);return{sharedOptions:n,includeOptions:o}}updateElement(t,e,s,i){isDirectUpdateMode(i)?Object.assign(t,s):this._resolveAnimations(e,i).update(t,s)}updateSharedOptions(t,e,s){t&&!isDirectUpdateMode(e)&&this._resolveAnimations(void 0,e).update(t,s)}_setStyle(t,e,s,i){t.active=i;const n=this.getStyle(e,i);this._resolveAnimations(e,s,i).update(t,{options:!i&&this.getSharedOptions(n)||n})}removeHoverStyle(t,e,s){this._setStyle(t,s,"active",false)}setHoverStyle(t,e,s){this._setStyle(t,s,"active",true)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",false)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",true)}_resyncElements(t){const e=this._data;const s=this._cachedMeta.data;for(const[t,e,s]of this._syncList)this[t](e,s);this._syncList=[];const i=s.length;const n=e.length;const o=Math.min(n,i);o&&this.parse(0,o);n>i?this._insertElements(i,n-i,t):n<i&&this._removeElements(n,i-n)}_insertElements(t,e,s=true){const i=this._cachedMeta;const n=i.data;const o=t+e;let a;const move=t=>{t.length+=e;for(a=t.length-1;a>=o;a--)t[a]=t[a-e]};move(n);for(a=t;a<o;++a)n[a]=new this.dataElementType;this._parsing&&move(i._parsed);this.parse(t,e);s&&this.updateElements(n,t,e,"reset")}updateElements(t,e,s,i){}_removeElements(t,e){const s=this._cachedMeta;if(this._parsing){const i=s._parsed.splice(t,e);s._stacked&&clearStacks(s,i)}s.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,s,i]=t;this[e](s,i)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const s=arguments.length-2;s&&this._sync(["_insertElements",t,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}function getAllScaleValues(t,e){if(!t._cache.$bar){const s=t.getMatchingVisibleMetas(e);let i=[];for(let e=0,n=s.length;e<n;e++)i=i.concat(s[e].controller.getAllParsedValues(t));t._cache.$bar=p(i.sort(((t,e)=>t-e)))}return t._cache.$bar}function computeMinSampleSize(t){const e=t.iScale;const s=getAllScaleValues(e,t.type);let i=e._length;let n,o,a,r;const updateMinAndPrev=()=>{if(a!==32767&&a!==-32768){f(r)&&(i=Math.min(i,Math.abs(a-r)||i));r=a}};for(n=0,o=s.length;n<o;++n){a=e.getPixelForValue(s[n]);updateMinAndPrev()}r=void 0;for(n=0,o=e.ticks.length;n<o;++n){a=e.getPixelForTick(n);updateMinAndPrev()}return i}function computeFitCategoryTraits(t,e,s,i){const n=s.barThickness;let o,a;if(m(n)){o=e.min*s.categoryPercentage;a=s.barPercentage}else{o=n*i;a=1}return{chunk:o/i,ratio:a,start:e.pixels[t]-o/2}}function computeFlexCategoryTraits(t,e,s,i){const n=e.pixels;const o=n[t];let a=t>0?n[t-1]:null;let r=t<n.length-1?n[t+1]:null;const l=s.categoryPercentage;a===null&&(a=o-(r===null?e.end-e.start:r-o));r===null&&(r=o+o-a);const c=o-(o-Math.min(a,r))/2*l;const h=Math.abs(r-a)/2*l;return{chunk:h/i,ratio:s.barPercentage,start:c}}function parseFloatBar(t,e,s,i){const n=s.parse(t[0],i);const o=s.parse(t[1],i);const a=Math.min(n,o);const r=Math.max(n,o);let l=a;let c=r;if(Math.abs(a)>Math.abs(r)){l=r;c=a}e[s.axis]=c;e._custom={barStart:l,barEnd:c,start:n,end:o,min:a,max:r}}function parseValue(t,e,s,i){a(t)?parseFloatBar(t,e,s,i):e[s.axis]=s.parse(t,i);return e}function parseArrayOrPrimitive(t,e,s,i){const n=t.iScale;const o=t.vScale;const a=n.getLabels();const r=n===o;const l=[];let c,h,d,u;for(c=s,h=s+i;c<h;++c){u=e[c];d={};d[n.axis]=r||n.parse(a[c],c);l.push(parseValue(u,d,o,c))}return l}function isFloatBar(t){return t&&t.barStart!==void 0&&t.barEnd!==void 0}function barSign(t,e,s){return t!==0?l(t):(e.isHorizontal()?1:-1)*(e.min>=s?1:-1)}function borderProps(t){let e,s,i,n,o;if(t.horizontal){e=t.base>t.x;s="left";i="right"}else{e=t.base<t.y;s="bottom";i="top"}if(e){n="end";o="start"}else{n="start";o="end"}return{start:s,end:i,reverse:e,top:n,bottom:o}}function setBorderSkipped(t,e,s,i){let n=e.borderSkipped;const o={};if(!n){t.borderSkipped=o;return}if(n===true){t.borderSkipped={top:true,right:true,bottom:true,left:true};return}const{start:a,end:r,reverse:l,top:c,bottom:h}=borderProps(t);if(n==="middle"&&s){t.enableBorderRadius=true;if((s._top||0)===i)n=c;else if((s._bottom||0)===i)n=h;else{o[parseEdge(h,a,r,l)]=true;n=c}}o[parseEdge(n,a,r,l)]=true;t.borderSkipped=o}function parseEdge(t,e,s,i){if(i){t=swap(t,e,s);t=startEnd(t,s,e)}else t=startEnd(t,e,s);return t}function swap(t,e,s){return t===e?s:t===s?e:t}function startEnd(t,e,s){return t==="start"?e:t==="end"?s:t}function setInflateAmount(t,{inflateAmount:e},s){t.inflateAmount=e==="auto"?s===1?.33:0:e}class BarController extends DatasetController{static id="bar";static defaults={datasetElementType:false,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:true,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:true,grid:{offset:true}},_value_:{type:"linear",beginAtZero:true}}};parsePrimitiveData(t,e,s,i){return parseArrayOrPrimitive(t,e,s,i)}parseArrayData(t,e,s,i){return parseArrayOrPrimitive(t,e,s,i)}parseObjectData(t,e,s,i){const{iScale:n,vScale:o}=t;const{xAxisKey:a="x",yAxisKey:r="y"}=this._parsing;const l=n.axis==="x"?a:r;const c=o.axis==="x"?a:r;const h=[];let d,u,f,p;for(d=s,u=s+i;d<u;++d){p=e[d];f={};f[n.axis]=n.parse(g(p,l),d);h.push(parseValue(g(p,c),f,o,d))}return h}updateRangeFromParsed(t,e,s,i){super.updateRangeFromParsed(t,e,s,i);const n=s._custom;if(n&&e===this._cachedMeta.vScale){t.min=Math.min(t.min,n.min);t.max=Math.max(t.max,n.max)}}getMaxOverflow(){return 0}getLabelAndValue(t){const e=this._cachedMeta;const{iScale:s,vScale:i}=e;const n=this.getParsed(t);const o=n._custom;const a=isFloatBar(o)?"["+o.start+", "+o.end+"]":""+i.getLabelForValue(n[i.axis]);return{label:""+s.getLabelForValue(n[s.axis]),value:a}}initialize(){this.enableOptionSharing=true;super.initialize();const t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){const e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,s,i){const n=i==="reset";const{index:o,_cachedMeta:{vScale:a}}=this;const r=a.getBasePixel();const l=a.isHorizontal();const c=this._getRuler();const{sharedOptions:h,includeOptions:d}=this._getSharedOptions(e,i);for(let u=e;u<e+s;u++){const e=this.getParsed(u);const s=n||m(e[a.axis])?{base:r,head:r}:this._calculateBarValuePixels(u);const g=this._calculateBarIndexPixels(u,c);const f=(e._stacks||{})[a.axis];const p={horizontal:l,base:s.base,enableBorderRadius:!f||isFloatBar(e._custom)||o===f._top||o===f._bottom,x:l?s.head:g.center,y:l?g.center:s.head,height:l?g.size:Math.abs(s.size),width:l?Math.abs(s.size):g.size};d&&(p.options=h||this.resolveDataElementOptions(u,t[u].active?"active":i));const x=p.options||t[u].options;setBorderSkipped(p,x,f,o);setInflateAmount(p,x,c.ratio);this.updateElement(t[u],u,p,i)}}_getStacks(t,e){const{iScale:s}=this._cachedMeta;const i=s.getMatchingVisibleMetas(this._type).filter((t=>t.controller.options.grouped));const n=s.options.stacked;const o=[];const skipNull=t=>{const s=t.controller.getParsed(e);const i=s&&s[t.vScale.axis];if(m(i)||isNaN(i))return true};for(const s of i)if(e===void 0||!skipNull(s)){(n===false||o.indexOf(s.stack)===-1||n===void 0&&s.stack===void 0)&&o.push(s.stack);if(s.index===t)break}o.length||o.push(void 0);return o}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,s){const i=this._getStacks(t,s);const n=e!==void 0?i.indexOf(e):-1;return n===-1?i.length-1:n}_getRuler(){const t=this.options;const e=this._cachedMeta;const s=e.iScale;const i=[];let n,o;for(n=0,o=e.data.length;n<o;++n)i.push(s.getPixelForValue(this.getParsed(n)[s.axis],n));const a=t.barThickness;const r=a||computeMinSampleSize(e);return{min:r,pixels:i,start:s._startPixel,end:s._endPixel,stackCount:this._getStackCount(),scale:s,grouped:t.grouped,ratio:a?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:e,_stacked:s,index:i},options:{base:n,minBarLength:o}}=this;const a=n||0;const r=this.getParsed(t);const c=r._custom;const h=isFloatBar(c);let d=r[e.axis];let u=0;let g=s?this.applyStack(e,r,s):d;let f,p;if(g!==d){u=g-d;g=d}if(h){d=c.barStart;g=c.barEnd-c.barStart;d!==0&&l(d)!==l(c.barEnd)&&(u=0);u+=d}const x=m(n)||h?u:n;let b=e.getPixelForValue(x);f=this.chart.getDataVisibility(t)?e.getPixelForValue(u+g):b;p=f-b;if(Math.abs(p)<o){p=barSign(p,e,a)*o;d===a&&(b-=p/2);const t=e.getPixelForDecimal(0);const n=e.getPixelForDecimal(1);const l=Math.min(t,n);const c=Math.max(t,n);b=Math.max(Math.min(b,c),l);f=b+p;s&&!h&&(r._stacks[e.axis]._visualValues[i]=e.getValueForPixel(f)-e.getValueForPixel(b))}if(b===e.getPixelForValue(a)){const t=l(p)*e.getLineWidthForValue(a)/2;b+=t;p-=t}return{size:p,base:b,head:f,center:f+p/2}}_calculateBarIndexPixels(t,e){const s=e.scale;const i=this.options;const n=i.skipNull;const o=h(i.maxBarThickness,Infinity);let a,r;if(e.grouped){const s=n?this._getStackCount(t):e.stackCount;const l=i.barThickness==="flex"?computeFlexCategoryTraits(t,e,i,s):computeFitCategoryTraits(t,e,i,s);const c=this._getStackIndex(this.index,this._cachedMeta.stack,n?t:void 0);a=l.start+l.chunk*c+l.chunk/2;r=Math.min(o,l.chunk*l.ratio)}else{a=s.getPixelForValue(this.getParsed(t)[s.axis],t);r=Math.min(o,e.min*e.ratio)}return{base:a-r/2,head:a+r/2,center:a,size:r}}draw(){const t=this._cachedMeta;const e=t.vScale;const s=t.data;const i=s.length;let n=0;for(;n<i;++n)this.getParsed(n)[e.axis]!==null&&s[n].draw(this._ctx)}}class BubbleController extends DatasetController{static id="bubble";static defaults={datasetElementType:false,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}};static overrides={scales:{x:{type:"linear"},y:{type:"linear"}}};initialize(){this.enableOptionSharing=true;super.initialize()}parsePrimitiveData(t,e,s,i){const n=super.parsePrimitiveData(t,e,s,i);for(let t=0;t<n.length;t++)n[t]._custom=this.resolveDataElementOptions(t+s).radius;return n}parseArrayData(t,e,s,i){const n=super.parseArrayData(t,e,s,i);for(let t=0;t<n.length;t++){const i=e[s+t];n[t]._custom=h(i[2],this.resolveDataElementOptions(t+s).radius)}return n}parseObjectData(t,e,s,i){const n=super.parseObjectData(t,e,s,i);for(let t=0;t<n.length;t++){const i=e[s+t];n[t]._custom=h(i&&i.r&&+i.r,this.resolveDataElementOptions(t+s).radius)}return n}getMaxOverflow(){const t=this._cachedMeta.data;let e=0;for(let s=t.length-1;s>=0;--s)e=Math.max(e,t[s].size(this.resolveDataElementOptions(s))/2);return e>0&&e}getLabelAndValue(t){const e=this._cachedMeta;const s=this.chart.data.labels||[];const{xScale:i,yScale:n}=e;const o=this.getParsed(t);const a=i.getLabelForValue(o.x);const r=n.getLabelForValue(o.y);const l=o._custom;return{label:s[t]||"",value:"("+a+", "+r+(l?", "+l:"")+")"}}update(t){const e=this._cachedMeta.data;this.updateElements(e,0,e.length,t)}updateElements(t,e,s,i){const n=i==="reset";const{iScale:o,vScale:a}=this._cachedMeta;const{sharedOptions:r,includeOptions:l}=this._getSharedOptions(e,i);const c=o.axis;const h=a.axis;for(let d=e;d<e+s;d++){const e=t[d];const s=!n&&this.getParsed(d);const u={};const g=u[c]=n?o.getPixelForDecimal(.5):o.getPixelForValue(s[c]);const f=u[h]=n?a.getBasePixel():a.getPixelForValue(s[h]);u.skip=isNaN(g)||isNaN(f);if(l){u.options=r||this.resolveDataElementOptions(d,e.active?"active":i);n&&(u.options.radius=0)}this.updateElement(e,d,u,i)}}resolveDataElementOptions(t,e){const s=this.getParsed(t);let i=super.resolveDataElementOptions(t,e);i.$shared&&(i=Object.assign({},i,{$shared:false}));const n=i.radius;e!=="active"&&(i.radius=0);i.radius+=h(s&&s._custom,n);return i}}function getRatioAndOffset(t,e,s){let i=1;let n=1;let o=0;let a=0;if(e<x){const r=t;const l=r+e;const c=Math.cos(r);const h=Math.sin(r);const d=Math.cos(l);const u=Math.sin(l);const calcMax=(t,e,i)=>b(t,r,l,true)?1:Math.max(e,e*s,i,i*s);const calcMin=(t,e,i)=>b(t,r,l,true)?-1:Math.min(e,e*s,i,i*s);const g=calcMax(0,c,d);const f=calcMax(_,h,u);const p=calcMin(y,c,d);const m=calcMin(y+_,h,u);i=(g-p)/2;n=(f-m)/2;o=-(g+p)/2;a=-(f+m)/2}return{ratioX:i,ratioY:n,offsetX:o,offsetY:a}}class DoughnutController extends DatasetController{static id="doughnut";static defaults={datasetElementType:false,dataElementType:"arc",animation:{animateRotate:true,animateScale:false},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:s,color:i}}=t.legend.options;return e.labels.map(((e,n)=>{const o=t.getDatasetMeta(0);const a=o.controller.getStyle(n);return{text:e,fillStyle:a.backgroundColor,strokeStyle:a.borderColor,fontColor:i,lineWidth:a.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(n),index:n}}))}return[]}},onClick(t,e,s){s.chart.toggleDataVisibility(e.index);s.chart.update()}}}};constructor(t,e){super(t,e);this.enableOptionSharing=true;this.innerRadius=void 0;this.outerRadius=void 0;this.offsetX=void 0;this.offsetY=void 0}linkScales(){}parse(t,e){const s=this.getDataset().data;const i=this._cachedMeta;if(this._parsing===false)i._parsed=s;else{let getter=t=>+s[t];if(n(s[t])){const{key:t="value"}=this._parsing;getter=e=>+g(s[e],t)}let o,a;for(o=t,a=t+e;o<a;++o)i._parsed[o]=getter(o)}}_getRotation(){return v(this.options.rotation-90)}_getCircumference(){return v(this.options.circumference)}_getRotationExtents(){let t=x;let e=-x;for(let s=0;s<this.chart.data.datasets.length;++s)if(this.chart.isDatasetVisible(s)&&this.chart.getDatasetMeta(s).type===this._type){const i=this.chart.getDatasetMeta(s).controller;const n=i._getRotation();const o=i._getCircumference();t=Math.min(t,n);e=Math.max(e,n+o)}return{rotation:t,circumference:e-t}}update(t){const e=this.chart;const{chartArea:s}=e;const i=this._cachedMeta;const n=i.data;const o=this.getMaxBorderWidth()+this.getMaxOffset(n)+this.options.spacing;const a=Math.max((Math.min(s.width,s.height)-o)/2,0);const r=Math.min(k(this.options.cutout,a),1);const l=this._getRingWeight(this.index);const{circumference:c,rotation:h}=this._getRotationExtents();const{ratioX:d,ratioY:u,offsetX:g,offsetY:f}=getRatioAndOffset(h,c,r);const p=(s.width-o)/d;const m=(s.height-o)/u;const x=Math.max(Math.min(p,m)/2,0);const b=M(this.options.radius,x);const _=Math.max(b*r,0);const y=(b-_)/this._getVisibleDatasetWeightTotal();this.offsetX=g*b;this.offsetY=f*b;i.total=this.calculateTotal();this.outerRadius=b-y*this._getRingWeightOffset(this.index);this.innerRadius=Math.max(this.outerRadius-y*l,0);this.updateElements(n,0,n.length,t)}_circumference(t,e){const s=this.options;const i=this._cachedMeta;const n=this._getCircumference();return e&&s.animation.animateRotate||!this.chart.getDataVisibility(t)||i._parsed[t]===null||i.data[t].hidden?0:this.calculateCircumference(i._parsed[t]*n/x)}updateElements(t,e,s,i){const n=i==="reset";const o=this.chart;const a=o.chartArea;const r=o.options;const l=r.animation;const c=(a.left+a.right)/2;const h=(a.top+a.bottom)/2;const d=n&&l.animateScale;const u=d?0:this.innerRadius;const g=d?0:this.outerRadius;const{sharedOptions:f,includeOptions:p}=this._getSharedOptions(e,i);let m=this._getRotation();let x;for(x=0;x<e;++x)m+=this._circumference(x,n);for(x=e;x<e+s;++x){const e=this._circumference(x,n);const s=t[x];const o={x:c+this.offsetX,y:h+this.offsetY,startAngle:m,endAngle:m+e,circumference:e,outerRadius:g,innerRadius:u};p&&(o.options=f||this.resolveDataElementOptions(x,s.active?"active":i));m+=e;this.updateElement(s,x,o,i)}}calculateTotal(){const t=this._cachedMeta;const e=t.data;let s=0;let i;for(i=0;i<e.length;i++){const n=t._parsed[i];n===null||isNaN(n)||!this.chart.getDataVisibility(i)||e[i].hidden||(s+=Math.abs(n))}return s}calculateCircumference(t){const e=this._cachedMeta.total;return e>0&&!isNaN(t)?x*(Math.abs(t)/e):0}getLabelAndValue(t){const e=this._cachedMeta;const s=this.chart;const i=s.data.labels||[];const n=S(e._parsed[t],s.options.locale);return{label:i[t]||"",value:n}}getMaxBorderWidth(t){let e=0;const s=this.chart;let i,n,o,a,r;if(!t)for(i=0,n=s.data.datasets.length;i<n;++i)if(s.isDatasetVisible(i)){o=s.getDatasetMeta(i);t=o.data;a=o.controller;break}if(!t)return 0;for(i=0,n=t.length;i<n;++i){r=a.resolveDataElementOptions(i);r.borderAlign!=="inner"&&(e=Math.max(e,r.borderWidth||0,r.hoverBorderWidth||0))}return e}getMaxOffset(t){let e=0;for(let s=0,i=t.length;s<i;++s){const t=this.resolveDataElementOptions(s);e=Math.max(e,t.offset||0,t.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let s=0;s<t;++s)this.chart.isDatasetVisible(s)&&(e+=this._getRingWeight(s));return e}_getRingWeight(t){return Math.max(h(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}class LineController extends DatasetController{static id="line";static defaults={datasetElementType:"line",dataElementType:"point",showLine:true,spanGaps:false};static overrides={scales:{_index_:{type:"category"},_value_:{type:"linear"}}};initialize(){this.enableOptionSharing=true;this.supportsDecimation=true;super.initialize()}update(t){const e=this._cachedMeta;const{dataset:s,data:i=[],_dataset:n}=e;const o=this.chart._animationsDisabled;let{start:a,count:r}=w(e,i,o);this._drawStart=a;this._drawCount=r;if(D(e)){a=0;r=i.length}s._chart=this.chart;s._datasetIndex=this.index;s._decimated=!!n._decimated;s.points=i;const l=this.resolveDatasetElementOptions(t);this.options.showLine||(l.borderWidth=0);l.segment=this.options.segment;this.updateElement(s,void 0,{animated:!o,options:l},t);this.updateElements(i,a,r,t)}updateElements(t,e,s,i){const n=i==="reset";const{iScale:o,vScale:a,_stacked:r,_dataset:l}=this._cachedMeta;const{sharedOptions:c,includeOptions:h}=this._getSharedOptions(e,i);const d=o.axis;const u=a.axis;const{spanGaps:g,segment:f}=this.options;const p=C(g)?g:Number.POSITIVE_INFINITY;const x=this.chart._animationsDisabled||n||i==="none";const b=e+s;const _=t.length;let y=e>0&&this.getParsed(e-1);for(let s=0;s<_;++s){const g=t[s];const _=x?g:{};if(s<e||s>=b){_.skip=true;continue}const v=this.getParsed(s);const k=m(v[u]);const M=_[d]=o.getPixelForValue(v[d],s);const S=_[u]=n||k?a.getBasePixel():a.getPixelForValue(r?this.applyStack(a,v,r):v[u],s);_.skip=isNaN(M)||isNaN(S)||k;_.stop=s>0&&Math.abs(v[d]-y[d])>p;if(f){_.parsed=v;_.raw=l.data[s]}h&&(_.options=c||this.resolveDataElementOptions(s,g.active?"active":i));x||this.updateElement(g,s,_,i);y=v}}getMaxOverflow(){const t=this._cachedMeta;const e=t.dataset;const s=e.options&&e.options.borderWidth||0;const i=t.data||[];if(!i.length)return s;const n=i[0].size(this.resolveDataElementOptions(0));const o=i[i.length-1].size(this.resolveDataElementOptions(i.length-1));return Math.max(s,n,o)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis);super.draw()}}class PolarAreaController extends DatasetController{static id="polarArea";static defaults={dataElementType:"arc",animation:{animateRotate:true,animateScale:true},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:s,color:i}}=t.legend.options;return e.labels.map(((e,n)=>{const o=t.getDatasetMeta(0);const a=o.controller.getStyle(n);return{text:e,fillStyle:a.backgroundColor,strokeStyle:a.borderColor,fontColor:i,lineWidth:a.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(n),index:n}}))}return[]}},onClick(t,e,s){s.chart.toggleDataVisibility(e.index);s.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:false},beginAtZero:true,grid:{circular:true},pointLabels:{display:false},startAngle:0}}};constructor(t,e){super(t,e);this.innerRadius=void 0;this.outerRadius=void 0}getLabelAndValue(t){const e=this._cachedMeta;const s=this.chart;const i=s.data.labels||[];const n=S(e._parsed[t].r,s.options.locale);return{label:i[t]||"",value:n}}parseObjectData(t,e,s,i){return P.bind(this)(t,e,s,i)}update(t){const e=this._cachedMeta.data;this._updateRadius();this.updateElements(e,0,e.length,t)}getMinMax(){const t=this._cachedMeta;const e={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};t.data.forEach(((t,s)=>{const i=this.getParsed(s).r;if(!isNaN(i)&&this.chart.getDataVisibility(s)){i<e.min&&(e.min=i);i>e.max&&(e.max=i)}}));return e}_updateRadius(){const t=this.chart;const e=t.chartArea;const s=t.options;const i=Math.min(e.right-e.left,e.bottom-e.top);const n=Math.max(i/2,0);const o=Math.max(s.cutoutPercentage?n/100*s.cutoutPercentage:1,0);const a=(n-o)/t.getVisibleDatasetCount();this.outerRadius=n-a*this.index;this.innerRadius=this.outerRadius-a}updateElements(t,e,s,i){const n=i==="reset";const o=this.chart;const a=o.options;const r=a.animation;const l=this._cachedMeta.rScale;const c=l.xCenter;const h=l.yCenter;const d=l.getIndexAngle(0)-.5*y;let u=d;let g;const f=360/this.countVisibleElements();for(g=0;g<e;++g)u+=this._computeAngle(g,i,f);for(g=e;g<e+s;g++){const e=t[g];let s=u;let a=u+this._computeAngle(g,i,f);let p=o.getDataVisibility(g)?l.getDistanceFromCenterForValue(this.getParsed(g).r):0;u=a;if(n){r.animateScale&&(p=0);r.animateRotate&&(s=a=d)}const m={x:c,y:h,innerRadius:0,outerRadius:p,startAngle:s,endAngle:a,options:this.resolveDataElementOptions(g,e.active?"active":i)};this.updateElement(e,g,m,i)}}countVisibleElements(){const t=this._cachedMeta;let e=0;t.data.forEach(((t,s)=>{!isNaN(this.getParsed(s).r)&&this.chart.getDataVisibility(s)&&e++}));return e}_computeAngle(t,e,s){return this.chart.getDataVisibility(t)?v(this.resolveDataElementOptions(t,e).angle||s):0}}class PieController extends DoughnutController{static id="pie";static defaults={cutout:0,rotation:0,circumference:360,radius:"100%"}}class RadarController extends DatasetController{static id="radar";static defaults={datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:true,elements:{line:{fill:"start"}}};static overrides={aspectRatio:1,scales:{r:{type:"radialLinear"}}};getLabelAndValue(t){const e=this._cachedMeta.vScale;const s=this.getParsed(t);return{label:e.getLabels()[t],value:""+e.getLabelForValue(s[e.axis])}}parseObjectData(t,e,s,i){return P.bind(this)(t,e,s,i)}update(t){const e=this._cachedMeta;const s=e.dataset;const i=e.data||[];const n=e.iScale.getLabels();s.points=i;if(t!=="resize"){const e=this.resolveDatasetElementOptions(t);this.options.showLine||(e.borderWidth=0);const o={_loop:true,_fullLoop:n.length===i.length,options:e};this.updateElement(s,void 0,o,t)}this.updateElements(i,0,i.length,t)}updateElements(t,e,s,i){const n=this._cachedMeta.rScale;const o=i==="reset";for(let a=e;a<e+s;a++){const e=t[a];const s=this.resolveDataElementOptions(a,e.active?"active":i);const r=n.getPointPositionForValue(a,this.getParsed(a).r);const l=o?n.xCenter:r.x;const c=o?n.yCenter:r.y;const h={x:l,y:c,angle:r.angle,skip:isNaN(l)||isNaN(c),options:s};this.updateElement(e,a,h,i)}}}class ScatterController extends DatasetController{static id="scatter";static defaults={datasetElementType:false,dataElementType:"point",showLine:false,fill:false};static overrides={interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}};getLabelAndValue(t){const e=this._cachedMeta;const s=this.chart.data.labels||[];const{xScale:i,yScale:n}=e;const o=this.getParsed(t);const a=i.getLabelForValue(o.x);const r=n.getLabelForValue(o.y);return{label:s[t]||"",value:"("+a+", "+r+")"}}update(t){const e=this._cachedMeta;const{data:s=[]}=e;const i=this.chart._animationsDisabled;let{start:n,count:o}=w(e,s,i);this._drawStart=n;this._drawCount=o;if(D(e)){n=0;o=s.length}if(this.options.showLine){this.datasetElementType||this.addElements();const{dataset:n,_dataset:o}=e;n._chart=this.chart;n._datasetIndex=this.index;n._decimated=!!o._decimated;n.points=s;const a=this.resolveDatasetElementOptions(t);a.segment=this.options.segment;this.updateElement(n,void 0,{animated:!i,options:a},t)}else if(this.datasetElementType){delete e.dataset;this.datasetElementType=false}this.updateElements(s,n,o,t)}addElements(){const{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line"));super.addElements()}updateElements(t,e,s,i){const n=i==="reset";const{iScale:o,vScale:a,_stacked:r,_dataset:l}=this._cachedMeta;const c=this.resolveDataElementOptions(e,i);const h=this.getSharedOptions(c);const d=this.includeOptions(i,h);const u=o.axis;const g=a.axis;const{spanGaps:f,segment:p}=this.options;const x=C(f)?f:Number.POSITIVE_INFINITY;const b=this.chart._animationsDisabled||n||i==="none";let _=e>0&&this.getParsed(e-1);for(let c=e;c<e+s;++c){const e=t[c];const s=this.getParsed(c);const f=b?e:{};const y=m(s[g]);const v=f[u]=o.getPixelForValue(s[u],c);const k=f[g]=n||y?a.getBasePixel():a.getPixelForValue(r?this.applyStack(a,s,r):s[g],c);f.skip=isNaN(v)||isNaN(k)||y;f.stop=c>0&&Math.abs(s[u]-_[u])>x;if(p){f.parsed=s;f.raw=l.data[c]}d&&(f.options=h||this.resolveDataElementOptions(c,e.active?"active":i));b||this.updateElement(e,c,f,i);_=s}this.updateSharedOptions(h,i,c)}getMaxOverflow(){const t=this._cachedMeta;const e=t.data||[];if(!this.options.showLine){let t=0;for(let s=e.length-1;s>=0;--s)t=Math.max(t,e[s].size(this.resolveDataElementOptions(s))/2);return t>0&&t}const s=t.dataset;const i=s.options&&s.options.borderWidth||0;if(!e.length)return i;const n=e[0].size(this.resolveDataElementOptions(0));const o=e[e.length-1].size(this.resolveDataElementOptions(e.length-1));return Math.max(i,n,o)/2}}var ee=Object.freeze({__proto__:null,BarController:BarController,BubbleController:BubbleController,DoughnutController:DoughnutController,LineController:LineController,PieController:PieController,PolarAreaController:PolarAreaController,RadarController:RadarController,ScatterController:ScatterController});function abstract(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class DateAdapterBase{static override(t){Object.assign(DateAdapterBase.prototype,t)}options;constructor(t){this.options=t||{}}init(){}formats(){return abstract()}parse(){return abstract()}format(){return abstract()}add(){return abstract()}diff(){return abstract()}startOf(){return abstract()}endOf(){return abstract()}}var se={_date:DateAdapterBase};function binarySearch(t,e,s,i){const{controller:n,data:o,_sorted:a}=t;const r=n._cachedMeta.iScale;if(r&&e===r.axis&&e!=="r"&&a&&o.length){const t=r._reversePixels?A:L;if(!i)return t(o,e,s);if(n._sharedOptions){const i=o[0];const n=typeof i.getRange==="function"&&i.getRange(e);if(n){const i=t(o,e,s-n);const a=t(o,e,s+n);return{lo:i.lo,hi:a.hi}}}}return{lo:0,hi:o.length-1}}function evaluateInteractionItems(t,e,s,i,n){const o=t.getSortedVisibleDatasetMetas();const a=s[e];for(let t=0,s=o.length;t<s;++t){const{index:s,data:r}=o[t];const{lo:l,hi:c}=binarySearch(o[t],e,a,n);for(let t=l;t<=c;++t){const e=r[t];e.skip||i(e,s,t)}}}function getDistanceMetricForAxis(t){const e=t.indexOf("x")!==-1;const s=t.indexOf("y")!==-1;return function(t,i){const n=e?Math.abs(t.x-i.x):0;const o=s?Math.abs(t.y-i.y):0;return Math.sqrt(Math.pow(n,2)+Math.pow(o,2))}}function getIntersectItems(t,e,s,i,n){const o=[];if(!n&&!t.isPointInArea(e))return o;const evaluationFunc=function(s,a,r){(n||T(s,t.chartArea,0))&&s.inRange(e.x,e.y,i)&&o.push({element:s,datasetIndex:a,index:r})};evaluateInteractionItems(t,s,e,evaluationFunc,true);return o}function getNearestRadialItems(t,e,s,i){let n=[];function evaluationFunc(t,s,o){const{startAngle:a,endAngle:r}=t.getProps(["startAngle","endAngle"],i);const{angle:l}=O(t,{x:e.x,y:e.y});b(l,a,r)&&n.push({element:t,datasetIndex:s,index:o})}evaluateInteractionItems(t,s,e,evaluationFunc);return n}function getNearestCartesianItems(t,e,s,i,n,o){let a=[];const r=getDistanceMetricForAxis(s);let l=Number.POSITIVE_INFINITY;function evaluationFunc(s,c,h){const d=s.inRange(e.x,e.y,n);if(i&&!d)return;const u=s.getCenterPoint(n);const g=!!o||t.isPointInArea(u);if(!g&&!d)return;const f=r(e,u);if(f<l){a=[{element:s,datasetIndex:c,index:h}];l=f}else f===l&&a.push({element:s,datasetIndex:c,index:h})}evaluateInteractionItems(t,s,e,evaluationFunc);return a}function getNearestItems(t,e,s,i,n,o){return o||t.isPointInArea(e)?s!=="r"||i?getNearestCartesianItems(t,e,s,i,n,o):getNearestRadialItems(t,e,s,n):[]}function getAxisItems(t,e,s,i,n){const o=[];const a=s==="x"?"inXRange":"inYRange";let r=false;evaluateInteractionItems(t,s,e,((t,i,l)=>{if(t[a](e[s],n)){o.push({element:t,datasetIndex:i,index:l});r=r||t.inRange(e.x,e.y,n)}}));return i&&!r?[]:o}var ie={evaluateInteractionItems:evaluateInteractionItems,modes:{index(t,e,s,i){const n=E(e,t);const o=s.axis||"x";const a=s.includeInvisible||false;const r=s.intersect?getIntersectItems(t,n,o,i,a):getNearestItems(t,n,o,false,i,a);const l=[];if(!r.length)return[];t.getSortedVisibleDatasetMetas().forEach((t=>{const e=r[0].index;const s=t.data[e];s&&!s.skip&&l.push({element:s,datasetIndex:t.index,index:e})}));return l},dataset(t,e,s,i){const n=E(e,t);const o=s.axis||"xy";const a=s.includeInvisible||false;let r=s.intersect?getIntersectItems(t,n,o,i,a):getNearestItems(t,n,o,false,i,a);if(r.length>0){const e=r[0].datasetIndex;const s=t.getDatasetMeta(e).data;r=[];for(let t=0;t<s.length;++t)r.push({element:s[t],datasetIndex:e,index:t})}return r},point(t,e,s,i){const n=E(e,t);const o=s.axis||"xy";const a=s.includeInvisible||false;return getIntersectItems(t,n,o,i,a)},nearest(t,e,s,i){const n=E(e,t);const o=s.axis||"xy";const a=s.includeInvisible||false;return getNearestItems(t,n,o,s.intersect,i,a)},x(t,e,s,i){const n=E(e,t);return getAxisItems(t,n,"x",s.intersect,i)},y(t,e,s,i){const n=E(e,t);return getAxisItems(t,n,"y",s.intersect,i)}}};const ne=["left","top","right","bottom"];function filterByPosition(t,e){return t.filter((t=>t.pos===e))}function filterDynamicPositionByAxis(t,e){return t.filter((t=>ne.indexOf(t.pos)===-1&&t.box.axis===e))}function sortByWeight(t,e){return t.sort(((t,s)=>{const i=e?s:t;const n=e?t:s;return i.weight===n.weight?i.index-n.index:i.weight-n.weight}))}function wrapBoxes(t){const e=[];let s,i,n,o,a,r;for(s=0,i=(t||[]).length;s<i;++s){n=t[s];({position:o,options:{stack:a,stackWeight:r=1}}=n);e.push({index:s,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:a&&o+a,stackWeight:r})}return e}function buildStacks(t){const e={};for(const s of t){const{stack:t,pos:i,stackWeight:n}=s;if(!t||!ne.includes(i))continue;const o=e[t]||(e[t]={count:0,placed:0,weight:0,size:0});o.count++;o.weight+=n}return e}function setLayoutDims(t,e){const s=buildStacks(t);const{vBoxMaxWidth:i,hBoxMaxHeight:n}=e;let o,a,r;for(o=0,a=t.length;o<a;++o){r=t[o];const{fullSize:a}=r.box;const l=s[r.stack];const c=l&&r.stackWeight/l.weight;if(r.horizontal){r.width=c?c*i:a&&e.availableWidth;r.height=n}else{r.width=i;r.height=c?c*n:a&&e.availableHeight}}return s}function buildLayoutBoxes(t){const e=wrapBoxes(t);const s=sortByWeight(e.filter((t=>t.box.fullSize)),true);const i=sortByWeight(filterByPosition(e,"left"),true);const n=sortByWeight(filterByPosition(e,"right"));const o=sortByWeight(filterByPosition(e,"top"),true);const a=sortByWeight(filterByPosition(e,"bottom"));const r=filterDynamicPositionByAxis(e,"x");const l=filterDynamicPositionByAxis(e,"y");return{fullSize:s,leftAndTop:i.concat(o),rightAndBottom:n.concat(l).concat(a).concat(r),chartArea:filterByPosition(e,"chartArea"),vertical:i.concat(n).concat(l),horizontal:o.concat(a).concat(r)}}function getCombinedMax(t,e,s,i){return Math.max(t[s],e[s])+Math.max(t[i],e[i])}function updateMaxPadding(t,e){t.top=Math.max(t.top,e.top);t.left=Math.max(t.left,e.left);t.bottom=Math.max(t.bottom,e.bottom);t.right=Math.max(t.right,e.right)}function updateDims(t,e,s,i){const{pos:o,box:a}=s;const r=t.maxPadding;if(!n(o)){s.size&&(t[o]-=s.size);const e=i[s.stack]||{size:0,count:1};e.size=Math.max(e.size,s.horizontal?a.height:a.width);s.size=e.size/e.count;t[o]+=s.size}a.getPadding&&updateMaxPadding(r,a.getPadding());const l=Math.max(0,e.outerWidth-getCombinedMax(r,t,"left","right"));const c=Math.max(0,e.outerHeight-getCombinedMax(r,t,"top","bottom"));const h=l!==t.w;const d=c!==t.h;t.w=l;t.h=c;return s.horizontal?{same:h,other:d}:{same:d,other:h}}function handleMaxPadding(t){const e=t.maxPadding;function updatePos(s){const i=Math.max(e[s]-t[s],0);t[s]+=i;return i}t.y+=updatePos("top");t.x+=updatePos("left");updatePos("right");updatePos("bottom")}function getMargins(t,e){const s=e.maxPadding;function marginForPositions(t){const i={left:0,top:0,right:0,bottom:0};t.forEach((t=>{i[t]=Math.max(e[t],s[t])}));return i}return marginForPositions(t?["left","right"]:["top","bottom"])}function fitBoxes(t,e,s,i){const n=[];let o,a,r,l,c,h;for(o=0,a=t.length,c=0;o<a;++o){r=t[o];l=r.box;l.update(r.width||e.w,r.height||e.h,getMargins(r.horizontal,e));const{same:a,other:d}=updateDims(e,s,r,i);c|=a&&n.length;h=h||d;l.fullSize||n.push(r)}return c&&fitBoxes(n,e,s,i)||h}function setBoxDims(t,e,s,i,n){t.top=s;t.left=e;t.right=e+i;t.bottom=s+n;t.width=i;t.height=n}function placeBoxes(t,e,s,i){const n=s.padding;let{x:o,y:a}=e;for(const r of t){const t=r.box;const l=i[r.stack]||{count:1,placed:0,weight:1};const c=r.stackWeight/l.weight||1;if(r.horizontal){const i=e.w*c;const o=l.size||t.height;f(l.start)&&(a=l.start);t.fullSize?setBoxDims(t,n.left,a,s.outerWidth-n.right-n.left,o):setBoxDims(t,e.left+l.placed,a,i,o);l.start=a;l.placed+=i;a=t.bottom}else{const i=e.h*c;const a=l.size||t.width;f(l.start)&&(o=l.start);t.fullSize?setBoxDims(t,o,n.top,a,s.outerHeight-n.bottom-n.top):setBoxDims(t,o,e.top+l.placed,a,i);l.start=o;l.placed+=i;o=t.right}}e.x=o;e.y=a}var oe={addBox(t,e){t.boxes||(t.boxes=[]);e.fullSize=e.fullSize||false;e.position=e.position||"top";e.weight=e.weight||0;e._layers=e._layers||function(){return[{z:0,draw(t){e.draw(t)}}]};t.boxes.push(e)},removeBox(t,e){const s=t.boxes?t.boxes.indexOf(e):-1;s!==-1&&t.boxes.splice(s,1)},configure(t,e,s){e.fullSize=s.fullSize;e.position=s.position;e.weight=s.weight},update(t,e,s,i){if(!t)return;const n=I(t.options.layout.padding);const o=Math.max(e-n.width,0);const a=Math.max(s-n.height,0);const r=buildLayoutBoxes(t.boxes);const l=r.vertical;const c=r.horizontal;R(t.boxes,(t=>{typeof t.beforeLayout==="function"&&t.beforeLayout()}));const h=l.reduce(((t,e)=>e.box.options&&e.box.options.display===false?t:t+1),0)||1;const d=Object.freeze({outerWidth:e,outerHeight:s,padding:n,availableWidth:o,availableHeight:a,vBoxMaxWidth:o/2/h,hBoxMaxHeight:a/2});const u=Object.assign({},n);updateMaxPadding(u,I(i));const g=Object.assign({maxPadding:u,w:o,h:a,x:n.left,y:n.top},n);const f=setLayoutDims(l.concat(c),d);fitBoxes(r.fullSize,g,d,f);fitBoxes(l,g,d,f);fitBoxes(c,g,d,f)&&fitBoxes(l,g,d,f);handleMaxPadding(g);placeBoxes(r.leftAndTop,g,d,f);g.x+=g.w;g.y+=g.h;placeBoxes(r.rightAndBottom,g,d,f);t.chartArea={left:g.left,top:g.top,right:g.left+g.w,bottom:g.top+g.h,height:g.h,width:g.w};R(r.chartArea,(e=>{const s=e.box;Object.assign(s,t.chartArea);s.update(g.w,g.h,{left:0,top:0,right:0,bottom:0})}))}};class BasePlatform{acquireContext(t,e){}releaseContext(t){return false}addEventListener(t,e,s){}removeEventListener(t,e,s){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,s,i){e=Math.max(0,e||t.width);s=s||t.height;return{width:e,height:Math.max(0,i?Math.floor(e/i):s)}}isAttached(t){return true}updateConfig(t){}}class BasicPlatform extends BasePlatform{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=false}}const ae="$chartjs";const re={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"};const isNullOrEmpty=t=>t===null||t==="";function initCanvas(t,e){const s=t.style;const i=t.getAttribute("height");const n=t.getAttribute("width");t[ae]={initial:{height:i,width:n,style:{display:s.display,height:s.height,width:s.width}}};s.display=s.display||"block";s.boxSizing=s.boxSizing||"border-box";if(isNullOrEmpty(n)){const e=F(t,"width");e!==void 0&&(t.width=e)}if(isNullOrEmpty(i))if(t.style.height==="")t.height=t.width/(e||2);else{const e=F(t,"height");e!==void 0&&(t.height=e)}return t}const le=!!B&&{passive:true};function addListener(t,e,s){t.addEventListener(e,s,le)}function removeListener(t,e,s){t.canvas.removeEventListener(e,s,le)}function fromNativeEvent(t,e){const s=re[t.type]||t.type;const{x:i,y:n}=E(t,e);return{type:s,chart:e,native:t,x:i!==void 0?i:null,y:n!==void 0?n:null}}function nodeListContains(t,e){for(const s of t)if(s===e||s.contains(e))return true}function createAttachObserver(t,e,s){const i=t.canvas;const n=new MutationObserver((t=>{let e=false;for(const s of t){e=e||nodeListContains(s.addedNodes,i);e=e&&!nodeListContains(s.removedNodes,i)}e&&s()}));n.observe(document,{childList:true,subtree:true});return n}function createDetachObserver(t,e,s){const i=t.canvas;const n=new MutationObserver((t=>{let e=false;for(const s of t){e=e||nodeListContains(s.removedNodes,i);e=e&&!nodeListContains(s.addedNodes,i)}e&&s()}));n.observe(document,{childList:true,subtree:true});return n}const ce=new Map;let he=0;function onWindowResize(){const t=window.devicePixelRatio;if(t!==he){he=t;ce.forEach(((e,s)=>{s.currentDevicePixelRatio!==t&&e()}))}}function listenDevicePixelRatioChanges(t,e){ce.size||window.addEventListener("resize",onWindowResize);ce.set(t,e)}function unlistenDevicePixelRatioChanges(t){ce.delete(t);ce.size||window.removeEventListener("resize",onWindowResize)}function createResizeObserver(t,e,s){const i=t.canvas;const n=i&&z(i);if(!n)return;const o=V(((t,e)=>{const i=n.clientWidth;s(t,e);i<n.clientWidth&&s()}),window);const a=new ResizeObserver((t=>{const e=t[0];const s=e.contentRect.width;const i=e.contentRect.height;s===0&&i===0||o(s,i)}));a.observe(n);listenDevicePixelRatioChanges(t,o);return a}function releaseObserver(t,e,s){s&&s.disconnect();e==="resize"&&unlistenDevicePixelRatioChanges(t)}function createProxyAndListen(t,e,s){const i=t.canvas;const n=V((e=>{t.ctx!==null&&s(fromNativeEvent(e,t))}),t);addListener(i,e,n);return n}class DomPlatform extends BasePlatform{acquireContext(t,e){const s=t&&t.getContext&&t.getContext("2d");if(s&&s.canvas===t){initCanvas(t,e);return s}return null}releaseContext(t){const e=t.canvas;if(!e[ae])return false;const s=e[ae].initial;["height","width"].forEach((t=>{const i=s[t];m(i)?e.removeAttribute(t):e.setAttribute(t,i)}));const i=s.style||{};Object.keys(i).forEach((t=>{e.style[t]=i[t]}));e.width=e.width;delete e[ae];return true}addEventListener(t,e,s){this.removeEventListener(t,e);const i=t.$proxies||(t.$proxies={});const n={attach:createAttachObserver,detach:createDetachObserver,resize:createResizeObserver};const o=n[e]||createProxyAndListen;i[e]=o(t,e,s)}removeEventListener(t,e){const s=t.$proxies||(t.$proxies={});const i=s[e];if(!i)return;const n={attach:releaseObserver,detach:releaseObserver,resize:releaseObserver};const o=n[e]||removeListener;o(t,e,i);s[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,s,i){return W(t,e,s,i)}isAttached(t){const e=z(t);return!!(e&&e.isConnected)}}function _detectPlatform(t){return!N()||typeof OffscreenCanvas!=="undefined"&&t instanceof OffscreenCanvas?BasicPlatform:DomPlatform}class Element{static defaults={};static defaultRoutes=void 0;x;y;active=false;options;$animations;tooltipPosition(t){const{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}hasValue(){return C(this.x)&&C(this.y)}getProps(t,e){const s=this.$animations;if(!e||!s)return this;const i={};t.forEach((t=>{i[t]=s[t]&&s[t].active()?s[t]._to:this[t]}));return i}}function autoSkip(t,e){const s=t.options.ticks;const i=determineMaxTicks(t);const n=Math.min(s.maxTicksLimit||i,i);const o=s.major.enabled?getMajorIndices(e):[];const a=o.length;const r=o[0];const l=o[a-1];const c=[];if(a>n){skipMajors(e,c,o,a/n);return c}const h=calculateSpacing(o,e,n);if(a>0){let t,s;const i=a>1?Math.round((l-r)/(a-1)):null;skip(e,c,h,m(i)?0:r-i,r);for(t=0,s=a-1;t<s;t++)skip(e,c,h,o[t],o[t+1]);skip(e,c,h,l,m(i)?e.length:l+i);return c}skip(e,c,h);return c}function determineMaxTicks(t){const e=t.options.offset;const s=t._tickSize();const i=t._length/s+(e?0:1);const n=t._maxLength/s;return Math.floor(Math.min(i,n))}function calculateSpacing(t,e,s){const i=getEvenSpacing(t);const n=e.length/s;if(!i)return Math.max(n,1);const o=H(i);for(let t=0,e=o.length-1;t<e;t++){const e=o[t];if(e>n)return e}return Math.max(n,1)}function getMajorIndices(t){const e=[];let s,i;for(s=0,i=t.length;s<i;s++)t[s].major&&e.push(s);return e}function skipMajors(t,e,s,i){let n=0;let o=s[0];let a;i=Math.ceil(i);for(a=0;a<t.length;a++)if(a===o){e.push(t[a]);n++;o=s[n*i]}}function skip(t,e,s,i,n){const o=h(i,0);const a=Math.min(h(n,t.length),t.length);let r=0;let l,c,d;s=Math.ceil(s);if(n){l=n-i;s=l/Math.floor(l/s)}d=o;while(d<0){r++;d=Math.round(o+r*s)}for(c=Math.max(o,0);c<a;c++)if(c===d){e.push(t[c]);r++;d=Math.round(o+r*s)}}function getEvenSpacing(t){const e=t.length;let s,i;if(e<2)return false;for(i=t[0],s=1;s<e;++s)if(t[s]-t[s-1]!==i)return false;return i}const reverseAlign=t=>t==="left"?"right":t==="right"?"left":t;const offsetFromEdge=(t,e,s)=>e==="top"||e==="left"?t[e]+s:t[e]-s;const getTicksLimit=(t,e)=>Math.min(e||t,t);function sample(t,e){const s=[];const i=t.length/e;const n=t.length;let o=0;for(;o<n;o+=i)s.push(t[Math.floor(o)]);return s}function getPixelForGridLine(t,e,s){const i=t.ticks.length;const n=Math.min(e,i-1);const o=t._startPixel;const a=t._endPixel;const r=1e-6;let l=t.getPixelForTick(n);let c;if(s){c=i===1?Math.max(l-o,a-l):e===0?(t.getPixelForTick(1)-l)/2:(l-t.getPixelForTick(n-1))/2;l+=n<e?c:-c;if(l<o-r||l>a+r)return}return l}function garbageCollect(t,e){R(t,(t=>{const s=t.gc;const i=s.length/2;let n;if(i>e){for(n=0;n<i;++n)delete t.data[s[n]];s.splice(0,i)}}))}function getTickMarkLength(t){return t.drawTicks?t.tickLength:0}function getTitleHeight(t,e){if(!t.display)return 0;const s=j(t.font,e);const i=I(t.padding);const n=a(t.text)?t.text.length:1;return n*s.lineHeight+i.height}function createScaleContext(t,e){return c(t,{scale:e,type:"scale"})}function createTickContext(t,e,s){return c(t,{tick:s,index:e,type:"tick"})}function titleAlign(t,e,s){let i=$(t);(s&&e!=="right"||!s&&e==="right")&&(i=reverseAlign(i));return i}function titleArgs(t,e,s,i){const{top:o,left:a,bottom:r,right:l,chart:c}=t;const{chartArea:h,scales:d}=c;let u=0;let g,f,p;const m=r-o;const x=l-a;if(t.isHorizontal()){f=U(i,a,l);if(n(s)){const t=Object.keys(s)[0];const i=s[t];p=d[t].getPixelForValue(i)+m-e}else p=s==="center"?(h.bottom+h.top)/2+m-e:offsetFromEdge(t,s,e);g=l-a}else{if(n(s)){const t=Object.keys(s)[0];const i=s[t];f=d[t].getPixelForValue(i)-x+e}else f=s==="center"?(h.left+h.right)/2-x+e:offsetFromEdge(t,s,e);p=U(i,r,o);u=s==="left"?-_:_}return{titleX:f,titleY:p,maxWidth:g,rotation:u}}class Scale extends Element{constructor(t){super();this.id=t.id;this.type=t.type;this.options=void 0;this.ctx=t.ctx;this.chart=t.chart;this.top=void 0;this.bottom=void 0;this.left=void 0;this.right=void 0;this.width=void 0;this.height=void 0;this._margins={left:0,right:0,top:0,bottom:0};this.maxWidth=void 0;this.maxHeight=void 0;this.paddingTop=void 0;this.paddingBottom=void 0;this.paddingLeft=void 0;this.paddingRight=void 0;this.axis=void 0;this.labelRotation=void 0;this.min=void 0;this.max=void 0;this._range=void 0;this.ticks=[];this._gridLineItems=null;this._labelItems=null;this._labelSizes=null;this._length=0;this._maxLength=0;this._longestTextCache={};this._startPixel=void 0;this._endPixel=void 0;this._reversePixels=false;this._userMax=void 0;this._userMin=void 0;this._suggestedMax=void 0;this._suggestedMin=void 0;this._ticksLength=0;this._borderValue=0;this._cache={};this._dataLimitsCached=false;this.$context=void 0}init(t){this.options=t.setContext(this.getContext());this.axis=t.axis;this._userMin=this.parse(t.min);this._userMax=this.parse(t.max);this._suggestedMin=this.parse(t.suggestedMin);this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:s,_suggestedMax:i}=this;t=Y(t,Number.POSITIVE_INFINITY);e=Y(e,Number.NEGATIVE_INFINITY);s=Y(s,Number.POSITIVE_INFINITY);i=Y(i,Number.NEGATIVE_INFINITY);return{min:Y(t,s),max:Y(e,i),minDefined:r(t),maxDefined:r(e)}}getMinMax(t){let{min:e,max:s,minDefined:i,maxDefined:n}=this.getUserBounds();let o;if(i&&n)return{min:e,max:s};const a=this.getMatchingVisibleMetas();for(let r=0,l=a.length;r<l;++r){o=a[r].controller.getMinMax(this,t);i||(e=Math.min(e,o.min));n||(s=Math.max(s,o.max))}e=n&&e>s?s:e;s=i&&e>s?e:s;return{min:Y(e,Y(s,e)),max:Y(s,Y(e,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){const e=this._labelItems||(this._labelItems=this._computeLabelItems(t));return e}beforeLayout(){this._cache={};this._dataLimitsCached=false}beforeUpdate(){X(this.options.beforeUpdate,[this])}update(t,e,s){const{beginAtZero:i,grace:n,ticks:o}=this.options;const a=o.sampleSize;this.beforeUpdate();this.maxWidth=t;this.maxHeight=e;this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s);this.ticks=null;this._labelSizes=null;this._gridLineItems=null;this._labelItems=null;this.beforeSetDimensions();this.setDimensions();this.afterSetDimensions();this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom;if(!this._dataLimitsCached){this.beforeDataLimits();this.determineDataLimits();this.afterDataLimits();this._range=K(this,n,i);this._dataLimitsCached=true}this.beforeBuildTicks();this.ticks=this.buildTicks()||[];this.afterBuildTicks();const r=a<this.ticks.length;this._convertTicksToLabels(r?sample(this.ticks,a):this.ticks);this.configure();this.beforeCalculateLabelRotation();this.calculateLabelRotation();this.afterCalculateLabelRotation();if(o.display&&(o.autoSkip||o.source==="auto")){this.ticks=autoSkip(this,this.ticks);this._labelSizes=null;this.afterAutoSkip()}r&&this._convertTicksToLabels(this.ticks);this.beforeFit();this.fit();this.afterFit();this.afterUpdate()}configure(){let t=this.options.reverse;let e,s;if(this.isHorizontal()){e=this.left;s=this.right}else{e=this.top;s=this.bottom;t=!t}this._startPixel=e;this._endPixel=s;this._reversePixels=t;this._length=s-e;this._alignToPixels=this.options.alignToPixels}afterUpdate(){X(this.options.afterUpdate,[this])}beforeSetDimensions(){X(this.options.beforeSetDimensions,[this])}setDimensions(){if(this.isHorizontal()){this.width=this.maxWidth;this.left=0;this.right=this.width}else{this.height=this.maxHeight;this.top=0;this.bottom=this.height}this.paddingLeft=0;this.paddingTop=0;this.paddingRight=0;this.paddingBottom=0}afterSetDimensions(){X(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext());X(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){X(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let s,i,n;for(s=0,i=t.length;s<i;s++){n=t[s];n.label=X(e.callback,[n.value,s,t],this)}}afterTickToLabelConversion(){X(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){X(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options;const e=t.ticks;const s=getTicksLimit(this.ticks.length,t.ticks.maxTicksLimit);const i=e.minRotation||0;const n=e.maxRotation;let o=i;let a,r,l;if(!this._isVisible()||!e.display||i>=n||s<=1||!this.isHorizontal()){this.labelRotation=i;return}const c=this._getLabelSizes();const h=c.widest.width;const d=c.highest.height;const u=G(this.chart.width-h,0,this.maxWidth);a=t.offset?this.maxWidth/s:u/(s-1);if(h+6>a){a=u/(s-(t.offset?.5:1));r=this.maxHeight-getTickMarkLength(t.grid)-e.padding-getTitleHeight(t.title,this.chart.options.font);l=Math.sqrt(h*h+d*d);o=q(Math.min(Math.asin(G((c.highest.height+6)/a,-1,1)),Math.asin(G(r/l,-1,1))-Math.asin(G(d/l,-1,1))));o=Math.max(i,Math.min(n,o))}this.labelRotation=o}afterCalculateLabelRotation(){X(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){X(this.options.beforeFit,[this])}fit(){const t={width:0,height:0};const{chart:e,options:{ticks:s,title:i,grid:n}}=this;const o=this._isVisible();const a=this.isHorizontal();if(o){const o=getTitleHeight(i,e.options.font);if(a){t.width=this.maxWidth;t.height=getTickMarkLength(n)+o}else{t.height=this.maxHeight;t.width=getTickMarkLength(n)+o}if(s.display&&this.ticks.length){const{first:e,last:i,widest:n,highest:o}=this._getLabelSizes();const r=s.padding*2;const l=v(this.labelRotation);const c=Math.cos(l);const h=Math.sin(l);if(a){const e=s.mirror?0:h*n.width+c*o.height;t.height=Math.min(this.maxHeight,t.height+e+r)}else{const e=s.mirror?0:c*n.width+h*o.height;t.width=Math.min(this.maxWidth,t.width+e+r)}this._calculatePadding(e,i,h,c)}}this._handleMargins();if(a){this.width=this._length=e.width-this._margins.left-this._margins.right;this.height=t.height}else{this.width=t.width;this.height=this._length=e.height-this._margins.top-this._margins.bottom}}_calculatePadding(t,e,s,i){const{ticks:{align:n,padding:o},position:a}=this.options;const r=this.labelRotation!==0;const l=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const a=this.getPixelForTick(0)-this.left;const c=this.right-this.getPixelForTick(this.ticks.length-1);let h=0;let d=0;if(r)if(l){h=i*t.width;d=s*e.height}else{h=s*t.height;d=i*e.width}else if(n==="start")d=e.width;else if(n==="end")h=t.width;else if(n!=="inner"){h=t.width/2;d=e.width/2}this.paddingLeft=Math.max((h-a+o)*this.width/(this.width-a),0);this.paddingRight=Math.max((d-c+o)*this.width/(this.width-c),0)}else{let s=e.height/2;let i=t.height/2;if(n==="start"){s=0;i=t.height}else if(n==="end"){s=e.height;i=0}this.paddingTop=s+o;this.paddingBottom=i+o}}_handleMargins(){if(this._margins){this._margins.left=Math.max(this.paddingLeft,this._margins.left);this._margins.top=Math.max(this.paddingTop,this._margins.top);this._margins.right=Math.max(this.paddingRight,this._margins.right);this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom)}}afterFit(){X(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion();this.generateTickLabels(t);let e,s;for(e=0,s=t.length;e<s;e++)if(m(t[e].label)){t.splice(e,1);s--;e--}this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let s=this.ticks;e<s.length&&(s=sample(s,e));this._labelSizes=t=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,s){const{ctx:i,_longestTextCache:n}=this;const o=[];const r=[];const l=Math.floor(e/getTicksLimit(e,s));let c=0;let h=0;let d,u,g,f,p,x,b,_,y,v,k;for(d=0;d<e;d+=l){f=t[d].label;p=this._resolveTickFontOptions(d);i.font=x=p.string;b=n[x]=n[x]||{data:{},gc:[]};_=p.lineHeight;y=v=0;if(m(f)||a(f)){if(a(f))for(u=0,g=f.length;u<g;++u){k=f[u];if(!m(k)&&!a(k)){y=J(i,b.data,b.gc,y,k);v+=_}}}else{y=J(i,b.data,b.gc,y,f);v=_}o.push(y);r.push(v);c=Math.max(y,c);h=Math.max(v,h)}garbageCollect(n,e);const M=o.indexOf(c);const S=r.indexOf(h);const valueAt=t=>({width:o[t]||0,height:r[t]||0});return{first:valueAt(0),last:valueAt(e-1),widest:valueAt(M),highest:valueAt(S),widths:o,heights:r}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return Q(this._alignToPixels?Z(this.chart,e,0):e)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const s=e[t];return s.$context||(s.$context=createTickContext(this.getContext(),t,s))}return this.$context||(this.$context=createScaleContext(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks;const e=v(this.labelRotation);const s=Math.abs(Math.cos(e));const i=Math.abs(Math.sin(e));const n=this._getLabelSizes();const o=t.autoSkipPadding||0;const a=n?n.widest.width+o:0;const r=n?n.highest.height+o:0;return this.isHorizontal()?r*s>a*i?a/s:r/i:r*i<a*s?r/s:a/i}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis;const s=this.chart;const i=this.options;const{grid:o,position:a,border:r}=i;const l=o.offset;const c=this.isHorizontal();const d=this.ticks;const u=d.length+(l?1:0);const g=getTickMarkLength(o);const f=[];const p=r.setContext(this.getContext());const m=p.display?p.width:0;const x=m/2;const alignBorderValue=function(t){return Z(s,t,m)};let b,_,y,v;let k,M,S,w,D,C,P,A;if(a==="top"){b=alignBorderValue(this.bottom);M=this.bottom-g;w=b-x;C=alignBorderValue(t.top)+x;A=t.bottom}else if(a==="bottom"){b=alignBorderValue(this.top);C=t.top;A=alignBorderValue(t.bottom)-x;M=b+x;w=this.top+g}else if(a==="left"){b=alignBorderValue(this.right);k=this.right-g;S=b-x;D=alignBorderValue(t.left)+x;P=t.right}else if(a==="right"){b=alignBorderValue(this.left);D=t.left;P=alignBorderValue(t.right)-x;k=b+x;S=this.left+g}else if(e==="x"){if(a==="center")b=alignBorderValue((t.top+t.bottom)/2+.5);else if(n(a)){const t=Object.keys(a)[0];const e=a[t];b=alignBorderValue(this.chart.scales[t].getPixelForValue(e))}C=t.top;A=t.bottom;M=b+x;w=M+g}else if(e==="y"){if(a==="center")b=alignBorderValue((t.left+t.right)/2);else if(n(a)){const t=Object.keys(a)[0];const e=a[t];b=alignBorderValue(this.chart.scales[t].getPixelForValue(e))}k=b-x;S=k-g;D=t.left;P=t.right}const L=h(i.ticks.maxTicksLimit,u);const T=Math.max(1,Math.ceil(u/L));for(_=0;_<u;_+=T){const t=this.getContext(_);const e=o.setContext(t);const i=r.setContext(t);const n=e.lineWidth;const a=e.color;const h=i.dash||[];const d=i.dashOffset;const u=e.tickWidth;const g=e.tickColor;const p=e.tickBorderDash||[];const m=e.tickBorderDashOffset;y=getPixelForGridLine(this,_,l);if(y!==void 0){v=Z(s,y,n);c?k=S=D=P=v:M=w=C=A=v;f.push({tx1:k,ty1:M,tx2:S,ty2:w,x1:D,y1:C,x2:P,y2:A,width:n,color:a,borderDash:h,borderDashOffset:d,tickWidth:u,tickColor:g,tickBorderDash:p,tickBorderDashOffset:m})}}this._ticksLength=u;this._borderValue=b;return f}_computeLabelItems(t){const e=this.axis;const s=this.options;const{position:i,ticks:o}=s;const r=this.isHorizontal();const l=this.ticks;const{align:c,crossAlign:h,padding:d,mirror:u}=o;const g=getTickMarkLength(s.grid);const f=g+d;const p=u?-d:f;const m=-v(this.labelRotation);const x=[];let b,_,y,k,M,S,w,D,C,P,A,L;let T="middle";if(i==="top"){S=this.bottom-p;w=this._getXAxisLabelAlignment()}else if(i==="bottom"){S=this.top+p;w=this._getXAxisLabelAlignment()}else if(i==="left"){const t=this._getYAxisLabelAlignment(g);w=t.textAlign;M=t.x}else if(i==="right"){const t=this._getYAxisLabelAlignment(g);w=t.textAlign;M=t.x}else if(e==="x"){if(i==="center")S=(t.top+t.bottom)/2+f;else if(n(i)){const t=Object.keys(i)[0];const e=i[t];S=this.chart.scales[t].getPixelForValue(e)+f}w=this._getXAxisLabelAlignment()}else if(e==="y"){if(i==="center")M=(t.left+t.right)/2-f;else if(n(i)){const t=Object.keys(i)[0];const e=i[t];M=this.chart.scales[t].getPixelForValue(e)}w=this._getYAxisLabelAlignment(g).textAlign}e==="y"&&(c==="start"?T="top":c==="end"&&(T="bottom"));const O=this._getLabelSizes();for(b=0,_=l.length;b<_;++b){y=l[b];k=y.label;const t=o.setContext(this.getContext(b));D=this.getPixelForTick(b)+o.labelOffset;C=this._resolveTickFontOptions(b);P=C.lineHeight;A=a(k)?k.length:1;const e=A/2;const s=t.color;const n=t.textStrokeColor;const c=t.textStrokeWidth;let d=w;if(r){M=D;w==="inner"&&(d=b===_-1?this.options.reverse?"left":"right":b===0?this.options.reverse?"right":"left":"center");L=i==="top"?h==="near"||m!==0?-A*P+P/2:h==="center"?-O.highest.height/2-e*P+P:-O.highest.height+P/2:h==="near"||m!==0?P/2:h==="center"?O.highest.height/2-e*P:O.highest.height-A*P;u&&(L*=-1);m===0||t.showLabelBackdrop||(M+=P/2*Math.sin(m))}else{S=D;L=(1-A)*P/2}let g;if(t.showLabelBackdrop){const e=I(t.backdropPadding);const s=O.heights[b];const i=O.widths[b];let n=L-e.top;let o=0-e.left;switch(T){case"middle":n-=s/2;break;case"bottom":n-=s;break}switch(w){case"center":o-=i/2;break;case"right":o-=i;break;case"inner":b===_-1?o-=i:b>0&&(o-=i/2);break}g={left:o,top:n,width:i+e.width,height:s+e.height,color:t.backdropColor}}x.push({label:k,font:C,textOffset:L,options:{rotation:m,color:s,strokeColor:n,strokeWidth:c,textAlign:d,textBaseline:T,translation:[M,S],backdrop:g}})}return x}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;const s=-v(this.labelRotation);if(s)return t==="top"?"left":"right";let i="center";e.align==="start"?i="left":e.align==="end"?i="right":e.align==="inner"&&(i="inner");return i}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:s,mirror:i,padding:n}}=this.options;const o=this._getLabelSizes();const a=t+n;const r=o.widest.width;let l;let c;if(e==="left")if(i){c=this.right+n;if(s==="near")l="left";else if(s==="center"){l="center";c+=r/2}else{l="right";c+=r}}else{c=this.right-a;if(s==="near")l="right";else if(s==="center"){l="center";c-=r/2}else{l="left";c=this.left}}else if(e==="right")if(i){c=this.left+n;if(s==="near")l="right";else if(s==="center"){l="center";c-=r/2}else{l="left";c-=r}}else{c=this.left+a;if(s==="near")l="left";else if(s==="center"){l="center";c+=r/2}else{l="right";c=this.right}}else l="right";return{textAlign:l,x:c}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart;const e=this.options.position;return e==="left"||e==="right"?{top:0,left:this.left,bottom:t.height,right:this.right}:e==="top"||e==="bottom"?{top:this.top,left:0,bottom:this.bottom,right:t.width}:void 0}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:s,top:i,width:n,height:o}=this;if(e){t.save();t.fillStyle=e;t.fillRect(s,i,n,o);t.restore()}}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const s=this.ticks;const i=s.findIndex((e=>e.value===t));if(i>=0){const t=e.setContext(this.getContext(i));return t.lineWidth}return 0}drawGrid(t){const e=this.options.grid;const s=this.ctx;const i=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let n,o;const drawLine=(t,e,i)=>{if(i.width&&i.color){s.save();s.lineWidth=i.width;s.strokeStyle=i.color;s.setLineDash(i.borderDash||[]);s.lineDashOffset=i.borderDashOffset;s.beginPath();s.moveTo(t.x,t.y);s.lineTo(e.x,e.y);s.stroke();s.restore()}};if(e.display)for(n=0,o=i.length;n<o;++n){const t=i[n];e.drawOnChartArea&&drawLine({x:t.x1,y:t.y1},{x:t.x2,y:t.y2},t);e.drawTicks&&drawLine({x:t.tx1,y:t.ty1},{x:t.tx2,y:t.ty2},{color:t.tickColor,width:t.tickWidth,borderDash:t.tickBorderDash,borderDashOffset:t.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:s,grid:i}}=this;const n=s.setContext(this.getContext());const o=s.display?n.width:0;if(!o)return;const a=i.setContext(this.getContext(0)).lineWidth;const r=this._borderValue;let l,c,h,d;if(this.isHorizontal()){l=Z(t,this.left,o)-o/2;c=Z(t,this.right,a)+a/2;h=d=r}else{h=Z(t,this.top,o)-o/2;d=Z(t,this.bottom,a)+a/2;l=c=r}e.save();e.lineWidth=n.width;e.strokeStyle=n.color;e.beginPath();e.moveTo(l,h);e.lineTo(c,d);e.stroke();e.restore()}drawLabels(t){const e=this.options.ticks;if(!e.display)return;const s=this.ctx;const i=this._computeLabelArea();i&&tt(s,i);const n=this.getLabelItems(t);for(const t of n){const e=t.options;const i=t.font;const n=t.label;const o=t.textOffset;et(s,n,0,o,i,e)}i&&st(s)}drawTitle(){const{ctx:t,options:{position:e,title:s,reverse:i}}=this;if(!s.display)return;const o=j(s.font);const r=I(s.padding);const l=s.align;let c=o.lineHeight/2;if(e==="bottom"||e==="center"||n(e)){c+=r.bottom;a(s.text)&&(c+=o.lineHeight*(s.text.length-1))}else c+=r.top;const{titleX:h,titleY:d,maxWidth:u,rotation:g}=titleArgs(this,c,e,l);et(t,s.text,0,0,o,{color:s.color,maxWidth:u,rotation:g,textAlign:titleAlign(l,e,i),textBaseline:"middle",translation:[h,d]})}draw(t){if(this._isVisible()){this.drawBackground();this.drawGrid(t);this.drawBorder();this.drawTitle();this.drawLabels(t)}}_layers(){const t=this.options;const e=t.ticks&&t.ticks.z||0;const s=h(t.grid&&t.grid.z,-1);const i=h(t.border&&t.border.z,0);return this._isVisible()&&this.draw===Scale.prototype.draw?[{z:s,draw:t=>{this.drawBackground();this.drawGrid(t);this.drawTitle()}},{z:i,draw:()=>{this.drawBorder()}},{z:e,draw:t=>{this.drawLabels(t)}}]:[{z:e,draw:t=>{this.draw(t)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas();const s=this.axis+"AxisID";const i=[];let n,o;for(n=0,o=e.length;n<o;++n){const o=e[n];o[s]!==this.id||t&&o.type!==t||i.push(o)}return i}_resolveTickFontOptions(t){const e=this.options.ticks.setContext(this.getContext(t));return j(e.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class TypedRegistry{constructor(t,e,s){this.type=t;this.scope=e;this.override=s;this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let s;isIChartComponent(e)&&(s=this.register(e));const i=this.items;const n=t.id;const a=this.scope+"."+n;if(!n)throw new Error("class does not have id: "+t);if(n in i)return a;i[n]=t;registerDefaults(t,a,s);this.override&&o.override(t.id,t.overrides);return a}get(t){return this.items[t]}unregister(t){const e=this.items;const s=t.id;const i=this.scope;s in e&&delete e[s];if(i&&s in o[i]){delete o[i][s];this.override&&delete it[s]}}}function registerDefaults(t,e,s){const i=nt(Object.create(null),[s?o.get(s):{},o.get(e),t.defaults]);o.set(e,i);t.defaultRoutes&&routeDefaults(e,t.defaultRoutes);t.descriptors&&o.describe(e,t.descriptors)}function routeDefaults(t,e){Object.keys(e).forEach((s=>{const i=s.split(".");const n=i.pop();const a=[t].concat(i).join(".");const r=e[s].split(".");const l=r.pop();const c=r.join(".");o.route(a,n,c,l)}))}function isIChartComponent(t){return"id"in t&&"defaults"in t}class Registry{constructor(){this.controllers=new TypedRegistry(DatasetController,"datasets",true);this.elements=new TypedRegistry(Element,"elements");this.plugins=new TypedRegistry(Object,"plugins");this.scales=new TypedRegistry(Scale,"scales");this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,s){[...e].forEach((e=>{const i=s||this._getRegistryForType(e);s||i.isForType(e)||i===this.plugins&&e.id?this._exec(t,i,e):R(e,(e=>{const i=s||this._getRegistryForType(e);this._exec(t,i,e)}))}))}_exec(t,e,s){const i=ot(t);X(s["before"+i],[],s);e[t](s);X(s["after"+i],[],s)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const s=this._typedRegistries[e];if(s.isForType(t))return s}return this.plugins}_get(t,e,s){const i=e.get(t);if(i===void 0)throw new Error('"'+t+'" is not a registered '+s+".");return i}}var de=new Registry;class PluginService{constructor(){this._init=[]}notify(t,e,s,i){if(e==="beforeInit"){this._init=this._createDescriptors(t,true);this._notify(this._init,t,"install")}const n=i?this._descriptors(t).filter(i):this._descriptors(t);const o=this._notify(n,t,e,s);if(e==="afterDestroy"){this._notify(n,t,"stop");this._notify(this._init,t,"uninstall")}return o}_notify(t,e,s,i){i=i||{};for(const n of t){const t=n.plugin;const o=t[s];const a=[e,i,n.options];if(X(o,a,t)===false&&i.cancelable)return false}return true}invalidate(){if(!m(this._cache)){this._oldCache=this._cache;this._cache=void 0}}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);this._notifyStateChanges(t);return e}_createDescriptors(t,e){const s=t&&t.config;const i=h(s.options&&s.options.plugins,{});const n=allPlugins(s);return i!==false||e?createDescriptors(t,n,i,e):[]}_notifyStateChanges(t){const e=this._oldCache||[];const s=this._cache;const diff=(t,e)=>t.filter((t=>!e.some((e=>t.plugin.id===e.plugin.id))));this._notify(diff(e,s),t,"stop");this._notify(diff(s,e),t,"start")}}function allPlugins(t){const e={};const s=[];const i=Object.keys(de.plugins.items);for(let t=0;t<i.length;t++)s.push(de.getPlugin(i[t]));const n=t.plugins||[];for(let t=0;t<n.length;t++){const i=n[t];if(s.indexOf(i)===-1){s.push(i);e[i.id]=true}}return{plugins:s,localIds:e}}function getOpts(t,e){return e||t!==false?t===true?{}:t:null}function createDescriptors(t,{plugins:e,localIds:s},i,n){const o=[];const a=t.getContext();for(const r of e){const e=r.id;const l=getOpts(i[e],n);l!==null&&o.push({plugin:r,options:pluginOpts(t.config,{plugin:r,local:s[e]},l,a)})}return o}function pluginOpts(t,{plugin:e,local:s},i,n){const o=t.pluginScopeKeys(e);const a=t.getOptionScopes(i,o);s&&e.defaults&&a.push(e.defaults);return t.createResolver(a,n,[""],{scriptable:false,indexable:false,allKeys:true})}function getIndexAxis(t,e){const s=o.datasets[t]||{};const i=(e.datasets||{})[t]||{};return i.indexAxis||e.indexAxis||s.indexAxis||"x"}function getAxisFromDefaultScaleID(t,e){let s=t;t==="_index_"?s=e:t==="_value_"&&(s=e==="x"?"y":"x");return s}function getDefaultScaleIDFromAxis(t,e){return t===e?"_index_":"_value_"}function idMatchesAxis(t){if(t==="x"||t==="y"||t==="r")return t}function axisFromPosition(t){return t==="top"||t==="bottom"?"x":t==="left"||t==="right"?"y":void 0}function determineAxis(t,...e){if(idMatchesAxis(t))return t;for(const s of e){const e=s.axis||axisFromPosition(s.position)||t.length>1&&idMatchesAxis(t[0].toLowerCase());if(e)return e}throw new Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function getAxisFromDataset(t,e,s){if(s[e+"AxisID"]===t)return{axis:e}}function retrieveAxisFromDatasets(t,e){if(e.data&&e.data.datasets){const s=e.data.datasets.filter((e=>e.xAxisID===t||e.yAxisID===t));if(s.length)return getAxisFromDataset(t,"x",s[0])||getAxisFromDataset(t,"y",s[0])}return{}}function mergeScaleConfig(t,e){const s=it[t.type]||{scales:{}};const i=e.scales||{};const a=getIndexAxis(t.type,e);const r=Object.create(null);Object.keys(i).forEach((e=>{const l=i[e];if(!n(l))return console.error(`Invalid scale configuration for scale: ${e}`);if(l._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${e}`);const c=determineAxis(e,l,retrieveAxisFromDatasets(e,t),o.scales[l.type]);const h=getDefaultScaleIDFromAxis(c,a);const d=s.scales||{};r[e]=at(Object.create(null),[{axis:c},l,d[c],d[h]])}));t.data.datasets.forEach((s=>{const n=s.type||t.type;const o=s.indexAxis||getIndexAxis(n,e);const a=it[n]||{};const l=a.scales||{};Object.keys(l).forEach((t=>{const e=getAxisFromDefaultScaleID(t,o);const n=s[e+"AxisID"]||e;r[n]=r[n]||Object.create(null);at(r[n],[{axis:e},i[n],l[t]])}))}));Object.keys(r).forEach((t=>{const e=r[t];at(e,[o.scales[e.type],o.scale])}));return r}function initOptions(t){const e=t.options||(t.options={});e.plugins=h(e.plugins,{});e.scales=mergeScaleConfig(t,e)}function initData(t){t=t||{};t.datasets=t.datasets||[];t.labels=t.labels||[];return t}function initConfig(t){t=t||{};t.data=initData(t.data);initOptions(t);return t}const ue=new Map;const ge=new Set;function cachedKeys(t,e){let s=ue.get(t);if(!s){s=e();ue.set(t,s);ge.add(s)}return s}const addIfFound=(t,e,s)=>{const i=g(e,s);i!==void 0&&t.add(i)};class Config{constructor(t){this._config=initConfig(t);this._scopeCache=new Map;this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=initData(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache();initOptions(t)}clearCache(){this._scopeCache.clear();this._resolverCache.clear()}datasetScopeKeys(t){return cachedKeys(t,(()=>[[`datasets.${t}`,""]]))}datasetAnimationScopeKeys(t,e){return cachedKeys(`${t}.transition.${e}`,(()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]]))}datasetElementScopeKeys(t,e){return cachedKeys(`${t}-${e}`,(()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]]))}pluginScopeKeys(t){const e=t.id;const s=this.type;return cachedKeys(`${s}-plugin-${e}`,(()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]]))}_cachedScopes(t,e){const s=this._scopeCache;let i=s.get(t);if(!i||e){i=new Map;s.set(t,i)}return i}getOptionScopes(t,e,s){const{options:i,type:n}=this;const a=this._cachedScopes(t,s);const r=a.get(e);if(r)return r;const l=new Set;e.forEach((e=>{if(t){l.add(t);e.forEach((e=>addIfFound(l,t,e)))}e.forEach((t=>addIfFound(l,i,t)));e.forEach((t=>addIfFound(l,it[n]||{},t)));e.forEach((t=>addIfFound(l,o,t)));e.forEach((t=>addIfFound(l,rt,t)))}));const c=Array.from(l);c.length===0&&c.push(Object.create(null));ge.has(e)&&a.set(e,c);return c}chartOptionScopes(){const{options:t,type:e}=this;return[t,it[e]||{},o.datasets[e]||{},{type:e},o,rt]}resolveNamedOptions(t,e,s,i=[""]){const n={$shared:true};const{resolver:o,subPrefixes:a}=getResolver(this._resolverCache,t,i);let r=o;if(needContext(o,e)){n.$shared=false;s=lt(s)?s():s;const e=this.createResolver(t,s,a);r=ct(o,s,e)}for(const t of e)n[t]=r[t];return n}createResolver(t,e,s=[""],i){const{resolver:o}=getResolver(this._resolverCache,t,s);return n(e)?ct(o,e,void 0,i):o}}function getResolver(t,e,s){let i=t.get(e);if(!i){i=new Map;t.set(e,i)}const n=s.join();let o=i.get(n);if(!o){const t=ht(e,s);o={resolver:t,subPrefixes:s.filter((t=>!t.toLowerCase().includes("hover")))};i.set(n,o)}return o}const hasFunction=t=>n(t)&&Object.getOwnPropertyNames(t).some((e=>lt(t[e])));function needContext(t,e){const{isScriptable:s,isIndexable:i}=dt(t);for(const n of e){const e=s(n);const o=i(n);const r=(o||e)&&t[n];if(e&&(lt(r)||hasFunction(r))||o&&a(r))return true}return false}var fe="4.4.1";const pe=["top","bottom","left","right","chartArea"];function positionIsHorizontal(t,e){return t==="top"||t==="bottom"||pe.indexOf(t)===-1&&e==="x"}function compare2Level(t,e){return function(s,i){return s[t]===i[t]?s[e]-i[e]:s[t]-i[t]}}function onAnimationsComplete(t){const e=t.chart;const s=e.options.animation;e.notifyPlugins("afterRender");X(s&&s.onComplete,[t],e)}function onAnimationProgress(t){const e=t.chart;const s=e.options.animation;X(s&&s.onProgress,[t],e)}function getCanvas(t){N()&&typeof t==="string"?t=document.getElementById(t):t&&t.length&&(t=t[0]);t&&t.canvas&&(t=t.canvas);return t}const me={};const getChart=t=>{const e=getCanvas(t);return Object.values(me).filter((t=>t.canvas===e)).pop()};function moveNumericKeys(t,e,s){const i=Object.keys(t);for(const n of i){const i=+n;if(i>=e){const o=t[n];delete t[n];(s>0||i>e)&&(t[i+s]=o)}}}function determineLastEvent(t,e,s,i){return s&&t.type!=="mouseout"?i?e:t:null}function getSizeForArea(t,e,s){return t.options.clip?t[s]:e[s]}function getDatasetArea(t,e){const{xScale:s,yScale:i}=t;return s&&i?{left:getSizeForArea(s,e,"left"),right:getSizeForArea(s,e,"right"),top:getSizeForArea(i,e,"top"),bottom:getSizeForArea(i,e,"bottom")}:e}class Chart{static defaults=o;static instances=me;static overrides=it;static registry=de;static version=fe;static getChart=getChart;static register(...t){de.add(...t);invalidatePlugins()}static unregister(...t){de.remove(...t);invalidatePlugins()}constructor(t,e){const s=this.config=new Config(e);const i=getCanvas(t);const n=getChart(i);if(n)throw new Error("Canvas is already in use. Chart with ID '"+n.id+"' must be destroyed before the canvas with ID '"+n.canvas.id+"' can be reused.");const o=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||_detectPlatform(i));this.platform.updateConfig(s);const a=this.platform.acquireContext(i,o.aspectRatio);const r=a&&a.canvas;const l=r&&r.height;const c=r&&r.width;this.id=ut();this.ctx=a;this.canvas=r;this.width=c;this.height=l;this._options=o;this._aspectRatio=this.aspectRatio;this._layers=[];this._metasets=[];this._stacks=void 0;this.boxes=[];this.currentDevicePixelRatio=void 0;this.chartArea=void 0;this._active=[];this._lastEvent=void 0;this._listeners={};this._responsiveListeners=void 0;this._sortedMetasets=[];this.scales={};this._plugins=new PluginService;this.$proxies={};this._hiddenIndices={};this.attached=false;this._animationsDisabled=void 0;this.$context=void 0;this._doResize=gt((t=>this.update(t)),o.resizeDelay||0);this._dataChanges=[];me[this.id]=this;if(a&&r){Zt.listen(this,"complete",onAnimationsComplete);Zt.listen(this,"progress",onAnimationProgress);this._initialize();this.attached&&this.update()}else console.error("Failed to create chart: can't acquire context from the given item")}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:s,height:i,_aspectRatio:n}=this;return m(t)?e&&n?n:i?s/i:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return de}_initialize(){this.notifyPlugins("beforeInit");this.options.responsive?this.resize():ft(this,this.options.devicePixelRatio);this.bindEvents();this.notifyPlugins("afterInit");return this}clear(){pt(this.canvas,this.ctx);return this}stop(){Zt.stop(this);return this}resize(t,e){Zt.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const s=this.options;const i=this.canvas;const n=s.maintainAspectRatio&&this.aspectRatio;const o=this.platform.getMaximumSize(i,t,e,n);const a=s.devicePixelRatio||this.platform.getDevicePixelRatio();const r=this.width?"resize":"attach";this.width=o.width;this.height=o.height;this._aspectRatio=this.aspectRatio;if(ft(this,a,true)){this.notifyPlugins("resize",{size:o});X(s.onResize,[this,o],this);this.attached&&this._doResize(r)&&this.render()}}ensureScalesHaveIDs(){const t=this.options;const e=t.scales||{};R(e,((t,e)=>{t.id=e}))}buildOrUpdateScales(){const t=this.options;const e=t.scales;const s=this.scales;const i=Object.keys(s).reduce(((t,e)=>{t[e]=false;return t}),{});let n=[];e&&(n=n.concat(Object.keys(e).map((t=>{const s=e[t];const i=determineAxis(t,s);const n=i==="r";const o=i==="x";return{options:s,dposition:n?"chartArea":o?"bottom":"left",dtype:n?"radialLinear":o?"category":"linear"}}))));R(n,(e=>{const n=e.options;const o=n.id;const a=determineAxis(o,n);const r=h(n.type,e.dtype);n.position!==void 0&&positionIsHorizontal(n.position,a)===positionIsHorizontal(e.dposition)||(n.position=e.dposition);i[o]=true;let l=null;if(o in s&&s[o].type===r)l=s[o];else{const t=de.getScale(r);l=new t({id:o,type:r,ctx:this.ctx,chart:this});s[l.id]=l}l.init(n,t)}));R(i,((t,e)=>{t||delete s[e]}));R(s,(t=>{oe.configure(this,t,t.options);oe.addBox(this,t)}))}_updateMetasets(){const t=this._metasets;const e=this.data.datasets.length;const s=t.length;t.sort(((t,e)=>t.index-e.index));if(s>e){for(let t=e;t<s;++t)this._destroyDatasetMeta(t);t.splice(e,s-e)}this._sortedMetasets=t.slice(0).sort(compare2Level("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks;t.forEach(((t,s)=>{e.filter((e=>e===t._dataset)).length===0&&this._destroyDatasetMeta(s)}))}buildOrUpdateControllers(){const t=[];const e=this.data.datasets;let s,i;this._removeUnreferencedMetasets();for(s=0,i=e.length;s<i;s++){const i=e[s];let n=this.getDatasetMeta(s);const a=i.type||this.config.type;if(n.type&&n.type!==a){this._destroyDatasetMeta(s);n=this.getDatasetMeta(s)}n.type=a;n.indexAxis=i.indexAxis||getIndexAxis(a,this.options);n.order=i.order||0;n.index=s;n.label=""+i.label;n.visible=this.isDatasetVisible(s);if(n.controller){n.controller.updateIndex(s);n.controller.linkScales()}else{const e=de.getController(a);const{datasetElementType:i,dataElementType:r}=o.datasets[a];Object.assign(e,{dataElementType:de.getElement(r),datasetElementType:i&&de.getElement(i)});n.controller=new e(this,s);t.push(n.controller)}}this._updateMetasets();return t}_resetElements(){R(this.data.datasets,((t,e)=>{this.getDatasetMeta(e).controller.reset()}),this)}reset(){this._resetElements();this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const s=this._options=e.createResolver(e.chartOptionScopes(),this.getContext());const i=this._animationsDisabled=!s.animation;this._updateScales();this._checkEventBindings();this._updateHiddenIndices();this._plugins.invalidate();if(this.notifyPlugins("beforeUpdate",{mode:t,cancelable:true})===false)return;const n=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let o=0;for(let t=0,e=this.data.datasets.length;t<e;t++){const{controller:e}=this.getDatasetMeta(t);const s=!i&&n.indexOf(e)===-1;e.buildOrUpdateElements(s);o=Math.max(+e.getMaxOverflow(),o)}o=this._minPadding=s.layout.autoPadding?o:0;this._updateLayout(o);i||R(n,(t=>{t.reset()}));this._updateDatasets(t);this.notifyPlugins("afterUpdate",{mode:t});this._layers.sort(compare2Level("z","_idx"));const{_active:a,_lastEvent:r}=this;r?this._eventHandler(r,true):a.length&&this._updateHoverStyles(a,a,true);this.render()}_updateScales(){R(this.scales,(t=>{oe.removeBox(this,t)}));this.ensureScalesHaveIDs();this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options;const e=new Set(Object.keys(this._listeners));const s=new Set(t.events);if(!mt(e,s)||!!this._responsiveListeners!==t.responsive){this.unbindEvents();this.bindEvents()}}_updateHiddenIndices(){const{_hiddenIndices:t}=this;const e=this._getUniformDataChanges()||[];for(const{method:s,start:i,count:n}of e){const e=s==="_removeElements"?-n:n;moveNumericKeys(t,i,e)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length;const makeSet=e=>new Set(t.filter((t=>t[0]===e)).map(((t,e)=>e+","+t.splice(1).join(","))));const s=makeSet(0);for(let t=1;t<e;t++)if(!mt(s,makeSet(t)))return;return Array.from(s).map((t=>t.split(","))).map((t=>({method:t[1],start:+t[2],count:+t[3]})))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:true})===false)return;oe.update(this,this.width,this.height,t);const e=this.chartArea;const s=e.width<=0||e.height<=0;this._layers=[];R(this.boxes,(t=>{if(!s||t.position!=="chartArea"){t.configure&&t.configure();this._layers.push(...t._layers())}}),this);this._layers.forEach(((t,e)=>{t._idx=e}));this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:true})!==false){for(let t=0,e=this.data.datasets.length;t<e;++t)this.getDatasetMeta(t).controller.configure();for(let e=0,s=this.data.datasets.length;e<s;++e)this._updateDataset(e,lt(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const s=this.getDatasetMeta(t);const i={meta:s,index:t,mode:e,cancelable:true};if(this.notifyPlugins("beforeDatasetUpdate",i)!==false){s.controller._update(e);i.cancelable=false;this.notifyPlugins("afterDatasetUpdate",i)}}render(){if(this.notifyPlugins("beforeRender",{cancelable:true})!==false)if(Zt.has(this))this.attached&&!Zt.running(this)&&Zt.start(this);else{this.draw();onAnimationsComplete({chart:this})}}draw(){let t;if(this._resizeBeforeDraw){const{width:t,height:e}=this._resizeBeforeDraw;this._resize(t,e);this._resizeBeforeDraw=null}this.clear();if(this.width<=0||this.height<=0)return;if(this.notifyPlugins("beforeDraw",{cancelable:true})===false)return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);this._drawDatasets();for(;t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets;const s=[];let i,n;for(i=0,n=e.length;i<n;++i){const n=e[i];t&&!n.visible||s.push(n)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(true)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:true})===false)return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx;const s=t._clip;const i=!s.disabled;const n=getDatasetArea(t,this.chartArea);const o={meta:t,index:t.index,cancelable:true};if(this.notifyPlugins("beforeDatasetDraw",o)!==false){i&&tt(e,{left:s.left===false?0:n.left-s.left,right:s.right===false?this.width:n.right+s.right,top:s.top===false?0:n.top-s.top,bottom:s.bottom===false?this.height:n.bottom+s.bottom});t.controller.draw();i&&st(e);o.cancelable=false;this.notifyPlugins("afterDatasetDraw",o)}}isPointInArea(t){return T(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,s,i){const n=ie.modes[e];return typeof n==="function"?n(this,t,s,i):[]}getDatasetMeta(t){const e=this.data.datasets[t];const s=this._metasets;let i=s.filter((t=>t&&t._dataset===e)).pop();if(!i){i={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:false};s.push(i)}return i}getContext(){return this.$context||(this.$context=c(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return false;const s=this.getDatasetMeta(t);return typeof s.hidden==="boolean"?!s.hidden:!e.hidden}setDatasetVisibility(t,e){const s=this.getDatasetMeta(t);s.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,s){const i=s?"show":"hide";const n=this.getDatasetMeta(t);const o=n.controller._resolveAnimations(void 0,i);if(f(e)){n.data[e].hidden=!s;this.update()}else{this.setDatasetVisibility(t,s);o.update(n,{visible:s});this.update((e=>e.datasetIndex===t?i:void 0))}}hide(t,e){this._updateVisibility(t,e,false)}show(t,e){this._updateVisibility(t,e,true)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy();delete this._metasets[t]}_stop(){let t,e;this.stop();Zt.remove(this);for(t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop();this.config.clearCache();if(t){this.unbindEvents();pt(t,e);this.platform.releaseContext(e);this.canvas=null;this.ctx=null}delete me[this.id];this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents();this.options.responsive?this.bindResponsiveEvents():this.attached=true}bindUserEvents(){const t=this._listeners;const e=this.platform;const _add=(s,i)=>{e.addEventListener(this,s,i);t[s]=i};const listener=(t,e,s)=>{t.offsetX=e;t.offsetY=s;this._eventHandler(t)};R(this.options.events,(t=>_add(t,listener)))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners;const e=this.platform;const _add=(s,i)=>{e.addEventListener(this,s,i);t[s]=i};const _remove=(s,i)=>{if(t[s]){e.removeEventListener(this,s,i);delete t[s]}};const listener=(t,e)=>{this.canvas&&this.resize(t,e)};let s;const attached=()=>{_remove("attach",attached);this.attached=true;this.resize();_add("resize",listener);_add("detach",s)};s=()=>{this.attached=false;_remove("resize",listener);this._stop();this._resize(0,0);_add("attach",attached)};e.isAttached(this.canvas)?attached():s()}unbindEvents(){R(this._listeners,((t,e)=>{this.platform.removeEventListener(this,e,t)}));this._listeners={};R(this._responsiveListeners,((t,e)=>{this.platform.removeEventListener(this,e,t)}));this._responsiveListeners=void 0}updateHoverStyle(t,e,s){const i=s?"set":"remove";let n,o,a,r;if(e==="dataset"){n=this.getDatasetMeta(t[0].datasetIndex);n.controller["_"+i+"DatasetHoverStyle"]()}for(a=0,r=t.length;a<r;++a){o=t[a];const e=o&&this.getDatasetMeta(o.datasetIndex).controller;e&&e[i+"HoverStyle"](o.element,o.datasetIndex,o.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[];const s=t.map((({datasetIndex:t,index:e})=>{const s=this.getDatasetMeta(t);if(!s)throw new Error("No dataset found at index "+t);return{datasetIndex:t,element:s.data[e],index:e}}));const i=!xt(s,e);if(i){this._active=s;this._lastEvent=null;this._updateHoverStyles(s,e)}}notifyPlugins(t,e,s){return this._plugins.notify(this,t,e,s)}isPluginEnabled(t){return this._plugins._cache.filter((e=>e.plugin.id===t)).length===1}_updateHoverStyles(t,e,s){const i=this.options.hover;const diff=(t,e)=>t.filter((t=>!e.some((e=>t.datasetIndex===e.datasetIndex&&t.index===e.index))));const n=diff(e,t);const o=s?t:diff(t,e);n.length&&this.updateHoverStyle(n,i.mode,false);o.length&&i.mode&&this.updateHoverStyle(o,i.mode,true)}_eventHandler(t,e){const s={event:t,replay:e,cancelable:true,inChartArea:this.isPointInArea(t)};const eventFilter=e=>(e.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",s,eventFilter)===false)return;const i=this._handleEvent(t,e,s.inChartArea);s.cancelable=false;this.notifyPlugins("afterEvent",s,eventFilter);(i||s.changed)&&this.render();return this}_handleEvent(t,e,s){const{_active:i=[],options:n}=this;const o=e;const a=this._getActiveElements(t,i,s,o);const r=bt(t);const l=determineLastEvent(t,this._lastEvent,s,r);if(s){this._lastEvent=null;X(n.onHover,[t,a,this],this);r&&X(n.onClick,[t,a,this],this)}const c=!xt(a,i);if(c||e){this._active=a;this._updateHoverStyles(a,i,e)}this._lastEvent=l;return c}_getActiveElements(t,e,s,i){if(t.type==="mouseout")return[];if(!s)return e;const n=this.options.hover;return this.getElementsAtEventForMode(t,n.mode,n,i)}}function invalidatePlugins(){return R(Chart.instances,(t=>t._plugins.invalidate()))}function clipArc(t,e,s){const{startAngle:i,pixelMargin:n,x:o,y:a,outerRadius:r,innerRadius:l}=e;let c=n/r;t.beginPath();t.arc(o,a,r,i-c,s+c);if(l>n){c=n/l;t.arc(o,a,l,s+c,i-c,true)}else t.arc(o,a,n,s+_,i-_);t.closePath();t.clip()}function toRadiusCorners(t){return _t(t,["outerStart","outerEnd","innerStart","innerEnd"])}function parseBorderRadius$1(t,e,s,i){const n=toRadiusCorners(t.options.borderRadius);const o=(s-e)/2;const a=Math.min(o,i*e/2);const computeOuterLimit=t=>{const e=(s-Math.min(o,t))*i/2;return G(t,0,Math.min(o,e))};return{outerStart:computeOuterLimit(n.outerStart),outerEnd:computeOuterLimit(n.outerEnd),innerStart:G(n.innerStart,0,a),innerEnd:G(n.innerEnd,0,a)}}function rThetaToXY(t,e,s,i){return{x:s+t*Math.cos(e),y:i+t*Math.sin(e)}}function pathArc(t,e,s,i,n,o){const{x:a,y:r,startAngle:l,pixelMargin:c,innerRadius:h}=e;const d=Math.max(e.outerRadius+i+s-c,0);const u=h>0?h+i+s+c:0;let g=0;const f=n-l;if(i){const t=h>0?h-i:0;const e=d>0?d-i:0;const s=(t+e)/2;const n=s!==0?f*s/(s+i):f;g=(f-n)/2}const p=Math.max(.001,f*d-s/y)/d;const m=(f-p)/2;const x=l+m+g;const b=n-m-g;const{outerStart:v,outerEnd:k,innerStart:M,innerEnd:S}=parseBorderRadius$1(e,u,d,b-x);const w=d-v;const D=d-k;const C=x+v/w;const P=b-k/D;const A=u+M;const L=u+S;const T=x+M/A;const O=b-S/L;t.beginPath();if(o){const e=(C+P)/2;t.arc(a,r,d,C,e);t.arc(a,r,d,e,P);if(k>0){const e=rThetaToXY(D,P,a,r);t.arc(e.x,e.y,k,P,b+_)}const s=rThetaToXY(L,b,a,r);t.lineTo(s.x,s.y);if(S>0){const e=rThetaToXY(L,O,a,r);t.arc(e.x,e.y,S,b+_,O+Math.PI)}const i=(b-S/u+(x+M/u))/2;t.arc(a,r,u,b-S/u,i,true);t.arc(a,r,u,i,x+M/u,true);if(M>0){const e=rThetaToXY(A,T,a,r);t.arc(e.x,e.y,M,T+Math.PI,x-_)}const n=rThetaToXY(w,x,a,r);t.lineTo(n.x,n.y);if(v>0){const e=rThetaToXY(w,C,a,r);t.arc(e.x,e.y,v,x-_,C)}}else{t.moveTo(a,r);const e=Math.cos(C)*d+a;const s=Math.sin(C)*d+r;t.lineTo(e,s);const i=Math.cos(P)*d+a;const n=Math.sin(P)*d+r;t.lineTo(i,n)}t.closePath()}function drawArc(t,e,s,i,n){const{fullCircles:o,startAngle:a,circumference:r}=e;let l=e.endAngle;if(o){pathArc(t,e,s,i,l,n);for(let e=0;e<o;++e)t.fill();isNaN(r)||(l=a+(r%x||x))}pathArc(t,e,s,i,l,n);t.fill();return l}function drawBorder(t,e,s,i,n){const{fullCircles:o,startAngle:a,circumference:r,options:l}=e;const{borderWidth:c,borderJoinStyle:h,borderDash:d,borderDashOffset:u}=l;const g=l.borderAlign==="inner";if(!c)return;t.setLineDash(d||[]);t.lineDashOffset=u;if(g){t.lineWidth=c*2;t.lineJoin=h||"round"}else{t.lineWidth=c;t.lineJoin=h||"bevel"}let f=e.endAngle;if(o){pathArc(t,e,s,i,f,n);for(let e=0;e<o;++e)t.stroke();isNaN(r)||(f=a+(r%x||x))}g&&clipArc(t,e,f);if(!o){pathArc(t,e,s,i,f,n);t.stroke()}}class ArcElement extends Element{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:true};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:true,_indexable:t=>t!=="borderDash"};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(t){super();this.options=void 0;this.circumference=void 0;this.startAngle=void 0;this.endAngle=void 0;this.innerRadius=void 0;this.outerRadius=void 0;this.pixelMargin=0;this.fullCircles=0;t&&Object.assign(this,t)}inRange(t,e,s){const i=this.getProps(["x","y"],s);const{angle:n,distance:o}=O(i,{x:t,y:e});const{startAngle:a,endAngle:r,innerRadius:l,outerRadius:c,circumference:d}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],s);const u=(this.options.spacing+this.options.borderWidth)/2;const g=h(d,r-a);const f=g>=x||b(n,a,r);const p=yt(o,l+u,c+u);return f&&p}getCenterPoint(t){const{x:e,y:s,startAngle:i,endAngle:n,innerRadius:o,outerRadius:a}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t);const{offset:r,spacing:l}=this.options;const c=(i+n)/2;const h=(o+a+l+r)/2;return{x:e+Math.cos(c)*h,y:s+Math.sin(c)*h}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){const{options:e,circumference:s}=this;const i=(e.offset||0)/4;const n=(e.spacing||0)/2;const o=e.circular;this.pixelMargin=e.borderAlign==="inner"?.33:0;this.fullCircles=s>x?Math.floor(s/x):0;if(s===0||this.innerRadius<0||this.outerRadius<0)return;t.save();const a=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(a)*i,Math.sin(a)*i);const r=1-Math.sin(Math.min(y,s||0));const l=i*r;t.fillStyle=e.backgroundColor;t.strokeStyle=e.borderColor;drawArc(t,this,l,n,o);drawBorder(t,this,l,n,o);t.restore()}}function setStyle(t,e,s=e){t.lineCap=h(s.borderCapStyle,e.borderCapStyle);t.setLineDash(h(s.borderDash,e.borderDash));t.lineDashOffset=h(s.borderDashOffset,e.borderDashOffset);t.lineJoin=h(s.borderJoinStyle,e.borderJoinStyle);t.lineWidth=h(s.borderWidth,e.borderWidth);t.strokeStyle=h(s.borderColor,e.borderColor)}function lineTo(t,e,s){t.lineTo(s.x,s.y)}function getLineMethod(t){return t.stepped?vt:t.tension||t.cubicInterpolationMode==="monotone"?kt:lineTo}function pathVars(t,e,s={}){const i=t.length;const{start:n=0,end:o=i-1}=s;const{start:a,end:r}=e;const l=Math.max(n,a);const c=Math.min(o,r);const h=n<a&&o<a||n>r&&o>r;return{count:i,start:l,loop:e.loop,ilen:c<l&&!h?i+c-l:c-l}}function pathSegment(t,e,s,i){const{points:n,options:o}=e;const{count:a,start:r,loop:l,ilen:c}=pathVars(n,s,i);const h=getLineMethod(o);let{move:d=true,reverse:u}=i||{};let g,f,p;for(g=0;g<=c;++g){f=n[(r+(u?c-g:g))%a];if(!f.skip){if(d){t.moveTo(f.x,f.y);d=false}else h(t,p,f,u,o.stepped);p=f}}if(l){f=n[(r+(u?c:0))%a];h(t,p,f,u,o.stepped)}return!!l}function fastPathSegment(t,e,s,i){const n=e.points;const{count:o,start:a,ilen:r}=pathVars(n,s,i);const{move:l=true,reverse:c}=i||{};let h=0;let d=0;let u,g,f,p,m,x;const pointIndex=t=>(a+(c?r-t:t))%o;const drawX=()=>{if(p!==m){t.lineTo(h,m);t.lineTo(h,p);t.lineTo(h,x)}};if(l){g=n[pointIndex(0)];t.moveTo(g.x,g.y)}for(u=0;u<=r;++u){g=n[pointIndex(u)];if(g.skip)continue;const e=g.x;const s=g.y;const i=e|0;if(i===f){s<p?p=s:s>m&&(m=s);h=(d*h+e)/++d}else{drawX();t.lineTo(e,s);f=i;d=0;p=m=s}x=s}drawX()}function _getSegmentMethod(t){const e=t.options;const s=e.borderDash&&e.borderDash.length;const i=!t._decimated&&!t._loop&&!e.tension&&e.cubicInterpolationMode!=="monotone"&&!e.stepped&&!s;return i?fastPathSegment:pathSegment}function _getInterpolationMethod(t){return t.stepped?Mt:t.tension||t.cubicInterpolationMode==="monotone"?St:wt}function strokePathWithCache(t,e,s,i){let n=e._path;if(!n){n=e._path=new Path2D;e.path(n,s,i)&&n.closePath()}setStyle(t,e.options);t.stroke(n)}function strokePathDirect(t,e,s,i){const{segments:n,options:o}=e;const a=_getSegmentMethod(e);for(const r of n){setStyle(t,o,r.style);t.beginPath();a(t,e,r,{start:s,end:s+i-1})&&t.closePath();t.stroke()}}const xe=typeof Path2D==="function";function draw(t,e,s,i){xe&&!e.options.segment?strokePathWithCache(t,e,s,i):strokePathDirect(t,e,s,i)}class LineElement extends Element{static id="line";static defaults={borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:true,cubicInterpolationMode:"default",fill:false,spanGaps:false,stepped:false,tension:0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};static descriptors={_scriptable:true,_indexable:t=>t!=="borderDash"&&t!=="fill"};constructor(t){super();this.animated=true;this.options=void 0;this._chart=void 0;this._loop=void 0;this._fullLoop=void 0;this._path=void 0;this._points=void 0;this._segments=void 0;this._decimated=false;this._pointsUpdated=false;this._datasetIndex=void 0;t&&Object.assign(this,t)}updateControlPoints(t,e){const s=this.options;if((s.tension||s.cubicInterpolationMode==="monotone")&&!s.stepped&&!this._pointsUpdated){const i=s.spanGaps?this._loop:this._fullLoop;Dt(this._points,s,t,i,e);this._pointsUpdated=true}}set points(t){this._points=t;delete this._segments;delete this._path;this._pointsUpdated=false}get points(){return this._points}get segments(){return this._segments||(this._segments=Ct(this,this.options.segment))}first(){const t=this.segments;const e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments;const e=this.points;const s=t.length;return s&&e[t[s-1].end]}interpolate(t,e){const s=this.options;const i=t[e];const n=this.points;const o=Pt(this,{property:e,start:i,end:i});if(!o.length)return;const a=[];const r=_getInterpolationMethod(s);let l,c;for(l=0,c=o.length;l<c;++l){const{start:c,end:h}=o[l];const d=n[c];const u=n[h];if(d===u){a.push(d);continue}const g=Math.abs((i-d[e])/(u[e]-d[e]));const f=r(d,u,g,s.stepped);f[e]=t[e];a.push(f)}return a.length===1?a[0]:a}pathSegment(t,e,s){const i=_getSegmentMethod(this);return i(t,this,e,s)}path(t,e,s){const i=this.segments;const n=_getSegmentMethod(this);let o=this._loop;e=e||0;s=s||this.points.length-e;for(const a of i)o&=n(t,this,a,{start:e,end:e+s-1});return!!o}draw(t,e,s,i){const n=this.options||{};const o=this.points||[];if(o.length&&n.borderWidth){t.save();draw(t,this,s,i);t.restore()}if(this.animated){this._pointsUpdated=false;this._path=void 0}}}function inRange$1(t,e,s,i){const n=t.options;const{[s]:o}=t.getProps([s],i);return Math.abs(e-o)<n.radius+n.hitRadius}class PointElement extends Element{static id="point";parsed;skip;stop;
/**
   * @type {any}
   */
static defaults={borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0};
/**
   * @type {any}
   */
static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super();this.options=void 0;this.parsed=void 0;this.skip=void 0;this.stop=void 0;t&&Object.assign(this,t)}inRange(t,e,s){const i=this.options;const{x:n,y:o}=this.getProps(["x","y"],s);return Math.pow(t-n,2)+Math.pow(e-o,2)<Math.pow(i.hitRadius+i.radius,2)}inXRange(t,e){return inRange$1(this,t,"x",e)}inYRange(t,e){return inRange$1(this,t,"y",e)}getCenterPoint(t){const{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}size(t){t=t||this.options||{};let e=t.radius||0;e=Math.max(e,e&&t.hoverRadius||0);const s=e&&t.borderWidth||0;return(e+s)*2}draw(t,e){const s=this.options;if(!(this.skip||s.radius<.1)&&T(this,e,this.size(s)/2)){t.strokeStyle=s.borderColor;t.lineWidth=s.borderWidth;t.fillStyle=s.backgroundColor;At(t,s,this.x,this.y)}}getRange(){const t=this.options||{};return t.radius+t.hitRadius}}function getBarBounds(t,e){const{x:s,y:i,base:n,width:o,height:a}=t.getProps(["x","y","base","width","height"],e);let r,l,c,h,d;if(t.horizontal){d=a/2;r=Math.min(s,n);l=Math.max(s,n);c=i-d;h=i+d}else{d=o/2;r=s-d;l=s+d;c=Math.min(i,n);h=Math.max(i,n)}return{left:r,top:c,right:l,bottom:h}}function skipOrLimit(t,e,s,i){return t?0:G(e,s,i)}function parseBorderWidth(t,e,s){const i=t.options.borderWidth;const n=t.borderSkipped;const o=Lt(i);return{t:skipOrLimit(n.top,o.top,0,s),r:skipOrLimit(n.right,o.right,0,e),b:skipOrLimit(n.bottom,o.bottom,0,s),l:skipOrLimit(n.left,o.left,0,e)}}function parseBorderRadius(t,e,s){const{enableBorderRadius:i}=t.getProps(["enableBorderRadius"]);const o=t.options.borderRadius;const a=Tt(o);const r=Math.min(e,s);const l=t.borderSkipped;const c=i||n(o);return{topLeft:skipOrLimit(!c||l.top||l.left,a.topLeft,0,r),topRight:skipOrLimit(!c||l.top||l.right,a.topRight,0,r),bottomLeft:skipOrLimit(!c||l.bottom||l.left,a.bottomLeft,0,r),bottomRight:skipOrLimit(!c||l.bottom||l.right,a.bottomRight,0,r)}}function boundingRects(t){const e=getBarBounds(t);const s=e.right-e.left;const i=e.bottom-e.top;const n=parseBorderWidth(t,s/2,i/2);const o=parseBorderRadius(t,s/2,i/2);return{outer:{x:e.left,y:e.top,w:s,h:i,radius:o},inner:{x:e.left+n.l,y:e.top+n.t,w:s-n.l-n.r,h:i-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}function inRange(t,e,s,i){const n=e===null;const o=s===null;const a=n&&o;const r=t&&!a&&getBarBounds(t,i);return r&&(n||yt(e,r.left,r.right))&&(o||yt(s,r.top,r.bottom))}function hasRadius(t){return t.topLeft||t.topRight||t.bottomLeft||t.bottomRight}function addNormalRectPath(t,e){t.rect(e.x,e.y,e.w,e.h)}function inflateRect(t,e,s={}){const i=t.x!==s.x?-e:0;const n=t.y!==s.y?-e:0;const o=(t.x+t.w!==s.x+s.w?e:0)-i;const a=(t.y+t.h!==s.y+s.h?e:0)-n;return{x:t.x+i,y:t.y+n,w:t.w+o,h:t.h+a,radius:t.radius}}class BarElement extends Element{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super();this.options=void 0;this.horizontal=void 0;this.base=void 0;this.width=void 0;this.height=void 0;this.inflateAmount=void 0;t&&Object.assign(this,t)}draw(t){const{inflateAmount:e,options:{borderColor:s,backgroundColor:i}}=this;const{inner:n,outer:o}=boundingRects(this);const a=hasRadius(o.radius)?Ot:addNormalRectPath;t.save();if(o.w!==n.w||o.h!==n.h){t.beginPath();a(t,inflateRect(o,e,n));t.clip();a(t,inflateRect(n,-e,o));t.fillStyle=s;t.fill("evenodd")}t.beginPath();a(t,inflateRect(n,e));t.fillStyle=i;t.fill();t.restore()}inRange(t,e,s){return inRange(this,t,e,s)}inXRange(t,e){return inRange(this,t,null,e)}inYRange(t,e){return inRange(this,null,t,e)}getCenterPoint(t){const{x:e,y:s,base:i,horizontal:n}=this.getProps(["x","y","base","horizontal"],t);return{x:n?(e+i)/2:e,y:n?s:(s+i)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}}var be=Object.freeze({__proto__:null,ArcElement:ArcElement,BarElement:BarElement,LineElement:LineElement,PointElement:PointElement});const _e=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"];const ye=_e.map((t=>t.replace("rgb(","rgba(").replace(")",", 0.5)")));function getBorderColor(t){return _e[t%_e.length]}function getBackgroundColor(t){return ye[t%ye.length]}function colorizeDefaultDataset(t,e){t.borderColor=getBorderColor(e);t.backgroundColor=getBackgroundColor(e);return++e}function colorizeDoughnutDataset(t,e){t.backgroundColor=t.data.map((()=>getBorderColor(e++)));return e}function colorizePolarAreaDataset(t,e){t.backgroundColor=t.data.map((()=>getBackgroundColor(e++)));return e}function getColorizer(t){let e=0;return(s,i)=>{const n=t.getDatasetMeta(i).controller;n instanceof DoughnutController?e=colorizeDoughnutDataset(s,e):n instanceof PolarAreaController?e=colorizePolarAreaDataset(s,e):n&&(e=colorizeDefaultDataset(s,e))}}function containsColorsDefinitions(t){let e;for(e in t)if(t[e].borderColor||t[e].backgroundColor)return true;return false}function containsColorsDefinition(t){return t&&(t.borderColor||t.backgroundColor)}var ve={id:"colors",defaults:{enabled:true,forceOverride:false},beforeLayout(t,e,s){if(!s.enabled)return;const{data:{datasets:i},options:n}=t.config;const{elements:o}=n;if(!s.forceOverride&&(containsColorsDefinitions(i)||containsColorsDefinition(n)||o&&containsColorsDefinitions(o)))return;const a=getColorizer(t);i.forEach(a)}};function lttbDecimation(t,e,s,i,n){const o=n.samples||i;if(o>=s)return t.slice(e,e+s);const a=[];const r=(s-2)/(o-2);let l=0;const c=e+s-1;let h=e;let d,u,g,f,p;a[l++]=t[h];for(d=0;d<o-2;d++){let i=0;let n=0;let o;const c=Math.floor((d+1)*r)+1+e;const m=Math.min(Math.floor((d+2)*r)+1,s)+e;const x=m-c;for(o=c;o<m;o++){i+=t[o].x;n+=t[o].y}i/=x;n/=x;const b=Math.floor(d*r)+1+e;const _=Math.min(Math.floor((d+1)*r)+1,s)+e;const{x:y,y:v}=t[h];g=f=-1;for(o=b;o<_;o++){f=.5*Math.abs((y-i)*(t[o].y-v)-(y-t[o].x)*(n-v));if(f>g){g=f;u=t[o];p=o}}a[l++]=u;h=p}a[l++]=t[c];return a}function minMaxDecimation(t,e,s,i){let n=0;let o=0;let a,r,l,c,h,d,u,g,f,p;const x=[];const b=e+s-1;const _=t[e].x;const y=t[b].x;const v=y-_;for(a=e;a<e+s;++a){r=t[a];l=(r.x-_)/v*i;c=r.y;const e=l|0;if(e===h){if(c<f){f=c;d=a}else if(c>p){p=c;u=a}n=(o*n+r.x)/++o}else{const s=a-1;if(!m(d)&&!m(u)){const e=Math.min(d,u);const i=Math.max(d,u);e!==g&&e!==s&&x.push({...t[e],x:n});i!==g&&i!==s&&x.push({...t[i],x:n})}a>0&&s!==g&&x.push(t[s]);x.push(r);h=e;o=0;f=p=c;d=u=g=a}}return x}function cleanDecimatedDataset(t){if(t._decimated){const e=t._data;delete t._decimated;delete t._data;Object.defineProperty(t,"data",{configurable:true,enumerable:true,writable:true,value:e})}}function cleanDecimatedData(t){t.data.datasets.forEach((t=>{cleanDecimatedDataset(t)}))}function getStartAndCountOfVisiblePointsSimplified(t,e){const s=e.length;let i=0;let n;const{iScale:o}=t;const{min:a,max:r,minDefined:l,maxDefined:c}=o.getUserBounds();l&&(i=G(L(e,o.axis,a).lo,0,s-1));n=c?G(L(e,o.axis,r).hi+1,i,s)-i:s-i;return{start:i,count:n}}var ke={id:"decimation",defaults:{algorithm:"min-max",enabled:false},beforeElementsUpdate:(t,e,i)=>{if(!i.enabled){cleanDecimatedData(t);return}const n=t.width;t.data.datasets.forEach(((e,o)=>{const{_data:a,indexAxis:r}=e;const l=t.getDatasetMeta(o);const c=a||e.data;if(s([r,t.options.indexAxis])==="y")return;if(!l.controller.supportsDecimation)return;const h=t.scales[l.xAxisID];if(h.type!=="linear"&&h.type!=="time")return;if(t.options.parsing)return;let{start:d,count:u}=getStartAndCountOfVisiblePointsSimplified(l,c);const g=i.threshold||4*n;if(u<=g){cleanDecimatedDataset(e);return}if(m(a)){e._data=c;delete e.data;Object.defineProperty(e,"data",{configurable:true,enumerable:true,get:function(){return this._decimated},set:function(t){this._data=t}})}let f;switch(i.algorithm){case"lttb":f=lttbDecimation(c,d,u,n,i);break;case"min-max":f=minMaxDecimation(c,d,u,n);break;default:throw new Error(`Unsupported decimation algorithm '${i.algorithm}'`)}e._decimated=f}))},destroy(t){cleanDecimatedData(t)}};function _segments(t,e,s){const i=t.segments;const n=t.points;const o=e.points;const a=[];for(const t of i){let{start:i,end:r}=t;r=_findSegmentEnd(i,r,n);const l=_getBounds(s,n[i],n[r],t.loop);if(!e.segments){a.push({source:t,target:l,start:n[i],end:n[r]});continue}const c=Pt(e,l);for(const e of c){const i=_getBounds(s,o[e.start],o[e.end],e.loop);const r=Et(t,n,i);for(const t of r)a.push({source:t,target:e,start:{[s]:_getEdge(l,i,"start",Math.max)},end:{[s]:_getEdge(l,i,"end",Math.min)}})}}return a}function _getBounds(t,e,s,i){if(i)return;let n=e[t];let o=s[t];if(t==="angle"){n=It(n);o=It(o)}return{property:t,start:n,end:o}}function _pointsFromSegments(t,e){const{x:s=null,y:i=null}=t||{};const n=e.points;const o=[];e.segments.forEach((({start:t,end:e})=>{e=_findSegmentEnd(t,e,n);const a=n[t];const r=n[e];if(i!==null){o.push({x:a.x,y:i});o.push({x:r.x,y:i})}else if(s!==null){o.push({x:s,y:a.y});o.push({x:s,y:r.y})}}));return o}function _findSegmentEnd(t,e,s){for(;e>t;e--){const t=s[e];if(!isNaN(t.x)&&!isNaN(t.y))break}return e}function _getEdge(t,e,s,i){return t&&e?i(t[s],e[s]):t?t[s]:e?e[s]:0}function _createBoundaryLine(t,e){let s=[];let i=false;if(a(t)){i=true;s=t}else s=_pointsFromSegments(t,e);return s.length?new LineElement({points:s,options:{tension:0},_loop:i,_fullLoop:i}):null}function _shouldApplyFill(t){return t&&t.fill!==false}function _resolveTarget(t,e,s){const i=t[e];let n=i.fill;const o=[e];let a;if(!s)return n;while(n!==false&&o.indexOf(n)===-1){if(!r(n))return n;a=t[n];if(!a)return false;if(a.visible)return n;o.push(n);n=a.fill}return false}function _decodeFill(t,e,s){const i=parseFillOption(t);if(n(i))return!isNaN(i.value)&&i;let o=parseFloat(i);return r(o)&&Math.floor(o)===o?decodeTargetIndex(i[0],e,o,s):["origin","start","end","stack","shape"].indexOf(i)>=0&&i}function decodeTargetIndex(t,e,s,i){t!=="-"&&t!=="+"||(s=e+s);return!(s===e||s<0||s>=i)&&s}function _getTargetPixel(t,e){let s=null;t==="start"?s=e.bottom:t==="end"?s=e.top:n(t)?s=e.getPixelForValue(t.value):e.getBasePixel&&(s=e.getBasePixel());return s}function _getTargetValue(t,e,s){let i;i=t==="start"?s:t==="end"?e.options.reverse?e.min:e.max:n(t)?t.value:e.getBaseValue();return i}function parseFillOption(t){const e=t.options;const s=e.fill;let i=h(s&&s.target,s);i===void 0&&(i=!!e.backgroundColor);return i!==false&&i!==null&&(i===true?"origin":i)}function _buildStackLine(t){const{scale:e,index:s,line:i}=t;const n=[];const o=i.segments;const a=i.points;const r=getLinesBelow(e,s);r.push(_createBoundaryLine({x:null,y:e.bottom},i));for(let t=0;t<o.length;t++){const e=o[t];for(let t=e.start;t<=e.end;t++)addPointsBelow(n,a[t],r)}return new LineElement({points:n,options:{}})}function getLinesBelow(t,e){const s=[];const i=t.getMatchingVisibleMetas("line");for(let t=0;t<i.length;t++){const n=i[t];if(n.index===e)break;n.hidden||s.unshift(n.dataset)}return s}function addPointsBelow(t,e,s){const i=[];for(let n=0;n<s.length;n++){const o=s[n];const{first:a,last:r,point:l}=findPoint(o,e,"x");if(!(!l||a&&r))if(a)i.unshift(l);else{t.push(l);if(!r)break}}t.push(...i)}function findPoint(t,e,s){const i=t.interpolate(e,s);if(!i)return{};const n=i[s];const o=t.segments;const a=t.points;let r=false;let l=false;for(let t=0;t<o.length;t++){const e=o[t];const i=a[e.start][s];const c=a[e.end][s];if(yt(n,i,c)){r=n===i;l=n===c;break}}return{first:r,last:l,point:i}}class simpleArc{constructor(t){this.x=t.x;this.y=t.y;this.radius=t.radius}pathSegment(t,e,s){const{x:i,y:n,radius:o}=this;e=e||{start:0,end:x};t.arc(i,n,o,e.end,e.start,true);return!s.bounds}interpolate(t){const{x:e,y:s,radius:i}=this;const n=t.angle;return{x:e+Math.cos(n)*i,y:s+Math.sin(n)*i,angle:n}}}function _getTarget(t){const{chart:e,fill:s,line:i}=t;if(r(s))return getLineByIndex(e,s);if(s==="stack")return _buildStackLine(t);if(s==="shape")return true;const n=computeBoundary(t);return n instanceof simpleArc?n:_createBoundaryLine(n,i)}function getLineByIndex(t,e){const s=t.getDatasetMeta(e);const i=s&&t.isDatasetVisible(e);return i?s.dataset:null}function computeBoundary(t){const e=t.scale||{};return e.getPointPositionForValue?computeCircularBoundary(t):computeLinearBoundary(t)}function computeLinearBoundary(t){const{scale:e={},fill:s}=t;const i=_getTargetPixel(s,e);if(r(i)){const t=e.isHorizontal();return{x:t?i:null,y:t?null:i}}return null}function computeCircularBoundary(t){const{scale:e,fill:s}=t;const i=e.options;const n=e.getLabels().length;const o=i.reverse?e.max:e.min;const a=_getTargetValue(s,e,o);const r=[];if(i.grid.circular){const t=e.getPointPositionForValue(0,o);return new simpleArc({x:t.x,y:t.y,radius:e.getDistanceFromCenterForValue(a)})}for(let t=0;t<n;++t)r.push(e.getPointPositionForValue(t,a));return r}function _drawfill(t,e,s){const i=_getTarget(e);const{line:n,scale:o,axis:a}=e;const r=n.options;const l=r.fill;const c=r.backgroundColor;const{above:h=c,below:d=c}=l||{};if(i&&n.points.length){tt(t,s);doFill(t,{line:n,target:i,above:h,below:d,area:s,scale:o,axis:a});st(t)}}function doFill(t,e){const{line:s,target:i,above:n,below:o,area:a,scale:r}=e;const l=s._loop?"angle":e.axis;t.save();if(l==="x"&&o!==n){clipVertical(t,i,a.top);fill(t,{line:s,target:i,color:n,scale:r,property:l});t.restore();t.save();clipVertical(t,i,a.bottom)}fill(t,{line:s,target:i,color:o,scale:r,property:l});t.restore()}function clipVertical(t,e,s){const{segments:i,points:n}=e;let o=true;let a=false;t.beginPath();for(const r of i){const{start:i,end:l}=r;const c=n[i];const h=n[_findSegmentEnd(i,l,n)];if(o){t.moveTo(c.x,c.y);o=false}else{t.lineTo(c.x,s);t.lineTo(c.x,c.y)}a=!!e.pathSegment(t,r,{move:a});a?t.closePath():t.lineTo(h.x,s)}t.lineTo(e.first().x,s);t.closePath();t.clip()}function fill(t,e){const{line:s,target:i,property:n,color:o,scale:a}=e;const r=_segments(s,i,n);for(const{source:e,target:l,start:c,end:h}of r){const{style:{backgroundColor:r=o}={}}=e;const d=i!==true;t.save();t.fillStyle=r;clipBounds(t,a,d&&_getBounds(n,c,h));t.beginPath();const u=!!s.pathSegment(t,e);let g;if(d){u?t.closePath():interpolatedLineTo(t,i,h,n);const e=!!i.pathSegment(t,l,{move:u,reverse:true});g=u&&e;g||interpolatedLineTo(t,i,c,n)}t.closePath();t.fill(g?"evenodd":"nonzero");t.restore()}}function clipBounds(t,e,s){const{top:i,bottom:n}=e.chart.chartArea;const{property:o,start:a,end:r}=s||{};if(o==="x"){t.beginPath();t.rect(a,i,r-a,n-i);t.clip()}}function interpolatedLineTo(t,e,s,i){const n=e.interpolate(s,i);n&&t.lineTo(n.x,n.y)}var Me={id:"filler",afterDatasetsUpdate(t,e,s){const i=(t.data.datasets||[]).length;const n=[];let o,a,r,l;for(a=0;a<i;++a){o=t.getDatasetMeta(a);r=o.dataset;l=null;r&&r.options&&r instanceof LineElement&&(l={visible:t.isDatasetVisible(a),index:a,fill:_decodeFill(r,a,i),chart:t,axis:o.controller.options.indexAxis,scale:o.vScale,line:r});o.$filler=l;n.push(l)}for(a=0;a<i;++a){l=n[a];l&&l.fill!==false&&(l.fill=_resolveTarget(n,a,s.propagate))}},beforeDraw(t,e,s){const i=s.drawTime==="beforeDraw";const n=t.getSortedVisibleDatasetMetas();const o=t.chartArea;for(let e=n.length-1;e>=0;--e){const s=n[e].$filler;if(s){s.line.updateControlPoints(o,s.axis);i&&s.fill&&_drawfill(t.ctx,s,o)}}},beforeDatasetsDraw(t,e,s){if(s.drawTime!=="beforeDatasetsDraw")return;const i=t.getSortedVisibleDatasetMetas();for(let e=i.length-1;e>=0;--e){const s=i[e].$filler;_shouldApplyFill(s)&&_drawfill(t.ctx,s,t.chartArea)}},beforeDatasetDraw(t,e,s){const i=e.meta.$filler;_shouldApplyFill(i)&&s.drawTime==="beforeDatasetDraw"&&_drawfill(t.ctx,i,t.chartArea)},defaults:{propagate:true,drawTime:"beforeDatasetDraw"}};const getBoxSize=(t,e)=>{let{boxHeight:s=e,boxWidth:i=e}=t;if(t.usePointStyle){s=Math.min(s,e);i=t.pointStyleWidth||Math.min(i,e)}return{boxWidth:i,boxHeight:s,itemHeight:Math.max(e,s)}};const itemsEqual=(t,e)=>t!==null&&e!==null&&t.datasetIndex===e.datasetIndex&&t.index===e.index;class Legend extends Element{constructor(t){super();this._added=false;this.legendHitBoxes=[];this._hoveredItem=null;this.doughnutMode=false;this.chart=t.chart;this.options=t.options;this.ctx=t.ctx;this.legendItems=void 0;this.columnSizes=void 0;this.lineWidths=void 0;this.maxHeight=void 0;this.maxWidth=void 0;this.top=void 0;this.bottom=void 0;this.left=void 0;this.right=void 0;this.height=void 0;this.width=void 0;this._margins=void 0;this.position=void 0;this.weight=void 0;this.fullSize=void 0}update(t,e,s){this.maxWidth=t;this.maxHeight=e;this._margins=s;this.setDimensions();this.buildLabels();this.fit()}setDimensions(){if(this.isHorizontal()){this.width=this.maxWidth;this.left=this._margins.left;this.right=this.width}else{this.height=this.maxHeight;this.top=this._margins.top;this.bottom=this.height}}buildLabels(){const t=this.options.labels||{};let e=X(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter((e=>t.filter(e,this.chart.data))));t.sort&&(e=e.sort(((e,s)=>t.sort(e,s,this.chart.data))));this.options.reverse&&e.reverse();this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}const s=t.labels;const i=j(s.font);const n=i.size;const o=this._computeTitleHeight();const{boxWidth:a,itemHeight:r}=getBoxSize(s,n);let l,c;e.font=i.string;if(this.isHorizontal()){l=this.maxWidth;c=this._fitRows(o,n,a,r)+10}else{c=this.maxHeight;l=this._fitCols(o,i,a,r)+10}this.width=Math.min(l,t.maxWidth||this.maxWidth);this.height=Math.min(c,t.maxHeight||this.maxHeight)}_fitRows(t,e,s,i){const{ctx:n,maxWidth:o,options:{labels:{padding:a}}}=this;const r=this.legendHitBoxes=[];const l=this.lineWidths=[0];const c=i+a;let h=t;n.textAlign="left";n.textBaseline="middle";let d=-1;let u=-c;this.legendItems.forEach(((t,g)=>{const f=s+e/2+n.measureText(t.text).width;if(g===0||l[l.length-1]+f+2*a>o){h+=c;l[l.length-(g>0?0:1)]=0;u+=c;d++}r[g]={left:0,top:u,row:d,width:f,height:i};l[l.length-1]+=f+a}));return h}_fitCols(t,e,s,i){const{ctx:n,maxHeight:o,options:{labels:{padding:a}}}=this;const r=this.legendHitBoxes=[];const l=this.columnSizes=[];const c=o-t;let h=a;let d=0;let u=0;let g=0;let f=0;this.legendItems.forEach(((t,o)=>{const{itemWidth:p,itemHeight:m}=calculateItemSize(s,e,n,t,i);if(o>0&&u+m+2*a>c){h+=d+a;l.push({width:d,height:u});g+=d+a;f++;d=u=0}r[o]={left:g,top:u,col:f,width:p,height:m};d=Math.max(d,p);u+=m+a}));h+=d;l.push({width:d,height:u});return h}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight();const{legendHitBoxes:e,options:{align:s,labels:{padding:i},rtl:n}}=this;const o=Rt(n,this.left,this.width);if(this.isHorizontal()){let n=0;let a=U(s,this.left+i,this.right-this.lineWidths[n]);for(const r of e){if(n!==r.row){n=r.row;a=U(s,this.left+i,this.right-this.lineWidths[n])}r.top+=this.top+t+i;r.left=o.leftForLtr(o.x(a),r.width);a+=r.width+i}}else{let n=0;let a=U(s,this.top+t+i,this.bottom-this.columnSizes[n].height);for(const r of e){if(r.col!==n){n=r.col;a=U(s,this.top+t+i,this.bottom-this.columnSizes[n].height)}r.top=a;r.left+=this.left+i;r.left=o.leftForLtr(o.x(r.left),r.width);a+=r.height+i}}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;tt(t,this);this._draw();st(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:s,ctx:i}=this;const{align:n,labels:a}=t;const r=o.color;const l=Rt(t.rtl,this.left,this.width);const c=j(a.font);const{padding:d}=a;const u=c.size;const g=u/2;let f;this.drawTitle();i.textAlign=l.textAlign("left");i.textBaseline="middle";i.lineWidth=.5;i.font=c.string;const{boxWidth:p,boxHeight:m,itemHeight:x}=getBoxSize(a,u);const drawLegendBox=function(t,e,s){if(isNaN(p)||p<=0||isNaN(m)||m<0)return;i.save();const n=h(s.lineWidth,1);i.fillStyle=h(s.fillStyle,r);i.lineCap=h(s.lineCap,"butt");i.lineDashOffset=h(s.lineDashOffset,0);i.lineJoin=h(s.lineJoin,"miter");i.lineWidth=n;i.strokeStyle=h(s.strokeStyle,r);i.setLineDash(h(s.lineDash,[]));if(a.usePointStyle){const o={radius:m*Math.SQRT2/2,pointStyle:s.pointStyle,rotation:s.rotation,borderWidth:n};const r=l.xPlus(t,p/2);const c=e+g;Ft(i,o,r,c,a.pointStyleWidth&&p)}else{const o=e+Math.max((u-m)/2,0);const a=l.leftForLtr(t,p);const r=Tt(s.borderRadius);i.beginPath();Object.values(r).some((t=>t!==0))?Ot(i,{x:a,y:o,w:p,h:m,radius:r}):i.rect(a,o,p,m);i.fill();n!==0&&i.stroke()}i.restore()};const fillText=function(t,e,s){et(i,s.text,t,e+x/2,c,{strikethrough:s.hidden,textAlign:l.textAlign(s.textAlign)})};const b=this.isHorizontal();const _=this._computeTitleHeight();f=b?{x:U(n,this.left+d,this.right-s[0]),y:this.top+d+_,line:0}:{x:this.left+d,y:U(n,this.top+_+d,this.bottom-e[0].height),line:0};Bt(this.ctx,t.textDirection);const y=x+d;this.legendItems.forEach(((o,r)=>{i.strokeStyle=o.fontColor;i.fillStyle=o.fontColor;const h=i.measureText(o.text).width;const u=l.textAlign(o.textAlign||(o.textAlign=a.textAlign));const m=p+g+h;let x=f.x;let v=f.y;l.setWidth(this.width);if(b){if(r>0&&x+m+d>this.right){v=f.y+=y;f.line++;x=f.x=U(n,this.left+d,this.right-s[f.line])}}else if(r>0&&v+y>this.bottom){x=f.x=x+e[f.line].width+d;f.line++;v=f.y=U(n,this.top+_+d,this.bottom-e[f.line].height)}const k=l.x(x);drawLegendBox(k,v,o);x=zt(u,x+p+g,b?x+m:this.right,t.rtl);fillText(l.x(x),v,o);if(b)f.x+=m+d;else if(typeof o.text!=="string"){const t=c.lineHeight;f.y+=calculateLegendItemHeight(o,t)+d}else f.y+=y}));Vt(this.ctx,t.textDirection)}drawTitle(){const t=this.options;const e=t.title;const s=j(e.font);const i=I(e.padding);if(!e.display)return;const n=Rt(t.rtl,this.left,this.width);const o=this.ctx;const a=e.position;const r=s.size/2;const l=i.top+r;let c;let h=this.left;let d=this.width;if(this.isHorizontal()){d=Math.max(...this.lineWidths);c=this.top+l;h=U(t.align,h,this.right-d)}else{const e=this.columnSizes.reduce(((t,e)=>Math.max(t,e.height)),0);c=l+U(t.align,this.top,this.bottom-e-t.labels.padding-this._computeTitleHeight())}const u=U(a,h,h+d);o.textAlign=n.textAlign($(a));o.textBaseline="middle";o.strokeStyle=e.color;o.fillStyle=e.color;o.font=s.string;et(o,e.text,u,c,s)}_computeTitleHeight(){const t=this.options.title;const e=j(t.font);const s=I(t.padding);return t.display?e.lineHeight+s.height:0}_getLegendItemAt(t,e){let s,i,n;if(yt(t,this.left,this.right)&&yt(e,this.top,this.bottom)){n=this.legendHitBoxes;for(s=0;s<n.length;++s){i=n[s];if(yt(t,i.left,i.left+i.width)&&yt(e,i.top,i.top+i.height))return this.legendItems[s]}}return null}handleEvent(t){const e=this.options;if(!isListened(t.type,e))return;const s=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const i=this._hoveredItem;const n=itemsEqual(i,s);i&&!n&&X(e.onLeave,[t,i,this],this);this._hoveredItem=s;s&&!n&&X(e.onHover,[t,s,this],this)}else s&&X(e.onClick,[t,s,this],this)}}function calculateItemSize(t,e,s,i,n){const o=calculateItemWidth(i,t,e,s);const a=calculateItemHeight(n,i,e.lineHeight);return{itemWidth:o,itemHeight:a}}function calculateItemWidth(t,e,s,i){let n=t.text;n&&typeof n!=="string"&&(n=n.reduce(((t,e)=>t.length>e.length?t:e)));return e+s.size/2+i.measureText(n).width}function calculateItemHeight(t,e,s){let i=t;typeof e.text!=="string"&&(i=calculateLegendItemHeight(e,s));return i}function calculateLegendItemHeight(t,e){const s=t.text?t.text.length:0;return e*s}function isListened(t,e){return!(t!=="mousemove"&&t!=="mouseout"||!e.onHover&&!e.onLeave)||!(!e.onClick||t!=="click"&&t!=="mouseup")}var Se={id:"legend",_element:Legend,start(t,e,s){const i=t.legend=new Legend({ctx:t.ctx,options:s,chart:t});oe.configure(t,i,s);oe.addBox(t,i)},stop(t){oe.removeBox(t,t.legend);delete t.legend},beforeUpdate(t,e,s){const i=t.legend;oe.configure(t,i,s);i.options=s},afterUpdate(t){const e=t.legend;e.buildLabels();e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:true,position:"top",align:"center",fullSize:true,reverse:false,weight:1e3,onClick(t,e,s){const i=e.datasetIndex;const n=s.chart;if(n.isDatasetVisible(i)){n.hide(i);e.hidden=true}else{n.show(i);e.hidden=false}},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){const e=t.data.datasets;const{labels:{usePointStyle:s,pointStyle:i,textAlign:n,color:o,useBorderRadius:a,borderRadius:r}}=t.legend.options;return t._getSortedDatasetMetas().map((t=>{const l=t.controller.getStyle(s?0:void 0);const c=I(l.borderWidth);return{text:e[t.index].label,fillStyle:l.backgroundColor,fontColor:o,hidden:!t.visible,lineCap:l.borderCapStyle,lineDash:l.borderDash,lineDashOffset:l.borderDashOffset,lineJoin:l.borderJoinStyle,lineWidth:(c.width+c.height)/4,strokeStyle:l.borderColor,pointStyle:i||l.pointStyle,rotation:l.rotation,textAlign:n||l.textAlign,borderRadius:a&&(r||l.borderRadius),datasetIndex:t.index}}),this)}},title:{color:t=>t.chart.options.color,display:false,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}};class Title extends Element{constructor(t){super();this.chart=t.chart;this.options=t.options;this.ctx=t.ctx;this._padding=void 0;this.top=void 0;this.bottom=void 0;this.left=void 0;this.right=void 0;this.width=void 0;this.height=void 0;this.position=void 0;this.weight=void 0;this.fullSize=void 0}update(t,e){const s=this.options;this.left=0;this.top=0;if(!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t;this.height=this.bottom=e;const i=a(s.text)?s.text.length:1;this._padding=I(s.padding);const n=i*j(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=n:this.width=n}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:e,left:s,bottom:i,right:n,options:o}=this;const a=o.align;let r=0;let l,c,h;if(this.isHorizontal()){c=U(a,s,n);h=e+t;l=n-s}else{if(o.position==="left"){c=s+t;h=U(a,i,e);r=y*-.5}else{c=n-t;h=U(a,e,i);r=y*.5}l=i-e}return{titleX:c,titleY:h,maxWidth:l,rotation:r}}draw(){const t=this.ctx;const e=this.options;if(!e.display)return;const s=j(e.font);const i=s.lineHeight;const n=i/2+this._padding.top;const{titleX:o,titleY:a,maxWidth:r,rotation:l}=this._drawArgs(n);et(t,e.text,0,0,s,{color:e.color,maxWidth:r,rotation:l,textAlign:$(e.align),textBaseline:"middle",translation:[o,a]})}}function createTitle(t,e){const s=new Title({ctx:t.ctx,options:e,chart:t});oe.configure(t,s,e);oe.addBox(t,s);t.titleBlock=s}var we={id:"title",_element:Title,start(t,e,s){createTitle(t,s)},stop(t){const e=t.titleBlock;oe.removeBox(t,e);delete t.titleBlock},beforeUpdate(t,e,s){const i=t.titleBlock;oe.configure(t,i,s);i.options=s},defaults:{align:"center",display:false,font:{weight:"bold"},fullSize:true,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:true,_indexable:false}};const De=new WeakMap;var Ce={id:"subtitle",start(t,e,s){const i=new Title({ctx:t.ctx,options:s,chart:t});oe.configure(t,i,s);oe.addBox(t,i);De.set(t,i)},stop(t){oe.removeBox(t,De.get(t));De.delete(t)},beforeUpdate(t,e,s){const i=De.get(t);oe.configure(t,i,s);i.options=s},defaults:{align:"center",display:false,font:{weight:"normal"},fullSize:true,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:true,_indexable:false}};const Pe={average(t){if(!t.length)return false;let e,s;let i=0;let n=0;let o=0;for(e=0,s=t.length;e<s;++e){const s=t[e].element;if(s&&s.hasValue()){const t=s.tooltipPosition();i+=t.x;n+=t.y;++o}}return{x:i/o,y:n/o}},nearest(t,e){if(!t.length)return false;let s=e.x;let i=e.y;let n=Number.POSITIVE_INFINITY;let o,a,r;for(o=0,a=t.length;o<a;++o){const s=t[o].element;if(s&&s.hasValue()){const t=s.getCenterPoint();const i=Wt(e,t);if(i<n){n=i;r=s}}}if(r){const t=r.tooltipPosition();s=t.x;i=t.y}return{x:s,y:i}}};function pushOrConcat(t,e){e&&(a(e)?Array.prototype.push.apply(t,e):t.push(e));return t}function splitNewlines(t){return(typeof t==="string"||t instanceof String)&&t.indexOf("\n")>-1?t.split("\n"):t}function createTooltipItem(t,e){const{element:s,datasetIndex:i,index:n}=e;const o=t.getDatasetMeta(i).controller;const{label:a,value:r}=o.getLabelAndValue(n);return{chart:t,label:a,parsed:o.getParsed(n),raw:t.data.datasets[i].data[n],formattedValue:r,dataset:o.getDataset(),dataIndex:n,datasetIndex:i,element:s}}function getTooltipSize(t,e){const s=t.chart.ctx;const{body:i,footer:n,title:o}=t;const{boxWidth:a,boxHeight:r}=e;const l=j(e.bodyFont);const c=j(e.titleFont);const h=j(e.footerFont);const d=o.length;const u=n.length;const g=i.length;const f=I(e.padding);let p=f.height;let m=0;let x=i.reduce(((t,e)=>t+e.before.length+e.lines.length+e.after.length),0);x+=t.beforeBody.length+t.afterBody.length;d&&(p+=d*c.lineHeight+(d-1)*e.titleSpacing+e.titleMarginBottom);if(x){const t=e.displayColors?Math.max(r,l.lineHeight):l.lineHeight;p+=g*t+(x-g)*l.lineHeight+(x-1)*e.bodySpacing}u&&(p+=e.footerMarginTop+u*h.lineHeight+(u-1)*e.footerSpacing);let b=0;const maxLineWidth=function(t){m=Math.max(m,s.measureText(t).width+b)};s.save();s.font=c.string;R(t.title,maxLineWidth);s.font=l.string;R(t.beforeBody.concat(t.afterBody),maxLineWidth);b=e.displayColors?a+2+e.boxPadding:0;R(i,(t=>{R(t.before,maxLineWidth);R(t.lines,maxLineWidth);R(t.after,maxLineWidth)}));b=0;s.font=h.string;R(t.footer,maxLineWidth);s.restore();m+=f.width;return{width:m,height:p}}function determineYAlign(t,e){const{y:s,height:i}=e;return s<i/2?"top":s>t.height-i/2?"bottom":"center"}function doesNotFitWithAlign(t,e,s,i){const{x:n,width:o}=i;const a=s.caretSize+s.caretPadding;return t==="left"&&n+o+a>e.width||(t==="right"&&n-o-a<0||void 0)}function determineXAlign(t,e,s,i){const{x:n,width:o}=s;const{width:a,chartArea:{left:r,right:l}}=t;let c="center";i==="center"?c=n<=(r+l)/2?"left":"right":n<=o/2?c="left":n>=a-o/2&&(c="right");doesNotFitWithAlign(c,t,e,s)&&(c="center");return c}function determineAlignment(t,e,s){const i=s.yAlign||e.yAlign||determineYAlign(t,s);return{xAlign:s.xAlign||e.xAlign||determineXAlign(t,e,s,i),yAlign:i}}function alignX(t,e){let{x:s,width:i}=t;e==="right"?s-=i:e==="center"&&(s-=i/2);return s}function alignY(t,e,s){let{y:i,height:n}=t;e==="top"?i+=s:i-=e==="bottom"?n+s:n/2;return i}function getBackgroundPoint(t,e,s,i){const{caretSize:n,caretPadding:o,cornerRadius:a}=t;const{xAlign:r,yAlign:l}=s;const c=n+o;const{topLeft:h,topRight:d,bottomLeft:u,bottomRight:g}=Tt(a);let f=alignX(e,r);const p=alignY(e,l,c);l==="center"?r==="left"?f+=c:r==="right"&&(f-=c):r==="left"?f-=Math.max(h,u)+n:r==="right"&&(f+=Math.max(d,g)+n);return{x:G(f,0,i.width-e.width),y:G(p,0,i.height-e.height)}}function getAlignedX(t,e,s){const i=I(s.padding);return e==="center"?t.x+t.width/2:e==="right"?t.x+t.width-i.right:t.x+i.left}function getBeforeAfterBodyLines(t){return pushOrConcat([],splitNewlines(t))}function createTooltipContext(t,e,s){return c(t,{tooltip:e,tooltipItems:s,type:"tooltip"})}function overrideCallbacks(t,e){const s=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return s?t.override(s):t}const Ae={beforeTitle:Nt,title(t){if(t.length>0){const e=t[0];const s=e.chart.data.labels;const i=s?s.length:0;if(this&&this.options&&this.options.mode==="dataset")return e.dataset.label||"";if(e.label)return e.label;if(i>0&&e.dataIndex<i)return s[e.dataIndex]}return""},afterTitle:Nt,beforeBody:Nt,beforeLabel:Nt,label(t){if(this&&this.options&&this.options.mode==="dataset")return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");const s=t.formattedValue;m(s)||(e+=s);return e},labelColor(t){const e=t.chart.getDatasetMeta(t.datasetIndex);const s=e.controller.getStyle(t.dataIndex);return{borderColor:s.borderColor,backgroundColor:s.backgroundColor,borderWidth:s.borderWidth,borderDash:s.borderDash,borderDashOffset:s.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){const e=t.chart.getDatasetMeta(t.datasetIndex);const s=e.controller.getStyle(t.dataIndex);return{pointStyle:s.pointStyle,rotation:s.rotation}},afterLabel:Nt,afterBody:Nt,beforeFooter:Nt,footer:Nt,afterFooter:Nt};function invokeCallbackWithFallback(t,e,s,i){const n=t[e].call(s,i);return typeof n==="undefined"?Ae[e].call(s,i):n}class Tooltip extends Element{static positioners=Pe;constructor(t){super();this.opacity=0;this._active=[];this._eventPosition=void 0;this._size=void 0;this._cachedAnimations=void 0;this._tooltipItems=[];this.$animations=void 0;this.$context=void 0;this.chart=t.chart;this.options=t.options;this.dataPoints=void 0;this.title=void 0;this.beforeBody=void 0;this.body=void 0;this.afterBody=void 0;this.footer=void 0;this.xAlign=void 0;this.yAlign=void 0;this.x=void 0;this.y=void 0;this.height=void 0;this.width=void 0;this.caretX=void 0;this.caretY=void 0;this.labelColors=void 0;this.labelPointStyles=void 0;this.labelTextColors=void 0}initialize(t){this.options=t;this._cachedAnimations=void 0;this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart;const s=this.options.setContext(this.getContext());const i=s.enabled&&e.options.animation&&s.animations;const n=new Animations(this.chart,i);i._cacheable&&(this._cachedAnimations=Object.freeze(n));return n}getContext(){return this.$context||(this.$context=createTooltipContext(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){const{callbacks:s}=e;const i=invokeCallbackWithFallback(s,"beforeTitle",this,t);const n=invokeCallbackWithFallback(s,"title",this,t);const o=invokeCallbackWithFallback(s,"afterTitle",this,t);let a=[];a=pushOrConcat(a,splitNewlines(i));a=pushOrConcat(a,splitNewlines(n));a=pushOrConcat(a,splitNewlines(o));return a}getBeforeBody(t,e){return getBeforeAfterBodyLines(invokeCallbackWithFallback(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:s}=e;const i=[];R(t,(t=>{const e={before:[],lines:[],after:[]};const n=overrideCallbacks(s,t);pushOrConcat(e.before,splitNewlines(invokeCallbackWithFallback(n,"beforeLabel",this,t)));pushOrConcat(e.lines,invokeCallbackWithFallback(n,"label",this,t));pushOrConcat(e.after,splitNewlines(invokeCallbackWithFallback(n,"afterLabel",this,t)));i.push(e)}));return i}getAfterBody(t,e){return getBeforeAfterBodyLines(invokeCallbackWithFallback(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:s}=e;const i=invokeCallbackWithFallback(s,"beforeFooter",this,t);const n=invokeCallbackWithFallback(s,"footer",this,t);const o=invokeCallbackWithFallback(s,"afterFooter",this,t);let a=[];a=pushOrConcat(a,splitNewlines(i));a=pushOrConcat(a,splitNewlines(n));a=pushOrConcat(a,splitNewlines(o));return a}_createItems(t){const e=this._active;const s=this.chart.data;const i=[];const n=[];const o=[];let a=[];let r,l;for(r=0,l=e.length;r<l;++r)a.push(createTooltipItem(this.chart,e[r]));t.filter&&(a=a.filter(((e,i,n)=>t.filter(e,i,n,s))));t.itemSort&&(a=a.sort(((e,i)=>t.itemSort(e,i,s))));R(a,(e=>{const s=overrideCallbacks(t.callbacks,e);i.push(invokeCallbackWithFallback(s,"labelColor",this,e));n.push(invokeCallbackWithFallback(s,"labelPointStyle",this,e));o.push(invokeCallbackWithFallback(s,"labelTextColor",this,e))}));this.labelColors=i;this.labelPointStyles=n;this.labelTextColors=o;this.dataPoints=a;return a}update(t,e){const s=this.options.setContext(this.getContext());const i=this._active;let n;let o=[];if(i.length){const t=Pe[s.position].call(this,i,this._eventPosition);o=this._createItems(s);this.title=this.getTitle(o,s);this.beforeBody=this.getBeforeBody(o,s);this.body=this.getBody(o,s);this.afterBody=this.getAfterBody(o,s);this.footer=this.getFooter(o,s);const e=this._size=getTooltipSize(this,s);const a=Object.assign({},t,e);const r=determineAlignment(this.chart,s,a);const l=getBackgroundPoint(s,a,r,this.chart);this.xAlign=r.xAlign;this.yAlign=r.yAlign;n={opacity:1,x:l.x,y:l.y,width:e.width,height:e.height,caretX:t.x,caretY:t.y}}else this.opacity!==0&&(n={opacity:0});this._tooltipItems=o;this.$context=void 0;n&&this._resolveAnimations().update(this,n);t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,s,i){const n=this.getCaretPosition(t,s,i);e.lineTo(n.x1,n.y1);e.lineTo(n.x2,n.y2);e.lineTo(n.x3,n.y3)}getCaretPosition(t,e,s){const{xAlign:i,yAlign:n}=this;const{caretSize:o,cornerRadius:a}=s;const{topLeft:r,topRight:l,bottomLeft:c,bottomRight:h}=Tt(a);const{x:d,y:u}=t;const{width:g,height:f}=e;let p,m,x,b,_,y;if(n==="center"){_=u+f/2;if(i==="left"){p=d;m=p-o;b=_+o;y=_-o}else{p=d+g;m=p+o;b=_-o;y=_+o}x=p}else{m=i==="left"?d+Math.max(r,c)+o:i==="right"?d+g-Math.max(l,h)-o:this.caretX;if(n==="top"){b=u;_=b-o;p=m-o;x=m+o}else{b=u+f;_=b+o;p=m+o;x=m-o}y=b}return{x1:p,x2:m,x3:x,y1:b,y2:_,y3:y}}drawTitle(t,e,s){const i=this.title;const n=i.length;let o,a,r;if(n){const l=Rt(s.rtl,this.x,this.width);t.x=getAlignedX(this,s.titleAlign,s);e.textAlign=l.textAlign(s.titleAlign);e.textBaseline="middle";o=j(s.titleFont);a=s.titleSpacing;e.fillStyle=s.titleColor;e.font=o.string;for(r=0;r<n;++r){e.fillText(i[r],l.x(t.x),t.y+o.lineHeight/2);t.y+=o.lineHeight+a;r+1===n&&(t.y+=s.titleMarginBottom-a)}}}_drawColorBox(t,e,s,i,o){const a=this.labelColors[s];const r=this.labelPointStyles[s];const{boxHeight:l,boxWidth:c}=o;const h=j(o.bodyFont);const d=getAlignedX(this,"left",o);const u=i.x(d);const g=l<h.lineHeight?(h.lineHeight-l)/2:0;const f=e.y+g;if(o.usePointStyle){const e={radius:Math.min(c,l)/2,pointStyle:r.pointStyle,rotation:r.rotation,borderWidth:1};const s=i.leftForLtr(u,c)+c/2;const n=f+l/2;t.strokeStyle=o.multiKeyBackground;t.fillStyle=o.multiKeyBackground;At(t,e,s,n);t.strokeStyle=a.borderColor;t.fillStyle=a.backgroundColor;At(t,e,s,n)}else{t.lineWidth=n(a.borderWidth)?Math.max(...Object.values(a.borderWidth)):a.borderWidth||1;t.strokeStyle=a.borderColor;t.setLineDash(a.borderDash||[]);t.lineDashOffset=a.borderDashOffset||0;const e=i.leftForLtr(u,c);const s=i.leftForLtr(i.xPlus(u,1),c-2);const r=Tt(a.borderRadius);if(Object.values(r).some((t=>t!==0))){t.beginPath();t.fillStyle=o.multiKeyBackground;Ot(t,{x:e,y:f,w:c,h:l,radius:r});t.fill();t.stroke();t.fillStyle=a.backgroundColor;t.beginPath();Ot(t,{x:s,y:f+1,w:c-2,h:l-2,radius:r});t.fill()}else{t.fillStyle=o.multiKeyBackground;t.fillRect(e,f,c,l);t.strokeRect(e,f,c,l);t.fillStyle=a.backgroundColor;t.fillRect(s,f+1,c-2,l-2)}}t.fillStyle=this.labelTextColors[s]}drawBody(t,e,s){const{body:i}=this;const{bodySpacing:n,bodyAlign:o,displayColors:a,boxHeight:r,boxWidth:l,boxPadding:c}=s;const h=j(s.bodyFont);let d=h.lineHeight;let u=0;const g=Rt(s.rtl,this.x,this.width);const fillLineOfText=function(s){e.fillText(s,g.x(t.x+u),t.y+d/2);t.y+=d+n};const f=g.textAlign(o);let p,m,x,b,_,y,v;e.textAlign=o;e.textBaseline="middle";e.font=h.string;t.x=getAlignedX(this,f,s);e.fillStyle=s.bodyColor;R(this.beforeBody,fillLineOfText);u=a&&f!=="right"?o==="center"?l/2+c:l+2+c:0;for(b=0,y=i.length;b<y;++b){p=i[b];m=this.labelTextColors[b];e.fillStyle=m;R(p.before,fillLineOfText);x=p.lines;if(a&&x.length){this._drawColorBox(e,t,b,g,s);d=Math.max(h.lineHeight,r)}for(_=0,v=x.length;_<v;++_){fillLineOfText(x[_]);d=h.lineHeight}R(p.after,fillLineOfText)}u=0;d=h.lineHeight;R(this.afterBody,fillLineOfText);t.y-=n}drawFooter(t,e,s){const i=this.footer;const n=i.length;let o,a;if(n){const r=Rt(s.rtl,this.x,this.width);t.x=getAlignedX(this,s.footerAlign,s);t.y+=s.footerMarginTop;e.textAlign=r.textAlign(s.footerAlign);e.textBaseline="middle";o=j(s.footerFont);e.fillStyle=s.footerColor;e.font=o.string;for(a=0;a<n;++a){e.fillText(i[a],r.x(t.x),t.y+o.lineHeight/2);t.y+=o.lineHeight+s.footerSpacing}}}drawBackground(t,e,s,i){const{xAlign:n,yAlign:o}=this;const{x:a,y:r}=t;const{width:l,height:c}=s;const{topLeft:h,topRight:d,bottomLeft:u,bottomRight:g}=Tt(i.cornerRadius);e.fillStyle=i.backgroundColor;e.strokeStyle=i.borderColor;e.lineWidth=i.borderWidth;e.beginPath();e.moveTo(a+h,r);o==="top"&&this.drawCaret(t,e,s,i);e.lineTo(a+l-d,r);e.quadraticCurveTo(a+l,r,a+l,r+d);o==="center"&&n==="right"&&this.drawCaret(t,e,s,i);e.lineTo(a+l,r+c-g);e.quadraticCurveTo(a+l,r+c,a+l-g,r+c);o==="bottom"&&this.drawCaret(t,e,s,i);e.lineTo(a+u,r+c);e.quadraticCurveTo(a,r+c,a,r+c-u);o==="center"&&n==="left"&&this.drawCaret(t,e,s,i);e.lineTo(a,r+h);e.quadraticCurveTo(a,r,a+h,r);e.closePath();e.fill();i.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart;const s=this.$animations;const i=s&&s.x;const n=s&&s.y;if(i||n){const s=Pe[t.position].call(this,this._active,this._eventPosition);if(!s)return;const o=this._size=getTooltipSize(this,t);const a=Object.assign({},s,this._size);const r=determineAlignment(e,t,a);const l=getBackgroundPoint(t,a,r,e);if(i._to!==l.x||n._to!==l.y){this.xAlign=r.xAlign;this.yAlign=r.yAlign;this.width=o.width;this.height=o.height;this.caretX=s.x;this.caretY=s.y;this._resolveAnimations().update(this,l)}}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let s=this.opacity;if(!s)return;this._updateAnimationTarget(e);const i={width:this.width,height:this.height};const n={x:this.x,y:this.y};s=Math.abs(s)<.001?0:s;const o=I(e.padding);const a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;if(e.enabled&&a){t.save();t.globalAlpha=s;this.drawBackground(n,t,i,e);Bt(t,e.textDirection);n.y+=o.top;this.drawTitle(n,t,e);this.drawBody(n,t,e);this.drawFooter(n,t,e);Vt(t,e.textDirection);t.restore()}}getActiveElements(){return this._active||[]}setActiveElements(t,e){const s=this._active;const i=t.map((({datasetIndex:t,index:e})=>{const s=this.chart.getDatasetMeta(t);if(!s)throw new Error("Cannot find a dataset at index "+t);return{datasetIndex:t,element:s.data[e],index:e}}));const n=!xt(s,i);const o=this._positionChanged(i,e);if(n||o){this._active=i;this._eventPosition=e;this._ignoreReplayEvents=true;this.update(true)}}handleEvent(t,e,s=true){if(e&&this._ignoreReplayEvents)return false;this._ignoreReplayEvents=false;const i=this.options;const n=this._active||[];const o=this._getActiveElements(t,n,e,s);const a=this._positionChanged(o,t);const r=e||!xt(o,n)||a;if(r){this._active=o;if(i.enabled||i.external){this._eventPosition={x:t.x,y:t.y};this.update(true,e)}}return r}_getActiveElements(t,e,s,i){const n=this.options;if(t.type==="mouseout")return[];if(!i)return e.filter((t=>this.chart.data.datasets[t.datasetIndex]&&this.chart.getDatasetMeta(t.datasetIndex).controller.getParsed(t.index)!==void 0));const o=this.chart.getElementsAtEventForMode(t,n.mode,n,s);n.reverse&&o.reverse();return o}_positionChanged(t,e){const{caretX:s,caretY:i,options:n}=this;const o=Pe[n.position].call(this,t,e);return o!==false&&(s!==o.x||i!==o.y)}}var Le={id:"tooltip",_element:Tooltip,positioners:Pe,afterInit(t,e,s){s&&(t.tooltip=new Tooltip({chart:t,options:s}))},beforeUpdate(t,e,s){t.tooltip&&t.tooltip.initialize(s)},reset(t,e,s){t.tooltip&&t.tooltip.initialize(s)},afterDraw(t){const e=t.tooltip;if(e&&e._willRender()){const s={tooltip:e};if(t.notifyPlugins("beforeTooltipDraw",{...s,cancelable:true})===false)return;e.draw(t.ctx);t.notifyPlugins("afterTooltipDraw",s)}},afterEvent(t,e){if(t.tooltip){const s=e.replay;t.tooltip.handleEvent(e.event,s,e.inChartArea)&&(e.changed=true)}},defaults:{enabled:true,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:true,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Ae},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>t!=="filter"&&t!=="itemSort"&&t!=="external",_indexable:false,callbacks:{_scriptable:false,_indexable:false},animation:{_fallback:false},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};var Te=Object.freeze({__proto__:null,Colors:ve,Decimation:ke,Filler:Me,Legend:Se,SubTitle:Ce,Title:we,Tooltip:Le});const addIfString=(t,e,s,i)=>{if(typeof e==="string"){s=t.push(e)-1;i.unshift({index:s,label:e})}else isNaN(e)&&(s=null);return s};function findOrAddLabel(t,e,s,i){const n=t.indexOf(e);if(n===-1)return addIfString(t,e,s,i);const o=t.lastIndexOf(e);return n!==o?s:n}const validIndex=(t,e)=>t===null?null:G(Math.round(t),0,e);function _getLabelForValue(t){const e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class CategoryScale extends Scale{static id="category";static defaults={ticks:{callback:_getLabelForValue}};constructor(t){super(t);this._startValue=void 0;this._valueRange=0;this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const t=this.getLabels();for(const{index:s,label:i}of e)t[s]===i&&t.splice(s,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(m(t))return null;const s=this.getLabels();e=isFinite(e)&&s[e]===t?e:findOrAddLabel(s,t,h(e,t),this._addedLabels);return validIndex(e,s.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:s,max:i}=this.getMinMax(true);if(this.options.bounds==="ticks"){t||(s=0);e||(i=this.getLabels().length-1)}this.min=s;this.max=i}buildTicks(){const t=this.min;const e=this.max;const s=this.options.offset;const i=[];let n=this.getLabels();n=t===0&&e===n.length-1?n:n.slice(t,e+1);this._valueRange=Math.max(n.length-(s?0:1),1);this._startValue=this.min-(s?.5:0);for(let s=t;s<=e;s++)i.push({value:s});return i}getLabelForValue(t){return _getLabelForValue.call(this,t)}configure(){super.configure();this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){typeof t!=="number"&&(t=this.parse(t));return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}function generateTicks$1(t,e){const s=[];const i=1e-14;const{bounds:n,step:o,min:a,max:r,precision:l,count:c,maxTicks:h,maxDigits:d,includeBounds:u}=t;const g=o||1;const f=h-1;const{min:p,max:x}=e;const b=!m(a);const _=!m(r);const y=!m(c);const v=(x-p)/(d+1);let k=Ht((x-p)/f/g)*g;let M,S,w,D;if(k<i&&!b&&!_)return[{value:p},{value:x}];D=Math.ceil(x/k)-Math.floor(p/k);D>f&&(k=Ht(D*k/f/g)*g);if(!m(l)){M=Math.pow(10,l);k=Math.ceil(k*M)/M}if(n==="ticks"){S=Math.floor(p/k)*k;w=Math.ceil(x/k)*k}else{S=p;w=x}if(b&&_&&o&&jt((r-a)/o,k/1e3)){D=Math.round(Math.min((r-a)/k,h));k=(r-a)/D;S=a;w=r}else if(y){S=b?a:S;w=_?r:w;D=c-1;k=(w-S)/D}else{D=(w-S)/k;D=$t(D,Math.round(D),k/1e3)?Math.round(D):Math.ceil(D)}const C=Math.max(Ut(k),Ut(S));M=Math.pow(10,m(l)?C:l);S=Math.round(S*M)/M;w=Math.round(w*M)/M;let P=0;if(b)if(u&&S!==a){s.push({value:a});S<a&&P++;$t(Math.round((S+P*k)*M)/M,a,relativeLabelSize(a,v,t))&&P++}else S<a&&P++;for(;P<D;++P){const t=Math.round((S+P*k)*M)/M;if(_&&t>r)break;s.push({value:t})}_&&u&&w!==r?s.length&&$t(s[s.length-1].value,r,relativeLabelSize(r,v,t))?s[s.length-1].value=r:s.push({value:r}):_&&w!==r||s.push({value:w});return s}function relativeLabelSize(t,e,{horizontal:s,minRotation:i}){const n=v(i);const o=(s?Math.sin(n):Math.cos(n))||.001;const a=.75*e*(""+t).length;return Math.min(e/o,a)}class LinearScaleBase extends Scale{constructor(t){super(t);this.start=void 0;this.end=void 0;this._startValue=void 0;this._endValue=void 0;this._valueRange=0}parse(t,e){return m(t)||(typeof t==="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options;const{minDefined:e,maxDefined:s}=this.getUserBounds();let{min:i,max:n}=this;const setMin=t=>i=e?i:t;const setMax=t=>n=s?n:t;if(t){const t=l(i);const e=l(n);t<0&&e<0?setMax(0):t>0&&e>0&&setMin(0)}if(i===n){let e=n===0?1:Math.abs(n*.05);setMax(n+e);t||setMin(i-e)}this.min=i;this.max=n}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:e,stepSize:s}=t;let i;if(s){i=Math.ceil(this.max/s)-Math.floor(this.min/s)+1;if(i>1e3){console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${i} ticks. Limiting to 1000.`);i=1e3}}else{i=this.computeTickLimit();e=e||11}e&&(i=Math.min(e,i));return i}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options;const e=t.ticks;let s=this.getTickLimit();s=Math.max(2,s);const i={maxTicks:s,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==false};const n=this._range||this;const o=generateTicks$1(i,n);t.bounds==="ticks"&&Yt(o,this,"value");if(t.reverse){o.reverse();this.start=this.max;this.end=this.min}else{this.start=this.min;this.end=this.max}return o}configure(){const t=this.ticks;let e=this.min;let s=this.max;super.configure();if(this.options.offset&&t.length){const i=(s-e)/Math.max(t.length-1,1)/2;e-=i;s+=i}this._startValue=e;this._endValue=s;this._valueRange=s-e}getLabelForValue(t){return S(t,this.chart.options.locale,this.options.ticks.format)}}class LinearScale extends LinearScaleBase{static id="linear";static defaults={ticks:{callback:Xt.formatters.numeric}};determineDataLimits(){const{min:t,max:e}=this.getMinMax(true);this.min=r(t)?t:0;this.max=r(e)?e:1;this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal();const e=t?this.width:this.height;const s=v(this.options.ticks.minRotation);const i=(t?Math.sin(s):Math.cos(s))||.001;const n=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,n.lineHeight/i))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}const log10Floor=t=>Math.floor(Kt(t));const changeExponent=(t,e)=>Math.pow(10,log10Floor(t)+e);function isMajor(t){const e=t/Math.pow(10,log10Floor(t));return e===1}function steps(t,e,s){const i=Math.pow(10,s);const n=Math.floor(t/i);const o=Math.ceil(e/i);return o-n}function startExp(t,e){const s=e-t;let i=log10Floor(s);while(steps(t,e,i)>10)i++;while(steps(t,e,i)<10)i--;return Math.min(i,log10Floor(t))}function generateTicks(t,{min:e,max:s}){e=Y(t.min,e);const i=[];const n=log10Floor(e);let o=startExp(e,s);let a=o<0?Math.pow(10,Math.abs(o)):1;const r=Math.pow(10,o);const l=n>o?Math.pow(10,n):0;const c=Math.round((e-l)*a)/a;const h=Math.floor((e-l)/r/10)*r*10;let d=Math.floor((c-h)/Math.pow(10,o));let u=Y(t.min,Math.round((l+h+d*Math.pow(10,o))*a)/a);while(u<s){i.push({value:u,major:isMajor(u),significand:d});d>=10?d=d<15?15:20:d++;if(d>=20){o++;d=2;a=o>=0?1:a}u=Math.round((l+h+d*Math.pow(10,o))*a)/a}const g=Y(t.max,u);i.push({value:g,major:isMajor(g),significand:d});return i}class LogarithmicScale extends Scale{static id="logarithmic";static defaults={ticks:{callback:Xt.formatters.logarithmic,major:{enabled:true}}};constructor(t){super(t);this.start=void 0;this.end=void 0;this._startValue=void 0;this._valueRange=0}parse(t,e){const s=LinearScaleBase.prototype.parse.apply(this,[t,e]);if(s!==0)return r(s)&&s>0?s:null;this._zero=true}determineDataLimits(){const{min:t,max:e}=this.getMinMax(true);this.min=r(t)?Math.max(0,t):null;this.max=r(e)?Math.max(0,e):null;this.options.beginAtZero&&(this._zero=true);this._zero&&this.min!==this._suggestedMin&&!r(this._userMin)&&(this.min=t===changeExponent(this.min,0)?changeExponent(this.min,-1):changeExponent(this.min,0));this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let s=this.min;let i=this.max;const setMin=e=>s=t?s:e;const setMax=t=>i=e?i:t;if(s===i)if(s<=0){setMin(1);setMax(10)}else{setMin(changeExponent(s,-1));setMax(changeExponent(i,1))}s<=0&&setMin(changeExponent(i,-1));i<=0&&setMax(changeExponent(s,1));this.min=s;this.max=i}buildTicks(){const t=this.options;const e={min:this._userMin,max:this._userMax};const s=generateTicks(e,this);t.bounds==="ticks"&&Yt(s,this,"value");if(t.reverse){s.reverse();this.start=this.max;this.end=this.min}else{this.start=this.min;this.end=this.max}return s}getLabelForValue(t){return t===void 0?"0":S(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure();this._startValue=Kt(t);this._valueRange=Kt(this.max)-Kt(t)}getPixelForValue(t){t!==void 0&&t!==0||(t=this.min);return t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(Kt(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const e=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+e*this._valueRange)}}function getTickBackdropHeight(t){const e=t.ticks;if(e.display&&t.display){const t=I(e.backdropPadding);return h(e.font&&e.font.size,o.font.size)+t.height}return 0}function measureLabelSize(t,e,s){s=a(s)?s:[s];return{w:Gt(t,e.string,s),h:s.length*e.lineHeight}}function determineLimits(t,e,s,i,n){return t===i||t===n?{start:e-s/2,end:e+s/2}:t<i||t>n?{start:e-s,end:e}:{start:e,end:e+s}}function fitWithPointLabels(t){const e={l:t.left+t._padding.left,r:t.right-t._padding.right,t:t.top+t._padding.top,b:t.bottom-t._padding.bottom};const s=Object.assign({},e);const i=[];const n=[];const o=t._pointLabels.length;const a=t.options.pointLabels;const r=a.centerPointLabels?y/o:0;for(let l=0;l<o;l++){const o=a.setContext(t.getPointLabelContext(l));n[l]=o.padding;const c=t.getPointPosition(l,t.drawingArea+n[l],r);const h=j(o.font);const d=measureLabelSize(t.ctx,h,t._pointLabels[l]);i[l]=d;const u=It(t.getIndexAngle(l)+r);const g=Math.round(q(u));const f=determineLimits(g,c.x,d.w,0,180);const p=determineLimits(g,c.y,d.h,90,270);updateLimits(s,e,u,f,p)}t.setCenterPoint(e.l-s.l,s.r-e.r,e.t-s.t,s.b-e.b);t._pointLabelItems=buildPointLabelItems(t,i,n)}function updateLimits(t,e,s,i,n){const o=Math.abs(Math.sin(s));const a=Math.abs(Math.cos(s));let r=0;let l=0;if(i.start<e.l){r=(e.l-i.start)/o;t.l=Math.min(t.l,e.l-r)}else if(i.end>e.r){r=(i.end-e.r)/o;t.r=Math.max(t.r,e.r+r)}if(n.start<e.t){l=(e.t-n.start)/a;t.t=Math.min(t.t,e.t-l)}else if(n.end>e.b){l=(n.end-e.b)/a;t.b=Math.max(t.b,e.b+l)}}function createPointLabelItem(t,e,s){const i=t.drawingArea;const{extra:n,additionalAngle:o,padding:a,size:r}=s;const l=t.getPointPosition(e,i+n+a,o);const c=Math.round(q(It(l.angle+_)));const h=yForAngle(l.y,r.h,c);const d=getTextAlignForAngle(c);const u=leftForTextAlign(l.x,r.w,d);return{visible:true,x:l.x,y:h,textAlign:d,left:u,top:h,right:u+r.w,bottom:h+r.h}}function isNotOverlapped(t,e){if(!e)return true;const{left:s,top:i,right:n,bottom:o}=t;const a=T({x:s,y:i},e)||T({x:s,y:o},e)||T({x:n,y:i},e)||T({x:n,y:o},e);return!a}function buildPointLabelItems(t,e,s){const i=[];const n=t._pointLabels.length;const o=t.options;const{centerPointLabels:a,display:r}=o.pointLabels;const l={extra:getTickBackdropHeight(o)/2,additionalAngle:a?y/n:0};let c;for(let o=0;o<n;o++){l.padding=s[o];l.size=e[o];const n=createPointLabelItem(t,o,l);i.push(n);if(r==="auto"){n.visible=isNotOverlapped(n,c);n.visible&&(c=n)}}return i}function getTextAlignForAngle(t){return t===0||t===180?"center":t<180?"left":"right"}function leftForTextAlign(t,e,s){s==="right"?t-=e:s==="center"&&(t-=e/2);return t}function yForAngle(t,e,s){s===90||s===270?t-=e/2:(s>270||s<90)&&(t-=e);return t}function drawPointLabelBox(t,e,s){const{left:i,top:n,right:o,bottom:a}=s;const{backdropColor:r}=e;if(!m(r)){const s=Tt(e.borderRadius);const l=I(e.backdropPadding);t.fillStyle=r;const c=i-l.left;const h=n-l.top;const d=o-i+l.width;const u=a-n+l.height;if(Object.values(s).some((t=>t!==0))){t.beginPath();Ot(t,{x:c,y:h,w:d,h:u,radius:s});t.fill()}else t.fillRect(c,h,d,u)}}function drawPointLabels(t,e){const{ctx:s,options:{pointLabels:i}}=t;for(let n=e-1;n>=0;n--){const e=t._pointLabelItems[n];if(!e.visible)continue;const o=i.setContext(t.getPointLabelContext(n));drawPointLabelBox(s,o,e);const a=j(o.font);const{x:r,y:l,textAlign:c}=e;et(s,t._pointLabels[n],r,l+a.lineHeight/2,a,{color:o.color,textAlign:c,textBaseline:"middle"})}}function pathRadiusLine(t,e,s,i){const{ctx:n}=t;if(s)n.arc(t.xCenter,t.yCenter,e,0,x);else{let s=t.getPointPosition(0,e);n.moveTo(s.x,s.y);for(let o=1;o<i;o++){s=t.getPointPosition(o,e);n.lineTo(s.x,s.y)}}}function drawRadiusLine(t,e,s,i,n){const o=t.ctx;const a=e.circular;const{color:r,lineWidth:l}=e;if((a||i)&&r&&l&&!(s<0)){o.save();o.strokeStyle=r;o.lineWidth=l;o.setLineDash(n.dash);o.lineDashOffset=n.dashOffset;o.beginPath();pathRadiusLine(t,s,a,i);o.closePath();o.stroke();o.restore()}}function createPointLabelContext(t,e,s){return c(t,{label:s,index:e,type:"pointLabel"})}class RadialLinearScale extends LinearScaleBase{static id="radialLinear";static defaults={display:true,animate:true,position:"chartArea",angleLines:{display:true,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:false},startAngle:0,ticks:{showLabelBackdrop:true,callback:Xt.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:true,font:{size:10},callback(t){return t},padding:5,centerPointLabels:false}};static defaultRoutes={"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"};static descriptors={angleLines:{_fallback:"grid"}};constructor(t){super(t);this.xCenter=void 0;this.yCenter=void 0;this.drawingArea=void 0;this._pointLabels=[];this._pointLabelItems=[]}setDimensions(){const t=this._padding=I(getTickBackdropHeight(this.options)/2);const e=this.width=this.maxWidth-t.width;const s=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+e/2+t.left);this.yCenter=Math.floor(this.top+s/2+t.top);this.drawingArea=Math.floor(Math.min(e,s)/2)}determineDataLimits(){const{min:t,max:e}=this.getMinMax(false);this.min=r(t)&&!isNaN(t)?t:0;this.max=r(e)&&!isNaN(e)?e:0;this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/getTickBackdropHeight(this.options))}generateTickLabels(t){LinearScaleBase.prototype.generateTickLabels.call(this,t);this._pointLabels=this.getLabels().map(((t,e)=>{const s=X(this.options.pointLabels.callback,[t,e],this);return s||s===0?s:""})).filter(((t,e)=>this.chart.getDataVisibility(e)))}fit(){const t=this.options;t.display&&t.pointLabels.display?fitWithPointLabels(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,e,s,i){this.xCenter+=Math.floor((t-e)/2);this.yCenter+=Math.floor((s-i)/2);this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,e,s,i))}getIndexAngle(t){const e=x/(this._pointLabels.length||1);const s=this.options.startAngle||0;return It(t*e+v(s))}getDistanceFromCenterForValue(t){if(m(t))return NaN;const e=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*e:(t-this.min)*e}getValueForDistanceFromCenter(t){if(m(t))return NaN;const e=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-e:this.min+e}getPointLabelContext(t){const e=this._pointLabels||[];if(t>=0&&t<e.length){const s=e[t];return createPointLabelContext(this.getContext(),t,s)}}getPointPosition(t,e,s=0){const i=this.getIndexAngle(t)-_+s;return{x:Math.cos(i)*e+this.xCenter,y:Math.sin(i)*e+this.yCenter,angle:i}}getPointPositionForValue(t,e){return this.getPointPosition(t,this.getDistanceFromCenterForValue(e))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:e,top:s,right:i,bottom:n}=this._pointLabelItems[t];return{left:e,top:s,right:i,bottom:n}}drawBackground(){const{backgroundColor:t,grid:{circular:e}}=this.options;if(t){const s=this.ctx;s.save();s.beginPath();pathRadiusLine(this,this.getDistanceFromCenterForValue(this._endValue),e,this._pointLabels.length);s.closePath();s.fillStyle=t;s.fill();s.restore()}}drawGrid(){const t=this.ctx;const e=this.options;const{angleLines:s,grid:i,border:n}=e;const o=this._pointLabels.length;let a,r,l;e.pointLabels.display&&drawPointLabels(this,o);i.display&&this.ticks.forEach(((t,e)=>{if(e!==0){r=this.getDistanceFromCenterForValue(t.value);const s=this.getContext(e);const a=i.setContext(s);const l=n.setContext(s);drawRadiusLine(this,a,r,o,l)}}));if(s.display){t.save();for(a=o-1;a>=0;a--){const i=s.setContext(this.getPointLabelContext(a));const{color:n,lineWidth:o}=i;if(o&&n){t.lineWidth=o;t.strokeStyle=n;t.setLineDash(i.borderDash);t.lineDashOffset=i.borderDashOffset;r=this.getDistanceFromCenterForValue(e.ticks.reverse?this.min:this.max);l=this.getPointPosition(a,r);t.beginPath();t.moveTo(this.xCenter,this.yCenter);t.lineTo(l.x,l.y);t.stroke()}}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx;const e=this.options;const s=e.ticks;if(!s.display)return;const i=this.getIndexAngle(0);let n,o;t.save();t.translate(this.xCenter,this.yCenter);t.rotate(i);t.textAlign="center";t.textBaseline="middle";this.ticks.forEach(((i,a)=>{if(a===0&&!e.reverse)return;const r=s.setContext(this.getContext(a));const l=j(r.font);n=this.getDistanceFromCenterForValue(this.ticks[a].value);if(r.showLabelBackdrop){t.font=l.string;o=t.measureText(i.label).width;t.fillStyle=r.backdropColor;const e=I(r.backdropPadding);t.fillRect(-o/2-e.left,-n-l.size/2-e.top,o+e.width,l.size+e.height)}et(t,i.label,0,-n,l,{color:r.color,strokeColor:r.textStrokeColor,strokeWidth:r.textStrokeWidth})}));t.restore()}drawTitle(){}}const Oe={millisecond:{common:true,size:1,steps:1e3},second:{common:true,size:1e3,steps:60},minute:{common:true,size:6e4,steps:60},hour:{common:true,size:36e5,steps:24},day:{common:true,size:864e5,steps:30},week:{common:false,size:6048e5,steps:4},month:{common:true,size:2628e6,steps:12},quarter:{common:false,size:7884e6,steps:4},year:{common:true,size:3154e7}};const Ee=Object.keys(Oe);function sorter(t,e){return t-e}function parse(t,e){if(m(e))return null;const s=t._adapter;const{parser:i,round:n,isoWeekday:o}=t._parseOpts;let a=e;typeof i==="function"&&(a=i(a));r(a)||(a=typeof i==="string"?s.parse(a,i):s.parse(a));if(a===null)return null;n&&(a=n!=="week"||!C(o)&&o!==true?s.startOf(a,n):s.startOf(a,"isoWeek",o));return+a}function determineUnitForAutoTicks(t,e,s,i){const n=Ee.length;for(let o=Ee.indexOf(t);o<n-1;++o){const t=Oe[Ee[o]];const n=t.steps?t.steps:Number.MAX_SAFE_INTEGER;if(t.common&&Math.ceil((s-e)/(n*t.size))<=i)return Ee[o]}return Ee[n-1]}function determineUnitForFormatting(t,e,s,i,n){for(let o=Ee.length-1;o>=Ee.indexOf(s);o--){const s=Ee[o];if(Oe[s].common&&t._adapter.diff(n,i,s)>=e-1)return s}return Ee[s?Ee.indexOf(s):0]}function determineMajorUnit(t){for(let e=Ee.indexOf(t)+1,s=Ee.length;e<s;++e)if(Oe[Ee[e]].common)return Ee[e]}function addTick(t,e,s){if(s){if(s.length){const{lo:i,hi:n}=qt(s,e);const o=s[i]>=e?s[i]:s[n];t[o]=true}}else t[e]=true}function setMajorTicks(t,e,s,i){const n=t._adapter;const o=+n.startOf(e[0].value,i);const a=e[e.length-1].value;let r,l;for(r=o;r<=a;r=+n.add(r,1,i)){l=s[r];l>=0&&(e[l].major=true)}return e}function ticksFromTimestamps(t,e,s){const i=[];const n={};const o=e.length;let a,r;for(a=0;a<o;++a){r=e[a];n[r]=a;i.push({value:r,major:false})}return o!==0&&s?setMajorTicks(t,i,n,s):i}class TimeScale extends Scale{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:false,unit:false,round:false,isoWeekday:false,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:false,major:{enabled:false}}};constructor(t){super(t);this._cache={data:[],labels:[],all:[]};this._unit="day";this._majorUnit=void 0;this._offsets={};this._normalized=false;this._parseOpts=void 0}init(t,e={}){const s=t.time||(t.time={});const i=this._adapter=new se._date(t.adapters.date);i.init(e);at(s.displayFormats,i.formats());this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday};super.init(t);this._normalized=e.normalized}parse(t,e){return t===void 0?null:parse(this,t)}beforeLayout(){super.beforeLayout();this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options;const e=this._adapter;const s=t.time.unit||"day";let{min:i,max:n,minDefined:o,maxDefined:a}=this.getUserBounds();function _applyBounds(t){o||isNaN(t.min)||(i=Math.min(i,t.min));a||isNaN(t.max)||(n=Math.max(n,t.max))}if(!o||!a){_applyBounds(this._getLabelBounds());t.bounds==="ticks"&&t.ticks.source==="labels"||_applyBounds(this.getMinMax(false))}i=r(i)&&!isNaN(i)?i:+e.startOf(Date.now(),s);n=r(n)&&!isNaN(n)?n:+e.endOf(Date.now(),s)+1;this.min=Math.min(i,n-1);this.max=Math.max(i+1,n)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY;let s=Number.NEGATIVE_INFINITY;if(t.length){e=t[0];s=t[t.length-1]}return{min:e,max:s}}buildTicks(){const t=this.options;const e=t.time;const s=t.ticks;const i=s.source==="labels"?this.getLabelTimestamps():this._generate();if(t.bounds==="ticks"&&i.length){this.min=this._userMin||i[0];this.max=this._userMax||i[i.length-1]}const n=this.min;const o=this.max;const a=Jt(i,n,o);this._unit=e.unit||(s.autoSkip?determineUnitForAutoTicks(e.minUnit,this.min,this.max,this._getLabelCapacity(n)):determineUnitForFormatting(this,a.length,e.minUnit,this.min,this.max));this._majorUnit=s.major.enabled&&this._unit!=="year"?determineMajorUnit(this._unit):void 0;this.initOffsets(i);t.reverse&&a.reverse();return ticksFromTimestamps(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map((t=>+t.value)))}initOffsets(t=[]){let e=0;let s=0;let i,n;if(this.options.offset&&t.length){i=this.getDecimalForValue(t[0]);e=t.length===1?1-i:(this.getDecimalForValue(t[1])-i)/2;n=this.getDecimalForValue(t[t.length-1]);s=t.length===1?n:(n-this.getDecimalForValue(t[t.length-2]))/2}const o=t.length<3?.5:.25;e=G(e,0,o);s=G(s,0,o);this._offsets={start:e,end:s,factor:1/(e+1+s)}}_generate(){const t=this._adapter;const e=this.min;const s=this.max;const i=this.options;const n=i.time;const o=n.unit||determineUnitForAutoTicks(n.minUnit,e,s,this._getLabelCapacity(e));const a=h(i.ticks.stepSize,1);const r=o==="week"&&n.isoWeekday;const l=C(r)||r===true;const c={};let d=e;let u,g;l&&(d=+t.startOf(d,"isoWeek",r));d=+t.startOf(d,l?"day":o);if(t.diff(s,e,o)>1e5*a)throw new Error(e+" and "+s+" are too far apart with stepSize of "+a+" "+o);const f=i.ticks.source==="data"&&this.getDataTimestamps();for(u=d,g=0;u<s;u=+t.add(u,a,o),g++)addTick(c,u,f);u!==s&&i.bounds!=="ticks"&&g!==1||addTick(c,u,f);return Object.keys(c).sort(sorter).map((t=>+t))}getLabelForValue(t){const e=this._adapter;const s=this.options.time;return s.tooltipFormat?e.format(t,s.tooltipFormat):e.format(t,s.displayFormats.datetime)}format(t,e){const s=this.options;const i=s.time.displayFormats;const n=this._unit;const o=e||i[n];return this._adapter.format(t,o)}_tickFormatFunction(t,e,s,i){const n=this.options;const o=n.ticks.callback;if(o)return X(o,[t,e,s],this);const a=n.time.displayFormats;const r=this._unit;const l=this._majorUnit;const c=r&&a[r];const h=l&&a[l];const d=s[e];const u=l&&h&&d&&d.major;return this._adapter.format(t,i||(u?h:c))}generateTickLabels(t){let e,s,i;for(e=0,s=t.length;e<s;++e){i=t[e];i.label=this._tickFormatFunction(i.value,e,t)}}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets;const s=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+s)*e.factor)}getValueForPixel(t){const e=this._offsets;const s=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+s*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks;const s=this.ctx.measureText(t).width;const i=v(this.isHorizontal()?e.maxRotation:e.minRotation);const n=Math.cos(i);const o=Math.sin(i);const a=this._resolveTickFontOptions(0).size;return{w:s*n+a*o,h:s*o+a*n}}_getLabelCapacity(t){const e=this.options.time;const s=e.displayFormats;const i=s[e.unit]||s.millisecond;const n=this._tickFormatFunction(t,0,ticksFromTimestamps(this,[t],this._majorUnit),i);const o=this._getLabelSize(n);const a=Math.floor(this.isHorizontal()?this.width/o.w:this.height/o.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[];let e,s;if(t.length)return t;const i=this.getMatchingVisibleMetas();if(this._normalized&&i.length)return this._cache.data=i[0].controller.getAllParsedValues(this);for(e=0,s=i.length;e<s;++e)t=t.concat(i[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let e,s;if(t.length)return t;const i=this.getLabels();for(e=0,s=i.length;e<s;++e)t.push(parse(this,i[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return p(t.sort(sorter))}}function interpolate(t,e,s){let i=0;let n=t.length-1;let o,a,r,l;if(s){e>=t[i].pos&&e<=t[n].pos&&({lo:i,hi:n}=L(t,"pos",e));({pos:o,time:r}=t[i]);({pos:a,time:l}=t[n])}else{e>=t[i].time&&e<=t[n].time&&({lo:i,hi:n}=L(t,"time",e));({time:o,pos:r}=t[i]);({time:a,pos:l}=t[n])}const c=a-o;return c?r+(l-r)*(e-o)/c:r}class TimeSeriesScale extends TimeScale{static id="timeseries";static defaults=TimeScale.defaults;constructor(t){super(t);this._table=[];this._minPos=void 0;this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable();const e=this._table=this.buildLookupTable(t);this._minPos=interpolate(e,this.min);this._tableRange=interpolate(e,this.max)-this._minPos;super.initOffsets(t)}buildLookupTable(t){const{min:e,max:s}=this;const i=[];const n=[];let o,a,r,l,c;for(o=0,a=t.length;o<a;++o){l=t[o];l>=e&&l<=s&&i.push(l)}if(i.length<2)return[{time:e,pos:0},{time:s,pos:1}];for(o=0,a=i.length;o<a;++o){c=i[o+1];r=i[o-1];l=i[o];Math.round((c+r)/2)!==l&&n.push({time:l,pos:o/(a-1)})}return n}_generate(){const t=this.min;const e=this.max;let s=super.getDataTimestamps();s.includes(t)&&s.length||s.splice(0,0,t);s.includes(e)&&s.length!==1||s.push(e);return s.sort(((t,e)=>t-e))}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps();const s=this.getLabelTimestamps();t=e.length&&s.length?this.normalize(e.concat(s)):e.length?e:s;t=this._cache.all=t;return t}getDecimalForValue(t){return(interpolate(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets;const s=this.getDecimalForPixel(t)/e.factor-e.end;return interpolate(this._table,s*this._tableRange+this._minPos,true)}}var Ie=Object.freeze({__proto__:null,CategoryScale:CategoryScale,LinearScale:LinearScale,LogarithmicScale:LogarithmicScale,RadialLinearScale:RadialLinearScale,TimeScale:TimeScale,TimeSeriesScale:TimeSeriesScale});const Re=[ee,be,Te,Ie];export{Animation,Animations,ArcElement,BarController,BarElement,BasePlatform,BasicPlatform,BubbleController,CategoryScale,Chart,ve as Colors,DatasetController,ke as Decimation,DomPlatform,DoughnutController,Element,Me as Filler,ie as Interaction,Se as Legend,LineController,LineElement,LinearScale,LogarithmicScale,PieController,PointElement,PolarAreaController,RadarController,RadialLinearScale,Scale,ScatterController,Ce as SubTitle,Xt as Ticks,TimeScale,TimeSeriesScale,we as Title,Le as Tooltip,se as _adapters,_detectPlatform,Zt as animator,ee as controllers,o as defaults,be as elements,oe as layouts,Te as plugins,Re as registerables,de as registry,Ie as scales};

