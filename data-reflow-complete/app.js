// Data Reflow Platform - Multi-Page Application with Color Scheme System (Fixed)

// Application data
const platformData = {
  features: [
    {
      icon: "database",
      title: "Smart Data Mapping",
      description: "AI-powered field mapping with 95% accuracy. Automatically detects relationships and suggests optimal transformations.",
      benefits: ["Reduces setup time by 80%", "Eliminates mapping errors", "Self-learning algorithms"]
    },
    {
      icon: "check-double", 
      title: "Quality Scoring & Monitoring",
      description: "Real-time data quality assessment with automated anomaly detection and cleansing recommendations.",
      benefits: ["Improves accuracy by 40%", "Automated issue detection", "Compliance reporting"]
    },
    {
      icon: "cogs",
      title: "ETL/ELT Processing", 
      description: "Flexible data transformation supporting both ETL and ELT patterns for any scale of operation.",
      benefits: ["Handles billions of records", "Real-time & batch processing", "Custom transformations"]
    },
    {
      icon: "plug",
      title: "600+ Pre-built Connectors",
      description: "Extensive connector library for all major databases, APIs, and cloud services with no-code setup.",
      benefits: ["Instant connectivity", "No coding required", "Regular updates"]
    },
    {
      icon: "chart-line",
      title: "Advanced Analytics",
      description: "Built-in analytics dashboard with predictive insights and custom visualization capabilities.",
      benefits: ["Real-time insights", "Custom dashboards", "Predictive analytics"]
    },
    {
      icon: "shield-alt",
      title: "Enterprise Security",
      description: "Bank-grade security with end-to-end encryption, role-based access, and compliance certifications.",
      benefits: ["End-to-end encryption", "Role-based access", "SOC 2 compliant"]
    }
  ],
  testimonials: [
    {
      quote: "Data Reflow transformed our analytics workflow. We reduced data processing time by 75% and improved decision-making across the organization.",
      author: "Sarah Johnson",
      title: "CTO",
      company: "TechFlow Solutions",
      rating: 5
    },
    {
      quote: "The smart mapping feature saved us months of manual work. ROI was immediate and our team can now focus on insights instead of data preparation.",
      author: "Marcus Chen",
      title: "Data Director",
      company: "RetailNext Analytics",
      rating: 5  
    },
    {
      quote: "HIPAA compliance made easy. The platform handles everything securely while providing the flexibility we need for healthcare analytics.",
      author: "Elena Rodriguez",
      title: "VP of Analytics",
      company: "HealthTech Innovations",
      rating: 5
    }
  ],
  pricing: [
    {
      name: "Starter",
      price: 29,
      features: ["Up to 5 data sources", "Basic ETL/ELT pipelines", "Email support", "1GB data storage", "Standard dashboards"],
      cta: "Start Free Trial"
    },
    {
      name: "Professional", 
      price: 99,
      popular: true,
      features: ["Up to 50 data sources", "Advanced ETL/ELT pipelines", "Priority support", "10GB data storage", "Smart data mapping", "Custom dashboards", "API access"],
      cta: "Start Free Trial"
    },
    {
      name: "Enterprise",
      price: 299,
      features: ["Unlimited data sources", "Custom connectors", "24/7 dedicated support", "100GB data storage", "White-label options", "Advanced security", "SLA guarantees"],
      cta: "Contact Sales"
    }
  ],
  industries: [
    {
      name: "Healthcare",
      icon: "heartbeat",
      description: "HIPAA-compliant data processing for healthcare organizations with real-time patient analytics.",
      pain_points: ["Patient data scattered across systems", "HIPAA compliance requirements", "Real-time monitoring needs", "Integration complexity"],
      solutions: ["HIPAA-compliant data processing", "Unified patient data views", "Real-time health monitoring", "Seamless EHR integration"],
      roi: "40% faster diagnosis times",
      case_study: "MedHealth Corp reduced patient data retrieval time by 65% while maintaining full HIPAA compliance and improving care coordination across 15 facilities."
    },
    {
      name: "Finance", 
      icon: "chart-line",
      description: "Secure financial data integration with automated compliance reporting and risk management.",
      pain_points: ["Regulatory reporting complexity", "Risk assessment challenges", "Market data fragmentation", "Compliance costs"],
      solutions: ["Automated SOX compliance", "Real-time risk monitoring", "Multi-source data integration", "Regulatory report generation"],
      roi: "60% reduction in reporting time", 
      case_study: "FinanceFlow automated 80% of their regulatory reporting processes, reducing compliance costs by $2M annually while improving accuracy."
    },
    {
      name: "Retail",
      icon: "shopping-cart",
      description: "Unified customer and sales data for better insights, inventory optimization, and personalization.",
      pain_points: ["Inventory data silos", "Customer behavior tracking", "Supply chain visibility", "Seasonal demand forecasting"],
      solutions: ["Unified inventory management", "360° customer analytics", "Supply chain optimization", "Predictive demand planning"],
      roi: "35% improvement in inventory turnover",
      case_study: "RetailMax increased customer satisfaction by 50% and reduced inventory costs by 30% with real-time analytics and demand forecasting."
    },
    {
      name: "Manufacturing",
      icon: "cogs",
      description: "IoT and operational data integration for predictive maintenance and quality control.",
      pain_points: ["Production data isolation", "Quality control monitoring", "Equipment maintenance scheduling", "Supply chain disruptions"],
      solutions: ["Integrated production analytics", "Real-time quality monitoring", "Predictive maintenance", "Supply chain visibility"], 
      roi: "25% reduction in downtime",
      case_study: "ManufacturingPro reduced unplanned downtime by 45% and improved product quality by 30% with IoT-driven predictive analytics."
    }
  ],
  statistics: {
    customers: "2,500+",
    dataProcessed: "10TB+", 
    uptime: "99.9%",
    timeReduction: "75%"
  },
  clientLogos: ["Microsoft", "Shopify", "Salesforce", "Stripe", "Asana", "Adobe", "Zoom", "HubSpot", "Netflix", "Spotify"],
  enterpriseFeatures: [
    {
      icon: "shield-alt",
      title: "Advanced Security",
      description: "Enterprise-grade security with field-level encryption, VPC deployment, and comprehensive audit logs."
    },
    {
      icon: "headset", 
      title: "Dedicated Support",
      description: "24/7 dedicated support team with guaranteed response times and technical account management."
    },
    {
      icon: "code",
      title: "Custom Connectors", 
      description: "Build custom connectors for proprietary systems with our SDK and professional services team."
    },
    {
      icon: "server",
      title: "On-Premise Deployment",
      description: "Deploy Data Reflow in your own infrastructure with full control and customization options."
    },
    {
      icon: "chart-bar",
      title: "Advanced Analytics",
      description: "Custom ML models, advanced statistical analysis, and integration with your existing BI tools."
    },
    {
      icon: "users-cog",
      title: "White-glove Onboarding", 
      description: "Comprehensive onboarding program with data migration assistance and team training."
    }
  ],
  complianceAndSecurity: [
    {
      icon: "shield-check",
      title: "SOC 2 Type II",
      description: "Independently audited security controls and processes",
      badge: "Certified"
    },
    {
      icon: "lock",
      title: "GDPR Compliant",
      description: "Full compliance with European data protection regulations",
      badge: "Compliant"
    },
    {
      icon: "certificate", 
      title: "HIPAA Ready",
      description: "Healthcare data protection with business associate agreements",
      badge: "Ready"
    },
    {
      icon: "university",
      title: "SOX Compliant",
      description: "Sarbanes-Oxley compliance for financial institutions",
      badge: "Compliant"
    }
  ],
  successStories: [
    {
      company: "TechFlow Solutions",
      industry: "Technology",
      challenge: "Data scattered across 15+ systems",
      solution: "Unified data platform with real-time processing",
      result: "75% faster insights, $1.2M cost savings",
      icon: "laptop-code"
    },
    {
      company: "HealthCore Medical",
      industry: "Healthcare", 
      challenge: "HIPAA compliance while maintaining performance",
      solution: "Compliant data pipeline with advanced analytics",
      result: "40% faster diagnosis, 100% compliance",
      icon: "hospital"
    },
    {
      company: "RetailMax",
      industry: "Retail",
      challenge: "Inventory optimization across 200+ stores",
      solution: "Real-time inventory analytics and forecasting",
      result: "30% inventory reduction, 50% better availability",
      icon: "store"
    }
  ],
  whiteGloveServices: [
    {
      icon: "rocket",
      title: "Implementation & Migration",
      description: "Complete data migration and platform setup with zero downtime guarantee."
    },
    {
      icon: "graduation-cap",
      title: "Training & Certification",
      description: "Comprehensive training programs for your team with certification options."
    },
    {
      icon: "user-tie",
      title: "Dedicated Success Manager",
      description: "Personal success manager to ensure optimal platform utilization and ROI."
    },
    {
      icon: "tools",
      title: "Custom Development",
      description: "Bespoke connectors and integrations developed by our engineering team."
    }
  ]
};

// Color schemes configuration
const colorSchemes = {
  default: {
    name: "Default Teal",
    primary: "#21808D",
    secondary: "#5E5240",
    accent: "#A84B2F"
  },
  sunset: {
    name: "Sunset Energy",
    primary: "#6B46C1",
    secondary: "#FF6B35", 
    accent: "#FF8A80"
  },
  electric: {
    name: "Electric Modern",
    primary: "#0EA5E9",
    secondary: "#10B981",
    accent: "#FBBF24"
  },
  earthy: {
    name: "Earthy Professional", 
    primary: "#059669",
    secondary: "#DC6803",
    accent: "#D97706"
  },
  royal: {
    name: "Royal Elegance",
    primary: "#1E3A8A",
    secondary: "#EC4899",
    accent: "#6EE7B7"
  },
  vibrant: {
    name: "Vibrant Tech",
    primary: "#DB2777",
    secondary: "#06B6D4", 
    accent: "#84CC16"
  }
};

// Application state
let isDarkMode = false;
let currentColorScheme = 'default';
let currentPage = 'home';
let isMenuOpen = false;
let isAnnualPricing = false;
let currentSolutionTab = 'Healthcare';
let animatedCounters = new Set();
let observers = [];
let isColorSchemeDropdownOpen = false;

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
  initializeTheme();
  initializeColorScheme();
  initializeRouting();
  setupEventListeners();
  populateAllContent();
  setupScrollAnimations();
  setupIntersectionObserver();
  showCurrentPage();
});

// Theme and Color Scheme Management
function initializeTheme() {
  const savedTheme = localStorage.getItem('dataReflow-theme');
  const systemDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
  
  isDarkMode = savedTheme ? savedTheme === 'dark' : systemDarkMode;
  updateTheme();
  setupNavbarScroll();
}

function initializeColorScheme() {
  const savedScheme = localStorage.getItem('dataReflow-color-scheme') || 'default';
  currentColorScheme = savedScheme;
  applyColorScheme(savedScheme, false);
  updateColorSchemeSelector();
}

function updateTheme() {
  const themeToggle = document.getElementById('themeToggle');
  if (!themeToggle) return;
  
  const icon = themeToggle.querySelector('i');
  const rootEl = document.documentElement;
  
  if (isDarkMode) {
    rootEl.setAttribute('data-theme', 'dark');
    if (icon) icon.className = 'fas fa-sun';
  } else {
    rootEl.setAttribute('data-theme', 'light');
    if (icon) icon.className = 'fas fa-moon';
  }
}

function applyColorScheme(scheme, animate = true) {
  const rootEl = document.documentElement;
  
  if (animate) {
    rootEl.classList.add('scheme-switching');
  }
  
  rootEl.setAttribute('data-color-scheme', scheme);
  currentColorScheme = scheme;
  
  if (animate) {
    setTimeout(() => {
      rootEl.classList.remove('scheme-switching');
    }, 500);
  }
  
  localStorage.setItem('dataReflow-color-scheme', scheme);
}

function updateColorSchemeSelector() {
  const options = document.querySelectorAll('.color-scheme-option');
  options.forEach(option => {
    const scheme = option.dataset.scheme;
    option.classList.toggle('active', scheme === currentColorScheme);
  });
}

function toggleTheme() {
  isDarkMode = !isDarkMode;
  updateTheme();
  localStorage.setItem('dataReflow-theme', isDarkMode ? 'dark' : 'light');
  showNotification(`Switched to ${isDarkMode ? 'dark' : 'light'} mode`, 'success');
}

// Routing System
function initializeRouting() {
  // Handle browser back/forward
  window.addEventListener('popstate', function(e) {
    const page = e.state ? e.state.page : 'home';
    navigateToPage(page, false);
  });
  
  // Set initial state
  history.replaceState({ page: currentPage }, '', '#' + currentPage);
}

function navigateToPage(pageName, addToHistory = true) {
  console.log('Navigating to page:', pageName);
  
  // Hide current page
  const currentPageEl = document.querySelector('.page.active');
  if (currentPageEl) {
    currentPageEl.classList.remove('active');
  }
  
  // Show new page
  const newPageEl = document.getElementById(pageName + '-page');
  if (newPageEl) {
    newPageEl.classList.add('active');
    currentPage = pageName;
    
    // Update navigation
    updateNavigation();
    
    // Add to browser history
    if (addToHistory) {
      history.pushState({ page: pageName }, '', '#' + pageName);
    }
    
    // Scroll to top
    window.scrollTo(0, 0);
    
    // Populate page-specific content if needed
    populatePageContent(pageName);
    
    // Re-run animations
    setupScrollAnimations();
    setupIntersectionObserver();
    
    // Close mobile menu if open
    if (isMenuOpen) {
      closeMobileMenu();
    }
    
    showNotification(`📄 Navigated to ${pageName} page`, 'info');
  } else {
    console.error('Page element not found:', pageName + '-page');
  }
}

function updateNavigation() {
  const navLinks = document.querySelectorAll('.nav-link[data-page]');
  navLinks.forEach(link => {
    const page = link.dataset.page;
    link.classList.toggle('active', page === currentPage);
  });
}

function showCurrentPage() {
  // Check if there's a hash in URL
  const hash = window.location.hash.replace('#', '');
  const validPages = ['home', 'pricing', 'solutions', 'enterprise'];
  
  if (hash && validPages.includes(hash)) {
    navigateToPage(hash, false);
  } else {
    navigateToPage('home', false);
  }
}

// Event Listeners Setup
function setupEventListeners() {
  // Theme toggle
  const themeToggle = document.getElementById('themeToggle');
  if (themeToggle) {
    themeToggle.addEventListener('click', toggleTheme);
  }

  // Color scheme selector
  const colorSchemeToggle = document.getElementById('colorSchemeToggle');
  
  if (colorSchemeToggle) {
    colorSchemeToggle.addEventListener('click', function(e) {
      e.stopPropagation();
      toggleColorSchemeDropdown();
    });
  }
  
  // Color scheme options
  const colorSchemeOptions = document.querySelectorAll('.color-scheme-option');
  colorSchemeOptions.forEach(option => {
    option.addEventListener('click', function() {
      const scheme = this.dataset.scheme;
      applyColorScheme(scheme);
      updateColorSchemeSelector();
      closeColorSchemeDropdown();
      showNotification(`✨ Switched to ${colorSchemes[scheme].name} theme`, 'success');
    });
  });
  
  // Close dropdown when clicking outside
  document.addEventListener('click', function(e) {
    if (!e.target.closest('.color-scheme-selector')) {
      closeColorSchemeDropdown();
    }
  });

  // Mobile menu toggle
  const menuToggle = document.getElementById('menuToggle');
  const navMenu = document.getElementById('navMenu');
  
  if (menuToggle && navMenu) {
    menuToggle.addEventListener('click', function() {
      isMenuOpen = !isMenuOpen;
      navMenu.classList.toggle('active', isMenuOpen);
      menuToggle.classList.toggle('active', isMenuOpen);
      document.body.style.overflow = isMenuOpen ? 'hidden' : '';
    });
  }

  // Navigation handling - FIXED
  document.addEventListener('click', function(e) {
    // Handle page navigation
    const target = e.target;
    const pageElement = target.closest('[data-page]');
    
    if (pageElement) {
      e.preventDefault();
      const page = pageElement.getAttribute('data-page');
      console.log('Navigation clicked, page:', page);
      if (page && ['home', 'pricing', 'solutions', 'enterprise'].includes(page)) {
        navigateToPage(page);
      }
      return;
    }
    
    // Handle other CTA buttons
    handleCTAButtons(e);
  });

  // Modal functionality
  setupModalHandlers();

  // ROI Calculator
  setupROICalculator();

  // Pricing toggle
  setupPricingToggle();
}

function toggleColorSchemeDropdown() {
  const dropdown = document.getElementById('colorSchemeDropdown');
  if (dropdown) {
    isColorSchemeDropdownOpen = !isColorSchemeDropdownOpen;
    dropdown.classList.toggle('active', isColorSchemeDropdownOpen);
  }
}

function closeColorSchemeDropdown() {
  const dropdown = document.getElementById('colorSchemeDropdown');
  if (dropdown) {
    isColorSchemeDropdownOpen = false;
    dropdown.classList.remove('active');
  }
}

function closeMobileMenu() {
  const navMenu = document.getElementById('navMenu');
  const menuToggle = document.getElementById('menuToggle');
  
  if (navMenu) navMenu.classList.remove('active');
  if (menuToggle) menuToggle.classList.remove('active');
  document.body.style.overflow = '';
  isMenuOpen = false;
}

function handleCTAButtons(e) {
  const target = e.target.closest('button') || e.target;
  const text = target.textContent || '';
  
  if (text.includes('Start Free Trial') || target.id === 'startTrialBtn') {
    e.preventDefault();
    showNotification('🚀 Redirecting to free trial signup...', 'success');
    // In real app: window.open('https://signup.datareflow.com', '_blank');
  }
  
  if (text.includes('Watch Demo') || target.id === 'watchDemoBtn') {
    e.preventDefault();
    showDemoModal();
  }
  
  if (text.includes('Contact Sales') || text.includes('Schedule Consultation') || text.includes('Schedule Demo')) {
    e.preventDefault();
    showNotification('📅 Opening calendar booking...', 'info');
    // In real app: window.open('https://calendar.datareflow.com', '_blank');
  }
  
  if (text.includes('Calculate Savings') || target.id === 'calculateROI') {
    e.preventDefault();
    calculateROI();
  }
}

function setupModalHandlers() {
  const modal = document.getElementById('demoModal');
  const modalClose = document.getElementById('modalClose');
  const modalOverlay = document.getElementById('modalOverlay');
  
  if (modalClose) {
    modalClose.addEventListener('click', closeDemoModal);
  }
  
  if (modalOverlay) {
    modalOverlay.addEventListener('click', closeDemoModal);
  }
  
  // Close modal on escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      if (modal && modal.classList.contains('active')) {
        closeDemoModal();
      }
      if (isColorSchemeDropdownOpen) {
        closeColorSchemeDropdown();
      }
    }
  });
}

function setupROICalculator() {
  const calculateBtn = document.getElementById('calculateROI');
  if (calculateBtn) {
    calculateBtn.addEventListener('click', calculateROI);
  }
}

function setupPricingToggle() {
  const pricingToggle = document.getElementById('pricingToggle');
  if (pricingToggle) {
    pricingToggle.addEventListener('click', function() {
      isAnnualPricing = !isAnnualPricing;
      pricingToggle.classList.toggle('active', isAnnualPricing);
      updatePricingDisplay();
      showNotification(`💰 Switched to ${isAnnualPricing ? 'annual' : 'monthly'} pricing`, 'success');
    });
  }
}

function setupNavbarScroll() {
  const navbar = document.getElementById('navbar');
  if (!navbar) return;
  
  let lastScrollY = window.scrollY;
  
  window.addEventListener('scroll', function() {
    const currentScrollY = window.scrollY;
    
    navbar.classList.toggle('scrolled', currentScrollY > 50);
    
    // Hide/show navbar on scroll
    if (currentScrollY > lastScrollY && currentScrollY > 100) {
      navbar.style.transform = 'translateY(-100%)';
    } else {
      navbar.style.transform = 'translateY(0)';
    }
    
    lastScrollY = currentScrollY;
  });
}

// Content Population
function populateAllContent() {
  populateClientLogos();
  populateFeatures();
  populateTestimonials();
  // Initialize pricing for first time
  populatePricing();
}

function populatePageContent(pageName) {
  switch(pageName) {
    case 'pricing':
      if (!document.querySelector('#pricingGrid .pricing-card')) {
        populatePricing();
      }
      populateFeatureComparison();
      break;
    case 'solutions':
      populateSolutions();
      populateCompliance();
      populateSuccessStories();
      break;
    case 'enterprise':
      populateEnterpriseFeatures();
      populateArchitectureDiagram();
      populateWhiteGloveServices();
      break;
  }
}

function populateClientLogos() {
  const logosGrid = document.getElementById('clientLogos');
  if (!logosGrid) return;

  logosGrid.innerHTML = '';
  
  platformData.clientLogos.forEach((logo, index) => {
    const logoElement = document.createElement('div');
    logoElement.className = 'client-logo';
    logoElement.style.animationDelay = `${index * 0.1}s`;
    logoElement.textContent = logo;
    logosGrid.appendChild(logoElement);
  });
}

function populateFeatures() {
  const featuresGrid = document.getElementById('featuresGrid');
  if (!featuresGrid) return;

  featuresGrid.innerHTML = '';
  
  platformData.features.forEach((feature, index) => {
    const featureCard = createFeatureCard(feature, index);
    featuresGrid.appendChild(featureCard);
  });
}

function createFeatureCard(feature, index) {
  const card = document.createElement('div');
  card.className = 'feature-card fade-in';
  card.style.animationDelay = `${index * 0.1}s`;
  
  card.innerHTML = `
    <div class="feature-icon">
      <i class="fas fa-${feature.icon}"></i>
    </div>
    <h3 class="feature-title">${feature.title}</h3>
    <p class="feature-description">${feature.description}</p>
    <ul class="feature-benefits">
      ${feature.benefits.map(benefit => `<li>${benefit}</li>`).join('')}
    </ul>
  `;
  
  return card;
}

function populateTestimonials() {
  const testimonialsCarousel = document.getElementById('testimonialsCarousel');
  if (!testimonialsCarousel) return;

  testimonialsCarousel.innerHTML = '';
  
  platformData.testimonials.forEach((testimonial, index) => {
    const testimonialCard = createTestimonialCard(testimonial, index);
    testimonialsCarousel.appendChild(testimonialCard);
  });
}

function createTestimonialCard(testimonial, index) {
  const card = document.createElement('div');
  card.className = 'testimonial-card fade-in';
  card.style.animationDelay = `${index * 0.1}s`;
  
  // Generate initials for avatar fallback
  const initials = testimonial.author.split(' ').map(n => n[0]).join('');
  
  card.innerHTML = `
    <div class="testimonial-quote">"${testimonial.quote}"</div>
    <div class="testimonial-author">
      <div class="author-avatar" style="background: linear-gradient(135deg, var(--color-primary), var(--scheme-secondary)); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 18px;">
        ${initials}
      </div>
      <div class="author-info">
        <div class="author-name">${testimonial.author}</div>
        <div class="author-title">${testimonial.title}, ${testimonial.company}</div>
        <div class="testimonial-rating">
          ${Array(testimonial.rating).fill().map(() => '<i class="fas fa-star star"></i>').join('')}
        </div>
      </div>
    </div>
  `;
  
  return card;
}

// Pricing Page Content - FIXED
function populatePricing() {
  const pricingGrid = document.getElementById('pricingGrid');
  if (!pricingGrid) return;

  pricingGrid.innerHTML = '';
  
  platformData.pricing.forEach((tier, index) => {
    const pricingCard = createPricingCard(tier, index);
    pricingGrid.appendChild(pricingCard);
  });
}

function createPricingCard(tier, index) {
  const card = document.createElement('div');
  card.className = `pricing-card fade-in ${tier.popular ? 'popular' : ''}`;
  card.style.animationDelay = `${index * 0.1}s`;
  
  let price, period;
  
  if (typeof tier.price === 'number') {
    const displayPrice = isAnnualPricing ? Math.round(tier.price * 0.8) : tier.price;
    price = `$${displayPrice}`;
    period = `per ${isAnnualPricing ? 'month (billed annually)' : 'month'}`;
  } else {
    price = `$${tier.price}`;
    period = 'contact for pricing';
  }
  
  card.innerHTML = `
    <div class="pricing-header">
      <h3 class="pricing-name">${tier.name}</h3>
      <div class="pricing-price">${price}</div>
      <div class="pricing-period">${period}</div>
    </div>
    <ul class="pricing-features">
      ${tier.features.map(feature => `<li>${feature}</li>`).join('')}
    </ul>
    <button class="btn ${tier.popular ? 'btn--primary' : 'btn--outline'} btn--full-width">
      ${tier.cta}
    </button>
  `;
  
  return card;
}

function updatePricingDisplay() {
  // Clear and repopulate pricing to ensure it works correctly
  populatePricing();
}

function populateFeatureComparison() {
  const comparisonTable = document.getElementById('comparisonTable');
  if (!comparisonTable) return;

  comparisonTable.innerHTML = `
    <div class="comparison-placeholder">
      <h3>Feature Comparison Table</h3>
      <p>Detailed side-by-side comparison of all features across plans</p>
      <div class="comparison-grid" style="display: grid; grid-template-columns: 2fr 1fr 1fr 1fr; gap: var(--space-16); margin-top: var(--space-24);">
        <div style="font-weight: var(--font-weight-semibold); color: var(--color-text);">Features</div>
        <div style="font-weight: var(--font-weight-semibold); color: var(--color-text); text-align: center;">Starter</div>
        <div style="font-weight: var(--font-weight-semibold); color: var(--color-text); text-align: center;">Professional</div>
        <div style="font-weight: var(--font-weight-semibold); color: var(--color-text); text-align: center;">Enterprise</div>
        <div style="color: var(--color-text-secondary); padding: var(--space-8) 0;">Data Sources</div>
        <div style="text-align: center; color: var(--color-text-secondary);">5</div>
        <div style="text-align: center; color: var(--color-text-secondary);">50</div>
        <div style="text-align: center; color: var(--color-text-secondary);">Unlimited</div>
        <div style="color: var(--color-text-secondary); padding: var(--space-8) 0;">Smart Mapping</div>
        <div style="text-align: center; color: var(--color-text-secondary);">❌</div>
        <div style="text-align: center; color: var(--color-success);">✅</div>
        <div style="text-align: center; color: var(--color-success);">✅</div>
        <div style="color: var(--color-text-secondary); padding: var(--space-8) 0;">Custom Connectors</div>
        <div style="text-align: center; color: var(--color-text-secondary);">❌</div>
        <div style="text-align: center; color: var(--color-text-secondary);">❌</div>
        <div style="text-align: center; color: var(--color-success);">✅</div>
        <div style="color: var(--color-text-secondary); padding: var(--space-8) 0;">24/7 Support</div>
        <div style="text-align: center; color: var(--color-text-secondary);">❌</div>
        <div style="text-align: center; color: var(--color-text-secondary);">❌</div>
        <div style="text-align: center; color: var(--color-success);">✅</div>
      </div>
    </div>
  `;
}

// Solutions Page Content
function populateSolutions() {
  const tabsNav = document.getElementById('solutionsTabsNav');
  const tabsContent = document.getElementById('solutionsTabsContent');
  
  if (!tabsNav || !tabsContent) return;

  // Create tab navigation
  tabsNav.innerHTML = '';
  platformData.industries.forEach((industry, index) => {
    const tabBtn = document.createElement('button');
    tabBtn.className = `tab-btn ${index === 0 ? 'active' : ''}`;
    tabBtn.textContent = industry.name;
    tabBtn.addEventListener('click', () => switchSolutionTab(industry.name));
    tabsNav.appendChild(tabBtn);
  });

  // Create tab content
  tabsContent.innerHTML = '';
  platformData.industries.forEach((industry, index) => {
    const tabContent = document.createElement('div');
    tabContent.className = `tab-content ${index === 0 ? 'active' : ''}`;
    tabContent.id = `tab-${industry.name.toLowerCase()}`;
    
    tabContent.innerHTML = `
      <div class="solution-content">
        <div class="solution-info">
          <h3><i class="fas fa-${industry.icon}"></i> ${industry.name} Solutions</h3>
          <p style="color: var(--color-text-secondary); margin-bottom: var(--space-24); line-height: 1.6;">${industry.description}</p>
          <div class="pain-points">
            <h4>Common Challenges</h4>
            <ul>
              ${industry.pain_points.map(point => `<li>${point}</li>`).join('')}
            </ul>
          </div>
          <div class="solution-benefits">
            <h4>Our Solutions</h4>
            <ul>
              ${industry.solutions.map(solution => `<li>${solution}</li>`).join('')}
            </ul>
          </div>
          <div class="roi-highlight">
            <div class="roi-value">${industry.roi}</div>
            <p>Average improvement with Data Reflow</p>
          </div>
        </div>
        <div class="solution-visual">
          <div class="case-study-card">
            <h4>Success Story</h4>
            <p>${industry.case_study}</p>
            <button class="btn btn--primary btn--sm">View Full Case Study</button>
          </div>
        </div>
      </div>
    `;
    
    tabsContent.appendChild(tabContent);
  });
}

function switchSolutionTab(industry) {
  // Update tab buttons
  document.querySelectorAll('.tab-btn').forEach(btn => {
    btn.classList.toggle('active', btn.textContent === industry);
  });
  
  // Update tab content
  document.querySelectorAll('.tab-content').forEach(content => {
    content.classList.toggle('active', content.id === `tab-${industry.toLowerCase()}`);
  });
  
  currentSolutionTab = industry;
  showNotification(`📊 Switched to ${industry} solutions`, 'info');
}

function populateCompliance() {
  const complianceGrid = document.getElementById('complianceGrid');
  if (!complianceGrid) return;

  complianceGrid.innerHTML = '';
  
  platformData.complianceAndSecurity.forEach((item, index) => {
    const complianceCard = document.createElement('div');
    complianceCard.className = 'compliance-card fade-in';
    complianceCard.style.animationDelay = `${index * 0.1}s`;
    
    complianceCard.innerHTML = `
      <div class="compliance-icon">
        <i class="fas fa-${item.icon}"></i>
      </div>
      <h4 class="compliance-title">${item.title}</h4>
      <p class="compliance-description">${item.description}</p>
      <div class="compliance-badge" style="background: rgba(var(--color-success-rgb), 0.1); color: var(--color-success); padding: var(--space-4) var(--space-12); border-radius: var(--radius-full); font-size: var(--font-size-sm); font-weight: var(--font-weight-semibold); margin-top: var(--space-12); display: inline-block;">
        ${item.badge}
      </div>
    `;
    
    complianceGrid.appendChild(complianceCard);
  });
}

function populateSuccessStories() {
  const storiesGrid = document.getElementById('storiesGrid');
  if (!storiesGrid) return;

  storiesGrid.innerHTML = '';
  
  platformData.successStories.forEach((story, index) => {
    const storyCard = document.createElement('div');
    storyCard.className = 'story-card fade-in';
    storyCard.style.animationDelay = `${index * 0.1}s`;
    
    storyCard.innerHTML = `
      <div class="story-icon" style="width: 60px; height: 60px; margin: 0 auto var(--space-16); background: linear-gradient(135deg, var(--color-primary), var(--scheme-secondary)); border-radius: var(--radius-base); display: flex; align-items: center; justify-content: center; color: var(--color-white); font-size: var(--font-size-xl);">
        <i class="fas fa-${story.icon}"></i>
      </div>
      <h4 style="text-align: center; margin-bottom: var(--space-8);">${story.company}</h4>
      <p style="text-align: center; color: var(--color-text-secondary); font-size: var(--font-size-sm); margin-bottom: var(--space-16);">${story.industry}</p>
      <div style="margin-bottom: var(--space-12);">
        <strong style="color: var(--color-text); font-size: var(--font-size-sm);">Challenge:</strong>
        <p style="color: var(--color-text-secondary); font-size: var(--font-size-sm); margin: var(--space-4) 0;">${story.challenge}</p>
      </div>
      <div style="margin-bottom: var(--space-12);">
        <strong style="color: var(--color-text); font-size: var(--font-size-sm);">Solution:</strong>
        <p style="color: var(--color-text-secondary); font-size: var(--font-size-sm); margin: var(--space-4) 0;">${story.solution}</p>
      </div>
      <div style="background: rgba(var(--color-success-rgb), 0.1); border: 1px solid rgba(var(--color-success-rgb), 0.2); border-radius: var(--radius-base); padding: var(--space-12); text-align: center;">
        <strong style="color: var(--color-success); font-size: var(--font-size-sm);">Result:</strong>
        <p style="color: var(--color-success); font-size: var(--font-size-sm); margin: var(--space-4) 0; font-weight: var(--font-weight-semibold);">${story.result}</p>
      </div>
    `;
    
    storiesGrid.appendChild(storyCard);
  });
}

// Enterprise Page Content
function populateEnterpriseFeatures() {
  const enterpriseFeaturesGrid = document.getElementById('enterpriseFeaturesGrid');
  if (!enterpriseFeaturesGrid) return;

  enterpriseFeaturesGrid.innerHTML = '';
  
  platformData.enterpriseFeatures.forEach((feature, index) => {
    const featureCard = document.createElement('div');
    featureCard.className = 'enterprise-feature-card fade-in';
    featureCard.style.animationDelay = `${index * 0.1}s`;
    
    featureCard.innerHTML = `
      <div class="enterprise-feature-icon">
        <i class="fas fa-${feature.icon}"></i>
      </div>
      <h4 class="enterprise-feature-title">${feature.title}</h4>
      <p class="enterprise-feature-description">${feature.description}</p>
    `;
    
    enterpriseFeaturesGrid.appendChild(featureCard);
  });
}

function populateArchitectureDiagram() {
  const architectureDiagram = document.getElementById('architectureDiagram');
  if (!architectureDiagram) return;

  architectureDiagram.innerHTML = `
    <div class="architecture-placeholder">
      <i class="fas fa-network-wired"></i>
      <h3>Enterprise Architecture</h3>
      <p>Scalable, secure, and compliant infrastructure designed for enterprise needs</p>
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--space-24); margin-top: var(--space-32); text-align: left;">
        <div style="background: rgba(var(--color-primary-rgb), 0.1); border: 1px solid rgba(var(--color-primary-rgb), 0.2); border-radius: var(--radius-base); padding: var(--space-20);">
          <h4 style="color: var(--color-primary); margin-bottom: var(--space-12);">Data Layer</h4>
          <p style="color: var(--color-text-secondary); font-size: var(--font-size-sm); margin: 0;">Distributed storage with automatic scaling and redundancy</p>
        </div>
        <div style="background: rgba(var(--scheme-secondary-rgb), 0.1); border: 1px solid rgba(var(--scheme-secondary-rgb), 0.2); border-radius: var(--radius-base); padding: var(--space-20);">
          <h4 style="color: var(--scheme-secondary); margin-bottom: var(--space-12);">Processing Layer</h4>
          <p style="color: var(--color-text-secondary); font-size: var(--font-size-sm); margin: 0;">Real-time and batch processing with auto-scaling capabilities</p>
        </div>
        <div style="background: rgba(var(--scheme-accent-rgb), 0.1); border: 1px solid rgba(var(--scheme-accent-rgb), 0.2); border-radius: var(--radius-base); padding: var(--space-20);">
          <h4 style="color: var(--scheme-accent); margin-bottom: var(--space-12);">Security Layer</h4>
          <p style="color: var(--color-text-secondary); font-size: var(--font-size-sm); margin: 0;">End-to-end encryption with role-based access controls</p>
        </div>
      </div>
    </div>
  `;
}

function populateWhiteGloveServices() {
  const servicesGrid = document.getElementById('servicesGrid');
  if (!servicesGrid) return;

  servicesGrid.innerHTML = '';
  
  platformData.whiteGloveServices.forEach((service, index) => {
    const serviceCard = document.createElement('div');
    serviceCard.className = 'service-card fade-in';
    serviceCard.style.animationDelay = `${index * 0.1}s`;
    
    serviceCard.innerHTML = `
      <div class="service-icon">
        <i class="fas fa-${service.icon}"></i>
      </div>
      <h4 class="service-title">${service.title}</h4>
      <p class="service-description">${service.description}</p>
    `;
    
    servicesGrid.appendChild(serviceCard);
  });
}

// ROI Calculator - FIXED
function calculateROI() {
  const currentCosts = parseFloat(document.getElementById('currentCosts')?.value) || 10000;
  const manualHours = parseFloat(document.getElementById('manualHours')?.value) || 40;
  const hourlyRate = parseFloat(document.getElementById('hourlyRate')?.value) || 75;
  
  // Calculate savings
  const laborCosts = (manualHours * hourlyRate * 52) / 12; // Monthly labor costs
  const timeSavings = laborCosts * 0.8; // 80% time savings
  const processingSavings = currentCosts * 0.4; // 40% processing cost reduction
  const monthlySavings = timeSavings + processingSavings;
  const annualSavings = monthlySavings * 12;
  
  // Assume average annual cost of our platform is $15,000
  const platformCost = 15000;
  const roi = ((annualSavings - platformCost) / platformCost) * 100;
  
  // Update results
  const monthlySavingsEl = document.getElementById('monthlySavings');
  const annualSavingsEl = document.getElementById('annualSavings');
  const roiPercentageEl = document.getElementById('roiPercentage');
  
  if (monthlySavingsEl) monthlySavingsEl.textContent = `$${Math.round(monthlySavings).toLocaleString()}`;
  if (annualSavingsEl) annualSavingsEl.textContent = `$${Math.round(annualSavings).toLocaleString()}`;
  if (roiPercentageEl) roiPercentageEl.textContent = `${Math.round(roi)}%`;
  
  // Animate results
  animateResults();
  showNotification('💰 ROI calculation complete!', 'success');
}

function animateResults() {
  const resultCards = document.querySelectorAll('.result-card');
  resultCards.forEach((card, index) => {
    card.style.animation = 'none';
    setTimeout(() => {
      card.style.animation = `slideInUp 0.5s ease-out ${index * 0.1}s both`;
    }, 10);
  });
}

// Animations and Observers
function setupScrollAnimations() {
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  // Clear existing observers
  observers.forEach(observer => observer.disconnect());
  observers = [];

  const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
      }
    });
  }, observerOptions);

  const fadeElements = document.querySelectorAll('.fade-in');
  fadeElements.forEach(element => {
    observer.observe(element);
  });
  
  observers.push(observer);
}

function setupIntersectionObserver() {
  // Setup counter animations for statistics
  const statNumbers = document.querySelectorAll('.stat-number');
  
  const statsObserver = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const statEl = entry.target;
        if (!animatedCounters.has(statEl)) {
          animateCounter(statEl);
          animatedCounters.add(statEl);
        }
      }
    });
  }, { threshold: 0.5 });

  statNumbers.forEach(stat => {
    statsObserver.observe(stat);
  });
  
  observers.push(statsObserver);
}

function animateCounter(element) {
  if (!element || element.classList.contains('animated')) return;
  
  element.classList.add('animated');
  const targetValue = element.dataset.target;
  let target, suffix = '';
  
  if (targetValue === '2500' || targetValue === '500') {
    target = parseInt(targetValue);
    suffix = '+';
  } else if (targetValue === '10' || targetValue === '1') {
    target = parseInt(targetValue);
    suffix = targetValue === '10' ? 'TB+' : 'PB+';
  } else if (targetValue === '80' || targetValue === '24') {
    target = parseInt(targetValue); 
    suffix = targetValue === '80' ? '%' : '/7';
  } else if (targetValue === '99.9') {
    target = 99.9;
    suffix = '%';
  } else {
    target = parseFloat(targetValue);
  }
  
  let start = 0;
  let increment = target / 60;
  let current = start;
  
  const timer = setInterval(function() {
    current += increment;
    
    if (current >= target) {
      current = target;
      clearInterval(timer);
    }
    
    let displayValue;
    if (suffix === '%' && target % 1 !== 0) {
      displayValue = current.toFixed(1);
    } else if (target >= 1000) {
      displayValue = Math.floor(current).toLocaleString();
    } else {
      displayValue = Math.floor(current);
    }
    
    element.textContent = displayValue + suffix;
  }, 25);
}

// Modal Functions
function showDemoModal() {
  const modal = document.getElementById('demoModal');
  if (modal) {
    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
    showNotification('🎬 Demo modal opened!', 'success');
  }
}

function closeDemoModal() {
  const modal = document.getElementById('demoModal');
  if (modal) {
    modal.classList.remove('active');
    document.body.style.overflow = '';
  }
}

// Notification System
function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  
  const colors = {
    success: 'var(--color-success)',
    error: 'var(--color-error)', 
    warning: 'var(--color-warning)',
    info: 'var(--color-primary)'
  };
  
  notification.style.cssText = `
    position: fixed;
    top: 100px;
    right: 20px;
    padding: var(--space-12) var(--space-20);
    background: ${colors[type]};
    color: var(--color-white);
    border-radius: var(--radius-base);
    box-shadow: var(--shadow-lg);
    z-index: 2001;
    font-weight: var(--font-weight-medium);
    max-width: 350px;
    font-size: var(--font-size-sm);
    animation: slideInRight 0.3s ease-out;
    backdrop-filter: blur(10px);
  `;
  
  notification.textContent = message;
  document.body.appendChild(notification);
  
  setTimeout(() => {
    notification.style.animation = 'slideOutRight 0.3s ease-in';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 300);
  }, 4000);
}

// Add notification animations CSS
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
  @keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }
  
  @keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
  }
`;
document.head.appendChild(notificationStyles);

// Handle window resize
let resizeTimeout;
window.addEventListener('resize', function() {
  clearTimeout(resizeTimeout);
  resizeTimeout = setTimeout(function() {
    if (window.innerWidth > 768 && isMenuOpen) {
      closeMobileMenu();
    }
  }, 250);
});

// Cleanup observers on page unload
window.addEventListener('beforeunload', function() {
  observers.forEach(observer => observer.disconnect());
});

// Performance monitoring and welcome message
window.addEventListener('load', function() {
  if (window.performance && window.performance.timing) {
    const perfData = window.performance.timing;
    const loadTime = perfData.loadEventEnd - perfData.navigationStart;
    console.log(`Data Reflow Platform loaded in ${loadTime}ms`);
    
    // Show welcome message with current color scheme
    setTimeout(() => {
      const schemeName = colorSchemes[currentColorScheme].name;
      showNotification(`🎨 Welcome! Using ${schemeName} color scheme`, 'info');
    }, 1000);
  }
});

// Enhanced interactivity
document.addEventListener('DOMContentLoaded', function() {
  // Add click tracking for analytics (mock)
  document.addEventListener('click', function(e) {
    const target = e.target.closest('button, .card, .tab-btn, .color-scheme-option, [data-page]');
    if (target) {
      console.log('User interaction:', {
        element: target.className,
        page: currentPage,
        text: target.textContent?.slice(0, 50),
        colorScheme: currentColorScheme,
        darkMode: isDarkMode,
        timestamp: new Date().toISOString()
      });
    }
  });
});