<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DataReflow - Developer-First Data Integration Platform | 350+ APIs & Real-time ETL</title>
  <meta name="description" content="DataReflow offers 350+ pre-built connectors, REST APIs, real-time ETL pipelines, and advanced data engineering tools for technical teams. Built on Rails 8 with enterprise security.">
  <meta name="keywords" content="data integration API, ETL platform, data engineering, REST API, webhooks, real-time data, developer tools">
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://datareflow.com/developers">
  <meta property="og:title" content="DataReflow - Developer-First Data Integration Platform">
  <meta property="og:description" content="350+ APIs, real-time ETL, and enterprise security for technical teams">
  <meta property="og:image" content="https://datareflow.com/images/og-technical.jpg">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:url" content="https://datareflow.com/developers">
  <meta property="twitter:title" content="DataReflow - Developer-First Data Integration Platform">
  <meta property="twitter:description" content="350+ APIs, real-time ETL, and enterprise security for technical teams">
  <meta property="twitter:image" content="https://datareflow.com/images/og-technical.jpg">

  <!-- Structured Data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "DataReflow",
    "applicationCategory": "DeveloperApplication",
    "operatingSystem": "Web",
    "description": "Developer-first data integration platform with 350+ APIs",
    "offers": {
      "@type": "Offer",
      "price": "149",
      "priceCurrency": "USD"
    }
  }
  </script>

  <style>
    :root {
      --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --tech-gradient: linear-gradient(135deg, #00c9ff 0%, #92fe9d 100%);
      --code-gradient: linear-gradient(135deg, #fc00ff 0%, #00dbde 100%);
      --dark-bg: #0f172a;
      --darker-bg: #020617;
      --glass-bg: rgba(255, 255, 255, 0.05);
      --glass-border: rgba(255, 255, 255, 0.1);
      --text-primary: #f8fafc;
      --text-secondary: #cbd5e1;
      --text-accent: #00c9ff;
      --code-bg: #1e293b;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'JetBrains Mono', 'Fira Code', monospace;
      line-height: 1.6;
      color: var(--text-primary);
      background: var(--dark-bg);
      min-height: 100vh;
    }

    .glass-morphism {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 15px;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    /* Header */
    .header {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      background: rgba(15, 23, 42, 0.9);
      backdrop-filter: blur(20px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding: 1rem 0;
    }

    .nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .logo {
      font-size: 1.8rem;
      font-weight: 700;
      color: var(--text-accent);
      text-decoration: none;
      font-family: 'JetBrains Mono', monospace;
    }

    .logo::before {
      content: "$ ";
      color: #92fe9d;
    }

    .nav-links {
      display: flex;
      list-style: none;
      gap: 2rem;
    }

    .nav-links a {
      color: var(--text-secondary);
      text-decoration: none;
      font-weight: 500;
      transition: all 0.3s ease;
      font-family: 'JetBrains Mono', monospace;
    }

    .nav-links a:hover {
      color: var(--text-accent);
      text-shadow: 0 0 10px rgba(0, 201, 255, 0.3);
    }

    .cta-button {
      background: var(--tech-gradient);
      color: var(--dark-bg);
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      text-decoration: none;
      font-weight: 600;
      font-family: 'JetBrains Mono', monospace;
      transition: all 0.3s ease;
      border: none;
      cursor: pointer;
      display: inline-block;
    }

    .cta-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0, 201, 255, 0.3);
    }

    /* Hero Section */
    .hero {
      padding: 8rem 0 6rem;
      background: linear-gradient(135deg, var(--dark-bg) 0%, var(--darker-bg) 100%);
      position: relative;
      overflow: hidden;
    }

    .hero::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23334155" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
      opacity: 0.3;
    }

    .hero-content {
      position: relative;
      z-index: 1;
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 4rem;
      align-items: center;
    }

    .hero-text h1 {
      font-size: 3.5rem;
      font-weight: 800;
      line-height: 1.1;
      margin-bottom: 1.5rem;
      background: var(--tech-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-family: 'JetBrains Mono', monospace;
    }

    .hero-text .subtitle {
      font-size: 1.2rem;
      color: var(--text-secondary);
      margin-bottom: 2rem;
      line-height: 1.7;
    }

    .code-block {
      background: var(--code-bg);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 1.5rem;
      font-family: 'JetBrains Mono', monospace;
      font-size: 0.9rem;
      color: var(--text-primary);
      position: relative;
      overflow: hidden;
    }

    .code-block::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: var(--tech-gradient);
    }

    .code-line {
      margin-bottom: 0.5rem;
    }

    .code-comment {
      color: #64748b;
    }

    .code-keyword {
      color: #00c9ff;
    }

    .code-string {
      color: #92fe9d;
    }

    .code-number {
      color: #fbbf24;
    }

    /* Tech Stats */
    .tech-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin: 4rem 0;
    }

    .tech-stat {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 15px;
      padding: 2rem;
      text-align: center;
      transition: all 0.3s ease;
    }

    .tech-stat:hover {
      transform: translateY(-5px);
      border-color: var(--text-accent);
    }

    .tech-stat-number {
      font-size: 2.5rem;
      font-weight: 800;
      color: var(--text-accent);
      font-family: 'JetBrains Mono', monospace;
    }

    .tech-stat-label {
      color: var(--text-secondary);
      font-size: 0.9rem;
      margin-top: 0.5rem;
    }

    /* Features Section */
    .features {
      padding: 6rem 0;
      background: var(--darker-bg);
    }

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      text-align: center;
      margin-bottom: 1rem;
      color: var(--text-primary);
      font-family: 'JetBrains Mono', monospace;
    }

    .section-subtitle {
      font-size: 1.2rem;
      color: var(--text-secondary);
      text-align: center;
      max-width: 600px;
      margin: 0 auto 4rem;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;
    }

    .feature-card {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 15px;
      padding: 2.5rem;
      transition: all 0.3s ease;
    }

    .feature-card:hover {
      transform: translateY(-5px);
      border-color: var(--text-accent);
      box-shadow: 0 20px 40px rgba(0, 201, 255, 0.1);
    }

    .feature-icon {
      width: 60px;
      height: 60px;
      background: var(--tech-gradient);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1.5rem;
      font-size: 1.5rem;
      color: var(--dark-bg);
    }

    .feature-title {
      font-size: 1.4rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--text-primary);
      font-family: 'JetBrains Mono', monospace;
    }

    .feature-description {
      color: var(--text-secondary);
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    .feature-tech-list {
      list-style: none;
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    .tech-badge {
      background: rgba(0, 201, 255, 0.1);
      color: var(--text-accent);
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      font-size: 0.8rem;
      font-family: 'JetBrains Mono', monospace;
      border: 1px solid rgba(0, 201, 255, 0.2);
    }

    /* Integrations Showcase */
    .integrations {
      padding: 6rem 0;
      background: var(--dark-bg);
    }

    .integrations-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 1.5rem;
      margin: 3rem 0;
    }

    .integration-item {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 12px;
      padding: 1.5rem;
      text-align: center;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .integration-item:hover {
      transform: translateY(-3px);
      border-color: var(--text-accent);
    }

    .integration-logo {
      width: 40px;
      height: 40px;
      background: var(--tech-gradient);
      border-radius: 8px;
      margin: 0 auto 1rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      color: var(--dark-bg);
    }

    .integration-name {
      font-size: 0.9rem;
      color: var(--text-secondary);
      font-family: 'JetBrains Mono', monospace;
    }

    /* API Documentation Preview */
    .api-docs {
      padding: 6rem 0;
      background: var(--darker-bg);
    }

    .api-preview {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 3rem;
      margin-top: 3rem;
    }

    .api-example {
      background: var(--code-bg);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      overflow: hidden;
    }

    .api-header {
      background: rgba(0, 201, 255, 0.1);
      padding: 1rem;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      font-family: 'JetBrains Mono', monospace;
      font-size: 0.9rem;
      color: var(--text-accent);
    }

    .api-body {
      padding: 1.5rem;
      font-family: 'JetBrains Mono', monospace;
      font-size: 0.8rem;
      line-height: 1.5;
    }

    /* Performance Metrics */
    .performance {
      padding: 6rem 0;
      background: var(--dark-bg);
    }

    .performance-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin-top: 3rem;
    }

    .performance-card {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 15px;
      padding: 2.5rem;
      text-align: center;
    }

    .performance-metric {
      font-size: 3rem;
      font-weight: 800;
      color: var(--text-accent);
      font-family: 'JetBrains Mono', monospace;
      margin-bottom: 0.5rem;
    }

    .performance-label {
      color: var(--text-secondary);
      font-size: 1rem;
    }

    .performance-description {
      color: var(--text-secondary);
      font-size: 0.9rem;
      margin-top: 1rem;
      line-height: 1.5;
    }

    /* CTA Section */
    .cta-section {
      background: var(--code-gradient);
      color: white;
      text-align: center;
      padding: 6rem 0;
    }

    .cta-section h2 {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 1rem;
      font-family: 'JetBrains Mono', monospace;
    }

    .cta-section p {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      color: rgba(255, 255, 255, 0.9);
    }

    .cta-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    .btn-secondary {
      background: rgba(255, 255, 255, 0.2);
      border: 2px solid white;
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      text-decoration: none;
      font-weight: 600;
      font-family: 'JetBrains Mono', monospace;
      transition: all 0.3s ease;
    }

    .btn-secondary:hover {
      background: white;
      color: var(--dark-bg);
    }

    /* Footer */
    .footer {
      background: var(--darker-bg);
      color: var(--text-secondary);
      padding: 3rem 0 1rem;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .footer-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      margin-bottom: 2rem;
    }

    .footer-section h3 {
      margin-bottom: 1rem;
      color: var(--text-primary);
      font-family: 'JetBrains Mono', monospace;
    }

    .footer-section ul {
      list-style: none;
    }

    .footer-section ul li {
      margin-bottom: 0.5rem;
    }

    .footer-section ul li a {
      color: var(--text-secondary);
      text-decoration: none;
      transition: color 0.3s ease;
      font-family: 'JetBrains Mono', monospace;
    }

    .footer-section ul li a:hover {
      color: var(--text-accent);
    }

    .footer-bottom {
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      padding-top: 2rem;
      text-align: center;
      font-family: 'JetBrains Mono', monospace;
    }

    /* Animations */
    @keyframes slideInLeft {
      from {
        opacity: 0;
        transform: translateX(-30px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    @keyframes slideInRight {
      from {
        opacity: 0;
        transform: translateX(30px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    .animate-slide-left {
      animation: slideInLeft 0.8s ease forwards;
    }

    .animate-slide-right {
      animation: slideInRight 0.8s ease forwards;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
      }
      
      .hero-text h1 {
        font-size: 2.5rem;
      }
      
      .nav-links {
        display: none;
      }
      
      .api-preview {
        grid-template-columns: 1fr;
      }
      
      .performance-grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <!-- Header -->
  <header class="header" data-controller="navbar">
    <nav class="nav container">
      <a href="/" class="logo">datareflow</a>
      <ul class="nav-links">
        <li><a href="#integrations">Integrations</a></li>
        <li><a href="#api">API Docs</a></li>
        <li><a href="#performance">Performance</a></li>
        <li><a href="#pricing">Pricing</a></li>
      </ul>
      <a href="#demo" class="cta-button">Try API</a>
    </nav>
  </header>

  <!-- Hero Section -->
  <section class="hero">
    <div class="container">
      <div class="hero-content">
        <div class="hero-text animate-slide-left">
          <h1>Developer-First Data Integration</h1>
          <p class="subtitle">
            Build powerful data pipelines with 350+ pre-built connectors, real-time APIs, and enterprise-grade infrastructure. Deploy in minutes, scale to billions of records.
          </p>
          
          <div class="cta-buttons">
            <a href="#api" class="cta-button">Explore API</a>
            <a href="#demo" class="btn-secondary">Live Demo</a>
          </div>
        </div>
        
        <div class="code-block animate-slide-right">
          <div class="code-line"><span class="code-comment"># Quick Start - Connect your first data source</span></div>
          <div class="code-line"><span class="code-keyword">curl</span> -X <span class="code-string">POST</span> \</div>
          <div class="code-line">  <span class="code-string">'https://api.datareflow.com/v1/sources'</span> \</div>
          <div class="code-line">  -H <span class="code-string">'Authorization: Bearer your_api_key'</span> \</div>
          <div class="code-line">  -H <span class="code-string">'Content-Type: application/json'</span> \</div>
          <div class="code-line">  -d <span class="code-string">'{</span></div>
          <div class="code-line">    <span class="code-string">"type": "quickbooks",</span></div>
          <div class="code-line">    <span class="code-string">"config": {</span></div>
          <div class="code-line">      <span class="code-string">"oauth_token": "your_token"</span></div>
          <div class="code-line">    <span class="code-string">}</span></div>
          <div class="code-line">  <span class="code-string">}'</span></div>
          <div class="code-line"></div>
          <div class="code-line"><span class="code-comment"># Response: source created in ~2 seconds</span></div>
        </div>
      </div>
      
      <div class="tech-stats">
        <div class="tech-stat">
          <div class="tech-stat-number" data-controller="counter" data-counter-target-value="350">0</div>
          <div class="tech-stat-label">Pre-built Connectors</div>
        </div>
        <div class="tech-stat">
          <div class="tech-stat-number" data-controller="counter" data-counter-target-value="99">0</div>
          <div class="tech-stat-label">% API Uptime SLA</div>
        </div>
        <div class="tech-stat">
          <div class="tech-stat-number" data-controller="counter" data-counter-target-value="500">0</div>
          <div class="tech-stat-label">ms Average Response Time</div>
        </div>
        <div class="tech-stat">
          <div class="tech-stat-number" data-controller="counter" data-counter-target-value="1">0</div>
          <div class="tech-stat-label">Billion Records/Month</div>
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section id="features" class="features">
    <div class="container">
      <h2 class="section-title">Built for Modern Data Engineering</h2>
      <p class="section-subtitle">
        Enterprise-grade infrastructure with developer-friendly APIs. Scale from prototype to production with zero configuration changes.
      </p>
      
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">🔗</div>
          <h3 class="feature-title">Universal Connectors</h3>
          <p class="feature-description">
            350+ pre-built connectors for databases, SaaS platforms, and APIs. Custom connector builder for proprietary systems.
          </p>
          <ul class="feature-tech-list">
            <li class="tech-badge">PostgreSQL</li>
            <li class="tech-badge">Salesforce</li>
            <li class="tech-badge">Shopify</li>
            <li class="tech-badge">REST API</li>
            <li class="tech-badge">GraphQL</li>
          </ul>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">⚡</div>
          <h3 class="feature-title">Real-time Streaming</h3>
          <p class="feature-description">
            Sub-second data latency with Redis Streams and WebSocket APIs. Process millions of events per second with horizontal scaling.
          </p>
          <ul class="feature-tech-list">
            <li class="tech-badge">WebSockets</li>
            <li class="tech-badge">Redis Streams</li>
            <li class="tech-badge">Event Sourcing</li>
            <li class="tech-badge">CDC</li>
          </ul>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">🛠️</div>
          <h3 class="feature-title">Advanced ETL Engine</h3>
          <p class="feature-description">
            Ruby-based transformation engine with SQL, Python, and JavaScript support. Visual pipeline builder with code export.
          </p>
          <ul class="feature-tech-list">
            <li class="tech-badge">Ruby/Kiba</li>
            <li class="tech-badge">SQL</li>
            <li class="tech-badge">Python</li>
            <li class="tech-badge">JavaScript</li>
          </ul>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">🔒</div>
          <h3 class="feature-title">Enterprise Security</h3>
          <p class="feature-description">
            SOC2 Type II compliant with end-to-end encryption, OAuth 2.0, and audit logging. Zero-trust architecture.
          </p>
          <ul class="feature-tech-list">
            <li class="tech-badge">SOC2</li>
            <li class="tech-badge">OAuth 2.0</li>
            <li class="tech-badge">AES-256</li>
            <li class="tech-badge">RBAC</li>
          </ul>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">📊</div>
          <h3 class="feature-title">Data Quality Engine</h3>
          <p class="feature-description">
            ML-powered data quality scoring with anomaly detection. Automated data profiling and validation rules.
          </p>
          <ul class="feature-tech-list">
            <li class="tech-badge">ML Scoring</li>
            <li class="tech-badge">Anomaly Detection</li>
            <li class="tech-badge">Data Profiling</li>
            <li class="tech-badge">Validation</li>
          </ul>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">🌐</div>
          <h3 class="feature-title">Multi-Cloud Architecture</h3>
          <p class="feature-description">
            Deploy on AWS, GCP, or Azure. Auto-scaling infrastructure with global edge locations for minimum latency.
          </p>
          <ul class="feature-tech-list">
            <li class="tech-badge">Kubernetes</li>
            <li class="tech-badge">Docker</li>
            <li class="tech-badge">Terraform</li>
            <li class="tech-badge">CDN</li>
          </ul>
        </div>
      </div>
    </div>
  </section>

  <!-- Integrations Showcase -->
  <section id="integrations" class="integrations">
    <div class="container">
      <h2 class="section-title">350+ Pre-built Integrations</h2>
      <p class="section-subtitle">
        Connect to any data source with our extensive connector library. New integrations added weekly.
      </p>
      
      <div class="integrations-grid">
        <div class="integration-item">
          <div class="integration-logo">SF</div>
          <div class="integration-name">Salesforce</div>
        </div>
        <div class="integration-item">
          <div class="integration-logo">QB</div>
          <div class="integration-name">QuickBooks</div>
        </div>
        <div class="integration-item">
          <div class="integration-logo">SH</div>
          <div class="integration-name">Shopify</div>
        </div>
        <div class="integration-item">
          <div class="integration-logo">HS</div>
          <div class="integration-name">HubSpot</div>
        </div>
        <div class="integration-item">
          <div class="integration-logo">ST</div>
          <div class="integration-name">Stripe</div>
        </div>
        <div class="integration-item">
          <div class="integration-logo">PG</div>
          <div class="integration-name">PostgreSQL</div>
        </div>
        <div class="integration-item">
          <div class="integration-logo">MY</div>
          <div class="integration-name">MySQL</div>
        </div>
        <div class="integration-item">
          <div class="integration-logo">MG</div>
          <div class="integration-name">MongoDB</div>
        </div>
        <div class="integration-item">
          <div class="integration-logo">SN</div>
          <div class="integration-name">Snowflake</div>
        </div>
        <div class="integration-item">
          <div class="integration-logo">BQ</div>
          <div class="integration-name">BigQuery</div>
        </div>
        <div class="integration-item">
          <div class="integration-logo">RS</div>
          <div class="integration-name">Redshift</div>
        </div>
        <div class="integration-item">
          <div class="integration-logo">+339</div>
          <div class="integration-name">More...</div>
        </div>
      </div>
    </div>
  </section>

  <!-- API Documentation Preview -->
  <section id="api" class="api-docs">
    <div class="container">
      <h2 class="section-title">Developer-Friendly REST API</h2>
      <p class="section-subtitle">
        Comprehensive REST API with OpenAPI 3.0 specification. SDKs available for Ruby, Python, Node.js, and Go.
      </p>
      
      <div class="api-preview">
        <div class="api-example">
          <div class="api-header">POST /v1/pipelines/execute</div>
          <div class="api-body">
<pre><span class="code-comment"># Execute pipeline with custom parameters</span>
<span class="code-keyword">curl</span> -X <span class="code-string">POST</span> \
  <span class="code-string">'https://api.datareflow.com/v1/pipelines/123/execute'</span> \
  -H <span class="code-string">'Authorization: Bearer your_api_key'</span> \
  -H <span class="code-string">'Content-Type: application/json'</span> \
  -d <span class="code-string">'{
    "mode": "async",
    "parameters": {
      "start_date": "2024-01-01",
      "batch_size": 10000
    },
    "webhook_url": "https://your-app.com/hooks"
  }'</span></pre>
          </div>
        </div>
        
        <div class="api-example">
          <div class="api-header">Response 200 OK</div>
          <div class="api-body">
<pre><span class="code-string">{
  "execution_id": "exec_1234567890",
  "status": "running",
  "pipeline_id": "pipe_123",
  "started_at": "2024-01-15T10:30:00Z",
  "estimated_completion": "2024-01-15T10:35:00Z",
  "progress": {
    "records_processed": 0,
    "total_records": 150000,
    "percentage": 0
  },
  "webhook_url": "https://your-app.com/hooks"
}</span></pre>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Performance Metrics -->
  <section id="performance" class="performance">
    <div class="container">
      <h2 class="section-title">Performance at Scale</h2>
      <p class="section-subtitle">
        Built on Rails 8 with Solid Queue for maximum performance and reliability
      </p>
      
      <div class="performance-grid">
        <div class="performance-card">
          <div class="performance-metric">< 500ms</div>
          <div class="performance-label">API Response Time</div>
          <div class="performance-description">
            95th percentile response time across all endpoints with global CDN acceleration
          </div>
        </div>
        
        <div class="performance-card">
          <div class="performance-metric">99.99%</div>
          <div class="performance-label">Data Accuracy</div>
          <div class="performance-description">
            Guaranteed data integrity with automated validation and error correction
          </div>
        </div>
        
        <div class="performance-card">
          <div class="performance-metric">1B+</div>
          <div class="performance-label">Records/Month</div>
          <div class="performance-description">
            Process billions of records monthly with auto-scaling infrastructure
          </div>
        </div>
        
        <div class="performance-card">
          <div class="performance-metric">24/7</div>
          <div class="performance-label">Monitoring</div>
          <div class="performance-description">
            Real-time monitoring with alerting and automated issue resolution
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="cta-section">
    <div class="container">
      <h2>Ready to Build?</h2>
      <p>Start integrating data in minutes with our developer-first platform</p>
      <div class="cta-buttons">
        <a href="#demo" class="cta-button">Start Free Trial</a>
        <a href="#docs" class="btn-secondary">Read API Docs</a>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <h3>$ datareflow</h3>
          <p>Developer-first data integration platform</p>
        </div>
        
        <div class="footer-section">
          <h3>Developer Resources</h3>
          <ul>
            <li><a href="#api-docs">API Documentation</a></li>
            <li><a href="#sdks">SDKs & Libraries</a></li>
            <li><a href="#webhooks">Webhooks</a></li>
            <li><a href="#changelog">Changelog</a></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h3>Integrations</h3>
          <ul>
            <li><a href="#databases">Databases</a></li>
            <li><a href="#saas">SaaS Platforms</a></li>
            <li><a href="#files">File Sources</a></li>
            <li><a href="#custom">Custom Connectors</a></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h3>Support</h3>
          <ul>
            <li><a href="#github">GitHub Issues</a></li>
            <li><a href="#discord">Discord Community</a></li>
            <li><a href="#status">Status Page</a></li>
            <li><a href="#security">Security</a></li>
          </ul>
        </div>
      </div>
      
      <div class="footer-bottom">
        <p>&copy; 2025 DataReflow. All rights reserved. | Built with ❤️ and Rails 8</p>
      </div>
    </div>
  </footer>

  <!-- Stimulus Controllers -->
  <script>
    // Counter Animation Controller
    class Counter {
      connect() {
        this.animateCounter();
      }
      
      animateCounter() {
        const targetValue = parseInt(this.element.dataset.counterTargetValue);
        const duration = 2000;
        const stepTime = 50;
        const steps = duration / stepTime;
        const increment = targetValue / steps;
        let current = 0;
        
        const timer = setInterval(() => {
          current += increment;
          if (current >= targetValue) {
            current = targetValue;
            clearInterval(timer);
          }
          this.element.textContent = Math.floor(current);
        }, stepTime);
      }
    }
    
    // Navbar Controller
    class Navbar {
      connect() {
        window.addEventListener("scroll", () => this.handleScroll());
      }
      
      handleScroll() {
        if (window.scrollY > 100) {
          this.element.style.background = "rgba(15, 23, 42, 0.95)";
          this.element.style.borderBottom = "1px solid rgba(0, 201, 255, 0.3)";
        } else {
          this.element.style.background = "rgba(15, 23, 42, 0.9)";
          this.element.style.borderBottom = "1px solid rgba(255, 255, 255, 0.1)";
        }
      }
    }
    
    // Initialize controllers
    document.addEventListener("DOMContentLoaded", () => {
      // Initialize counters
      document.querySelectorAll('[data-controller*="counter"]').forEach(element => {
        const counter = new Counter();
        counter.element = element;
        counter.connect();
      });
      
      // Initialize navbar
      document.querySelectorAll('[data-controller*="navbar"]').forEach(element => {
        const navbar = new Navbar();
        navbar.element = element;
        navbar.connect();
      });
      
      // Smooth scrolling for anchor links
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href'));
          if (target) {
            target.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        });
      });
    });
  </script>
</body>
</html>