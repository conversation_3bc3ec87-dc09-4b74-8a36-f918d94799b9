# SME Data Refinery Platform

## Project Overview

A Rails 8-native data integration and analytics platform designed specifically for Small and Medium Enterprises (SMEs). This platform democratizes enterprise-grade data capabilities with SME-friendly pricing, no-code interfaces, and rapid time-to-value.

## Vision Statement

Empower SMEs to make data-driven decisions by providing enterprise-grade data integration, transformation, and analytics capabilities at SME-friendly prices and complexity levels.

## Key Value Propositions

- **SME-First Design**: Built specifically for businesses with 5-500 employees
- **Transparent Pricing**: $149-899/month with clear usage limits
- **No-Code Experience**: Business users can set up integrations without technical teams
- **Rails 8 Native**: Modern architecture using Solid Queue, Solid Cache, and Solid Cable
- **Rapid Deployment**: From signup to insights in under 30 minutes

## Technical Architecture

### Stack Overview
```
Frontend: Rails 8 + Hotwire/Turbo + Tailwind CSS
Backend: Ruby on Rails 8.0+
Database: PostgreSQL with multi-tenant row-level security
Background Jobs: Solid Queue (Rails 8 native)
Caching: Solid Cache (Rails 8 native)  
Real-time: Solid Cable (Rails 8 native)
ETL Engine: Kiba + Custom Ruby services
Authentication: Rails built-in with encryption
```

### Core Architecture Principles

1. **Multi-Tenant by Design**: Every model scoped to Organization using acts_as_tenant
2. **Service Objects**: Business logic encapsulated in testable service classes
3. **Background Processing**: All heavy operations via Solid Queue jobs
4. **Real-time Updates**: Hotwire/Turbo Streams for live dashboard updates
5. **Security First**: Encrypted credentials, audit logs, SOC2 compliance ready

### Directory Structure
```
app/
├── controllers/          # Rails controllers with Pundit authorization
├── models/              # Domain models with multi-tenant scoping
├── services/            # Business logic and external API interactions
├── jobs/                # Background processing with Solid Queue
├── channels/            # ActionCable channels for real-time updates
├── views/               # ERB templates with Hotwire integration
└── javascript/          # Stimulus controllers for interactivity

lib/
├── connectors/          # Data source connector implementations
├── transformers/        # Data transformation engines
└── quality_scorers/     # Data quality analysis algorithms

config/
├── routes.rb           # RESTful routes with nested resources
├── solid_queue.yml     # Background job configuration
└── application.rb      # Rails 8 configuration
```

## Development Setup

### Prerequisites
- Ruby 3.2+
- Rails 8.0+
- PostgreSQL 15+
- Node.js 18+ (for Tailwind CSS)

### Initial Setup
```bash
# Clone and setup
git clone <repository-url>
cd sme-data-refinery
bundle install

# Database setup
rails db:create
rails db:migrate
rails db:seed

# Install Solid gems
rails solid_queue:install
rails solid_cache:install
rails solid_cable:install

# Start development server
bin/dev
```

### Environment Variables
```bash
# .env
DATABASE_URL=postgresql://localhost/sme_data_refinery_development
RAILS_MASTER_KEY=<generated-key>
ENCRYPTION_KEY=<32-byte-hex-key>

# Optional: External service keys
OPENAI_API_KEY=<for-smart-mapping>
STRIPE_SECRET_KEY=<for-billing>
```

## Core Domain Models

### Organization (Tenant)
Multi-tenant boundary with plan-based feature access
```ruby
has_many :users, :data_sources, :pipelines, :dashboards
validates :plan, inclusion: { in: %w[starter professional business enterprise] }
```

### DataSource 
External system connections with encrypted credentials
```ruby
belongs_to :organization
validates :kind, inclusion: { in: %w[api database file stream webhook] }
encrypts :credentials
```

### Pipeline
Data processing workflows with ETL/ELT modes
```ruby
belongs_to :organization
has_many :data_quality_reports
validates :mode, inclusion: { in: %w[etl elt] }
```

### Integration
Specific data source configurations and mappings
```ruby
belongs_to :data_source
encrypts :suggested_mappings
scope :active, -> { where(status: 'active') }
```

## Key Features Implementation

### 1. Smart Data Mapping
AI-powered field matching using Levenshtein distance and confidence scoring
- Service: `SmartMappingService`
- Job: `SchemaAnalysisJob` 
- UI: Real-time mapping suggestions via Turbo Streams

### 2. Data Quality Scoring
Automated quality assessment across multiple dimensions
- Metrics: Completeness, Uniqueness, Timeliness, Accuracy
- Service: `DataQualityScorerService`
- Algorithm: Weighted scoring with ML-based anomaly detection

### 3. Real-time Dashboards
Live-updating analytics with Hotwire/Turbo integration
- Technology: ActionCable + Solid Cable
- Updates: Pipeline status, quality scores, data flows
- Performance: Sub-second update delivery

### 4. Custom Connector Builder
Visual connector creation for non-standard data sources
- UI: Drag-and-drop interface with live testing
- Backend: Dynamic connector generation and validation
- Storage: Encrypted configuration in `CustomConnector` model

## API Design

### RESTful Endpoints
```
Organizations (Tenant context)
├── GET    /organizations/:id
├── PATCH  /organizations/:id
│
├── Data Sources
│   ├── GET    /organizations/:org_id/data_sources
│   ├── POST   /organizations/:org_id/data_sources
│   ├── POST   /organizations/:org_id/data_sources/:id/test_connection
│   │
│   └── Integrations
│       ├── GET    /data_sources/:ds_id/integrations
│       ├── POST   /data_sources/:ds_id/integrations
│       └── POST   /integrations/:id/analyze_schema
│
├── Pipelines
│   ├── GET    /organizations/:org_id/pipelines
│   ├── POST   /organizations/:org_id/pipelines
│   ├── POST   /pipelines/:id/execute
│   └── GET    /pipelines/:id/status
│
└── Dashboards
    ├── GET    /organizations/:org_id/dashboards
    └── POST   /organizations/:org_id/dashboards
```

### Webhook Endpoints
```
POST /webhooks/data_source/:id    # Incoming data from external sources
POST /webhooks/pipeline/:id       # Pipeline execution triggers
```

## Testing Strategy

### Test Coverage Requirements
- Models: 100% coverage with unit tests
- Services: 100% coverage with comprehensive scenarios
- Controllers: Integration tests for all endpoints
- Jobs: Background job testing with inline execution
- System: End-to-end user flows

### Test Categories
```ruby
# spec/models/ - Model validations and associations
# spec/services/ - Business logic and external API mocking
# spec/jobs/ - Background job execution and error handling
# spec/requests/ - API endpoint testing
# spec/system/ - Full user journey testing with Capybara
```

### Quality Gates
- All tests must pass before deployment
- Code coverage >90%
- Security scanning with Brakeman
- Performance testing for data processing

## Deployment Architecture

### Development Environment
- Single Rails server with Solid Queue workers
- PostgreSQL database with sample data
- Mailcatcher for email testing

### Staging Environment
- Production-like setup with reduced resources
- Separate database with anonymized production data
- Full monitoring and logging stack

### Production Environment
```
Load Balancer (AWS ALB)
├── Web Servers (2+ Rails instances)
├── Background Workers (Solid Queue processes)
├── Database (PostgreSQL with read replicas)
├── Cache (Redis for session/fragment caching)
└── Monitoring (Application metrics and logs)
```

## Security & Compliance

### Data Protection
- All sensitive data encrypted at rest using Rails encryption
- TLS 1.3 for data in transit
- Multi-factor authentication support
- Regular security audits and penetration testing

### Compliance Readiness
- SOC2 Type II compliance framework
- GDPR data processing and retention policies
- Audit logging for all data access and modifications
- Data deletion and export capabilities

### Access Control
- Role-based permissions with Pundit policies
- Organization-level data isolation
- API key management for external integrations
- Session management and timeout controls

## Monitoring & Observability

### Application Metrics
- Request/response times and error rates
- Background job processing metrics
- Database query performance
- Data pipeline success/failure rates

### Business Metrics
- User activation and engagement
- Feature adoption rates
- Customer health scores
- Revenue and subscription metrics

### Alerting
- System health and performance alerts
- Data pipeline failure notifications
- Security incident detection
- Customer impact notifications

## Contributing Guidelines

### Code Standards
- Follow Rails conventions and best practices
- Use Rubocop with project-specific rules
- Write descriptive commit messages
- Include tests for all new functionality

### Development Workflow
1. Create feature branch from main
2. Implement feature with comprehensive tests
3. Submit pull request with description
4. Code review by senior team member
5. Deploy to staging for validation
6. Merge to main and deploy to production

### Code Review Checklist
- [ ] Functionality works as specified
- [ ] Tests cover all scenarios including edge cases
- [ ] Security considerations addressed
- [ ] Performance impact evaluated
- [ ] Documentation updated
- [ ] Multi-tenant data isolation verified

## Claude Code Integration

This project is optimized for Claude Code development workflows:

### Project Structure Recognition
- Clear separation of concerns with service objects
- Comprehensive test coverage for reliable refactoring
- Well-documented APIs and data models
- Consistent naming conventions throughout

### Common Development Tasks
```bash
# Generate new data connector
claude generate connector --name="HubSpot CRM" --type=api

# Create new pipeline transformation
claude generate transformer --input=json --output=csv

# Add new dashboard component
claude generate dashboard_component --name="quality_score_chart"

# Implement new service object
claude generate service --name="DataExportService" --purpose="CSV export"
```

### Testing with Claude Code
```bash
# Run focused tests during development
claude test --pattern="DataQualityScorer"

# Generate test data for specific scenarios
claude test_data --model=Pipeline --scenario="failed_execution"

# Performance testing for data processing
claude benchmark --service="EtlProcessorService" --records=1000000
```

This documentation provides Claude Code with comprehensive context for understanding the project structure, making intelligent suggestions, and implementing features that align with the established patterns and architectural decisions.