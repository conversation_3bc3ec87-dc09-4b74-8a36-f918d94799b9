# app/models/organization.rb
class Organization < ApplicationRecord
  acts_as_tenant
  
  has_many :users, dependent: :destroy
  has_many :data_sources, dependent: :destroy
  has_many :dashboards, dependent: :destroy
  has_many :pipelines, through: :data_sources
  
  validates :name, presence: true
  validates :plan, inclusion: { in: %w[starter professional enterprise] }
  
  encrypts :settings
  
  def monthly_data_limit
    case plan
    when 'starter' then 10_000_000    # 10M rows
    when 'professional' then 100_000_000  # 100M rows
    when 'enterprise' then Float::INFINITY
    end
  end
end

# app/models/user.rb
class User < ApplicationRecord
  acts_as_tenant :organization
  
  has_secure_password
  has_many :dashboards, dependent: :destroy
  has_many :custom_connectors, dependent: :destroy
  
  validates :email, presence: true, uniqueness: { scope: :organization_id }
  validates :role, inclusion: { in: %w[admin member viewer] }
  
  def can_create_connectors?
    %w[admin member].include?(role)
  end
end

# app/models/data_source.rb
class DataSource < ApplicationRecord
  acts_as_tenant :organization
  
  has_many :integrations, dependent: :destroy
  has_many :pipelines, through: :integrations
  
  validates :name, presence: true
  validates :kind, inclusion: { in: %w[api database file stream webhook] }
  validates :connection_url, presence: true
  
  encrypts :credentials
  
  def test_connection
    DataSourceTestJob.perform_later(id)
  end
end

# app/models/integration.rb
class Integration < ApplicationRecord
  acts_as_tenant :organization
  
  belongs_to :data_source
  has_many :source_pipelines, dependent: :destroy
  has_many :pipelines, through: :source_pipelines
  
  validates :status, inclusion: { in: %w[pending active failed] }
  
  encrypts :suggested_mappings
  
  scope :active, -> { where(status: 'active') }
end

# app/models/pipeline.rb
class Pipeline < ApplicationRecord
  acts_as_tenant :organization
  
  has_many :source_pipelines, dependent: :destroy
  has_many :integrations, through: :source_pipelines
  has_many :data_mappings, dependent: :destroy
  has_many :data_quality_reports, dependent: :destroy
  
  validates :mode, inclusion: { in: %w[etl elt] }
  validates :status, inclusion: { in: %w[pending running completed failed] }
  
  encrypts :configuration
  
  def execute!
    PipelineExecutionJob.perform_later(id)
  end
  
  def latest_quality_score
    data_quality_reports.order(created_at: :desc).first&.quality_score
  end
end

# app/models/data_mapping.rb
class DataMapping < ApplicationRecord
  acts_as_tenant :organization
  
  belongs_to :pipeline
  
  validates :confidence_score, numericality: { in: 0.0..1.0 }
  
  encrypts :mapping_json
end

# app/models/data_quality_report.rb
class DataQualityReport < ApplicationRecord
  acts_as_tenant :organization
  
  belongs_to :pipeline
  
  validates :quality_score, numericality: { in: 0..100 }
  
  encrypts :recommendations
  
  scope :recent, -> { where('created_at > ?', 30.days.ago) }
  scope :high_quality, -> { where('quality_score >= ?', 80) }
end

# app/models/dashboard.rb
class Dashboard < ApplicationRecord
  acts_as_tenant :organization
  
  belongs_to :user
  has_one :insight, dependent: :destroy
  
  validates :name, presence: true
  
  encrypts :layout_json
end

# app/models/custom_connector.rb
class CustomConnector < ApplicationRecord
  acts_as_tenant :organization
  
  belongs_to :user
  belongs_to :data_source, optional: true
  
  validates :name, presence: true
  validates :status, inclusion: { in: %w[draft testing active failed] }
  
  encrypts :config_json
end