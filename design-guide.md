# Data Reflow Design System Guide

## Brand Identity

### Mission
Enterprise-grade data analytics platform designed to transform raw data into actionable insights for SMEs.

### Value Proposition
- 30% cost reduction
- 40% efficiency improvement  
- ROI within 90 days
- 99.9% uptime guarantee

## Design Principles

1. **Professional & Trustworthy**: Enterprise-grade appearance that instills confidence
2. **Clear Hierarchy**: Information architecture that guides users naturally
3. **Data-Focused**: Visualizations and metrics take center stage
4. **Accessible**: WCAG 2.1 AA compliance for inclusive design
5. **Performance**: Fast, responsive, and efficient

## Color System

### Primary Colors
```css
/* Light Mode */
--color-primary: rgba(33, 128, 141, 1);       /* #21808D - Teal */
--color-primary-hover: rgba(29, 116, 128, 1); /* #1D7480 */
--color-primary-active: rgba(26, 104, 115, 1);/* #1A6873 */

/* Dark Mode */
--color-primary: rgba(50, 184, 198, 1);       /* #32B8C6 - Light Teal */
--color-primary-hover: rgba(45, 166, 178, 1); /* #2DA6B2 */
--color-primary-active: rgba(41, 150, 161, 1);/* #2996A1 */
```

### Neutral Colors
```css
/* Light Mode */
--color-background: rgba(252, 252, 249, 1);   /* #FCFCF9 - Off-white */
--color-surface: rgba(255, 255, 253, 1);      /* #FFFFFD - White */
--color-text: rgba(19, 52, 59, 1);           /* #13343B - Dark Blue-Gray */
--color-text-secondary: rgba(98, 108, 113, 1);/* #626C71 - Gray */

/* Dark Mode */
--color-background: rgba(31, 33, 33, 1);      /* #1F2121 - Near Black */
--color-surface: rgba(38, 40, 40, 1);         /* #262828 - Dark Gray */
--color-text: rgba(245, 245, 245, 1);        /* #F5F5F5 - Off-White */
--color-text-secondary: rgba(167, 169, 169, 0.7);
```

### Semantic Colors
```css
--color-success: /* Same as primary */
--color-error: rgba(192, 21, 47, 1);         /* #C0152F - Red */
--color-warning: rgba(168, 75, 47, 1);       /* #A84B2F - Orange */
--color-info: rgba(98, 108, 113, 1);         /* #626C71 - Gray */
```

## Typography

### Font Stack
```css
--font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
--font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
```

### Type Scale
| Level | Size | Use Case |
|-------|------|----------|
| xs    | 11px | Micro text, labels |
| sm    | 12px | Secondary text, captions |
| base  | 14px | Body text |
| lg    | 16px | Emphasized body |
| xl    | 18px | Section headers |
| 2xl   | 20px | Sub-headings |
| 3xl   | 24px | Page titles |
| 4xl   | 30px | Hero titles |

### Font Weights
- Normal: 400
- Medium: 500
- Semibold: 550
- Bold: 600

## Spacing System

Based on 4px grid:
```css
--space-1: 1px
--space-2: 2px
--space-4: 4px    /* 0.25rem */
--space-6: 6px    /* 0.375rem */
--space-8: 8px    /* 0.5rem */
--space-10: 10px  /* 0.625rem */
--space-12: 12px  /* 0.75rem */
--space-16: 16px  /* 1rem */
--space-20: 20px  /* 1.25rem */
--space-24: 24px  /* 1.5rem */
--space-32: 32px  /* 2rem */
```

## Border Radius
```css
--radius-sm: 6px   /* Small elements */
--radius-base: 8px /* Default */
--radius-md: 10px  /* Cards */
--radius-lg: 12px  /* Large cards */
--radius-full: 9999px /* Pills, avatars */
```

## Shadows
```css
--shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02)
--shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02)
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04), 0 2px 4px -1px rgba(0, 0, 0, 0.02)
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04), 0 4px 6px -2px rgba(0, 0, 0, 0.02)
```

## Component Patterns

### Buttons
```css
/* Primary */
.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

/* Secondary */
.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

/* Outline */
.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}
```

### Cards
```css
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
}
```

### Status Indicators
```css
.status {
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}
```

## Layout Grid

### Container Widths
```css
--container-sm: 640px
--container-md: 768px
--container-lg: 1024px
--container-xl: 1280px
```

### Breakpoints
- Mobile: < 640px
- Tablet: 640px - 1024px
- Desktop: > 1024px

## Animation & Transitions

### Duration
```css
--duration-fast: 150ms
--duration-normal: 250ms
```

### Easing
```css
--ease-standard: cubic-bezier(0.16, 1, 0.3, 1)
```

### Common Animations
- Fade In Up: For content entering view
- Slide In: For notifications
- Scale: For hover states

## Accessibility Guidelines

### Focus States
```css
--focus-ring: 0 0 0 3px var(--color-focus-ring);
--focus-outline: 2px solid var(--color-primary);
```

### Color Contrast
- Normal text: 4.5:1 minimum
- Large text: 3:1 minimum
- Interactive elements: Clear hover/focus states

### Keyboard Navigation
- All interactive elements keyboard accessible
- Logical tab order
- Skip links for main content

## Usage Guidelines

### Professional Tone
- Clear, concise messaging
- Data-driven value propositions
- Enterprise-appropriate language

### Visual Hierarchy
1. Hero sections with strong CTAs
2. Social proof (testimonials, metrics)
3. Feature benefits with visuals
4. Clear pricing tiers
5. Strong closing CTA

### Content Patterns
- Lead with benefits, not features
- Use specific metrics and numbers
- Include customer success stories
- Show real dashboard examples
- Transparent pricing

## Implementation Notes

### Performance
- Lazy load images
- Optimize font loading
- Minimize CSS/JS bundles
- Use CSS custom properties for theming

### SEO
- Semantic HTML structure
- Proper heading hierarchy
- Meta descriptions
- Alt text for images

### Testing
- Cross-browser compatibility
- Responsive design testing
- Accessibility audits
- Performance monitoring