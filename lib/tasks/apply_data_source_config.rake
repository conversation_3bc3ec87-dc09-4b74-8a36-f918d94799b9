namespace :data_source do
  desc "Apply DataSourceConfig to all files (run after server restart)"
  task apply_config: :environment do
    puts "Applying DataSourceConfig to files..."
    
    # Update controllers
    controller_file = Rails.root.join('app/controllers/dashboard/data_sources_controller.rb')
    content = File.read(controller_file)
    
    content.gsub!('@recent_syncs = @data_source.sync_logs.recent.limit(5)', 
                  '@recent_syncs = @data_source.sync_logs.recent.limit(DataSourceConfig.ui[\'recent_syncs_limit\'])')
    content.gsub!('@sample_data_result = @data_source.fetch_sample_data(limit: 20)', 
                  '@sample_data_result = @data_source.fetch_sample_data(limit: DataSourceConfig.ui[\'sample_data_limit\'])')
    content.gsub!('@sync_logs = @data_source.sync_logs.recent.page(params[:page]).per(20)', 
                  '@sync_logs = @data_source.sync_logs.recent.page(params[:page]).per(DataSourceConfig.ui[\'pagination_per_page\'])')
    content.gsub!("uploads_dir = Rails.root.join('storage', 'uploads', 'csv', current_organization.id.to_s)", 
                  "uploads_dir = Rails.root.join(DataSourceConfig.uploads['base_path'], 'csv', current_organization.id.to_s)")
    
    File.write(controller_file, content)
    puts "✓ Updated data_sources_controller.rb"
    
    # Update model
    model_file = Rails.root.join('app/models/data_source.rb')
    content = File.read(model_file)
    
    content.gsub!('1.hour.ago', 
                  'DataSourceConfig.sync[\'needs_sync_threshold_hours\'].hours.ago')
    content.gsub!('def fetch_sample_data(limit: 10)', 
                  'def fetch_sample_data(limit: DataSourceConfig.ui[\'sample_data_limit\'])')
    
    # Replace calculate_next_sync_time method
    old_method = content[/def calculate_next_sync_time.*?end\n  end/m]
    if old_method
      new_method = <<-RUBY
  def calculate_next_sync_time
    return nil if sync_frequency.blank? || sync_frequency == 'manual'
    
    seconds = DataSourceConfig.sync_frequencies[sync_frequency]
    seconds ? seconds.seconds.from_now : nil
  end
      RUBY
      content.sub!(old_method, new_method.strip + "\n  end")
    end
    
    File.write(model_file, content)
    puts "✓ Updated data_source.rb"
    
    puts "\nConfiguration successfully applied!"
    puts "The DataSourceConfig is now being used throughout the application."
  end
  
  desc "Check if DataSourceConfig is loaded"
  task check_config: :environment do
    begin
      puts "DataSourceConfig loaded: #{DataSourceConfig.config.present? ? 'Yes' : 'No'}"
      if DataSourceConfig.config.present?
        puts "\nConfiguration values:"
        puts "- CSV max file size: #{DataSourceConfig.csv['max_file_size']} bytes"
        puts "- Recent syncs limit: #{DataSourceConfig.ui['recent_syncs_limit']}"
        puts "- Sample data limit: #{DataSourceConfig.ui['sample_data_limit']}"
        puts "- Pagination per page: #{DataSourceConfig.ui['pagination_per_page']}"
      end
    rescue => e
      puts "DataSourceConfig not loaded: #{e.message}"
      puts "Please restart the Rails server to load the configuration."
    end
  end
end